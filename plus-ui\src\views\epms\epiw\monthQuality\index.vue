<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="监测日期" prop="detectionTime">
              <el-date-picker
                clearable
                v-model="queryParams.params"
                type="monthrange"
                value-format="YYYY-MM"
                start-placeholder="起始月份"
                end-placeholder="结束月份"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="所属地" prop="operationAreaId">
              <el-select v-model="queryParams.operationAreaId" clearable placeholder="请选择所属地">
                <el-option v-for="dict in suoShuDiList" :key="dict.operationAreaId" :label="dict.operationAreaName" :value="dict.operationAreaId" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epms:monthQuality:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:monthQuality:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:monthQuality:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:monthQuality:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="monthQualityList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column label="监测月份" align="center" prop="detectionTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.detectionTime, '{y}-{m}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="所属地" min-width="100" prop="operationAreaId">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="上传时间" align="center" prop="uploadTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="月度水质监测" placement="top">
              <el-button v-hasPermi="['epms:monthQuality:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >监测预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:monthQuality:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epms:monthQuality:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['epms:monthQuality:remove']">删除</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改月度水质监测对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="monthQualityFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="监测月份" prop="detectionTime">
          <el-date-picker v-model="form.detectionTime" clearable placeholder="请选择监测月份" type="month" value-format="YYYY-MM"> </el-date-picker>
        </el-form-item>
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select filterable v-model="form.operationAreaId" placeholder="选择所属地" clearable allow-create>
            <el-option
              v-for="operationArea in suoShuDiList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="水质监测附件" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.qualityReportId"
            :disabled="false"
            attach-category="monthQuality"
            attach-source-type="operationArea"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="monthQualityFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <el-form-item label="监测月份" prop="detectionTime">
          <el-date-picker v-model="form.detectionTime" clearable placeholder="请选择监测月份" type="month" value-format="YYYY-MM"> </el-date-picker>
        </el-form-item>
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select filterable v-model="form.operationAreaId" placeholder="选择所属地" clearable allow-create>
            <el-option
              v-for="operationArea in suoShuDiList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker v-model="form.uploadTime" clearable placeholder="上传时间" type="datetime"> </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MonthQuality" lang="ts">
import { addMonthQuality, delMonthQuality, getMonthQuality, listMonthQuality, updateMonthQuality } from '@/api/epms/epiw/monthQuality';
import { MonthQualityForm, MonthQualityQuery, MonthQualityVO } from '@/api/epms/epiw/monthQuality/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const operationAreaQuery = ref<OperationAreaQuery>({});
const suoShuDiList = ref<OperationAreaVO[]>([]);
const monthQualityList = ref<MonthQualityVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const monthQualityFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MonthQualityForm = {
  qualityReportId: undefined,
  detectionTime: undefined,
  file: undefined
};
const data = reactive<PageData<MonthQualityForm, MonthQualityQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    operationAreaId: undefined,
    detectionTime: undefined,
    file: undefined,
    params: {}
  },
  rules: {
    qualityReportId: [{ required: true, message: '监测报告id不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '所属地不能为空', trigger: 'blur' }],
    detectionTime: [{ required: true, message: '监测时间不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询月度水质监测列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMonthQuality(queryParams.value);
  monthQualityList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  monthQualityFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.params = {};
  queryParams.value.operationAreaId = null;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: MonthQualityVO[]) => {
  ids.value = selection.map((item) => item.qualityReportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '';
  }
  const operationAreaItem = suoShuDiList.value.find((item) => item.operationAreaId === operationAreaId);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加月度水质监测';
};

const handlePreview = async (row?: MonthQualityVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.qualityReportId,
    attachSourceType: 'operationArea',
    attachCategory: 'monthQuality'
  });
};
const handleDetail = async (row?: MonthQualityVO) => {
  reset();
  const _qualityReportId = row?.qualityReportId || ids.value[0];
  const res = await getMonthQuality(_qualityReportId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '月度水质监测详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: MonthQualityVO) => {
  reset();
  const _qualityReportId = row?.qualityReportId || ids.value[0];
  const res = await getMonthQuality(_qualityReportId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改月度水质监测';
};

/** 提交按钮 */
const submitForm = () => {
  monthQualityFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.qualityReportId) {
        await updateMonthQuality(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addMonthQuality(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: MonthQualityVO) => {
  const _qualityReportIds = row?.qualityReportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除月度水质监测编号为"' + _qualityReportIds + '"的数据项？').finally(() => (loading.value = false));
  await delMonthQuality(_qualityReportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/monthQuality/export',
    {
      ...queryParams.value
    },
    `月度水质监测_${new Date().getTime()}.xlsx`
  );
};
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaType = 0;
  const pianquRes = await listOperationArea(operationAreaQuery.value); //查询所属地
  //排除矿管站和输油处
  const excludeIds = ['1906889578986254337', '1906889534388219906'];
  suoShuDiList.value = pianquRes.rows.filter((item) => !excludeIds.includes(item.operationAreaId));
};
const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
  form.value.balanceReportName = fielInfo.name;
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getoperationAreaList();
  getList();
});
</script>
