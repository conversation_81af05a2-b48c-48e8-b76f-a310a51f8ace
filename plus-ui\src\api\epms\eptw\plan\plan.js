import request from '@/utils/request.js';

// 查询指标计划列表
export function listPlan(query) {
  return request({
    url: '/epms/plan/list',
    method: 'get',
    params: query
  });
}

// 查询指标计划详细
export function getPlan(indicatorPlanId) {
  return request({
    url: '/epms/plan/' + indicatorPlanId,
    method: 'get'
  });
}

// 新增指标计划
export function addPlan(data) {
  return request({
    url: '/epms/plan',
    method: 'post',
    data: data
  });
}

// 修改指标计划
export function updatePlan(data) {
  return request({
    url: '/epms/plan',
    method: 'put',
    data: data
  });
}
// 修改指标计划
export function updateThreeLevelPlan(data) {
  return request({
    url: '/epms/plan/editThreeLevel',
    method: 'post',
    data: data
  });
}

// 删除指标计划
export function delPlan(delParam) {
  return request({
    url: '/epms/plan',
    method: 'delete',
    params: delParam
  });
}
// 删除指标计划
export function delThreeLevelPlan(delParam) {
  return request({
    url: '/epms/plan/deleteThreeLevelPlan',
    method: 'post',
    data: delParam
  });
}

// 查询指标计划列表
export function listPlanWithIndicator(query) {
  return request({
    url: '/epms/plan/listWithIndicator',
    method: 'get',
    params: query
  });
}
// 查询指标计划列表
export function listPlanWithParentId(query) {
  return request({
    url: '/epms/plan/listPlanWithParentId',
    method: 'get',
    params: query
  });
}

export function queryTempIndicatorMap(query) {
  return request({
    url: '/epms/indicatortemplatedetail/queryTempIndicatorMap',
    method: 'post',
    data: query
  });
}

export function initAreaPlan(query) {
  return request({
    url: '/epms/plan/initAreaPlan',
    method: 'post',
    params: query
  });
}

export function insertOrUpdateDetail(query) {
  return request({
    url: '/epms/plandetail/insertOrUpdateDetail',
    method: 'post',
    data: query
  });
}

export function selectQuYuList(query) {
  return request({
    url: '/epms/operationArea/list',
    method: 'get',
    params: query
  });
}
export function initMonthPlan(query) {
  return request({
    url: '/epms/plan/initMonthPlan',
    method: 'post',
    data: query
  });
}
export function queryMonthvalues(query) {
  return request({
    url: '/epms/plan/queryMonthvalues',
    method: 'post',
    data: query
  });
}
