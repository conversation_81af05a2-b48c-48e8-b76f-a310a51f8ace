import{Q as v,aH as E,c as _,o as l,q as t,p as i,t as o,A as N,B as I,F as B,C as T,x as c,D as P,w as f,K as k,J as x,v as C,M as q,a7 as Z,z as d,ay as F,ax as V,E as S}from"./index-Bm6k27Yz.js";import{E as j}from"./el-row-DPMJBKHh.js";import{E as M}from"./el-progress-D8KwIucv.js";import{E as Q}from"./el-col-BjQCqZOL.js";import{t as z,q as A}from"./jhtj-VOIBVzie.js";import{l as K}from"./index-BJWPDEaL.js";import{m as O}from"./dayjs.min-Dqr9lGM-.js";const R={name:"jhtj",computed:{cpuNianHuanBi:function(){return this.leiJiData.nianHuanBi&&this.leiJiData.nianHuanBi===-1?"--":this.leiJiData.nianHuanBi||0},cpuYueHuanBi:function(){return this.leiJiData.yueJiHua&&this.leiJiData.yueJiHua===-1?"--":this.leiJiData.yueJiHua||0}},data(){return{currentNengYuanName:"取水指标",leiJiLoading:!1,dropDownLoading:!1,nengYuanZongHaoLoading:!1,qbLoading:!1,form:{org:0,nengYuanId:1,indicatorTypeId:103,dateTime:O().format("YYYY-MM-DD"),indicatorPlanType:2},leiJiData:{},nengYuanList:[],echartItem:null,nengYuanZongHaoData:[],getParkData:[],licenseQuery:{licenseType:1}}},mounted(){this.initData();let a=this;window.addEventListener("resize",function(){a.echartItem&&a.echartItem.resize()})},methods:{async initData(){await this.getParkList(),await this.loadNengYuanType(),await this.loadLeiJi()},setCurrentNengYuanName(){this.nengYuanList&&this.nengYuanList.length>0?this.currentNengYuanName=this.nengYuanList.filter(a=>a.value===this.form.nengYuanId)[0].label:this.currentNengYuanName=""},search(){this.loadLeiJi(),this.setCurrentNengYuanName()},async getParkList(){await K(this.licenseQuery).then(a=>{a.code===200?(this.getParkData=a.rows,this.form.org=this.getParkData[0].waterDrawingLicenseId):this.$modal.msgError("获取数据失败")})},async loadNengYuanType(){try{this.dropDownLoading=!0,await A().then(a=>{if(a){this.nengYuanList=[];for(const n of a.data)this.nengYuanList.push({label:n.aliasName,value:n.indicatorId})}}),this.dropDownLoading=!1}catch{S.error("获取能源类型的下拉信息失败"),this.dropDownLoading=!1}},async loadLeiJi(){try{this.leiJiLoading=!0,this.nengYuanZongHaoData=[],await z({danwei:this.form.org,indicatorId:this.form.nengYuanId,indicatorTypeId:this.form.indicatorTypeId,dateTime:this.form.dateTime,indicatorPlanType:this.form.indicatorPlanType}).then(a=>{a.code===200&&(this.leiJiData=a.data,this.leiJiData.shengYu=this.leiJiData.nianJiHua-this.leiJiData.nianLeiJi,this.nengYuanZongHaoData.push(this.leiJiData))})}catch(a){this.$modal.msgError(`左上角的累计数据获取异常,${a}`)}finally{this.leiJiLoading=!1}}}},U={class:"p-2"},G={class:"jhtjFull"},W={class:"totalBox"},X={class:"totalBoxHeader",style:{background:"#e6a23c"}},$={class:"totalBoxCont"},ee={class:"num"},ae={class:"titProgress"};function ie(a,n,ne,te,e,D){const J=P,L=I,g=N,y=k,w=q,r=Q,Y=M,m=j,p=Z,u=V,H=F,b=E("hasPermi"),h=C;return l(),_("div",U,[t("div",G,[t("div",null,[i(w,{ref:"queryParams",inline:!0,"label-width":"100",model:e.form,class:"mt10 mb10"},{default:o(()=>[i(g,{label:"用水批复函",prop:"applyId"},{default:o(()=>[i(L,{modelValue:e.form.org,"onUpdate:modelValue":n[0]||(n[0]=s=>e.form.org=s),placeholder:"选择用水批复函"},{default:o(()=>[(l(!0),_(B,null,T(e.getParkData,s=>(l(),c(J,{key:s.waterDrawingLicenseId,label:s.waterDrawingLicenseCode,value:s.waterDrawingLicenseId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(g,null,{default:o(()=>[f((l(),c(y,{disabled:e.dropDownLoading||e.nengYuanZongHaoLoading||e.leiJiLoading||e.qbLoading,icon:"Search",type:"primary",onClick:D.search},{default:o(()=>n[1]||(n[1]=[x("查询 ")])),_:1},8,["disabled","onClick"])),[[b,["emcs:plan:quanBuNengHaoTongJi"]],[h,e.dropDownLoading||e.nengYuanZongHaoLoading||e.leiJiLoading||e.qbLoading]])]),_:1})]),_:1},8,["model"]),i(m,{gutter:10,style:{height:"200px"}},{default:o(()=>[f((l(),c(r,{span:24,style:{height:"100%"}},{default:o(()=>[i(p,{class:"leftDetailBox",style:{height:"100%"}},{default:o(()=>[t("div",null,[i(m,{gutter:20},{default:o(()=>[i(r,{span:9},{default:o(()=>[t("div",W,[t("div",X,[t("span",null,d(e.currentNengYuanName),1)]),t("div",$,d(e.leiJiData.nianJiHua||0)+" 万方",1)])]),_:1}),i(r,{span:15,class:"totalDetail"},{default:o(()=>[t("div",null,[n[2]||(n[2]=t("span",{class:"tit"},"取水指标:",-1)),t("span",ee,d(e.leiJiData.nianJiHua||0)+" 万方",1)]),t("div",ae,[n[3]||(n[3]=t("span",{class:"tit"},"累计取水:",-1)),i(Y,{percentage:e.leiJiData.nianJiHua&&e.leiJiData.nianJiHua!==0?Number((e.leiJiData.nianLeiJi*100/e.leiJiData.nianJiHua).toFixed(0)):0,"stroke-width":10},null,8,["percentage"])]),n[4]||(n[4]=t("div",null,null,-1))]),_:1})]),_:1})])]),_:1})]),_:1})),[[h,e.leiJiLoading]])]),_:1}),i(p,{class:"mt10"},{default:o(()=>[i(H,{stripe:"",data:e.nengYuanZongHaoData},{default:o(()=>[i(u,{prop:"nianJiHua",align:"center",label:"总取水指标(万方)"}),i(u,{prop:"nianLeiJi",align:"center",label:"累计取水量(万方)"}),i(u,{prop:"shengYu",align:"center",label:"剩余取水量(万方)"})]),_:1},8,["data"])]),_:1})])])])}const ge=v(R,[["render",ie],["__scopeId","data-v-cc88b99a"]]);export{ge as default};
