import{d as X,h as se,ak as P,a as de,r as i,bj as ue,ai as F,X as ce,b as pe,c as V,o as A,p as t,t as d,w as x,q as ve,a7 as me,M as ge,e as b,A as be,B as fe,F as _e,C as ye,x as $,D as he,K as we,J as B,am as O,aI as Ce,ay as Te,ax as Se,v as Ne,az as Fe}from"./index-D07cMzhp.js";import{E as Ae}from"./el-row-CikYE3zA.js";import{_ as Ie}from"./index-BWMgqvQ9.js";import{E as De}from"./el-col-BaG5Rg5z.js";import{E as Ee}from"./el-date-picker-HyhB9X9n.js";import{l as qe}from"./index-tnh-DMgh.js";import{o as L}from"./index-ChPmfMlc.js";import{u as I,X as xe}from"./xlsx-BexUIDLF.js";import"./el-tree-DW6MoFaI.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";const Be={class:"p-2"},Le={class:"mb-[10px]"},Ue=X({name:"WasteStorageRecord"}),et=X({...Ue,setup(ke){const{proxy:y}=se(),{epwf_transport_condition:Qe,epwf_container_type:Re,epwf_approval_status:ze}=P(y==null?void 0:y.useDict("epwf_transport_condition","epwf_container_type","epwf_approval_status"));de();const U=i([]);i(!1);const D=i(!0),k=i([]),Y=i([]),E=i(!0),j=i([]),Q=i([]),G=i(!0),J=i(!0),R=i(null),q=i(0),h=i(),K=i(["",""]),H=i(["",""]),Z=i(["",""]);ue(),i({}),F({visible:!1,url:""});const z=i();i(),F({visible:!1,title:""}),F({visible:!1,title:""});const ee=F({form:{...{recordId:void 0,generateBatchCode:void 0,incomeBatchCode:void 0,outboundBatchCode:void 0,disposalBatchCode:void 0,generateTime:void 0,incomeTime:void 0,outboundTime:void 0,containerCode:void 0,containerType:void 0,containerNumber:void 0,wasteCommonName:void 0,wasteNationalName:void 0,wasteCategory:void 0,wasteCode:void 0,generateQuantity:void 0,incomeQuantity:void 0,outboundQuantity:void 0,disposalQuantity:void 0,unit:void 0,facilityCode:void 0,generateFacilityCode:void 0,facilityType:void 0,generateAgent:void 0,transportAgent:void 0,storageAgent:void 0,outboundAgent:void 0,generateDestination:void 0,outboundDestination:void 0,generateWasteUnitId:void 0,centralizedUnitId:void 0,approvalStatus:1,approvalOpinions:void 0,territorialUnitId:void 0,generatePlace:void 0,generateFile:void 0,weighFile:void 0,storeFile:void 0,labelFile:void 0,transportCondition:void 0,transportApplicationFile:void 0,transportWasteFile:void 0,wasteLabel:void 0,transportMultFile:void 0,poundList:void 0,processPhotos:void 0,disposalWay:void 0,receiveType:void 0,businessLicenseName:void 0,businessLicenseEncode:void 0,wasteComponentName:void 0,wasteShape:void 0,dangerCharacteristics:void 0,address:void 0}},queryParams:{pageNum:1,pageSize:10,generateBatchCode:void 0,incomeBatchCode:void 0,outboundBatchCode:void 0,disposalBatchCode:void 0,containerCode:void 0,containerType:void 0,containerNumber:void 0,wasteCommonName:void 0,wasteNationalName:void 0,wasteCategory:void 0,wasteCode:void 0,generateQuantity:void 0,incomeQuantity:void 0,outboundQuantity:void 0,disposalQuantity:void 0,unit:void 0,facilityCode:void 0,generateFacilityCode:void 0,facilityType:void 0,generateAgent:void 0,transportAgent:void 0,storageAgent:void 0,outboundAgent:void 0,generateDestination:void 0,outboundDestination:void 0,generateWasteUnitId:void 0,centralizedUnitId:void 0,approvalStatus:void 0,approvalOpinions:void 0,territorialUnitId:void 0,generatePlace:void 0,generateFile:void 0,weighFile:void 0,storeFile:void 0,labelFile:void 0,transportCondition:void 0,transportApplicationFile:void 0,transportWasteFile:void 0,wasteLabel:void 0,transportMultFile:void 0,poundList:void 0,processPhotos:void 0,disposalWay:void 0,receiveType:void 0,businessLicenseName:void 0,businessLicenseEncode:void 0,wasteComponentName:void 0,wasteShape:void 0,dangerCharacteristics:void 0,address:void 0,includeSelf:!1,params:{generateTime:void 0,incomeTime:void 0,outboundTime:void 0}},rules:{recordId:[{required:!0,message:"记录ID不能为空",trigger:"blur"}],generateWasteUnitId:[{required:!0,message:"产废单位不能为空",trigger:"blur"}],centralizedUnitId:[{required:!0,message:"归口部门不能为空",trigger:"blur"}],territorialUnitId:[{required:!0,message:"属地单位不能为空",trigger:"blur"}],generateBatchCode:[{required:!0,message:"产生批次不能为空",trigger:"blur"}],generateFacilityCode:[{required:!0,message:"产生危险废物设施编码不能为空",trigger:"blur"}],generateQuantity:[{required:!0,message:"产生量不能为空",trigger:"blur"}],unit:[{required:!0,message:"计量单位不能为空",trigger:"blur"}],generateTime:[{required:!0,message:"产生时间不能为空",trigger:"blur"}],generatePlace:[{required:!0,message:"危废产生地点不能为空",trigger:"blur"}],containerCode:[{required:!0,message:"容器/包装编码不能为空",trigger:"blur"}],containerType:[{required:!0,message:"容器/包装类型不能为空",trigger:"blur"}],containerNumber:[{required:!0,message:"容器/包装数量不能为空",trigger:"blur"}],generateAgent:[{required:!0,message:"产生部门经办人不能为空",trigger:"blur"}],generateDestination:[{required:!0,message:"产生去向不能为空",trigger:"blur"}]}}),{queryParams:l,form:Me,rules:Pe}=P(ee),C=async()=>{if(D.value=!0,l.value.params={},h.value){l.value.params=typeof l.value.params=="object"&&!Array.isArray(l.value.params)?l.value.params:{};const e=h.value;l.value.params.beginGenerateTime=`${h.value}-01 00:00:00`;const[u,r]=e.split("-");if(!u||!r)throw new Error("时间格式错误");const n=new Date(u,parseInt(r,10),0).getDate(),v=`${u}-${r}-${n.toString().padStart(2,"0")} 23:59:59`;l.value.params.endGenerateTime=v}const a=await qe(l.value);U.value=a.rows,q.value=a.total,D.value=!1};ce(()=>Q.value.reduce((a,e)=>(a[e.deptId]=e.deptName,a),{}));const W=()=>{l.value.pageNum=1,C()},te=()=>{var a;K.value=["",""],H.value=["",""],Z.value=["",""],(a=z.value)==null||a.resetFields(),W()},ae=a=>(l.value.pageNum-1)*l.value.pageSize+a+1,oe=async()=>{const a=await L(null,null);Q.value=a.data;const e=await L(null,2);Y.value=e.data;const u=await L(null,3);k.value=u.data},re=a=>{j.value=a.map(e=>e.recordId),G.value=a.length!=1,J.value=!a.length},ne=a=>{const{columns:e,data:u}=a,r=[];return e.forEach((n,v)=>{if(v===0){r[v]="合计";return}if(n.property==="generateQuantity"){const _=u.map(f=>Number(f[n.property])||0).reduce((f,c)=>f+c,0);r[v]=`${_.toFixed(2)} `}else r[v]=""}),r},ie=()=>{var a;try{const e=(a=R.value)==null?void 0:a.$el;let u=e.querySelector(".el-table__fixed");u||(u=e);const r=I.table_to_book(u,{raw:!0}),n=r.Sheets[r.SheetNames[0]],v=[],m=I.decode_range(n["!ref"]);for(let c=m.s.c;c<=m.e.c;c++){let s=0;for(let g=m.s.r;g<=m.e.r;g++){const o=I.encode_cell({r:g,c}),w=n[o];if(w&&w.v){const T=String(w.v).split("").reduce((S,N)=>S+(N.charCodeAt(0)>255?2:1),0);T>s&&(s=T)}}v.push({wch:Math.min(s+2,60)})}n["!cols"]=v;const _={alignment:{horizontal:"center",vertical:"center",wrapText:!0},border:{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},font:{sz:11,name:"宋体",color:{rgb:"000000"}}};Object.keys(n).forEach(c=>{if(!c.startsWith("!")){const s=n[c];s.s=s.s?{...s.s,..._}:{..._},typeof s.v=="number"&&(s.z=s.z||"0.00")}}),n["!merges"]&&n["!merges"].forEach(c=>{for(let s=c.s.r;s<=c.e.r;s++)for(let g=c.s.c;g<=c.e.c;g++){const o=I.encode_cell({r:s,c:g});n[o]||(n[o]={t:"s",v:""}),n[o].s={..._}}});const f=xe.write(r,{bookType:"xlsx",bookSST:!1,type:"binary",cellStyles:!0});saveAs(new Blob([le(f)],{type:"application/octet-stream"}),`危废产生报表_${new Date().getTime()}.xlsx`)}catch(e){typeof console<"u"&&console.error(e)}},le=a=>{const e=new ArrayBuffer(a.length),u=new Uint8Array(e);for(let r=0;r<a.length;r++)u[r]=a.charCodeAt(r)&255;return e};return pe(()=>{C(),oe()}),(a,e)=>{var S,N;const u=he,r=fe,n=be,v=Ee,m=we,_=ge,f=me,c=De,s=Ie,g=Ae,o=Se,w=Te,M=Fe,T=Ne;return A(),V("div",Be,[t(Ce,{"enter-active-class":(S=b(y))==null?void 0:S.animate.searchAnimate.enter,"leave-active-class":(N=b(y))==null?void 0:N.animate.searchAnimate.leave},{default:d(()=>[x(ve("div",Le,[t(f,{shadow:"hover"},{default:d(()=>[t(_,{ref_key:"queryFormRef",ref:z,model:b(l),inline:!0,"label-width":"100px"},{default:d(()=>[t(n,{label:"属地单位",style:{width:"308px"}},{default:d(()=>[t(r,{modelValue:b(l).territorialUnitId,"onUpdate:modelValue":e[0]||(e[0]=p=>b(l).territorialUnitId=p),placeholder:"请选择属地单位"},{default:d(()=>[(A(!0),V(_e,null,ye(k.value,p=>(A(),$(u,{key:p.deptId,label:p.deptName,value:p.deptId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(n,{label:"产生月份",style:{width:"308px"}},{default:d(()=>[t(v,{modelValue:h.value,"onUpdate:modelValue":e[1]||(e[1]=p=>h.value=p),"default-value":new Date,placeholder:"选择月份",type:"month","value-format":"YYYY-MM"},null,8,["modelValue","default-value"])]),_:1}),t(n,null,{default:d(()=>[t(m,{type:"primary",icon:"Search",onClick:W},{default:d(()=>e[5]||(e[5]=[B("搜索")])),_:1}),t(m,{icon:"Refresh",onClick:te},{default:d(()=>e[6]||(e[6]=[B("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[O,E.value]])]),_:1},8,["enter-active-class","leave-active-class"]),t(f,{shadow:"never"},{header:d(()=>[t(g,{gutter:10,class:"mb8"},{default:d(()=>[t(c,{span:1.5},{default:d(()=>[t(m,{type:"warning",plain:"",icon:"Download",onClick:ie},{default:d(()=>e[7]||(e[7]=[B("导出")])),_:1})]),_:1}),t(s,{showSearch:E.value,"onUpdate:showSearch":e[2]||(e[2]=p=>E.value=p),onQueryTable:C},null,8,["showSearch"])]),_:1})]),default:d(()=>[x((A(),$(w,{ref_key:"reportTable",ref:R,data:U.value,"summary-method":ne,"show-summary":"",stripe:"",onSelectionChange:re},{default:d(()=>[t(o,{index:ae,label:"序号",type:"index",width:"55"}),t(o,{label:"产生批次编码",align:"center",prop:"generateBatchCode"}),t(o,{label:"产生时间",align:"center",prop:"generateTime"}),t(o,{label:"危险废物名称",align:"center"},{default:d(()=>[t(o,{label:"行业俗称",align:"center",prop:"wasteCommonName"}),t(o,{label:"国家危废名录名称",align:"center",prop:"wasteNationalName"})]),_:1}),t(o,{label:"危险废物类别",align:"center",prop:"wasteCategory"}),t(o,{label:"危险废物代码",align:"center",prop:"wasteCode"}),t(o,{label:"产生量",align:"center",prop:"generateQuantity"}),t(o,{label:"计量单位",align:"center",prop:"unit"}),t(o,{label:"容器/包装编码",align:"center",prop:"containerCode"}),t(o,{label:"容器/包装类型",align:"center",prop:"containerType"}),t(o,{label:"容器/包装数量",align:"center",prop:"containerNumber"}),t(o,{label:"产生危险废物设施编码",align:"center",prop:"generateFacilityCode"}),t(o,{label:"产生部门经办人",align:"center",prop:"generateAgent"}),t(o,{label:"去向",align:"center",prop:"generateDestination"})]),_:1},8,["data"])),[[T,D.value]]),x(t(M,{total:q.value,page:b(l).pageNum,"onUpdate:page":e[3]||(e[3]=p=>b(l).pageNum=p),limit:b(l).pageSize,"onUpdate:limit":e[4]||(e[4]=p=>b(l).pageSize=p),onPagination:C},null,8,["total","page","limit"]),[[O,q.value>0]])]),_:1})])}}});export{et as default};
