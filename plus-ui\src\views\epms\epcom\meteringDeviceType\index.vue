<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="类型名称" prop="deviceTypeName">
              <el-input v-model="queryParams.deviceTypeName" placeholder="请输入类型名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd()" v-hasPermi="['epms:meteringDeviceType:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table
        stripe
        ref="meteringDeviceTypeTableRef"
        v-loading="loading"
        :data="meteringDeviceTypeList"
        row-key="deviceTypeId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column align="left" label="计量器具类型" prop="deviceTypeName" />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:meteringDeviceType:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epms:meteringDeviceType:edit']" />
            </el-tooltip>
            <el-tooltip content="新增" placement="top">
              <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['epms:meteringDeviceType:add']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['epms:meteringDeviceType:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加或修改计量器具类型对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="meteringDeviceTypeFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="父类型" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="meteringDeviceTypeOptions"
            :props="{ value: 'deviceTypeId', label: 'deviceTypeName', children: 'children' }"
            value-key="deviceTypeId"
            placeholder="请选择父id"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="类型名称" prop="deviceTypeName">
          <el-input v-model="form.deviceTypeName" placeholder="请输入类型名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="meteringDeviceTypeFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <el-form-item label="父类型" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="meteringDeviceTypeOptions"
            :props="{ value: 'deviceTypeId', label: 'deviceTypeName', children: 'children' }"
            value-key="deviceTypeId"
            placeholder="请选择父id"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="类型名称" prop="deviceTypeName">
          <el-input v-model="form.deviceTypeName" placeholder="请输入类型名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MeteringDeviceType" lang="ts">
import {
  addMeteringDeviceType,
  delMeteringDeviceType,
  getMeteringDeviceType,
  listMeteringDeviceType,
  updateMeteringDeviceType
} from '@/api/epms/epcom/meteringDeviceType';
import { MeteringDeviceTypeForm, MeteringDeviceTypeQuery, MeteringDeviceTypeVO } from '@/api/epms/epcom/meteringDeviceType/types';

type MeteringDeviceTypeOption = {
  deviceTypeId: number;
  deviceTypeName: string;
  children?: MeteringDeviceTypeOption[];
};

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const meteringDeviceTypeList = ref<MeteringDeviceTypeVO[]>([]);
const meteringDeviceTypeOptions = ref<MeteringDeviceTypeOption[]>([]);
const buttonLoading = ref(false);
const showSearch = ref(true);
const isExpandAll = ref(true);
const loading = ref(false);

const queryFormRef = ref<ElFormInstance>();
const meteringDeviceTypeFormRef = ref<ElFormInstance>();
const meteringDeviceTypeTableRef = ref<ElTableInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MeteringDeviceTypeForm = {
  deviceTypeId: undefined,
  deviceTypeName: undefined,
  parentId: undefined
};

const data = reactive<PageData<MeteringDeviceTypeForm, MeteringDeviceTypeQuery>>({
  form: { ...initFormData },
  queryParams: {
    deviceTypeName: undefined,
    parentId: undefined,
    params: {}
  },
  rules: {
    deviceTypeId: [{ required: true, message: '类型id不能为空', trigger: 'blur' }],
    deviceTypeName: [{ required: true, message: '类型名称不能为空', trigger: 'blur' }],
    parentId: [{ required: true, message: '父id不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询计量器具类型列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMeteringDeviceType(queryParams.value);
  const data = proxy?.handleTree<MeteringDeviceTypeVO>(res.data, 'deviceTypeId', 'parentId');
  if (data) {
    meteringDeviceTypeList.value = data;
    loading.value = false;
  }
};

/** 查询计量器具类型下拉树结构 */
const getTreeselect = async () => {
  const res = await listMeteringDeviceType();
  meteringDeviceTypeOptions.value = [];
  const data: MeteringDeviceTypeOption = { deviceTypeId: 0, deviceTypeName: '顶级节点', children: [] };
  data.children = proxy?.handleTree<MeteringDeviceTypeOption>(res.data, 'deviceTypeId', 'parentId');
  meteringDeviceTypeOptions.value.push(data);
};

// 取消按钮
const cancel = () => {
  reset();
  dialog.visible = false;
};
const detailcancel = () => {
  reset();
  dialog.visible = false;
};

// 表单重置
const reset = () => {
  form.value = { ...initFormData };
  meteringDeviceTypeFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 新增按钮操作 */
const handleAdd = (row?: MeteringDeviceTypeVO) => {
  reset();
  getTreeselect();
  if (row != null && row.deviceTypeId) {
    form.value.parentId = row.deviceTypeId;
  } else {
    form.value.parentId = 0;
  }
  dialog.visible = true;
  dialog.title = '添加计量器具类型';
};

/** 展开/折叠操作 */
const handleToggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  toggleExpandAll(meteringDeviceTypeList.value, isExpandAll.value);
};

/** 展开/折叠操作 */
const toggleExpandAll = (data: MeteringDeviceTypeVO[], status: boolean) => {
  data.forEach((item) => {
    meteringDeviceTypeTableRef.value?.toggleRowExpansion(item, status);
    if (item.children && item.children.length > 0) toggleExpandAll(item.children, status);
  });
};

const handleDetail = async (row: MeteringDeviceTypeVO) => {
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.parentId = row.parentId;
  }
  const res = await getMeteringDeviceType(row.deviceTypeId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '计量器具类型详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row: MeteringDeviceTypeVO) => {
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.parentId = row.parentId;
  }
  const res = await getMeteringDeviceType(row.deviceTypeId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改计量器具类型';
};

/** 提交按钮 */
const submitForm = () => {
  meteringDeviceTypeFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.deviceTypeId) {
        await updateMeteringDeviceType(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addMeteringDeviceType(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row: MeteringDeviceTypeVO) => {
  await proxy?.$modal.confirm('是否确认删除计量器具类型编号为"' + row.deviceTypeId + '"的数据项？');
  loading.value = true;
  await delMeteringDeviceType(row.deviceTypeId).finally(() => (loading.value = false));
  await getList();
  proxy?.$modal.msgSuccess('删除成功');
};

onMounted(() => {
  getList();
});
</script>
