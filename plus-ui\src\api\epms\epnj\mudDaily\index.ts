import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MudDailyVO, MudDailyForm, MudDailyQuery } from '@/api/epms/epnj/mudDaily/types';

/**
 * 查询泥浆日报列表
 * @param query
 * @returns {*}
 */

export const listMudDaily = (query?: MudDailyQuery): AxiosPromise<MudDailyVO[]> => {
  return request({
    url: '/epms/mudDaily/list',
    method: 'get',
    params: query
  });
};

export const getMudDailyStatistics = (query?: MudDailyQuery): AxiosPromise<MudDailyVO[]> => {
  return request({
    url: '/epms/mudDaily/getMudDailyStatistics',
    method: 'get',
    params: query
  });
};

/**
 * 查询泥浆日报详细
 * @param id
 */
export const getMudDaily = (id: string | number): AxiosPromise<MudDailyVO> => {
  return request({
    url: '/epms/mudDaily/' + id,
    method: 'get'
  });
};

/**
 * 新增泥浆日报
 * @param data
 */
export const addMudDaily = (data: MudDailyForm) => {
  return request({
    url: '/epms/mudDaily',
    method: 'post',
    data: data
  });
};

/**
 * 修改泥浆日报
 * @param data
 */
export const updateMudDaily = (data: MudDailyForm) => {
  return request({
    url: '/epms/mudDaily',
    method: 'put',
    data: data
  });
};

/**
 * 删除泥浆日报
 * @param id
 */
export const delMudDaily = (id: string | number | Array<string | number>) => {
  return request({
    url: '/epms/mudDaily/' + id,
    method: 'delete'
  });
};

export const reCompute = (date) => {
  return request({
    url: '/epms/mudDaily/reCompute',
    method: 'get',
    params: date
  });
};

export const jiZhongMonthStat = (year,query?: MudDailyQuery) => {
  return request({
    url: '/epms/mudDaily/jiZhongMonthStat',
    method: 'get',
    params: {
      year: year,
      ...query
    }
  });
};
export const suiZuanMonthStat = (query?: MudDailyQuery) => {
  return request({
    url: '/epms/mudDaily/suiZuanMonthStat',
    method: 'get',
    params: query
  });
};
