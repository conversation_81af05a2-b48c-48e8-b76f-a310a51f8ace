import{d as E,h as $,ak as q,r as p,b as R,aH as U,c as P,o as N,q as f,p as e,t as n,M as H,e as o,A as J,G as M,H as k,K as Q,J as S,a7 as A,w,x as G,ay as O,ax as j,z as C,aJ as W,aL as X,v as Y,az as Z,am as ee,N as oe}from"./index-D07cMzhp.js";import{l as te,f as ae}from"./index-DhTJxyQH.js";const le={class:"p-2"},ne={class:"mb-[10px]"},se=E({name:"Online"}),de=E({...se,setup(re){const{proxy:r}=$(),{sys_device_type:V}=q(r==null?void 0:r.useDict("sys_device_type")),v=p([]),d=p(!0),c=p(0),b=p(),t=p({pageNum:1,pageSize:10,ipaddr:"",userName:""}),m=async()=>{d.value=!0;const i=await te(t.value);v.value=i.rows,c.value=i.total,d.value=!1},u=()=>{t.value.pageNum=1,m()},z=()=>{var i;(i=b.value)==null||i.resetFields(),u()},L=async i=>{const[l]=await oe(r==null?void 0:r.$modal.confirm('是否确认强退名称为"'+i.userName+'"的用户?'));l||(await ae(i.tokenId),await m(),r==null||r.$modal.msgSuccess("删除成功"))};return R(()=>{m()}),(i,l)=>{const h=M,_=J,g=Q,T=H,y=A,s=j,F=W,x=X,B=O,D=Z,I=U("hasPermi"),K=Y;return N(),P("div",le,[f("div",ne,[e(y,{shadow:"hover"},{default:n(()=>[e(T,{ref_key:"queryFormRef",ref:b,model:o(t),inline:!0},{default:n(()=>[e(_,{label:"登录地址",prop:"ipaddr"},{default:n(()=>[e(h,{modelValue:o(t).ipaddr,"onUpdate:modelValue":l[0]||(l[0]=a=>o(t).ipaddr=a),placeholder:"请输入登录地址",clearable:"",onKeyup:k(u,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"用户名称",prop:"userName"},{default:n(()=>[e(h,{modelValue:o(t).userName,"onUpdate:modelValue":l[1]||(l[1]=a=>o(t).userName=a),placeholder:"请输入用户名称",clearable:"",onKeyup:k(u,["enter"])},null,8,["modelValue"])]),_:1}),e(_,null,{default:n(()=>[e(g,{type:"primary",icon:"Search",onClick:u},{default:n(()=>l[4]||(l[4]=[S("搜索")])),_:1}),e(g,{icon:"Refresh",onClick:z},{default:n(()=>l[5]||(l[5]=[S("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),e(y,{shadow:"hover"},{default:n(()=>[w((N(),G(B,{border:"",data:o(v).slice((o(t).pageNum-1)*o(t).pageSize,o(t).pageNum*o(t).pageSize),style:{width:"100%"}},{default:n(()=>[e(s,{label:"序号",width:"50",type:"index",align:"center"},{default:n(a=>[f("span",null,C((o(t).pageNum-1)*o(t).pageSize+a.$index+1),1)]),_:1}),e(s,{label:"会话编号",align:"center",prop:"tokenId","show-overflow-tooltip":!0}),e(s,{label:"登录名称",align:"center",prop:"userName","show-overflow-tooltip":!0}),e(s,{label:"客户端",align:"center",prop:"clientKey","show-overflow-tooltip":!0}),e(s,{label:"设备类型",align:"center"},{default:n(a=>[e(F,{options:o(V),value:a.row.deviceType},null,8,["options","value"])]),_:1}),e(s,{label:"所属部门",align:"center",prop:"deptName","show-overflow-tooltip":!0}),e(s,{label:"主机",align:"center",prop:"ipaddr","show-overflow-tooltip":!0}),e(s,{label:"登录地点",align:"center",prop:"loginLocation","show-overflow-tooltip":!0}),e(s,{label:"操作系统",align:"center",prop:"os","show-overflow-tooltip":!0}),e(s,{label:"浏览器",align:"center",prop:"browser","show-overflow-tooltip":!0}),e(s,{label:"登录时间",align:"center",prop:"loginTime",width:"180"},{default:n(a=>[f("span",null,C(o(r).parseTime(a.row.loginTime)),1)]),_:1}),e(s,{align:"center","class-name":"small-padding fixed-width",label:"操作"},{default:n(a=>[e(x,{content:"强退",placement:"top"},{default:n(()=>[w(e(g,{link:"",type:"primary",icon:"Delete",onClick:ie=>L(a.row)},null,8,["onClick"]),[[I,["monitor:online:forceLogout"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[K,o(d)]]),w(e(D,{page:o(t).pageNum,"onUpdate:page":l[2]||(l[2]=a=>o(t).pageNum=a),limit:o(t).pageSize,"onUpdate:limit":l[3]||(l[3]=a=>o(t).pageSize=a),total:o(c)},null,8,["page","limit","total"]),[[ee,o(c)>0]])]),_:1})])}}});export{de as default};
