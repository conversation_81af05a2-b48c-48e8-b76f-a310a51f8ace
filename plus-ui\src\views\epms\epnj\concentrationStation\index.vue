<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="集中站名称" prop="operationAreaName">
              <el-input v-model="queryParams.operationAreaName" placeholder="请输入集中站名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" v-hasPermi="['epms:operationArea:query']">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epms:operationArea:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:operationArea:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:operationArea:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:operationArea:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="operationAreaList" stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" type="index" width="60" :index="indexMethod" />
        <el-table-column label="集中站名称" align="center" prop="operationAreaName" />
        <el-table-column align="center" label="所属地" prop="operationAreaParentId">
          <template #default="scope">
            {{ getAdministrativeAreaText(scope.row.operationAreaParentId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:operationArea:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epms:operationArea:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:operationArea:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改集中站对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="operationAreaFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="所属区域" prop="operationAreaParentId">
          <el-select v-model="form.operationAreaParentId" placeholder="请选择所属区域" clearable>
            <el-option v-for="item in areaList" :key="item.operationAreaId" :label="item.operationAreaName" :value="item.operationAreaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="集中站名称" prop="operationAreaName">
          <el-input v-model="form.operationAreaName" placeholder="请输入集中站名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 详情对话框 -->
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="operationAreaFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <el-form-item label="所属区域" prop="operationAreaParentId">
          <el-select v-model="form.operationAreaParentId" placeholder="请选择所属区域" clearable>
            <el-option v-for="item in areaList" :key="item.operationAreaId" :label="item.operationAreaName" :value="item.operationAreaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="集中站名称" prop="operationAreaName">
          <el-input v-model="form.operationAreaName" placeholder="请输入集中站名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OperationArea" lang="ts">
import { addOperationArea, delOperationArea, getOperationArea, listOperationArea, updateOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaForm, OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_administrative_area } = toRefs<any>(proxy?.useDict('eptw_administrative_area'));

const operationAreaList = ref<OperationAreaVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const oitIds = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const operationAreaFormRef = ref<ElFormInstance>();

const areaList = ref<OperationAreaVO[]>([]);
const areaQuery = ref<OperationAreaQuery>({
  operationAreaType: 0
});

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: OperationAreaForm = {
  operationAreaId: undefined,
  operationAreaName: undefined,
  iotId: undefined,
  operationAreaType: 2
};
const data = reactive<PageData<OperationAreaForm, OperationAreaQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    operationAreaType: 2,
    operationAreaName: undefined,
    params: {}
  },
  rules: {
    operationAreaName: [{ required: true, message: '集中站名称不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询集中站列表 */
const getList = async () => {
  loading.value = true;
  const res = await listOperationArea(queryParams.value);
  operationAreaList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const getWorkAreaList = async () => {
  const res = await listOperationArea(areaQuery.value);
  areaList.value = res.rows;
};
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  operationAreaFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: OperationAreaVO[]) => {
  ids.value = selection.map((item) => item.operationAreaId);
  oitIds.value = selection.filter((item) => item.iotId != null).map((item) => item.iotId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加集中站';
};
/** 详情按钮操作 */
const handleDetail = async (row?: OperationAreaVO) => {
  reset();
  const _operationAreaId = row?.operationAreaId || ids.value[0];
  const res = await getOperationArea(_operationAreaId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '集中站详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: OperationAreaVO) => {
  reset();
  const _operationAreaId = row?.operationAreaId || ids.value[0];
  const res = await getOperationArea(_operationAreaId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改集中站';
};

/** 提交按钮 */
const submitForm = () => {
  operationAreaFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.operationAreaId) {
        await updateOperationArea(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addOperationArea(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: OperationAreaVO) => {
  const _operationAreaIds = row?.operationAreaId || ids.value;
  const _oitId = row?.iotId || oitIds.value;
  await proxy?.$modal.confirm('是否确认删除集中站编号为"' + _operationAreaIds + '"的数据项？').finally(() => (loading.value = false));
  await delOperationArea(_operationAreaIds, _oitId);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/operationArea/export',
    {
      ...queryParams.value
    },
    `集中站_${new Date().getTime()}.xlsx`
  );
};

const getAdministrativeAreaText = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationArea = areaList.value.find((item) => item.operationAreaId === operationAreaId.toString());
  return operationArea ? operationArea.operationAreaName : '未知';
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
  getWorkAreaList();
});
</script>
