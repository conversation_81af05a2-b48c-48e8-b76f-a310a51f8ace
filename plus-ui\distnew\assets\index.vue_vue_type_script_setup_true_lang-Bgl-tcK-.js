import{d as l,bz as d,r as o,X as u,b as m,w as p,v as h,e,c as _,o as g,a3 as f,q as v}from"./index-D07cMzhp.js";const w=["src"],b=l({__name:"index",props:{src:d.string.isRequired},setup(s){const i=s,t=o(document.documentElement.clientHeight-94.5+"px;"),n=o(!0),r=u(()=>i.src);return m(()=>{setTimeout(()=>{n.value=!1},300),window.onresize=function(){t.value=document.documentElement.clientHeight-94.5+"px;"}}),(c,x)=>{const a=h;return p((g(),_("div",{style:f("height:"+e(t))},[v("iframe",{src:e(r),frameborder:"no",style:{width:"100%",height:"100%"},scrolling:"auto"},null,8,w)],4)),[[a,e(n)]])}}});export{b as _};
