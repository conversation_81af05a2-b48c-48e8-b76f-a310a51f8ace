<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="报告名称" prop="qualityReportName">
              <el-input v-model="queryParams.qualityReportName" clearable placeholder="请输入水质监测报告名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <!--            <el-form-item label="行政区域" prop="administrativeArea">-->
            <!--              <el-select v-model="queryParams.administrativeArea" placeholder="请选择行政区域" clearable @keyup.enter="handleQuery">-->
            <!--                <el-option v-for="dict in eptw_administrative_area" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="所属地" prop="operationAreaId">
              <el-select v-model="queryParams.operationAreaId" clearable placeholder="请选择所属地" @keyup.enter="handleQuery">
                <el-option
                  v-for="dict in operationAreaList"
                  :key="dict.operationAreaId"
                  :label="dict.operationAreaName"
                  :value="dict.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="监测年度" prop="year">
              <el-date-picker
                v-model="queryParams.params"
                type="yearrange"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始年度"
                end-placeholder="结束年度"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterWellQuality:add']" icon="Plus" plain type="primary" @click="handleAdd"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterWellQuality:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterWellQuality:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterWellQuality:export']" icon="Download" plain type="warning" @click="handleExport">导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="waterWellQualityList" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="报告名称" min-width="400" prop="qualityReportName" show-overflow-tooltip />
        <!--        <el-table-column label="行政区域" align="center" prop="administrativeArea">-->
        <!--          <template #default="scope">-->
        <!--            {{ getAdministrativeAreaText(scope.row.administrativeArea) }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="所属地" align="center" prop="operationAreaName" min-width="120">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="报告日期" align="center" prop="qualityReportTime" min-width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.qualityReportTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="监测年度" prop="year" />
        <el-table-column align="center" label="剩余时间(天)" prop="effectiveDay" />
        <el-table-column label="上传时间" align="center" prop="uploadTime" width="130">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="305">
          <template #default="scope">
            <el-tooltip content="水质监测报告" placement="top">
              <el-button v-hasPermi="['epms:waterWellQuality:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >报告预览
              </el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:waterWellQuality:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情
              </el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epms:waterWellQuality:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"
                >修改
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:waterWellQuality:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改水源井水质监测报告对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="waterWellQualityFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="报告名称" prop="qualityReportName">
          <el-input v-model="form.qualityReportName" placeholder="请输入水质监测报告" />
        </el-form-item>
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" placeholder="请选择所属地">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告日期" prop="qualityReportTime">
          <el-date-picker v-model="form.qualityReportTime" clearable format="YYYY-MM-DD" placeholder="报告日期" type="data" value-format="YYYY-MM-DD">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="监测年度" prop="year">
          <el-date-picker v-model="form.year" format="YYYY" placeholder="请选择监测年度" type="year" value-format="YYYY" />
        </el-form-item>
        <el-form-item label="文件列表" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.qualityReportId"
            :disabled="false"
            attach-category="waterWellQuality"
            attach-source-type="operationArea"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="waterWellQualityFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <el-form-item label="报告名称" prop="qualityReportName">
          <el-input v-model="form.qualityReportName" placeholder="请输入水质监测报告" />
        </el-form-item>
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" placeholder="请选择所属地">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告日期" prop="qualityReportTime">
          <el-date-picker v-model="form.qualityReportTime" clearable format="YYYY-MM-DD" placeholder="报告日期" type="data" value-format="YYYY-MM-DD">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="监测年度" prop="year">
          <el-date-picker v-model="form.year" format="YYYY" placeholder="请选择监测年度" type="year" value-format="YYYY" />
        </el-form-item>
        <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker v-model="form.uploadTime" clearable placeholder="上传时间" type="datetime"> </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WaterWellQuality" lang="ts">
import {
  addWaterWellQuality,
  delWaterWellQuality,
  getWaterWellQuality,
  listWaterWellQuality,
  updateWaterWellQuality
} from '@/api/epms/eptw/waterWellQuality';
import { WaterWellQualityForm, WaterWellQualityQuery, WaterWellQualityVO } from '@/api/epms/eptw/waterWellQuality/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_administrative_area } = toRefs<any>(proxy?.useDict('eptw_administrative_area'));

const waterWellQualityList = ref<WaterWellQualityVO[]>([]);
const operationAreaList = ref<OperationAreaVO[]>([]); // 所属地列表
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const operationAreaQuery = ref<OperationAreaQuery>({});

const queryFormRef = ref<ElFormInstance>();
const waterWellQualityFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WaterWellQualityForm = {
  qualityReportId: undefined,
  qualityReportName: undefined,
  administrativeArea: undefined,
  operationAreaId: undefined,
  file: undefined,
  year: undefined,
  status: 0 //新增上传时默认正常
};
const data = reactive<PageData<WaterWellQualityForm, WaterWellQualityQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    qualityReportName: undefined,
    administrativeArea: undefined,
    operationAreaId: undefined,
    file: undefined,
    year: undefined,
    params: {}
  },
  rules: {
    qualityReportId: [{ required: true, message: '水质监测报告id不能为空', trigger: 'blur' }],
    year: [{ required: true, message: '监测年度不能为空', trigger: 'blur' }],
    qualityReportName: [{ required: true, message: '水质监测报告名称不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '所属地不能为空', trigger: 'blur' }],
    qualityReportTime: [{ required: true, message: '报告日期不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询水源井水质监测报告列表 */
const getList = async () => {
  loading.value = true;
  const res = await listWaterWellQuality(queryParams.value);
  waterWellQualityList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 查询所属地列表 */
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaParentId = 0;
  const res = await listOperationArea(operationAreaQuery.value);
  operationAreaList.value = res.rows;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  waterWellQualityFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WaterWellQualityVO[]) => {
  ids.value = selection.map((item) => item.qualityReportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加水源井水质监测报告';
};

/** 预览按钮操作 */
const handlePreview = async (row?: WaterWellQualityVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.qualityReportId,
    attachSourceType: 'operationArea',
    attachCategory: 'waterWellQuality'
  });
};
/** 详情按钮操作 */
const handleDetail = async (row?: WaterWellQualityVO) => {
  reset();
  const _qualityReportId = row?.qualityReportId || ids.value[0];
  const res = await getWaterWellQuality(_qualityReportId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '水质监测报告详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: WaterWellQualityVO) => {
  reset();
  const _qualityReportId = row?.qualityReportId || ids.value[0];
  const res = await getWaterWellQuality(_qualityReportId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改水质监测报告';
};

/** 提交按钮 */
const submitForm = () => {
  waterWellQualityFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.qualityReportId) {
        await updateWaterWellQuality(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWaterWellQuality(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WaterWellQualityVO) => {
  const _qualityReportIds = row?.qualityReportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除水质监测报告编号为"' + _qualityReportIds + '"的数据项？').finally(() => (loading.value = false));
  await delWaterWellQuality(_qualityReportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/waterWellQuality/export',
    {
      ...queryParams.value
    },
    `水质监测报告_${new Date().getTime()}.xlsx`
  );
};

const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
  form.value.balanceReportName = fielInfo.name;
};

const getAdministrativeAreaText = (administrativeArea: number) => {
  if (!administrativeArea) {
    return '未知';
  }
  const administrativeAreaItem = eptw_administrative_area.value.find((item) => item.value === administrativeArea.toString());
  return administrativeAreaItem ? administrativeAreaItem.label : '未知';
};

const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationAreaItem = operationAreaList.value.find((item) => item.operationAreaId === operationAreaId && item.operationAreaParentId === 0);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
  getoperationAreaList();
});
</script>
