<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="96px">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属地" prop="workAreaId">
              <el-select v-model="queryParams.workAreaId" placeholder="请选择作业区" clearable>
                <el-option
                  v-for="item in operationAreaList"
                  :key="item.operationAreaId"
                  :label="item.operationAreaName"
                  :value="item.operationAreaId"
                  @keyup.enter="handleQuery"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="年度" style="width: 336px">
              <el-date-picker
                v-model="yearRange"
                value-format="YYYY"
                type="yearrange"
                range-separator="-"
                start-placeholder="开始年份"
                end-placeholder="结束年份"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['eptd:landReclamation:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['eptd:landReclamation:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="landReclamationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" type="index" width="60" :index="indexMethod" />
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column align="center" label="所属地" prop="workAreaId">
          <template #default="scope">
            {{ operationAreaList.find((item) => item.operationAreaId === scope.row.workAreaId)?.operationAreaName || '未知' }}
          </template>
        </el-table-column>
        <el-table-column label="项目年度" align="center" prop="year" />
        <el-table-column label="文件查看" align="center" class-name="small-padding fixed-width" min-width="200">
          <template #default="scope">
            <el-tooltip content="查看复垦计划" placement="top">
              <el-button
                v-hasPermi="['eptd:landReclamation:preview']"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.projectId, 'landReclamationPlanAttachment')"
                >复垦计划</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['eptd:landReclamation:edit']">修改</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改土地复垦信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="landReclamationFormRef" :model="form" :rules="rules" label-width="96px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" disabled placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="所属地" prop="workAreaId">
          <el-select v-model="form.workAreaId" placeholder="请选择所属地" clearable disabled>
            <el-option v-for="item in operationAreaList" :key="item.operationAreaId" :label="item.operationAreaName" :value="item.operationAreaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目年度" prop="startTime">
          <el-date-picker v-model="form.year" clearable disabled placeholder="请选择项目年度" type="year" value-format="YYYY"> </el-date-picker>
        </el-form-item>
        <el-form-item label="复垦计划" prop="reclamationPlanAttachment">
          <attachFileUpload
            v-model="form.reclamationPlanAttachment"
            :attach-source-id="form.projectId"
            :disabled="false"
            attach-category="landReclamationPlanAttachment"
            attach-source-type="landReclamationProcess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 附件预览 -->
    <el-dialog title="附件预览" v-model="previewDialog.visible" width="80%" he append-to-body>
      <div style="height: 68vh">
        <component :is="previewComponent" v-if="previewComponent" v-bind="previewProps" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="LandReclamation" lang="ts">
import { addLandReclamation, getLandReclamation, listLandReclamation, updateLandReclamation } from '@/api/epms/eptd/landReclamation';
import { LandReclamationForm, LandReclamationQuery, LandReclamationVO } from '@/api/epms/eptd/landReclamation/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { shallowRef } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const landReclamationList = ref<LandReclamationVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const landReclamationFormRef = ref<ElFormInstance>();
const operationAreaQuery = ref<OperationAreaQuery>({}); // 所属地片区查询条件
const operationAreaList = ref<OperationAreaVO[]>([]); // 所属地列表
const yearRange = ref<[DateModelType, DateModelType]>(['', '']); //项目日期查询时间段

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const previewComponent = shallowRef();
const previewProps = ref({} as Record<string, any>);
const previewDialog = reactive({
  visible: false,
  url: ''
});
const initFormData: LandReclamationForm = {
  projectId: undefined,
  projectName: undefined,
  workAreaId: undefined,
  year: undefined,
  reclamationPlan: undefined,
  expertOpinion: undefined,
  governmentRecord: undefined,
  reclamationPlanAttachment: undefined,
  siteSupervision: undefined,
  acceptanceMaterials: undefined,
  routineSupervision: undefined
};
const data = reactive<PageData<LandReclamationForm, LandReclamationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    workAreaId: undefined,
    params: {}
  },
  rules: {
    projectId: [{ required: true, message: '项目id不能为空', trigger: 'blur' }],
    projectName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
/** 查询土地复垦信息列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, yearRange.value, 'Year');
  const res = await listLandReclamation(queryParams.value);
  landReclamationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  landReclamationFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: LandReclamationVO[]) => {
  ids.value = selection.map((item) => item.projectId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加土地复垦信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: LandReclamationVO) => {
  reset();
  const _projectId = row?.projectId || ids.value[0];
  const res = await getLandReclamation(_projectId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改土地复垦信息';
};

/** 提交按钮 */
const submitForm = () => {
  landReclamationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.projectId) {
        await updateLandReclamation(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addLandReclamation(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'eptd/landReclamation/export',
    {
      ...queryParams.value
    },
    `landReclamation_${new Date().getTime()}.xlsx`
  );
};

/** 查询所属地、片区列表 */
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaParentId = 0;
  operationAreaQuery.value.operationAreaType = 0;
  const res = await listOperationArea(operationAreaQuery.value);
  operationAreaList.value = res.rows;
};

/** 审批弹窗附件预览 */
const previewFile = async (applicationId?: string | number, category?: string) => {
  if (!applicationId) return;
  proxy.showAttachPreview({
    attachSourceId: applicationId,
    attachSourceType: 'landReclamationProcess',
    attachCategory: category
  });
  // const props = {
  //   attachSourceId: applicationId,
  //   attachSourceType: "landReclamationProcess",
  //   attachCategory: category
  // };
  //
  // // 异步加载 preview.vue 组件
  // previewComponent.value = defineAsyncComponent(() =>
  //   import('@/views/comm/attach/preview.vue')
  // );
  //
  // // 传递参数给 preview.vue
  // previewProps.value = props;
  // previewDialog.visible = true;
};
onMounted(() => {
  getoperationAreaList();
  getList();
});
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  line-height: 18px;
  align-items: center;
}
:deep(.el-date-editor) {
  --el-date-editor-width: 100%;
}
</style>
