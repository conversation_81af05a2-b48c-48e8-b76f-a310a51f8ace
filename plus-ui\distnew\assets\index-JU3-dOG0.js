import{aC as Y,Q as U,m as v,aH as P,c as _,o as p,q as c,p as a,w as x,t as r,M as R,A as T,B as N,F as b,C as D,x as y,D as B,K as M,J as u,am as O,a7 as z,aD as Q,G as H,z as G,ar as J,aA as K,W,aQ as X,g as Z}from"./index-D07cMzhp.js";import{E as j}from"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import{E as $}from"./el-date-picker-HyhB9X9n.js";import{m as V}from"./dayjs.min-Brw96_N0.js";import{l as ee}from"./index-BhIIZXqy.js";import"./index-VIEDZI2D.js";function te(t){return Y({url:"/epms/waterharvestDataReport/getDataList",method:"get",params:t})}function le(t){return Y({url:"/epms/waterharvestDataReport/updateData",method:"get",params:t})}const oe={name:"zhuCaiChuShuiLuRu",data(){return{loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,operationAreaList:[],tableData:[],queryParams:{year:V().format("YYYY"),operationAreaId:null},gridOptions:{border:!0,stripe:!0,resizable:!0,showOverflow:!0,keepSource:!0,height:600,editConfig:{trigger:"dblclick",mode:"cell",showStatus:!0},footerMethod:this.footerMethod,columns:[]},upload:{year:V().format("YYYY"),operationAreaId:null},uploadConfig:{open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+Z(),clientid:"d03a2cc250450eba90621e43da78686d"},url:"/prod-api/epms/waterharvestDataReport/importData",loading:null},rules:{year:[{required:!0,message:"请选择年份",trigger:"change"}]}}},async created(){this.getAreaList()},methods:{handleSelectionChange(t){this.ids=t.map(e=>e),this.single=t.length!==1,this.multiple=!t.length},async getAreaList(){const e=await ee({operationAreaType:0}),s=["1906889578986254337","1906889534388219906"];this.operationAreaList=e.rows.filter(i=>!s.includes(i.operationAreaId)),this.getList()},handleQuery(){this.getList()},async getList(){this.loading=!0;const t=await te(this.queryParams);this.tableData=t.rows.map(e=>{const s=e.riqi.reduce((i,l)=>(i+=parseFloat(e[l]),i),0);return e.rowSum=s.toFixed(3),e}),this.initColumn(this.tableData),this.tableData=t.rows,this.loading=!1},showValue(t){return t.row[t.column.property]},initColumn(t){const e=[{title:"序号",type:"seq",width:50,align:"center",fixed:"left"},{field:"blockName",title:"单位",width:150,align:"center",fixed:"left"},{field:"bengName",title:"注水站",width:180,align:"center",fixed:"left"}],s={field:"月采出水量",title:"月采出水量(万m³)",width:150,align:"center",children:[]};for(const i in t[0].riqi)s.children.push({field:t[0].riqi[i],title:t[0].riqi[i],width:150,align:"center",editRender:{},slots:{default:"val_default",edit:"val_edit"}});e.push(s),e.push({field:"rowSum",title:"合计",width:150,align:"center"}),this.gridOptions.columns=e},editClosedEvent(t){let e=t.row,i=t.column.property;const l=e[i],n=this.$refs.xTable;le({monthDate:i,bujianId:e.bengId,value:l}).then(g=>{if(g.code===200){const d=e.riqi.reduce((m,h)=>(m+=parseFloat(e[h]),m),0);e.rowSum=d.toFixed(3),n.reloadRow(e,null,i),this.$modal.msgSuccess("修改成功")}})},handleExport(){this.download("epms/waterharvestDataReport/exportData",{...this.queryParams},`数据报表_${new Date().getTime()}.xlsx`)},async handleImport(){this.uploadConfig.title="数据导入",this.uploadConfig.open=!0,this.upload.leiXingId=null},downloadTemp(){this.$refs.uploadRef.validate(t=>{t&&(this.loading=!0,this.download("epms/waterharvestDataReport/downloadTemplate",{...this.upload},`导入模板_${new Date().getTime()}.xlsx`),this.loading=!1)})},handleFileUploadProgress(t,e,s){this.uploadConfig.isUploading=!0,this.uploadConfig.loading=X.service({lock:!0,text:"Loading",background:"rgba(0, 0, 0, 0.7)"})},handleFileSuccess(t,e,s){this.uploadConfig.open=!1,this.uploadConfig.isUploading=!1,this.$refs.upload.clearFiles(),this.uploadConfig.loading&&this.uploadConfig.loading.close(),W.alert(t.msg,{title:"导入结果",dangerouslyUseHtml:!0}),this.getList()},submitFileForm(){this.$refs.upload.submit()},footerMethod({columns:t,data:e}){return[t.map((i,l)=>l===0?"合计":[1,2].includes(l)?"-":this.sumNum(e,i.property))]},sumNum(t,e){let s=0;for(let i=0;i<t.length;i++)s+=parseFloat(t[i][e]);return s=s.toFixed(3),s}}},ae={class:"common-layout p-2"},re={class:"mb-[10px]"},ie={slot:"footer",class:"dialog-footer"};function se(t,e,s,i,l,n){const g=$,d=T,m=B,h=N,f=M,w=R,C=z,k=H,I=v("vxe-grid"),E=v("upload-filled"),q=J,F=j,S=K,A=P("dialog-drag");return p(),_("div",ae,[c("div",re,[a(C,{shadow:"hover"},{default:r(()=>[x(a(w,{model:l.queryParams,ref:"queryForm",inline:!0,"label-width":"68px"},{default:r(()=>[a(d,{label:"年份",prop:"year"},{default:r(()=>[a(g,{modelValue:l.queryParams.year,"onUpdate:modelValue":e[0]||(e[0]=o=>l.queryParams.year=o),"append-to-body":!1,class:"searchDate",format:"YYYY",type:"year","value-format":"YYYY"},null,8,["modelValue"])]),_:1}),a(d,{label:"所属地",prop:"operationAreaId"},{default:r(()=>[a(h,{modelValue:l.queryParams.operationAreaId,"onUpdate:modelValue":e[1]||(e[1]=o=>l.queryParams.operationAreaId=o),"popper-append-to-body":!1,class:"searchDate",clearable:"",filterable:"",placeholder:"选择所属地"},{default:r(()=>[(p(!0),_(b,null,D(l.operationAreaList,o=>(p(),y(m,{key:o.iotId,label:o.operationAreaName,value:o.iotId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{style:{"padding-left":"20px"}},{default:r(()=>[a(f,{type:"primary",icon:"Search",onClick:n.handleQuery},{default:r(()=>e[6]||(e[6]=[u("搜索")])),_:1},8,["onClick"]),a(f,{type:"primary",icon:"Upload",onClick:n.handleImport},{default:r(()=>e[7]||(e[7]=[u("导入")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),[[O,l.showSearch]])]),_:1})]),a(C,{shadow:"never",class:"grid-container"},{default:r(()=>[a(I,Q({ref:"xTable"},l.gridOptions,{data:l.tableData,height:"auto",onEditClosed:n.editClosedEvent,"show-footer":!0,"footer-method":n.footerMethod}),{val_default:r(o=>[u(G(n.showValue(o)),1)]),val_edit:r(o=>[a(k,{modelValue:o.row[o.column.property],"onUpdate:modelValue":L=>o.row[o.column.property]=L},null,8,["modelValue","onUpdate:modelValue"])]),_:1},16,["data","onEditClosed","footer-method"])]),_:1}),x((p(),y(S,{title:l.uploadConfig.title,modelValue:l.uploadConfig.open,"onUpdate:modelValue":e[5]||(e[5]=o=>l.uploadConfig.open=o),"append-to-body":!0,width:"400px"},{default:r(()=>[a(w,{ref:"uploadRef",model:l.upload,rules:l.rules,inline:!0,"label-width":"68px"},{default:r(()=>[a(d,{label:"年份",prop:"year"},{default:r(()=>[a(g,{modelValue:l.upload.year,"onUpdate:modelValue":e[2]||(e[2]=o=>l.upload.year=o),class:"searchDate",format:"YYYY",type:"year","value-format":"YYYY"},null,8,["modelValue"])]),_:1}),a(d,{label:"所属地",prop:"operationAreaId"},{default:r(()=>[a(h,{modelValue:l.upload.operationAreaId,"onUpdate:modelValue":e[3]||(e[3]=o=>l.upload.operationAreaId=o),"popper-append-to-body":!1,class:"searchDate",clearable:"",filterable:"",placeholder:"选择所属地"},{default:r(()=>[(p(!0),_(b,null,D(l.operationAreaList,o=>(p(),y(m,{key:o.iotId,label:o.operationAreaName,value:o.iotId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,null,{default:r(()=>[a(f,{type:"primary",plain:"",onClick:n.downloadTemp},{default:r(()=>e[8]||(e[8]=[u("生成模板")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"]),a(F,{ref:"upload",limit:1,drag:"",accept:".xlsx, .xls",headers:l.uploadConfig.headers,action:l.uploadConfig.url+"?updateSupport="+l.uploadConfig.updateSupport,disabled:l.uploadConfig.isUploading,"on-progress":n.handleFileUploadProgress,"on-success":n.handleFileSuccess,"auto-upload":!1},{tip:r(()=>e[9]||(e[9]=[c("div",{class:"el-upload__tip"},"提示：仅允许导入“xls”或“xlsx”格式文件！",-1)])),default:r(()=>[a(q,{class:"el-icon--upload"},{default:r(()=>[a(E)]),_:1}),e[10]||(e[10]=c("div",{class:"el-upload__text"},[u("将文件拖到此处，或 "),c("em",null,"点击上传")],-1))]),_:1},8,["headers","action","disabled","on-progress","on-success"]),c("div",ie,[a(f,{type:"primary",onClick:n.submitFileForm,class:"sure"},{default:r(()=>e[11]||(e[11]=[u("确 定")])),_:1},8,["onClick"]),a(f,{onClick:e[4]||(e[4]=o=>l.uploadConfig.open=!1),class:"cancel"},{default:r(()=>e[12]||(e[12]=[u("取 消")])),_:1})])]),_:1},8,["title","modelValue"])),[[A]])])}const ge=U(oe,[["render",se],["__scopeId","data-v-d6f4de7c"]]);export{ge as default};
