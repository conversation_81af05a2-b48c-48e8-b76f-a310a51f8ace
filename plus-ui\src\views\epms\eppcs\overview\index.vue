<template>
  <div class="waterline p-2">
    <el-row style="justify-content: center">
      <div class="header">
        <span>采排水水质总览</span>
      </div>
    </el-row>
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleTabClick" style="position: relative; top: -40px">
      <el-tab-pane label="简介" name="jianjie">
        <jianjie :attach-source-type="'cpsZongLan'" :notice-id="'1930878552481181704'" />
      </el-tab-pane>
      <!--      <el-tab-pane label="总览" name="zonglan" lazy="lazy">-->
      <!--        <zonglan />-->
      <!--      </el-tab-pane>-->
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import Jianjie from '@/views/epms/components/overview/jianjie.vue';

const activeName = ref('jianjie');
const chartRef = ref(null);
const handleTabClick = async () => {
  await nextTick();
  setTimeout(() => {
    if (chartRef.value) {
      chartRef.value.resizeChart();
    }
  }, 100);
};

onMounted(() => {});
</script>

<style scoped>
.waterline {
  height: inherit;
}
.header {
  font-weight: bold;
  margin-bottom: 10px;
  align-items: center;
  font-size: 35px;
  letter-spacing: 7px;
}
</style>
