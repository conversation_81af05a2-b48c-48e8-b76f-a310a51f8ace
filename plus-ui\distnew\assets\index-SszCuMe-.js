import{d as oe,h as Re,ak as W,r as f,ai as Z,X as ke,b as Pe,aH as Ue,c as U,o as u,p as l,t as a,w as g,q as h,a7 as Ce,M as Ee,e as t,A as Ne,G as De,H as C,B as Te,F as A,C as q,x as _,D as Se,K as Fe,J as p,am as ee,aI as $e,ay as xe,ax as Le,aJ as he,z as le,aL as Ae,v as qe,az as Be,a8 as Ke,aA as je,Q as ze}from"./index-D07cMzhp.js";import{_ as He}from"./index-DVHplxfU.js";import{E as Me}from"./el-date-picker-HyhB9X9n.js";import{E as Qe}from"./el-row-CikYE3zA.js";import{_ as Ye}from"./index-BWMgqvQ9.js";import{E as Je}from"./el-col-BaG5Rg5z.js";import{l as Oe,g as Ge,u as Xe,a as We,d as Ze}from"./index-Bo3H13et.js";import{o as el}from"./index-ChPmfMlc.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";import"./el-tree-DW6MoFaI.js";const ll={class:"p-2"},ol={class:"mb-[10px]"},tl={class:"dialog-footer"},al=oe({name:"InspectionRecord"}),rl=oe({...al,setup(nl){const{proxy:d}=Re(),{epfj_procedure_type:E}=W(d==null?void 0:d.useDict("epfj_procedure_type")),B=f([]),w=f(!1),R=f(!0),k=f(!0),N=f([]),K=f(!0),j=f(!0),D=f(0),T=f([]),z=f(),S=f(),b=Z({visible:!1,title:""}),H={recordId:void 0,recordName:void 0,recordProcedure:void 0,recordUnitId:void 0,recordTime:void 0,inspector:void 0,recordPlace:void 0,recordInformation:void 0,file:void 0,recordResult:void 0,inspectionType:5},te=Z({form:{...H},queryParams:{pageNum:1,pageSize:10,recordName:void 0,recordProcedure:void 0,recordUnitId:void 0,recordTime:void 0,inspector:void 0,recordPlace:void 0,recordInformation:void 0,file:void 0,recordResult:void 0,inspectionType:5,params:{}},rules:{recordId:[{required:!0,message:"记录id不能为空",trigger:"blur"}]}}),{queryParams:i,form:n,rules:ae}=W(te),V=async()=>{R.value=!0;const r=await Oe(i.value);B.value=r.rows,D.value=r.total,R.value=!1},re=ke(()=>T.value.reduce((r,e)=>(r[e.deptId]=e.deptName,r),{})),ne=async r=>{d.showAttachPreview({attachSourceId:r.recordId,attachSourceType:"fjInspectionRecord",attachCategory:"inspectionRecordFile"})},de=async()=>{const r=await el(null,3);T.value=r.data},ie=()=>{F(),b.visible=!1},F=()=>{var r;n.value={...H},(r=S.value)==null||r.resetFields()},y=()=>{i.value.pageNum=1,V()},se=()=>{var r;(r=z.value)==null||r.resetFields(),y()},ue=r=>{N.value=r.map(e=>e.recordId),K.value=r.length!=1,j.value=!r.length},pe=()=>{F(),b.visible=!0,b.title="添加封井巡检记录"},M=async r=>{F();const e=(r==null?void 0:r.recordId)||N.value[0],c=await Ge(e);Object.assign(n.value,c.data),b.visible=!0,b.title="修改封井巡检记录"},ce=()=>{var r;(r=S.value)==null||r.validate(async e=>{e&&(w.value=!0,n.value.recordId?await Xe(n.value).finally(()=>w.value=!1):await We(n.value).finally(()=>w.value=!1),d==null||d.$modal.msgSuccess("操作成功"),b.visible=!1,await V())})},Q=async r=>{const e=(r==null?void 0:r.recordId)||N.value;await(d==null?void 0:d.$modal.confirm('是否确认删除封井巡检记录编号为"'+e+'"的数据项？').finally(()=>R.value=!1)),await Ze(e),d==null||d.$modal.msgSuccess("删除成功"),await V()},me=()=>{d==null||d.download("epwf/inspectionRecord/export",{...i.value},`封井定期巡检_${new Date().getTime()}.xlsx`)};return Pe(()=>{V(),de()}),(r,e)=>{var O,G;const c=De,s=Ne,$=Se,x=Te,m=Fe,Y=Ee,J=Ce,P=Je,fe=Ye,ve=Qe,v=Le,_e=he,L=Ae,be=xe,ge=Be,Ve=Me,ye=He,Ie=je,I=Ue("hasPermi"),we=qe;return u(),U("div",ll,[l($e,{"enter-active-class":(O=t(d))==null?void 0:O.animate.searchAnimate.enter,"leave-active-class":(G=t(d))==null?void 0:G.animate.searchAnimate.leave},{default:a(()=>[g(h("div",ol,[l(J,{shadow:"hover"},{default:a(()=>[l(Y,{ref_key:"queryFormRef",ref:z,model:t(i),inline:!0},{default:a(()=>[l(s,{label:"记录名称",prop:"recordName"},{default:a(()=>[l(c,{modelValue:t(i).recordName,"onUpdate:modelValue":e[0]||(e[0]=o=>t(i).recordName=o),placeholder:"请输入记录名称",clearable:"",onKeyup:C(y,["enter"])},null,8,["modelValue"])]),_:1}),l(s,{label:"记录流程",prop:"recordProcedure"},{default:a(()=>[l(x,{modelValue:t(i).recordProcedure,"onUpdate:modelValue":e[1]||(e[1]=o=>t(i).recordProcedure=o),clearable:"",placeholder:"请选择记录流程"},{default:a(()=>[(u(!0),U(A,null,q(t(E),o=>(u(),_($,{key:o.value,label:o.label,value:parseInt(o.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"检查人员",prop:"inspector"},{default:a(()=>[l(c,{modelValue:t(i).inspector,"onUpdate:modelValue":e[2]||(e[2]=o=>t(i).inspector=o),placeholder:"请输入检查人员",clearable:"",onKeyup:C(y,["enter"])},null,8,["modelValue"])]),_:1}),l(s,{label:"检查地点",prop:"recordPlace"},{default:a(()=>[l(c,{modelValue:t(i).recordPlace,"onUpdate:modelValue":e[3]||(e[3]=o=>t(i).recordPlace=o),placeholder:"请输入检查地点",clearable:"",onKeyup:C(y,["enter"])},null,8,["modelValue"])]),_:1}),l(s,{label:"检查结果",prop:"recordResult"},{default:a(()=>[l(c,{modelValue:t(i).recordResult,"onUpdate:modelValue":e[4]||(e[4]=o=>t(i).recordResult=o),placeholder:"请输入检查结果",clearable:"",onKeyup:C(y,["enter"])},null,8,["modelValue"])]),_:1}),l(s,null,{default:a(()=>[l(m,{type:"primary",icon:"Search",onClick:y},{default:a(()=>e[20]||(e[20]=[p("搜索")])),_:1}),l(m,{icon:"Refresh",onClick:se},{default:a(()=>e[21]||(e[21]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[ee,t(k)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(J,{shadow:"never"},{header:a(()=>[l(ve,{gutter:10,class:"mb8"},{default:a(()=>[l(P,{span:1.5},{default:a(()=>[g((u(),_(m,{type:"primary",plain:"",icon:"Plus",onClick:pe},{default:a(()=>e[22]||(e[22]=[p("新增")])),_:1})),[[I,["epwf:inspectionRecord:add"]]])]),_:1}),l(P,{span:1.5},{default:a(()=>[g((u(),_(m,{disabled:t(K),icon:"Edit",plain:"",type:"success",onClick:e[5]||(e[5]=o=>M())},{default:a(()=>e[23]||(e[23]=[p("修改")])),_:1},8,["disabled"])),[[I,["epwf:inspectionRecord:edit"]]])]),_:1}),l(P,{span:1.5},{default:a(()=>[g((u(),_(m,{disabled:t(j),icon:"Delete",plain:"",type:"danger",onClick:e[6]||(e[6]=o=>Q())},{default:a(()=>e[24]||(e[24]=[p("删除")])),_:1},8,["disabled"])),[[I,["epwf:inspectionRecord:remove"]]])]),_:1}),l(P,{span:1.5},{default:a(()=>[g((u(),_(m,{type:"warning",plain:"",icon:"Download",onClick:me},{default:a(()=>e[25]||(e[25]=[p("导出")])),_:1})),[[I,["epwf:inspectionRecord:export"]]])]),_:1}),l(fe,{showSearch:t(k),"onUpdate:showSearch":e[7]||(e[7]=o=>Ke(k)?k.value=o:null),onQueryTable:V},null,8,["showSearch"])]),_:1})]),default:a(()=>[g((u(),_(be,{data:t(B),stripe:"",onSelectionChange:ue},{default:a(()=>[l(v,{type:"selection",width:"55",align:"center"}),l(v,{label:"记录名称",align:"center",prop:"recordName"}),l(v,{label:"记录流程",align:"center",prop:"recordProcedure"},{default:a(o=>[l(_e,{options:t(E),value:o.row.recordProcedure},null,8,["options","value"])]),_:1}),l(v,{label:"检查单位",align:"center",prop:"recordUnitId"},{default:a(o=>[p(le(t(re)[o.row.recordUnitId]||"未知"),1)]),_:1}),l(v,{label:"记录时间",align:"center",prop:"recordTime",width:"180"},{default:a(o=>[h("span",null,le(r.parseTime(o.row.recordTime,"{y}-{m}-{d}")),1)]),_:1}),l(v,{label:"检查人员",align:"center",prop:"inspector"}),l(v,{label:"检查地点",align:"center",prop:"recordPlace"}),l(v,{label:"检查信息记录",align:"center",prop:"recordInformation"}),l(v,{label:"检查结果",align:"center",prop:"recordResult"}),l(v,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作",width:"305"},{default:a(o=>[l(L,{content:"记录附件预览",placement:"top"},{default:a(()=>[l(m,{link:"",type:"primary",icon:"Document",onClick:X=>ne(o.row)},{default:a(()=>e[26]||(e[26]=[p("记录附件预览")])),_:2},1032,["onClick"])]),_:2},1024),l(L,{content:"修改",placement:"top"},{default:a(()=>[g((u(),_(m,{link:"",type:"primary",icon:"Edit",onClick:X=>M(o.row)},{default:a(()=>e[27]||(e[27]=[p("修改")])),_:2},1032,["onClick"])),[[I,["epwf:inspectionRecord:edit"]]])]),_:2},1024),l(L,{content:"删除",placement:"top"},{default:a(()=>[g((u(),_(m,{icon:"Delete",link:"",type:"primary",onClick:X=>Q(o.row)},{default:a(()=>e[28]||(e[28]=[p("删除")])),_:2},1032,["onClick"])),[[I,["epwf:inspectionRecord:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[we,t(R)]]),g(l(ge,{total:t(D),page:t(i).pageNum,"onUpdate:page":e[8]||(e[8]=o=>t(i).pageNum=o),limit:t(i).pageSize,"onUpdate:limit":e[9]||(e[9]=o=>t(i).pageSize=o),onPagination:V},null,8,["total","page","limit"]),[[ee,t(D)>0]])]),_:1}),l(Ie,{title:t(b).title,modelValue:t(b).visible,"onUpdate:modelValue":e[19]||(e[19]=o=>t(b).visible=o),width:"700px","append-to-body":""},{footer:a(()=>[h("div",tl,[l(m,{loading:t(w),type:"primary",onClick:ce},{default:a(()=>e[29]||(e[29]=[p("确 定")])),_:1},8,["loading"]),l(m,{onClick:ie},{default:a(()=>e[30]||(e[30]=[p("取 消")])),_:1})])]),default:a(()=>[l(Y,{ref_key:"inspectionRecordFormRef",ref:S,model:t(n),rules:t(ae),"label-width":"120px"},{default:a(()=>[l(s,{label:"记录名称",prop:"recordName"},{default:a(()=>[l(c,{modelValue:t(n).recordName,"onUpdate:modelValue":e[10]||(e[10]=o=>t(n).recordName=o),placeholder:"请输入记录名称"},null,8,["modelValue"])]),_:1}),l(s,{label:"记录流程",prop:"recordProcedure"},{default:a(()=>[l(x,{modelValue:t(n).recordProcedure,"onUpdate:modelValue":e[11]||(e[11]=o=>t(n).recordProcedure=o),placeholder:"请选择记录流程"},{default:a(()=>[(u(!0),U(A,null,q(t(E),o=>(u(),_($,{key:o.value,label:o.label,value:parseInt(o.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"检查单位",prop:"recordUnitId"},{default:a(()=>[l(x,{modelValue:t(n).recordUnitId,"onUpdate:modelValue":e[12]||(e[12]=o=>t(n).recordUnitId=o),placeholder:"请选择检查单位"},{default:a(()=>[(u(!0),U(A,null,q(t(T),o=>(u(),_($,{key:o.deptId,label:o.deptName,value:o.deptId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"记录时间",prop:"recordTime"},{default:a(()=>[l(Ve,{modelValue:t(n).recordTime,"onUpdate:modelValue":e[13]||(e[13]=o=>t(n).recordTime=o),clearable:"",placeholder:"请选择记录时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(s,{label:"检查人员",prop:"inspector"},{default:a(()=>[l(c,{modelValue:t(n).inspector,"onUpdate:modelValue":e[14]||(e[14]=o=>t(n).inspector=o),placeholder:"请输入检查人员"},null,8,["modelValue"])]),_:1}),l(s,{label:"检查地点",prop:"recordPlace"},{default:a(()=>[l(c,{modelValue:t(n).recordPlace,"onUpdate:modelValue":e[15]||(e[15]=o=>t(n).recordPlace=o),placeholder:"请输入检查地点"},null,8,["modelValue"])]),_:1}),l(s,{label:"检查信息记录",prop:"recordInformation"},{default:a(()=>[l(c,{modelValue:t(n).recordInformation,"onUpdate:modelValue":e[16]||(e[16]=o=>t(n).recordInformation=o),placeholder:"请输入检查信息记录"},null,8,["modelValue"])]),_:1}),l(s,{label:"记录附件",prop:"file"},{default:a(()=>[l(ye,{modelValue:t(n).file,"onUpdate:modelValue":e[17]||(e[17]=o=>t(n).file=o),"attach-source-id":t(n).recordId,disabled:!1,"attach-category":"inspectionRecordFile","attach-source-type":"fjInspectionRecord"},null,8,["modelValue","attach-source-id"])]),_:1}),l(s,{label:"检查结果",prop:"recordResult"},{default:a(()=>[l(c,{modelValue:t(n).recordResult,"onUpdate:modelValue":e[18]||(e[18]=o=>t(n).recordResult=o),placeholder:"请输入检查结果"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Il=ze(rl,[["__scopeId","data-v-c9be4620"]]);export{Il as default};
