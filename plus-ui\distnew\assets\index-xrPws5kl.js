import{aC as E,d as ke,h as Le,ak as me,r as b,ai as M,X as Me,b as Ye,aH as Be,c as Y,o as d,p as l,t as a,w as y,q as I,a7 as He,M as Ke,e as t,A as Oe,G as Qe,H as x,B as Je,F as fe,C as ce,x as m,D as Ge,K as Xe,J as c,am as ge,aI as xe,ay as We,ax as Ze,z as U,aJ as el,y as $,aL as ll,v as al,az as tl,a8 as nl,aA as ol,i as ve}from"./index-D07cMzhp.js";import{_ as sl}from"./index-DVHplxfU.js";import{E as il}from"./el-date-picker-HyhB9X9n.js";import{E as ul}from"./el-row-CikYE3zA.js";import{_ as rl}from"./index-BWMgqvQ9.js";import{E as dl}from"./el-col-BaG5Rg5z.js";import{l as be}from"./index-DwdrO0Il.js";import{m as pl}from"./dayjs.min-Brw96_N0.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./index-VIEDZI2D.js";import"./el-tree-DW6MoFaI.js";const ml=g=>E({url:"/epms/eppcs/unsealingInfo/list",method:"get",params:g}),_e=g=>E({url:"/epms/eppcs/unsealingInfo/"+g,method:"get"}),fl=g=>E({url:"/epms/eppcs/unsealingInfo",method:"post",data:g}),cl=g=>E({url:"/epms/eppcs/unsealingInfo",method:"put",data:g}),gl=g=>E({url:"/epms/eppcs/unsealingInfo/"+g,method:"delete"}),we=(g,i=1)=>E({url:`/epms/eppcs/unsealingInfo/submit/${g}`,method:"post",params:{userType:i}}),vl=(g,i,P,j,T)=>E({url:`/epms/eppcs/unsealingInfo/confirm/${g}`,method:"post",params:{userType:i,action:P,approvalRemark:j,approver:T}}),bl={class:"p-2"},_l={class:"mb-[10px]"},wl={key:1},kl={class:"dialog-footer"},yl={class:"dialog-footer"},Il=ke({name:"UnsealingInfo"}),jl=ke({...Il,setup(g){const{proxy:i}=Le(),{eppcs_unsealing_status:P}=me(i==null?void 0:i.useDict("eppcs_unsealing_status")),j=b([]),T=b(!1),q=b(!0),A=b(!0),B=b([]),W=b(!0),Z=b(!0),H=b(0),ee=b(),K=b(),k=M({visible:!1,title:""}),D=M({visible:!1}),O=b(),z=b(!1),ye=M({approvalRemark:[{required:!0,message:"审批意见不能为空",trigger:"blur"}]}),Q=b([]);Me(()=>!s.value.unsealingId);const _=b(!1),le={unsealingId:void 0,applicationId:void 0,sampleName:void 0,sampleNumber:void 0,unsealPhoto:void 0,unsealTime:void 0,unsealer:void 0,unsealingStatus:0,remark:void 0,approvalTime:void 0,approvalRemark:void 0},Ie=M({form:{...le},queryParams:{pageNum:1,pageSize:10,projectName:void 0,sampleName:void 0,sampleNumber:void 0,unsealingStatus:void 0,params:{}},rules:{applicationId:[{required:!0,message:"取样申请不能为空",trigger:"change"}],sampleName:[{required:!0,message:"样品名称不能为空",trigger:"blur"}],sampleNumber:[{required:!0,message:"样品编号不能为空",trigger:"blur"}],unsealTime:[{required:!0,message:"拆封时间不能为空",trigger:"change"}],unsealer:[{required:!0,message:"拆封人不能为空",trigger:"blur"}]}}),{queryParams:f,form:s,rules:Ve}=me(Ie),V=async()=>{q.value=!0;try{const o=await ml(f.value),e=o.rows||[];if(e.length>0){const r=[...new Set(e.map(u=>u.applicationId).filter(u=>u))];if(r.length>0)try{const u=await be({applicationIds:r.join(","),pageNum:1,pageSize:r.length}),h=new Map;(u.rows||[]).forEach(C=>{h.set(C.applicationId,C)}),e.forEach(C=>{const p=h.get(C.applicationId);p&&(C.projectName=p.projectName)})}catch(u){console.warn("查询申请信息失败:",u)}}j.value=e,H.value=o.total}catch(o){console.error("查询列表失败:",o),i==null||i.$modal.msgError("查询列表失败")}finally{q.value=!1}},he=()=>{J(),_.value=!1,k.visible=!1},J=()=>{var o;s.value={...le},(o=K.value)==null||o.resetFields(),se()},R=()=>{f.value.pageNum=1,V()},Ce=()=>{var o;(o=ee.value)==null||o.resetFields(),R()},Ne=o=>{B.value=o.map(e=>e.unsealingId),W.value=o.length!=1,Z.value=!o.length},Se=async()=>{J(),_.value=!1,s.value.unsealTime=pl().format("YYYY-MM-DD HH:mm:ss"),s.value.unsealingStatus=0;try{const o=ve();o.nickname&&(s.value.unsealer=o.nickname)}catch(o){console.warn("获取用户信息失败:",o)}k.visible=!0,k.title="填报拆封信息"},ae=async o=>{J(),_.value=!1;const e=(o==null?void 0:o.unsealingId)||B.value[0],r=await _e(e);Object.assign(s.value,r.data),k.visible=!0,k.title="修改拆封信息"},te=(o="save")=>{var e;(e=K.value)==null||e.validate(async r=>{if(r){T.value=!0;try{let u=s.value.unsealingId;u?await cl(s.value):(u=(await fl(s.value)).data,s.value.unsealingId=u),o==="submit"&&u&&await we(u,1),i==null||i.$modal.msgSuccess("操作成功"),k.visible=!1,await V()}catch(u){console.error("操作失败:",u),i==null||i.$modal.msgError("操作失败")}finally{T.value=!1}}})},ne=async o=>{const e=(o==null?void 0:o.unsealingId)||B.value;await(i==null?void 0:i.$modal.confirm('是否确认删除拆封信息编号为"'+e+'"的数据项？').finally(()=>q.value=!1)),await gl(e),i==null||i.$modal.msgSuccess("删除成功"),await V()},Ue=()=>{i==null||i.download("epms/eppcs/unsealingInfo/export",{...f.value},`unsealingInfo_${new Date().getTime()}.xlsx`)},$e=o=>o.unsealingStatus===2,Ee=async o=>{i.showAttachPreview({attachSourceId:o.unsealingId,attachSourceType:"unsealingInfo",attachCategory:"general"})},Te=async o=>{try{await(i==null?void 0:i.$modal.confirm("确认提交该拆封信息吗？提交后将进入确认流程，无法修改。")),await we(o.unsealingId,1),i==null||i.$modal.msgSuccess("提交成功"),await V()}catch(e){e!=="cancel"&&(console.error("提交失败:",e),i==null||i.$modal.msgError("提交失败"))}},Pe=o=>{s.value={...o},D.visible=!0},oe=async o=>{if(!(!O.value||!await O.value.validate()))try{z.value=!0;const r=ve(),u=(r==null?void 0:r.nickname)||(r==null?void 0:r.name)||"当前用户";await vl(s.value.unsealingId,1,o,s.value.approvalRemark,u);const h=o===1?"确认":"驳回";i==null||i.$modal.msgSuccess(`${h}成功`),D.visible=!1,await V()}catch(r){console.error("确认失败:",r),i==null||i.$modal.msgError("确认失败")}finally{z.value=!1}},se=async()=>{try{const o=await be({currentPhase:3,pageSize:-1});Q.value=o.rows||[]}catch(o){console.error("获取取样申请列表失败:",o)}},De=o=>{_e(o.unsealingId).then(e=>{s.value=e.data,_.value=!0,k.visible=!0,k.title="查看拆封信息详情"})},Re=o=>{const e=Q.value.find(r=>r.applicationId===o);e&&(s.value.projectName=e.projectName)};return Ye(async()=>{await se(),await V()}),(o,e)=>{var de,pe;const r=Qe,u=Oe,h=Ge,C=Je,p=Xe,G=Ke,X=He,v=dl,Fe=rl,S=ul,w=Ze,ie=el,L=ll,je=We,qe=tl,Ae=il,ue=sl,re=ol,N=Be("hasPermi"),ze=al;return d(),Y("div",bl,[l(xe,{"enter-active-class":(de=t(i))==null?void 0:de.animate.searchAnimate.enter,"leave-active-class":(pe=t(i))==null?void 0:pe.animate.searchAnimate.leave},{default:a(()=>[y(I("div",_l,[l(X,{shadow:"hover"},{default:a(()=>[l(G,{ref_key:"queryFormRef",ref:ee,inline:!0,model:t(f),"label-width":"100px"},{default:a(()=>[l(u,{label:"检测项目名称",prop:"projectName"},{default:a(()=>[l(r,{modelValue:t(f).projectName,"onUpdate:modelValue":e[0]||(e[0]=n=>t(f).projectName=n),clearable:"",placeholder:"请输入检测项目名称",onKeyup:x(R,["enter"])},null,8,["modelValue"])]),_:1}),l(u,{label:"样品名称",prop:"sampleName"},{default:a(()=>[l(r,{modelValue:t(f).sampleName,"onUpdate:modelValue":e[1]||(e[1]=n=>t(f).sampleName=n),clearable:"",placeholder:"请输入样品名称",onKeyup:x(R,["enter"])},null,8,["modelValue"])]),_:1}),l(u,{label:"样品编号",prop:"sampleNumber"},{default:a(()=>[l(r,{modelValue:t(f).sampleNumber,"onUpdate:modelValue":e[2]||(e[2]=n=>t(f).sampleNumber=n),clearable:"",placeholder:"请输入样品编号",onKeyup:x(R,["enter"])},null,8,["modelValue"])]),_:1}),l(u,{label:"拆封状态",prop:"unsealingStatus"},{default:a(()=>[l(C,{modelValue:t(f).unsealingStatus,"onUpdate:modelValue":e[3]||(e[3]=n=>t(f).unsealingStatus=n),clearable:"",placeholder:"请选择拆封状态"},{default:a(()=>[(d(!0),Y(fe,null,ce(t(P),n=>(d(),m(h,{key:n.value,label:n.label,value:parseInt(n.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u,null,{default:a(()=>[l(p,{icon:"Search",type:"primary",onClick:R},{default:a(()=>e[25]||(e[25]=[c("搜索")])),_:1}),l(p,{icon:"Refresh",onClick:Ce},{default:a(()=>e[26]||(e[26]=[c("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[ge,t(A)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(X,{shadow:"never"},{header:a(()=>[l(S,{gutter:10,class:"mb8"},{default:a(()=>[l(v,{span:1.5},{default:a(()=>[y((d(),m(p,{icon:"Plus",plain:"",type:"primary",onClick:Se},{default:a(()=>e[27]||(e[27]=[c("填报拆封")])),_:1})),[[N,["eppcs:unsealingInfo:add"]]])]),_:1}),l(v,{span:1.5},{default:a(()=>[y((d(),m(p,{disabled:t(W),icon:"Edit",plain:"",type:"success",onClick:e[4]||(e[4]=n=>ae())},{default:a(()=>e[28]||(e[28]=[c("修改")])),_:1},8,["disabled"])),[[N,["eppcs:unsealingInfo:edit"]]])]),_:1}),l(v,{span:1.5},{default:a(()=>[y((d(),m(p,{disabled:t(Z),icon:"Delete",plain:"",type:"danger",onClick:e[5]||(e[5]=n=>ne())},{default:a(()=>e[29]||(e[29]=[c("删除")])),_:1},8,["disabled"])),[[N,["eppcs:unsealingInfo:remove"]]])]),_:1}),l(v,{span:1.5},{default:a(()=>[y((d(),m(p,{icon:"Download",plain:"",type:"warning",onClick:Ue},{default:a(()=>e[30]||(e[30]=[c("导出")])),_:1})),[[N,["eppcs:unsealingInfo:export"]]])]),_:1}),l(Fe,{showSearch:t(A),"onUpdate:showSearch":e[6]||(e[6]=n=>nl(A)?A.value=n:null),onQueryTable:V},null,8,["showSearch"])]),_:1})]),default:a(()=>[y((d(),m(je,{data:t(j),border:"",stripe:"",onSelectionChange:Ne},{default:a(()=>[l(w,{align:"center",type:"selection",width:"55"}),l(w,{index:n=>(t(f).pageNum-1)*t(f).pageSize+n+1,align:"center",label:"序号",type:"index",width:"55"},null,8,["index"]),l(w,{align:"center",label:"检测项目名称",prop:"projectName","show-overflow-tooltip":""}),l(w,{align:"center",label:"样品名称",prop:"sampleName","show-overflow-tooltip":""}),l(w,{align:"center",label:"样品编号",prop:"sampleNumber","show-overflow-tooltip":""}),l(w,{align:"center",label:"实验室内样品密封条照片",prop:"unsealPhoto",width:"120"},{default:a(n=>[n.row.unsealPhoto?(d(),m(p,{key:0,icon:"Document",link:"",type:"info",onClick:F=>Ee(n.row)},{default:a(()=>e[31]||(e[31]=[c("查看")])),_:2},1032,["onClick"])):(d(),Y("span",wl,"-"))]),_:1}),l(w,{align:"center",label:"拆封时间",prop:"unsealTime",width:"110"},{default:a(n=>[I("span",null,U(o.parseTime(n.row.unsealTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(w,{align:"center",label:"拆封人",prop:"unsealer","show-overflow-tooltip":""}),l(w,{align:"center",label:"拆封状态",prop:"unsealingStatus",width:"100"},{default:a(n=>[l(ie,{options:t(P),value:n.row.unsealingStatus},null,8,["options","value"])]),_:1}),l(w,{align:"center",label:"审批时间",prop:"approvalTime",width:"110"},{default:a(n=>[I("span",null,U(o.parseTime(n.row.approvalTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(w,{align:"center",label:"审批意见",prop:"approvalRemark","show-overflow-tooltip":""}),l(w,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作",width:"220"},{default:a(n=>[l(p,{icon:"View",link:"",type:"primary",onClick:F=>De(n.row)},{default:a(()=>e[32]||(e[32]=[c("查看")])),_:2},1032,["onClick"]),[0,1].includes(n.row.unsealingStatus)?(d(),m(L,{key:0,content:"修改",placement:"top"},{default:a(()=>[y((d(),m(p,{icon:"Edit",link:"",type:"primary",onClick:F=>ae(n.row)},{default:a(()=>e[33]||(e[33]=[c(" 修改 ")])),_:2},1032,["onClick"])),[[N,["eppcs:unsealingInfo:edit"]]])]),_:2},1024)):$("",!0),[0,1].includes(n.row.unsealingStatus)?(d(),m(L,{key:1,content:"提交",placement:"top"},{default:a(()=>[y((d(),m(p,{icon:"Upload",link:"",type:"success",onClick:F=>Te(n.row)},{default:a(()=>e[34]||(e[34]=[c(" 提交 ")])),_:2},1032,["onClick"])),[[N,["eppcs:unsealingInfo:submit"]]])]),_:2},1024)):$("",!0),$e(n.row)?(d(),m(L,{key:2,content:"确认",placement:"top"},{default:a(()=>[y((d(),m(p,{icon:"Check",link:"",type:"warning",onClick:F=>Pe(n.row)},{default:a(()=>e[35]||(e[35]=[c(" 确认 ")])),_:2},1032,["onClick"])),[[N,["eppcs:unsealingInfo:confirm"]]])]),_:2},1024)):$("",!0),[0,1].includes(n.row.unsealingStatus)?(d(),m(L,{key:3,content:"删除",placement:"top"},{default:a(()=>[y((d(),m(p,{icon:"Delete",link:"",type:"danger",onClick:F=>ne(n.row)},{default:a(()=>e[36]||(e[36]=[c(" 删除 ")])),_:2},1032,["onClick"])),[[N,["eppcs:unsealingInfo:remove"]]])]),_:2},1024)):$("",!0)]),_:1})]),_:1},8,["data"])),[[ze,t(q)]]),y(l(qe,{limit:t(f).pageSize,"onUpdate:limit":e[7]||(e[7]=n=>t(f).pageSize=n),page:t(f).pageNum,"onUpdate:page":e[8]||(e[8]=n=>t(f).pageNum=n),total:t(H),onPagination:V},null,8,["limit","page","total"]),[[ge,t(H)>0]])]),_:1}),l(re,{modelValue:t(k).visible,"onUpdate:modelValue":e[18]||(e[18]=n=>t(k).visible=n),title:t(k).title,"append-to-body":"",width:"800px"},{footer:a(()=>[I("div",kl,[t(_)?$("",!0):(d(),m(p,{key:0,loading:t(T),type:"primary",onClick:e[16]||(e[16]=n=>te("save"))},{default:a(()=>e[37]||(e[37]=[c("保存")])),_:1},8,["loading"])),!t(_)&&(t(s).unsealingStatus===0||t(s).unsealingStatus===1)?(d(),m(p,{key:1,loading:t(T),type:"success",onClick:e[17]||(e[17]=n=>te("submit"))},{default:a(()=>e[38]||(e[38]=[c("提交")])),_:1},8,["loading"])):$("",!0),l(p,{onClick:he},{default:a(()=>e[39]||(e[39]=[c("取消")])),_:1})])]),default:a(()=>[l(G,{ref_key:"unsealingInfoFormRef",ref:K,model:t(s),rules:t(Ve),"label-width":"120px"},{default:a(()=>[l(S,{gutter:20},{default:a(()=>[l(v,{span:12},{default:a(()=>[l(u,{label:"取样申请",prop:"applicationId"},{default:a(()=>[l(C,{modelValue:t(s).applicationId,"onUpdate:modelValue":e[9]||(e[9]=n=>t(s).applicationId=n),disabled:t(_),placeholder:"请选择取样申请",style:{width:"100%"},onChange:Re},{default:a(()=>[(d(!0),Y(fe,null,ce(t(Q),n=>(d(),m(h,{key:n.applicationId,label:`${n.projectName} - ${n.samplingPoint}`,value:n.applicationId},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(u,{label:"样品名称",prop:"sampleName"},{default:a(()=>[l(r,{modelValue:t(s).sampleName,"onUpdate:modelValue":e[10]||(e[10]=n=>t(s).sampleName=n),disabled:t(_),placeholder:"请输入样品名称"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(S,{gutter:20},{default:a(()=>[l(v,{span:12},{default:a(()=>[l(u,{label:"样品编号",prop:"sampleNumber"},{default:a(()=>[l(r,{modelValue:t(s).sampleNumber,"onUpdate:modelValue":e[11]||(e[11]=n=>t(s).sampleNumber=n),disabled:t(_),placeholder:"请输入样品编号"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(u,{label:"拆封时间",prop:"unsealTime"},{default:a(()=>[l(Ae,{modelValue:t(s).unsealTime,"onUpdate:modelValue":e[12]||(e[12]=n=>t(s).unsealTime=n),disabled:t(_),placeholder:"请选择拆封时间",style:{width:"100%"},type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(S,{gutter:20},{default:a(()=>[l(v,{span:12},{default:a(()=>[l(u,{label:"拆封人",prop:"unsealer"},{default:a(()=>[l(r,{modelValue:t(s).unsealer,"onUpdate:modelValue":e[13]||(e[13]=n=>t(s).unsealer=n),disabled:t(_),placeholder:"请输入拆封人"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(u,{label:"实验室内样品密封条照片",prop:"unsealPhoto"},{default:a(()=>[l(ue,{modelValue:t(s).unsealPhoto,"onUpdate:modelValue":e[14]||(e[14]=n=>t(s).unsealPhoto=n),"attach-source-id":t(s).unsealingId,disabled:t(_),"file-size":50,limit:10,"attach-category":"unseal","attach-source-type":"unsealingInfo"},null,8,["modelValue","attach-source-id","disabled"])]),_:1}),l(u,{label:"备注",prop:"remark"},{default:a(()=>[l(r,{modelValue:t(s).remark,"onUpdate:modelValue":e[15]||(e[15]=n=>t(s).remark=n),disabled:t(_),rows:3,placeholder:"请输入备注信息",type:"textarea"},null,8,["modelValue","disabled"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),l(re,{modelValue:t(D).visible,"onUpdate:modelValue":e[24]||(e[24]=n=>t(D).visible=n),"append-to-body":"",title:"确认拆封信息",width:"700px"},{footer:a(()=>[I("div",yl,[l(p,{loading:t(z),type:"success",onClick:e[21]||(e[21]=n=>oe(1))},{default:a(()=>e[41]||(e[41]=[c("确认")])),_:1},8,["loading"]),l(p,{loading:t(z),type:"danger",onClick:e[22]||(e[22]=n=>oe(2))},{default:a(()=>e[42]||(e[42]=[c("驳回")])),_:1},8,["loading"]),l(p,{onClick:e[23]||(e[23]=n=>t(D).visible=!1)},{default:a(()=>e[43]||(e[43]=[c("取消")])),_:1})])]),default:a(()=>[l(G,{ref_key:"confirmFormRef",ref:O,model:t(s),rules:t(ye),"label-width":"120px"},{default:a(()=>[l(X,{shadow:"never",style:{"margin-bottom":"20px"}},{header:a(()=>e[40]||(e[40]=[I("span",{style:{"font-weight":"bold"}},"拆封信息",-1)])),default:a(()=>[l(S,{gutter:20},{default:a(()=>[l(v,{span:12},{default:a(()=>[l(u,{label:"样品名称"},{default:a(()=>[I("span",null,U(t(s).sampleName),1)]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(u,{label:"样品编号"},{default:a(()=>[I("span",null,U(t(s).sampleNumber),1)]),_:1})]),_:1})]),_:1}),l(S,{gutter:20},{default:a(()=>[l(v,{span:12},{default:a(()=>[l(u,{label:"拆封时间"},{default:a(()=>[I("span",null,U(o.parseTime(t(s).unsealTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(u,{label:"拆封人"},{default:a(()=>[I("span",null,U(t(s).unsealer),1)]),_:1})]),_:1})]),_:1}),l(S,{gutter:20},{default:a(()=>[l(v,{span:12},{default:a(()=>[l(u,{label:"当前状态"},{default:a(()=>[l(ie,{options:t(P),value:t(s).unsealingStatus},null,8,["options","value"])]),_:1})]),_:1})]),_:1}),t(s).remark?(d(),m(u,{key:0,label:"备注信息"},{default:a(()=>[I("span",null,U(t(s).remark),1)]),_:1})):$("",!0),l(u,{label:"实验室内样品密封条照片",prop:"unsealPhoto"},{default:a(()=>[l(ue,{modelValue:t(s).unsealPhoto,"onUpdate:modelValue":e[19]||(e[19]=n=>t(s).unsealPhoto=n),"attach-source-id":t(s).unsealingId,disabled:!0,"attach-category":"unseal","attach-source-type":"unsealingInfo"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1}),l(u,{label:"审批意见",prop:"approvalRemark"},{default:a(()=>[l(r,{modelValue:t(s).approvalRemark,"onUpdate:modelValue":e[20]||(e[20]=n=>t(s).approvalRemark=n),rows:4,placeholder:"请输入审批意见（必填）",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});export{jl as default};
