import{d as me,h as Ee,ak as pe,u as Ue,r as p,ai as E,b as Le,c as f,o as u,p as a,t as o,w as U,q as K,a7 as Pe,M as Fe,e as t,A as xe,G as qe,H as Re,B as Ye,F as L,C as P,x as _,D as Me,K as ze,J as y,am as J,aI as He,ay as Be,ax as $e,z as w,aJ as Qe,y as h,aw as je,v as Ke,az as Je,aA as Oe,aL as We,Q as Ge}from"./index-D07cMzhp.js";import{E as Xe,a as Ze}from"./el-tab-pane-B0KEvacl.js";import{E as el}from"./el-date-picker-HyhB9X9n.js";import{g as ll,u as al,a as tl}from"./index-fayfDNIG.js";import{l as ol}from"./index-DmNU79vT.js";import{l as nl}from"./index-BhIIZXqy.js";import{b as rl}from"./index-DAD2mDUi.js";import il from"./jizhong-CgiWIIid.js";import dl from"./suizuan-DOOVR7wc.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";import"./index-D1htDV9m.js";import"./xlsx-BexUIDLF.js";const ul={class:"p-2"},sl={class:"mb-[10px]"},pl={key:0},ml={key:1},gl={key:0},yl={key:1},vl={key:0},fl={key:0},cl={key:0},bl={key:1},_l={class:"dialog-footer"},wl=me({name:"DrillingDaily"}),Vl=me({...wl,setup(Dl){const{proxy:b}=Ee(),{epnj_handling_type:F,drilling_well_type:O,epfy_medium_category:ge,epnj_transport_hunhe_medium_type:kl}=pe(b==null?void 0:b.useDict("epnj_handling_type","drilling_well_type","epfy_medium_category","epnj_transport_hunhe_medium_type")),N=Ue(),k=p([]),A=p(!1),x=p(!0),ye=p(!0);p([]),p(!0),p(!0);const q=p(0),W=p(),R=p(),C=E({visible:!1,title:""}),I=E({visible:!1,title:""}),Y=E({visible:!1,title:""}),v=p({pageNum:1,pageSize:10}),M=p(0),G=p([]),X=p([]),Z=p({}),ee=p([]),T=p([]),le={id:void 0,date:void 0,drillingCompany:void 0,drillingTeamNumber:void 0,wellNumber:void 0,wellType:void 0,spudDate:void 0,surfaceCasing:void 0,surfaceCasingDepth:void 0,designDepth:void 0,dailyFootage:void 0,currentDepth:void 0,mudHandlingType:void 0},ve=E({form:{...le},queryParams:{pageNum:1,pageSize:10,date:void 0,drillingCompany:void 0,drillingTeamNumber:void 0,wellNumber:void 0,wellType:void 0,spudDate:void 0,mudHandlingType:void 0,params:{}},rules:{id:[{required:!0,message:"id不能为空",trigger:"blur"}],date:[{required:!0,message:"日期不能为空",trigger:"blur"}]}}),{queryParams:s,form:r,rules:fe}=pe(ve),S=async()=>{x.value=!0;const n=await ll(s.value);let l=[];n.rows.forEach(g=>{const d=g.id;g.wellList&&g.wellList.length>0?g.wellList.forEach(m=>{l.push({...g,...m,drillingCompany:m.drillingCompany,wellNumber:m.wellName,wellType:m.wellType,spudDate:m.spudDate,groupId:d})}):l.push({...g,drillingCompany:"",wellNumber:"",wellType:null,spudDate:null,groupId:d})}),k.value=l,q.value=n.total,x.value=!1},ce=()=>{be(),C.visible=!1},be=()=>{var n;r.value={...le},(n=R.value)==null||n.resetFields()},z=()=>{s.value.pageNum=1,S()},_e=()=>{Y.visible=!0},we=()=>{var n;(n=W.value)==null||n.resetFields(),z()},Ve=()=>{var n;(n=R.value)==null||n.validate(async l=>{l&&(A.value=!0,r.value.id?await al(r.value).finally(()=>A.value=!1):await tl(r.value).finally(()=>A.value=!1),b==null||b.$modal.msgSuccess("操作成功"),C.visible=!1,await S())})},De=async()=>{const n=await ol();X.value=n.rows,X.value.forEach(l=>{l.id=l.prepId,l.label=l.wellName,T.value.push(l)})},ke=async()=>{Z.value.operationAreaType=2;const n=await nl(Z.value);ee.value=n.rows,ee.value.forEach(l=>{l.id=l.operationAreaId,l.label=l.operationAreaName,T.value.push(l)})},ae=n=>T.value.find(l=>l.id==n)?T.value.find(l=>l.id==n).label:"未知",Ce=({row:n,column:l,rowIndex:g,columnIndex:d})=>{var V;if([].includes(d))if(g===0||((V=k.value[g-1])==null?void 0:V.groupId)!==n.groupId){let D=0;for(let c=g;c<k.value.length&&k.value[c].groupId===n.groupId;c++)D++;return{rowspan:D,colspan:1}}else return{rowspan:0,colspan:0}},Te=(n,l)=>{v.value.mediumCategory=l,v.value.unloadLocation=ae(n.disposeId),v.value.relationId=void 0,v.value.date=n.date.split(" ")[0],H()},te=(n,l)=>{v.value.unloadLocation=void 0,v.value.mediumCategory=l,v.value.relationId=n.disposeId,v.value.date=n.date.split(" ")[0],H()},H=()=>{rl(v.value).then(n=>{G.value=n.rows,M.value=n.total,I.visible=!0})},he={1:"gxTransportRecord",2:"yxTransportRecord",3:"hhxTransportRecord"},Ne=async n=>{const l=he[n==null?void 0:n.mediumCategory]||"";n.mediumCategory,I.visible=!1,b.showAttachPreview({attachSourceId:n.transportId,attachSourceType:l})};return Le(()=>{var n,l;N.query.date!==void 0&&(s.value.date=((n=N.query.date)==null?void 0:n.toString())||""),N.query.wellNumber!==void 0&&(s.value.wellNames=((l=N.query.wellNumber)==null?void 0:l.toString())||""),ke(),De(),S()}),(n,l)=>{var ue,se;const g=el,d=xe,m=qe,V=Me,D=Ye,c=ze,oe=Fe,ne=Pe,i=$e,B=Qe,$=je,re=Be,ie=Je,Q=Oe,Ae=We,de=Ze,Ie=Xe,Se=Ke;return u(),f("div",ul,[a(He,{"enter-active-class":(ue=t(b))==null?void 0:ue.animate.searchAnimate.enter,"leave-active-class":(se=t(b))==null?void 0:se.animate.searchAnimate.leave},{default:o(()=>[U(K("div",sl,[a(ne,{shadow:"hover"},{default:o(()=>[a(oe,{ref_key:"queryFormRef",ref:W,inline:!0,model:t(s),"label-width":"100px"},{default:o(()=>[a(d,{label:"日期",prop:"date"},{default:o(()=>[a(g,{modelValue:t(s).date,"onUpdate:modelValue":l[0]||(l[0]=e=>t(s).date=e),clearable:"",placeholder:"请选择日期",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),a(d,{label:"钻井井号",prop:"wellNumber"},{default:o(()=>[a(m,{modelValue:t(s).wellNames,"onUpdate:modelValue":l[1]||(l[1]=e=>t(s).wellNames=e),clearable:"",placeholder:"请输入钻井井号",onKeyup:Re(z,["enter"])},null,8,["modelValue"])]),_:1}),a(d,{label:"处理地点",prop:"disposeId"},{default:o(()=>[a(D,{modelValue:t(s).disposeId,"onUpdate:modelValue":l[2]||(l[2]=e=>t(s).disposeId=e),clearable:"",filterable:"",placeholder:"请选择处理地点",style:{width:"240px"}},{default:o(()=>[(u(!0),f(L,null,P(t(T),e=>(u(),_(V,{key:e.id,label:e.label,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"泥浆处理类型",prop:"disposeType"},{default:o(()=>[a(D,{modelValue:t(s).disposeType,"onUpdate:modelValue":l[3]||(l[3]=e=>t(s).disposeType=e),placeholder:"请选择处理方式",clearable:""},{default:o(()=>[(u(!0),f(L,null,P(t(F),e=>(u(),_(V,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,null,{default:o(()=>[a(c,{icon:"Search",type:"primary",onClick:z},{default:o(()=>l[22]||(l[22]=[y("搜索")])),_:1}),a(c,{icon:"Refresh",onClick:we},{default:o(()=>l[23]||(l[23]=[y("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[J,t(ye)]])]),_:1},8,["enter-active-class","leave-active-class"]),a(c,{icon:"Search",type:"primary",onClick:_e},{default:o(()=>l[24]||(l[24]=[y("月度统计")])),_:1}),a(ne,{shadow:"never",style:{"margin-top":"10px"}},{default:o(()=>[U((u(),_(re,{stripe:"",data:t(k),"span-method":Ce,style:{width:"100%"},border:""},{default:o(()=>[a(i,{prop:"date",label:"日期",align:"center"},{default:o(e=>[y(w(n.parseTime(e.row.date,"{y}-{m}-{d}")),1)]),_:1}),a(i,{align:"center",label:"钻井公司",prop:"drillingCompany"}),a(i,{align:"center",label:"钻井井号",prop:"wellNumber"}),a(i,{prop:"wellType",label:"井别",align:"center"},{default:o(e=>[a(B,{options:t(O),value:e.row.wellType},null,8,["options","value"])]),_:1}),a(i,{prop:"spudDate",label:"开钻日期",align:"center"},{default:o(e=>[y(w(n.parseTime(e.row.spudDate,"{y}-{m}-{d}")),1)]),_:1}),a(i,{prop:"disposeId",label:"处理地点",align:"center","min-width":"90"},{default:o(e=>[y(w(ae(e.row.disposeId)),1)]),_:1}),a(i,{prop:"disposeType",label:"泥浆处理类型",align:"center"},{default:o(e=>[a(B,{options:t(F),value:e.row.disposeType},null,8,["options","value"])]),_:1}),a(i,{prop:"slurryDisposeAmount",label:"泥浆处理量（方）",align:"center"},{default:o(e=>[e.row.slurryDisposeAmount!=null&&e.row.slurryDisposeAmount!=0&&e.row.disposeType==1?(u(),_($,{key:0,class:"allowClick",type:"success",onClick:j=>Te(e.row,3)},{default:o(()=>[y(w(e.row.slurryDisposeAmount),1)]),_:2},1032,["onClick"])):h("",!0)]),_:1}),a(i,{label:"泥浆暂存量（方）",align:"center",prop:"slurryStagingAmount"},{default:o(e=>[e.row.slurryStagingAmount?h("",!0):(u(),f("span",pl,"/"))]),_:1}),a(i,{prop:"mudPullingAmount",label:"泥饼拉运量（方）",align:"center"},{default:o(e=>[e.row.mudPullingAmount?(u(),_($,{key:0,class:"allowClick",type:"success",onClick:j=>te(e.row,1)},{default:o(()=>[y(w(e.row.mudPullingAmount),1)]),_:2},1032,["onClick"])):(u(),f("span",ml,"/"))]),_:1}),a(i,{label:"泥饼暂存量（方）",align:"center",prop:"mudStagingAmount"},{default:o(e=>[e.row.mudStagingAmount?h("",!0):(u(),f("span",gl,"/"))]),_:1}),a(i,{prop:"waterPullingAmount",label:"滤水拉运量（方）",align:"center"},{default:o(e=>[e.row.waterPullingAmount?(u(),_($,{key:0,class:"allowClick",type:"success",onClick:j=>te(e.row,2)},{default:o(()=>[y(w(e.row.waterPullingAmount),1)]),_:2},1032,["onClick"])):(u(),f("span",yl,"/"))]),_:1}),a(i,{label:"滤水暂存量（方）",align:"center",prop:"waterStagingAmount"},{default:o(e=>[e.row.waterStagingAmount?h("",!0):(u(),f("span",vl,"/"))]),_:1}),a(i,{prop:"totalPullingAmount",label:"总拉运量（方）",align:"center"},{default:o(e=>[e.row.totalPullingAmount?h("",!0):(u(),f("span",fl,"/"))]),_:1}),a(i,{prop:"loss",label:"损耗（方）",align:"center"},{default:o(e=>[e.row.loss===null||e.row.loss===void 0||e.row.loss<0?(u(),f("span",cl,"/")):(u(),f("span",bl,w(e.row.loss),1))]),_:1})]),_:1},8,["data"])),[[Se,t(x)]]),U(a(ie,{limit:t(s).pageSize,"onUpdate:limit":l[4]||(l[4]=e=>t(s).pageSize=e),page:t(s).pageNum,"onUpdate:page":l[5]||(l[5]=e=>t(s).pageNum=e),total:t(q),onPagination:S},null,8,["limit","page","total"]),[[J,t(q)>0]])]),_:1}),a(Q,{modelValue:t(C).visible,"onUpdate:modelValue":l[17]||(l[17]=e=>t(C).visible=e),title:t(C).title,"append-to-body":"",width:"500px"},{footer:o(()=>[K("div",_l,[a(c,{loading:t(A),type:"primary",onClick:Ve},{default:o(()=>l[27]||(l[27]=[y("确 定")])),_:1},8,["loading"]),a(c,{onClick:ce},{default:o(()=>l[28]||(l[28]=[y("取 消")])),_:1})])]),default:o(()=>[a(oe,{ref_key:"drillingDailyFormRef",ref:R,model:t(r),rules:t(fe),"label-width":"100px"},{default:o(()=>[a(d,{label:"日期",prop:"date"},{default:o(()=>[a(g,{modelValue:t(r).date,"onUpdate:modelValue":l[6]||(l[6]=e=>t(r).date=e),clearable:"",placeholder:"请选择日期",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),a(d,{label:"钻井公司",prop:"drillingCompany"},{default:o(()=>[a(m,{modelValue:t(r).drillingCompany,"onUpdate:modelValue":l[7]||(l[7]=e=>t(r).drillingCompany=e),placeholder:"请输入钻井公司"},null,8,["modelValue"])]),_:1}),a(d,{label:"钻井队号",prop:"drillingTeamNumber"},{default:o(()=>[a(m,{modelValue:t(r).drillingTeamNumber,"onUpdate:modelValue":l[8]||(l[8]=e=>t(r).drillingTeamNumber=e),placeholder:"请输入钻井队号"},null,8,["modelValue"])]),_:1}),a(d,{label:"钻井井号",prop:"wellNumber"},{default:o(()=>[a(m,{modelValue:t(r).wellNumber,"onUpdate:modelValue":l[9]||(l[9]=e=>t(r).wellNumber=e),placeholder:"请输入钻井井号"},null,8,["modelValue"])]),_:1}),a(d,{label:"井别",prop:"wellType"},{default:o(()=>[a(D,{modelValue:t(r).wellType,"onUpdate:modelValue":l[10]||(l[10]=e=>t(r).wellType=e),placeholder:"请选择井别"},{default:o(()=>[(u(!0),f(L,null,P(t(O),e=>(u(),_(V,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"开钻日期",prop:"spudDate"},{default:o(()=>[a(g,{modelValue:t(r).spudDate,"onUpdate:modelValue":l[11]||(l[11]=e=>t(r).spudDate=e),clearable:"",placeholder:"请选择开钻日期",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),a(d,{label:"设计井深",prop:"designDepth"},{default:o(()=>[a(m,{modelValue:t(r).designDepth,"onUpdate:modelValue":l[12]||(l[12]=e=>t(r).designDepth=e),placeholder:"请输入设计井深"},{append:o(()=>l[25]||(l[25]=[y("米")])),_:1},8,["modelValue"])]),_:1}),a(d,{label:"日进尺",prop:"dailyFootage"},{default:o(()=>[a(m,{modelValue:t(r).dailyFootage,"onUpdate:modelValue":l[13]||(l[13]=e=>t(r).dailyFootage=e),placeholder:"请输入日进尺"},null,8,["modelValue"])]),_:1}),a(d,{label:"目前井深",prop:"currentDepth"},{default:o(()=>[a(m,{modelValue:t(r).currentDepth,"onUpdate:modelValue":l[14]||(l[14]=e=>t(r).currentDepth=e),placeholder:"请输入目前井深"},{append:o(()=>l[26]||(l[26]=[y("米")])),_:1},8,["modelValue"])]),_:1}),a(d,{label:"工作内容",prop:"workContent"},{default:o(()=>[a(m,{modelValue:t(r).workContent,"onUpdate:modelValue":l[15]||(l[15]=e=>t(r).workContent=e),rows:2,placeholder:"请输入工作内容",type:"textarea"},null,8,["modelValue"])]),_:1}),a(d,{label:"泥浆处理类型",prop:"mudHandlingType"},{default:o(()=>[a(D,{modelValue:t(r).mudHandlingType,"onUpdate:modelValue":l[16]||(l[16]=e=>t(r).mudHandlingType=e),placeholder:"请选择泥浆处理类型"},{default:o(()=>[(u(!0),f(L,null,P(t(F),e=>(u(),_(V,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),a(Q,{title:"数据统计",modelValue:t(I).visible,"onUpdate:modelValue":l[20]||(l[20]=e=>t(I).visible=e),width:"1200px","append-to-body":""},{default:o(()=>[a(re,{stripe:"",data:t(G)},{default:o(()=>[a(i,{align:"center",type:"selection",width:"55"}),a(i,{label:"拉运时间",align:"center",prop:"transportTime",width:"140"},{default:o(e=>[K("span",null,w(n.parseTime(e.row.transportTime,"{y}-{m}-{d}")),1)]),_:1}),a(i,{label:"分类",align:"center",prop:"mediumCategory"},{default:o(e=>[a(B,{options:t(ge),value:e.row.mediumCategory},null,8,["options","value"])]),_:1}),a(i,{align:"center",label:"起运点",prop:"departurePoint"}),a(i,{align:"center",label:"数量(方)",prop:"number"}),a(i,{align:"center",label:"拉运人",prop:"transporter"}),a(i,{align:"center",label:"车牌号",prop:"licensePlate"}),a(i,{align:"center",label:"接收点",prop:"arrivalPoint"}),a(i,{align:"center",label:"接收人",prop:"receiver"}),a(i,{align:"center",label:"备注信息",prop:"remark"}),a(i,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作",width:"120"},{default:o(e=>[a(Ae,{content:"拉运材料",placement:"top"},{default:o(()=>[a(c,{link:"",type:"primary",icon:"Document",onClick:j=>Ne(e.row)},{default:o(()=>l[29]||(l[29]=[y("拉运材料")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),U(a(ie,{limit:t(v).pageSize,"onUpdate:limit":l[18]||(l[18]=e=>t(v).pageSize=e),page:t(v).pageNum,"onUpdate:page":l[19]||(l[19]=e=>t(v).pageNum=e),total:t(M),onPagination:H},null,8,["limit","page","total"]),[[J,t(M)>0]])]),_:1},8,["modelValue"]),a(Q,{modelValue:t(Y).visible,"onUpdate:modelValue":l[21]||(l[21]=e=>t(Y).visible=e),width:"1200px","append-to-body":""},{default:o(()=>[a(Ie,{class:"demo-tabs",style:{position:"relative"},type:"card"},{default:o(()=>[a(de,{label:"集中站"},{default:o(()=>[a(il)]),_:1}),a(de,{label:"随钻"},{default:o(()=>[a(dl)]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),ql=Ge(Vl,[["__scopeId","data-v-d8779690"]]);export{ql as default};
