package com.biz.epms.epnj.service;

import com.biz.epms.epnj.domain.bo.EpnjMudDailyBo;
import com.biz.epms.epnj.domain.vo.EpnjMudDailyVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.text.ParseException;
import java.util.Collection;
import java.util.List;

/**
 * 泥浆日报Service接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface IEpnjMudDailyService {

    /**
     * 查询泥浆日报
     *
     * @param id 主键
     * @return 泥浆日报
     */
    EpnjMudDailyVo queryById(Long id);

    /**
     * 分页查询泥浆日报列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 泥浆日报分页列表
     */
    TableDataInfo<EpnjMudDailyVo> queryPageList(EpnjMudDailyBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的泥浆日报列表
     *
     * @param bo 查询条件
     * @return 泥浆日报列表
     */
    List<EpnjMudDailyVo> queryList(EpnjMudDailyBo bo);

    /**
     * 新增泥浆日报
     *
     * @param bo 泥浆日报
     * @return 是否新增成功
     */
    Boolean insertByBo(EpnjMudDailyBo bo);

    /**
     * 修改泥浆日报
     *
     * @param bo 泥浆日报
     * @return 是否修改成功
     */
    Boolean updateByBo(EpnjMudDailyBo bo);

    /**
     * 校验并批量删除泥浆日报信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    EpnjMudDailyBo getLoss(EpnjMudDailyBo bo) throws ParseException;

    /**
     * 计算拉运量
     * @param date
     */
    void calcTransportQuantity(String date);
}
