<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :inline="true" :model="queryParams" label-width="100px">
            <el-form-item label="日期" prop="date">
              <el-date-picker v-model="queryParams.date" clearable placeholder="请选择日期" type="date" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="钻井井号" prop="wellNumber">
              <el-input v-model="queryParams.wellNames" clearable placeholder="请输入钻井井号" @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="处理地点" prop="disposeId">
              <el-select v-model="queryParams.disposeId" clearable filterable placeholder="请选择处理地点" style="width: 240px">
                <el-option v-for="item in allList" :key="item.id" :label="item.label" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="泥浆处理类型" prop="disposeType">
              <el-select v-model="queryParams.disposeType" placeholder="请选择处理方式" clearable>
                <el-option v-for="dict in epnj_handling_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>
    <el-button icon="Search" type="primary" @click="handleMonthStat">月度统计</el-button>
    <el-card shadow="never" style="margin-top: 10px">
      <!--      <template #header>-->
      <!--        <el-row :gutter="10" class="mb8">-->
      <!--&lt;!&ndash;          <el-col :span="1.5">&ndash;&gt;-->
      <!--&lt;!&ndash;            <el-button v-hasPermi="['epnj:drillingDaily:export']" icon="Download" plain type="warning"&ndash;&gt;-->
      <!--&lt;!&ndash;                       @click="handleExport">导出&ndash;&gt;-->
      <!--&lt;!&ndash;            </el-button>&ndash;&gt;-->
      <!--&lt;!&ndash;          </el-col>&ndash;&gt;-->
      <!--          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
      <!--        </el-row>-->
      <!--      </template>-->

      <el-table stripe v-loading="loading" :data="drillingDailyList" :span-method="spanMethod" style="width: 100%" border>
        <el-table-column prop="date" label="日期" align="center">
          <template #default="scope">
            {{ parseTime(scope.row.date, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>

        <el-table-column align="center" label="钻井公司" prop="drillingCompany" />
        <el-table-column align="center" label="钻井井号" prop="wellNumber" />

        <el-table-column prop="wellType" label="井别" align="center">
          <template #default="scope">
            <dict-tag :options="drilling_well_type" :value="scope.row.wellType" />
          </template>
        </el-table-column>

        <el-table-column prop="spudDate" label="开钻日期" align="center">
          <template #default="scope">
            {{ parseTime(scope.row.spudDate, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>

        <el-table-column prop="disposeId" label="处理地点" align="center" min-width="90">
          <template #default="scope">
            {{ getKayName(scope.row.disposeId) }}
          </template>
        </el-table-column>

        <el-table-column prop="disposeType" label="泥浆处理类型" align="center">
          <template #default="scope">
            <dict-tag :options="epnj_handling_type" :value="scope.row.disposeType" />
          </template>
        </el-table-column>

        <el-table-column prop="slurryDisposeAmount" label="泥浆处理量（方）" align="center">
          <template #default="scope">
            <el-tag
              v-if="scope.row.slurryDisposeAmount != null && scope.row.slurryDisposeAmount != 0 && scope.row.disposeType == 1"
              class="allowClick"
              type="success"
              @click="getRecords(scope.row, 3)"
            >
              {{ scope.row.slurryDisposeAmount }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="泥浆暂存量（方）" align="center" prop="slurryStagingAmount">
          <template #default="scope">
            <span v-if="!scope.row.slurryStagingAmount">/</span>
          </template>
        </el-table-column>
        <el-table-column prop="mudPullingAmount" label="泥饼拉运量（方）" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.mudPullingAmount" class="allowClick" type="success" @click="handleClick(scope.row, 1)">
              {{ scope.row.mudPullingAmount }}
            </el-tag>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column label="泥饼暂存量（方）" align="center" prop="mudStagingAmount">
          <template #default="scope">
            <span v-if="!scope.row.mudStagingAmount">/</span>
          </template>
        </el-table-column>
        <el-table-column prop="waterPullingAmount" label="滤水拉运量（方）" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.waterPullingAmount" class="allowClick" type="success" @click="handleClick(scope.row, 2)">
              {{ scope.row.waterPullingAmount }}
            </el-tag>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column label="滤水暂存量（方）" align="center" prop="waterStagingAmount">
          <template #default="scope">
            <span v-if="!scope.row.waterStagingAmount">/</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalPullingAmount" label="总拉运量（方）" align="center">
          <template #default="scope">
            <span v-if="!scope.row.totalPullingAmount">/</span>
          </template>
        </el-table-column>
        <el-table-column prop="loss" label="损耗（方）" align="center">
          <template #default="scope">
            <span v-if="scope.row.loss === null || scope.row.loss === undefined || scope.row.loss < 0">/</span>
            <span v-else>{{ scope.row.loss }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改钻井日报对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" append-to-body width="500px">
      <el-form ref="drillingDailyFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="日期" prop="date">
          <el-date-picker v-model="form.date" clearable placeholder="请选择日期" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"> </el-date-picker>
        </el-form-item>

        <el-form-item label="钻井公司" prop="drillingCompany">
          <el-input v-model="form.drillingCompany" placeholder="请输入钻井公司" />
        </el-form-item>
        <el-form-item label="钻井队号" prop="drillingTeamNumber">
          <el-input v-model="form.drillingTeamNumber" placeholder="请输入钻井队号" />
        </el-form-item>
        <el-form-item label="钻井井号" prop="wellNumber">
          <el-input v-model="form.wellNumber" placeholder="请输入钻井井号" />
        </el-form-item>
        <el-form-item label="井别" prop="wellType">
          <el-select v-model="form.wellType" placeholder="请选择井别">
            <el-option v-for="dict in drilling_well_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="开钻日期" prop="spudDate">
          <el-date-picker v-model="form.spudDate" clearable placeholder="请选择开钻日期" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="设计井深" prop="designDepth">
          <el-input v-model="form.designDepth" placeholder="请输入设计井深">
            <template #append>米</template>
          </el-input>
        </el-form-item>
        <el-form-item label="日进尺" prop="dailyFootage">
          <el-input v-model="form.dailyFootage" placeholder="请输入日进尺" />
        </el-form-item>
        <el-form-item label="目前井深" prop="currentDepth">
          <el-input v-model="form.currentDepth" placeholder="请输入目前井深">
            <template #append>米</template>
          </el-input>
        </el-form-item>
        <el-form-item label="工作内容" prop="workContent">
          <el-input v-model="form.workContent" :rows="2" placeholder="请输入工作内容" type="textarea" />
        </el-form-item>

        <el-form-item label="泥浆处理类型" prop="mudHandlingType">
          <el-select v-model="form.mudHandlingType" placeholder="请选择泥浆处理类型">
            <el-option v-for="dict in epnj_handling_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog title="数据统计" v-model="recordDialog.visible" width="1200px" append-to-body>
      <el-table stripe :data="transportRecordList">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column label="拉运时间" align="center" prop="transportTime" width="140">
          <template #default="scope">
            <span>{{ parseTime(scope.row.transportTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分类" align="center" prop="mediumCategory">
          <template #default="scope">
            <dict-tag :options="epfy_medium_category" :value="scope.row.mediumCategory" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="起运点" prop="departurePoint" />
        <el-table-column align="center" label="数量(方)" prop="number" />
        <el-table-column align="center" label="拉运人" prop="transporter" />
        <el-table-column align="center" label="车牌号" prop="licensePlate" />
        <el-table-column align="center" label="接收点" prop="arrivalPoint" />
        <el-table-column align="center" label="接收人" prop="receiver" />
        <el-table-column align="center" label="备注信息" prop="remark" />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-tooltip content="拉运材料" placement="top">
              <el-button link type="primary" icon="Document" @click="handlePreview(scope.row)">拉运材料</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="transportTotal > 0"
        v-model:limit="recordQuery.pageSize"
        v-model:page="recordQuery.pageNum"
        :total="transportTotal"
        @pagination="getAppList"
      />
    </el-dialog>
    <el-dialog v-model="monthDialog.visible" width="1200px" append-to-body>
      <el-tabs class="demo-tabs" style="position: relative" type="card">
        <el-tab-pane label="集中站">
          <Jizhong />
        </el-tab-pane>
        <el-tab-pane label="随钻">
          <suizuan />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script lang="ts" name="DrillingDaily" setup>
import { addDrillingDaily, delDrillingDaily, getDrillingDaily, getTableData, updateDrillingDaily } from '@/api/epms/epnj/drillingDaily';
import { DrillingDailyForm, DrillingDailyVO } from '@/api/epms/epnj/drillingDaily/types';
import { listWellPreparation } from '@/api/epms/epnj/wellPreparation';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { WellPreparationVO } from '@/api/epms/epnj/wellPreparation/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { TransportRecordQuery, TransportRecordVO } from '@/api/epms/epfy/transportRecord/types';
import { getListByDaliy } from '@/api/epms/epfy/transportRecord';
import { MudDailyForm, MudDailyQuery } from '@/api/epms/epnj/mudDaily/types';
import Jizhong from '@/views/epms/epnj/monthlyStatistics/jizhong.vue';
import suizuan from '@/views/epms/epnj/monthlyStatistics/suizuan.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epnj_handling_type, drilling_well_type, epfy_medium_category, epnj_transport_hunhe_medium_type } = toRefs<any>(
  proxy?.useDict('epnj_handling_type', 'drilling_well_type', 'epfy_medium_category', 'epnj_transport_hunhe_medium_type')
);

const route = useRoute();
const drillingDailyList = ref<DrillingDailyVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const drillingDailyFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const recordDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const monthDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const recordQuery = ref<TransportRecordQuery>({
  pageNum: 1,
  pageSize: 10
});
const transportTotal = ref(0);
const transportRecordList = ref<TransportRecordVO[]>([]);

const wellList = ref<WellPreparationVO[]>([]);
const areaQuery = ref<OperationAreaQuery>({});
const areaList = ref<OperationAreaVO[]>([]);
const allList = ref([]);

const initFormData: DrillingDailyForm = {
  id: undefined,
  date: undefined,
  drillingCompany: undefined,
  drillingTeamNumber: undefined,
  wellNumber: undefined,
  wellType: undefined,
  spudDate: undefined,
  surfaceCasing: undefined,
  surfaceCasingDepth: undefined,
  designDepth: undefined,
  dailyFootage: undefined,
  currentDepth: undefined,
  mudHandlingType: undefined
};
const data = reactive<PageData<MudDailyForm, MudDailyQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    date: undefined,
    disposeName: undefined,
    disposeId: undefined,
    drillingCompany: undefined,
    drillingTeamNumber: undefined,
    wellNumber: undefined,
    wellType: undefined,
    spudDate: undefined,
    mudHandlingType: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    date: [{ required: true, message: '日期不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 定义 props
const props = defineProps({
  disposeName: {
    type: String,
    default: ''
  },
  disposeId: {
    type: String,
    default: ''
  }
});

/** 查询钻井日报列表 */
const getList = async () => {
  loading.value = true;
  if (queryParams.value.disposeName) {
    const selectedItem = allList.value.find((item: any) => item.label === queryParams.value.disposeName);
    if (selectedItem) {
      queryParams.value.disposeId = selectedItem.id;
    }
  }
  const res = await getTableData(queryParams.value);

  const flatList: any[] = [];

  res.rows.forEach((item: any) => {
    const groupId = item.id; // 使用主表 id 作为分组标识
    if (item.wellList && item.wellList.length > 0) {
      item.wellList.forEach((well: any) => {
        flatList.push({
          ...item,
          ...well,
          drillingCompany: well.drillingCompany,
          wellNumber: well.wellName,
          wellType: well.wellType,
          spudDate: well.spudDate,
          groupId // 添加分组标识
        });
      });
    } else {
      flatList.push({
        ...item,
        drillingCompany: '',
        wellNumber: '',
        wellType: null,
        spudDate: null,
        groupId
      });
    }
  });

  drillingDailyList.value = flatList;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  drillingDailyFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 搜索按钮操作 */
const handleMonthStat = () => {
  monthDialog.visible = true;
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DrillingDailyVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加钻井日报';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: DrillingDailyVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getDrillingDaily(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改钻井日报';
};

/** 提交按钮 */
const submitForm = () => {
  drillingDailyFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDrillingDaily(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addDrillingDaily(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DrillingDailyVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除钻井日报编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delDrillingDaily(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epnj/drillingDaily/export',
    {
      ...queryParams.value
    },
    `drillingDaily_${new Date().getTime()}.xlsx`
  );
};

const getWellList = async () => {
  const res = await listWellPreparation();
  wellList.value = res.rows;
  wellList.value.forEach((item) => {
    item.id = item.prepId;
    item.label = item.wellName;
    allList.value.push(item);
  });
};

/** 获取集中站列表 */
const queryAreaList = async () => {
  areaQuery.value.operationAreaType = 2;
  const res = await listOperationArea(areaQuery.value);
  areaList.value = res.rows;
  areaList.value.forEach((item) => {
    item.id = item.operationAreaId;
    item.label = item.operationAreaName;
    allList.value.push(item);
  });
};

const getKayName = (id: string) => {
  return allList.value.find((item: any) => item.id == id) ? allList.value.find((item: any) => item.id == id).label : '未知';
};
const getDrillingCompanies = (wellList: any[]) => {
  if (wellList.length > 0) {
    return wellList[0].drillingCompany;
  }
};

const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 定义需要合并的列索引（从 0 开始）
  // const mergeColumns = [0, 5, 6,  7, 8, 9, 10,11,12,13,14,15]; // 对应 date, disposeId, slurryDisposeAmount 等列
  const mergeColumns = []; // 对应 date, disposeId, slurryDisposeAmount 等列

  if (!mergeColumns.includes(columnIndex)) return;

  // 检查当前行和下一行是否属于同一个 group
  if (rowIndex === 0 || drillingDailyList.value[rowIndex - 1]?.groupId !== row.groupId) {
    // 只有第一个属于该 group 的行返回 rowspan
    let count = 0;
    for (let i = rowIndex; i < drillingDailyList.value.length; i++) {
      if (drillingDailyList.value[i].groupId === row.groupId) {
        count++;
      } else {
        break;
      }
    }
    return { rowspan: count, colspan: 1 };
  } else {
    // 其他属于同组的行隐藏
    return { rowspan: 0, colspan: 0 };
  }
};

const getRecords = (row, type) => {
  recordQuery.value.mediumCategory = type;
  recordQuery.value.unloadLocation = getKayName(row.disposeId);
  recordQuery.value.relationId = undefined;
  recordQuery.value.date = row.date.split(' ')[0];
  getAppList();
};
const handleClick = (row, type) => {
  recordQuery.value.unloadLocation = undefined;
  recordQuery.value.mediumCategory = type;
  recordQuery.value.relationId = row.disposeId;
  recordQuery.value.date = row.date.split(' ')[0];
  getAppList();
};
const getAppList = () => {
  getListByDaliy(recordQuery.value).then((res) => {
    transportRecordList.value = res.rows;
    transportTotal.value = res.total;
    recordDialog.visible = true;
  });
};
const typeMap: Record<number, string> = {
  1: 'gxTransportRecord',
  2: 'yxTransportRecord',
  3: 'hhxTransportRecord'
};
const handlePreview = async (row?: TransportRecordVO) => {
  const attachSourceType = typeMap[row?.mediumCategory] || '';
  row.mediumCategory;
  recordDialog.visible = false;
  proxy.showAttachPreview({
    attachSourceId: row.transportId,
    attachSourceType: attachSourceType
  });
};
// 监听 disposeName或 disposeId  并设置对应的 disposeId 和触发查询
watch(
  () => [props.disposeName, props.disposeId],
  ([newName, newId]) => {
    // 如果提供了disposeId，优先使用id进行查询
    if (newId) {
      queryParams.value.disposeId = newId;
    }
    if (newName) {
      queryParams.value.disposeName = newName;
    }

    // 根据情况决定是否需要触发查询
    if (newId || newName) {
      handleQuery(); // 主动触发查询
    }
  },
  { immediate: true }
);
onMounted(async () => {
  if (route.query.date !== undefined) {
    queryParams.value.date = route.query.date?.toString() || '';
  }
  if (route.query.wellNumber !== undefined) {
    queryParams.value.wellNames = route.query.wellNumber?.toString() || '';
  }

  await queryAreaList();
  await getWellList();
  getList();
});
</script>
<style scoped>
.allowClick {
  color: #1e9fff;
  background-color: #fdfdfd;
  cursor: pointer;
}
</style>
