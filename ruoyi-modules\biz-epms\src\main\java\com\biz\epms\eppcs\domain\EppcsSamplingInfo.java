package com.biz.epms.eppcs.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.comm.attach.annotation.AttachField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 取样信息对象 eppcs_sampling_info
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("eppcs_sampling_info")
public class EppcsSamplingInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 样品id
     */
    @TableId(value = "sampling_id")
    private Long samplingId;

    /**
     * 关联申请ID
     */
    private Long applicationId;

    /**
     * 样品编号
     */
    private String sampleNumber;

    /**
     * 取样时间
     */
    private Date samplingTime;

    /**
     * 取样类型
     */
    private String samplingType;

    /**
     * 取样量(ml)
     */
    private BigDecimal samplingAmount;

    /**
     * 取样人
     */
    private String sampler;

    /**
     * 取样过程照片附件
     */
    @AttachField
    private String processPhoto;

    /**
     * 封条照片附件
     */
    @AttachField
    private String sealPhoto;

    /**
     * 取样状态 (0待取样 1 驳回 2待属地现场负责人确认 3待属地领导确认 4 取样完成)
     */
    private Integer samplingStatus;


    /**
     * 审批时间
     */
    private Date approvalTime;

    /**
     * 审批人
     */
    private String approver;

    /**
     * 审批意见
     */
    private String approvalRemark;

    /**
     * 备注
     */
    private String remark;




}
