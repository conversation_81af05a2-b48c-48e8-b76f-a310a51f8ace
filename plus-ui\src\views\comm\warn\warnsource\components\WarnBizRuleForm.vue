<template>
  <div>
    <el-divider><span style="font-size: 15px">业务告警规则</span></el-divider>
    <el-form ref="form" :model="formData" label-width="auto">
      <el-row>
        <el-col :span="24">
          <el-form-item label="获取数据SQL">
            <ace-input ref="aceInput" v-model="formData.sourceDataSql" lang="sql" placeholder="请输入获取数据SQL语句" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="告警规则">
            <ace-input ref="aceInput" v-model="formData.warnRule" :format="true" lang="groovy" placeholder="请输入告警规则" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="设备名称字段">
            <el-input v-model="formData.deviceNameField" placeholder="请输入设备名称字段名" type="textarea" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备ID字段">
            <el-input v-model="formData.deviceIdField" placeholder="请输入设备ID字段名" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import '@/components/AceEditor/aceConfig.js';
import AceInput from './AceInupt.vue';

export default {
  name: 'WarnBizRuleForm',
  components: { AceInput },
  props: {
    modelValue: String
  },
  data() {
    return {
      opt: {
        useWorker: true, // 启用语法检查,必须为true
        enableBasicAutocompletion: true, // 自动补全
        enableLiveAutocompletion: true, // 智能补全
        enableSnippets: true, // 启用代码段
        showPrintMargin: false, // 去掉灰色的线，printMarginColumn
        highlightActiveLine: true, // 高亮行
        highlightSelectedWord: true, // 高亮选中的字符
        tabSize: 4, // tab锁进字符
        fontSize: 14, // 设置字号
        wrap: false, // 是否换行
        readonly: false // 是否可编辑
        // minLines: 10, // 最小行数，minLines和maxLines同时设置之后，可以不用给editor再设置高度
        // maxLines: 50, // 最大行数
      },
      formData: {
        sourceDataSql: '',
        warnRule: '',
        deviceNameField: '',
        deviceIdField: ''
      }
    };
  },
  watch: {
    // 当表单数据变化时通知父组件更新
    formData: {
      deep: true,
      handler(newVal) {
        const rule = JSON.stringify(newVal);
        if (rule !== this.modelValue) {
          this.$emit('update:modelValue', rule);
        }
      }
    },
    modelValue: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal != null) {
          const data = JSON.parse(newVal);
          this.formData = { ...data };
        }
      }
    }
  },
  methods: {}
};
</script>

<style scoped></style>
