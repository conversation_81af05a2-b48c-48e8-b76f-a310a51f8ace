import{d as De,h as Be,ak as ye,r,ai as R,b as Qe,aH as je,c as J,o as f,p as l,t,w as c,q as I,a7 as Oe,M as qe,e as o,A as Je,a8 as _e,B as Ke,F as be,C as we,x as w,D as We,K as he,J as p,am as K,aI as Ge,ay as He,ax as Xe,z as C,aJ as Ze,aw as el,aL as ll,v as al,az as tl,G as ol,aA as nl,Q as il}from"./index-D07cMzhp.js";import{E as sl}from"./el-row-CikYE3zA.js";import{_ as dl}from"./index-BWMgqvQ9.js";import{E as ul}from"./el-col-BaG5Rg5z.js";import{E as rl}from"./el-date-picker-HyhB9X9n.js";import{l as Ae,g as pl,u as ml,a as gl,d as vl,r as fl}from"./index-D1htDV9m.js";import{l as cl}from"./index-BhIIZXqy.js";import{l as yl}from"./index-DmNU79vT.js";import{b as _l}from"./index-DAD2mDUi.js";import"./el-tree-DW6MoFaI.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";const bl={class:"p-2"},wl={class:"mb-[10px]"},Al={class:"dialog-footer"},Dl={class:"dialog-footer"},Vl=De({name:"MudDaily"}),Cl=De({...Vl,setup(Sl){const{proxy:u}=Be(),{epnj_handling_type:Ve,epfy_medium_category:Ce,epnj_transport_hunhe_medium_type:Se}=ye(u==null?void 0:u.useDict("epnj_handling_type","epfy_medium_category","epnj_transport_hunhe_medium_type")),W=r([]),S=r(!1),A=r(!0),E=r(!0),F=r([]),h=r(!0),G=r(!0),Y=r(0),P=r(["",""]),$=r(!1),H=r([]),X=r({}),T=r([]),Z=r(),x=r(),U=r([]),_=R({visible:!1,title:""}),k=R({visible:!1,title:""}),z=R({visible:!1,title:""}),m=r({pageNum:1,pageSize:10}),B=r(0),ee=r([]),L=r({date:""}),le={id:void 0,date:void 0,disposeType:1,disposeId:void 0,slurryDisposeAmount:void 0,mudPullingAmount:void 0,mudStagingAmount:void 0,waterPullingAmount:void 0,waterStagingAmount:void 0,loss:void 0},Pe=R({form:{...le},queryParams:{pageNum:1,pageSize:10,disposeType:1,disposeId:void 0,slurryDisposeAmount:void 0,mudPullingAmount:void 0,mudStagingAmount:void 0,waterPullingAmount:void 0,waterStagingAmount:void 0,loss:void 0,params:{date:void 0}},rules:{id:[{required:!0,message:"主键ID不能为空",trigger:"blur"}]}}),{queryParams:g,form:i,rules:ke}=ye(Pe),Ie=async()=>{const n=await yl();H.value=n.rows,H.value.forEach(e=>{e.id=e.prepId,e.label=e.wellName,U.value.push(e)})},Ee=async()=>{X.value.operationAreaType=2;const n=await cl(X.value);T.value=n.rows,T.value.forEach(e=>{e.id=e.operationAreaId,e.label=e.operationAreaName,U.value.push(e)})},ae=async(n,e)=>{if(n.date&&n.disposeType&&n.disposeId){let y={disposeType:n.disposeType,disposeId:n.disposeId,date:n.date};const d=await Ae(y);d.total>0?(i.value.id=d.rows[0].id,i.value.mudPullingAmount=d.rows[0].mudPullingAmount,i.value.mudStagingAmount=d.rows[0].mudStagingAmount,i.value.slurryDisposeAmount=d.rows[0].slurryDisposeAmount,i.value.waterPullingAmount=d.rows[0].waterPullingAmount,i.value.waterStagingAmount=d.rows[0].waterStagingAmount,i.value.loss=d.rows[0].loss):(i.value.id=void 0,i.value.mudPullingAmount=void 0,i.value.mudStagingAmount=void 0,i.value.slurryDisposeAmount=void 0,i.value.waterPullingAmount=void 0,i.value.waterStagingAmount=void 0,i.value.loss=void 0)}},te=n=>U.value.find(e=>e.id==n)?U.value.find(e=>e.id==n).label:"未知",b=async()=>{A.value=!0,g.value.params={},u==null||u.addDateRange(g.value,P.value,"Date");const n=await Ae(g.value);W.value=n.rows,Y.value=n.total,A.value=!1},oe=()=>{ne(),_.visible=!1,k.visible=!1},ne=()=>{var n;i.value={...le},(n=x.value)==null||n.resetFields()},ie=()=>{g.value.pageNum=1,b()},Te=()=>{var n;P.value=["",""],(n=Z.value)==null||n.resetFields(),ie()},Ue=n=>{F.value=n.map(e=>e.id),h.value=n.length!=1,G.value=!n.length},se=async n=>{ne(),$.value=!0;const e=(n==null?void 0:n.id)||F.value[0],y=await pl(e);Object.assign(i.value,y.data),_.visible=!0,_.title="修改泥浆日报"},Le=()=>{var n;(n=x.value)==null||n.validate(async e=>{e&&(S.value=!0,i.value.id?await ml(i.value).finally(()=>S.value=!1):await gl(i.value).finally(()=>S.value=!1),u==null||u.$modal.msgSuccess("操作成功"),_.visible=!1,await b())})},de=async n=>{const e=(n==null?void 0:n.id)||F.value;await(u==null?void 0:u.$modal.confirm('是否确认删除泥浆日报编号为"'+e+'"的数据项？').finally(()=>A.value=!1)),await vl(e),u==null||u.$modal.msgSuccess("删除成功"),await b()},Ne=()=>{u==null||u.download("epms/mudDaily/export",{...g.value},`mudDaily_${new Date().getTime()}.xlsx`)},Me=()=>{k.visible=!0},Re=async()=>{k.visible=!1,A.value=!0,await fl(L.value),await b(),A.value=!1},Fe=(n,e,y,d)=>{m.value.unloadLocation=void 0,m.value.relationId=void 0,e.property==="mudPullingAmount"?(m.value.mediumCategory=1,m.value.relationId=n.disposeId,m.value.date=n.date,N()):e.property==="waterPullingAmount"?(m.value.mediumCategory=2,m.value.relationId=n.disposeId,m.value.date=n.date,N()):e.property==="slurryDisposeAmount"&&(m.value.mediumCategory=3,m.value.unloadLocation=te(n.disposeId),m.value.relationId=void 0,m.value.date=n.date,N())},N=()=>{_l(m.value).then(n=>{ee.value=n.rows,B.value=n.total,z.visible=!0})};return Qe(()=>{Ie(),Ee(),b()}),(n,e)=>{var fe,ce;const y=rl,d=Je,ue=We,re=Ke,v=he,Q=qe,pe=Oe,M=ul,Ye=dl,$e=sl,s=Xe,j=Ze,O=el,me=ll,ge=He,ve=tl,D=ol,q=nl,V=je("hasPermi"),xe=al;return f(),J("div",bl,[l(Ge,{"enter-active-class":(fe=o(u))==null?void 0:fe.animate.searchAnimate.enter,"leave-active-class":(ce=o(u))==null?void 0:ce.animate.searchAnimate.leave},{default:t(()=>[c(I("div",wl,[l(pe,{shadow:"hover"},{default:t(()=>[l(Q,{ref_key:"queryFormRef",ref:Z,model:o(g),inline:!0},{default:t(()=>[l(d,{label:"日期",style:{width:"308px"}},{default:t(()=>[l(y,{modelValue:o(P),"onUpdate:modelValue":e[0]||(e[0]=a=>_e(P)?P.value=a:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]},null,8,["modelValue","default-time"])]),_:1}),l(d,{label:"集中站名称","label-width":"90",prop:"disposeId"},{default:t(()=>[l(re,{modelValue:o(g).disposeId,"onUpdate:modelValue":e[1]||(e[1]=a=>o(g).disposeId=a),clearable:"",placeholder:"请选择集中站名称"},{default:t(()=>[(f(!0),J(be,null,we(o(T),a=>(f(),w(ue,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(d,null,{default:t(()=>[l(v,{type:"primary",icon:"Search",onClick:ie},{default:t(()=>e[23]||(e[23]=[p("搜索")])),_:1}),l(v,{icon:"Refresh",onClick:Te},{default:t(()=>e[24]||(e[24]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[K,o(E)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(pe,{shadow:"never"},{header:t(()=>[l($e,{gutter:10,class:"mb8"},{default:t(()=>[l(M,{span:1.5},{default:t(()=>[c((f(),w(v,{disabled:o(h),icon:"Edit",plain:"",type:"success",onClick:e[2]||(e[2]=a=>se())},{default:t(()=>e[25]||(e[25]=[p("修改 ")])),_:1},8,["disabled"])),[[V,["epms:mudDaily:edit"]]])]),_:1}),l(M,{span:1.5},{default:t(()=>[c((f(),w(v,{disabled:o(G),icon:"Delete",plain:"",type:"danger",onClick:e[3]||(e[3]=a=>de())},{default:t(()=>e[26]||(e[26]=[p("删除 ")])),_:1},8,["disabled"])),[[V,["epms:mudDaily:remove"]]])]),_:1}),l(M,{span:1.5},{default:t(()=>[c((f(),w(v,{icon:"Download",plain:"",type:"warning",onClick:Ne},{default:t(()=>e[27]||(e[27]=[p(" 导出 ")])),_:1})),[[V,["epms:mudDaily:export"]]])]),_:1}),l(M,{span:1.5},{default:t(()=>[c((f(),w(v,{icon:"RefreshRight",plain:"",type:"primary",onClick:Me},{default:t(()=>e[28]||(e[28]=[p("重新计算 ")])),_:1})),[[V,["epms:mudDaily:reCompute"]]])]),_:1}),l(Ye,{showSearch:o(E),"onUpdate:showSearch":e[4]||(e[4]=a=>_e(E)?E.value=a:null),onQueryTable:b},null,8,["showSearch"])]),_:1})]),default:t(()=>[c((f(),w(ge,{data:o(W),stripe:"",onCellClick:Fe,onSelectionChange:Ue},{default:t(()=>[l(s,{align:"center",type:"selection",width:"55"}),l(s,{label:"日期",align:"center",prop:"date",width:"180"},{default:t(a=>[I("span",null,C(n.parseTime(a.row.date,"{y}-{m}-{d}")),1)]),_:1}),l(s,{label:"处理类型",align:"center",prop:"disposeType"},{default:t(a=>[l(j,{options:o(Ve),value:a.row.disposeType},null,8,["options","value"])]),_:1}),l(s,{label:"处理地点",align:"center",prop:"disposeId"},{default:t(a=>[p(C(te(a.row.disposeId)),1)]),_:1}),l(s,{label:"泥浆拉运量（方）",align:"center",prop:"slurryDisposeAmount"},{default:t(a=>[l(O,{type:"success",class:"allowClick"},{default:t(()=>[p(C(a.row.slurryDisposeAmount),1)]),_:2},1024)]),_:1}),l(s,{align:"center",label:"泥浆暂存量（方）",prop:"slurryStagingAmount"}),l(s,{label:"泥饼拉运量（方）",align:"center",prop:"mudPullingAmount"},{default:t(a=>[l(O,{type:"success",class:"allowClick"},{default:t(()=>[p(C(a.row.mudPullingAmount),1)]),_:2},1024)]),_:1}),l(s,{align:"center",label:"泥饼暂存量（方）",prop:"mudStagingAmount"}),l(s,{label:"滤水拉运量（方）",align:"center",prop:"waterPullingAmount"},{default:t(a=>[l(O,{type:"success",class:"allowClick"},{default:t(()=>[p(C(a.row.waterPullingAmount),1)]),_:2},1024)]),_:1}),l(s,{align:"center",label:"滤水暂存量（方）",prop:"waterStagingAmount"}),l(s,{align:"center",label:"总拉运量（方）",prop:"totalPullingAmount"}),l(s,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作"},{default:t(a=>[l(me,{content:"修改",placement:"top"},{default:t(()=>[c(l(v,{icon:"Edit",link:"",type:"primary",onClick:ze=>se(a.row)},null,8,["onClick"]),[[V,["epms:mudDaily:edit"]]])]),_:2},1024),l(me,{content:"删除",placement:"top"},{default:t(()=>[c(l(v,{icon:"Delete",link:"",type:"primary",onClick:ze=>de(a.row)},null,8,["onClick"]),[[V,["epms:mudDaily:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[xe,o(A)]]),c(l(ve,{limit:o(g).pageSize,"onUpdate:limit":e[5]||(e[5]=a=>o(g).pageSize=a),page:o(g).pageNum,"onUpdate:page":e[6]||(e[6]=a=>o(g).pageNum=a),total:o(Y),onPagination:b},null,8,["limit","page","total"]),[[K,o(Y)>0]])]),_:1}),l(q,{title:o(_).title,modelValue:o(_).visible,"onUpdate:modelValue":e[17]||(e[17]=a=>o(_).visible=a),width:"500px","append-to-body":""},{footer:t(()=>[I("div",Al,[l(v,{loading:o(S),type:"primary",onClick:Le},{default:t(()=>e[35]||(e[35]=[p("确 定")])),_:1},8,["loading"]),l(v,{onClick:oe},{default:t(()=>e[36]||(e[36]=[p("取 消")])),_:1})])]),default:t(()=>[l(Q,{ref_key:"mudDailyFormRef",ref:x,model:o(i),rules:o(ke),"label-width":"100px"},{default:t(()=>[l(d,{label:"日期",prop:"date"},{default:t(()=>[l(y,{modelValue:o(i).date,"onUpdate:modelValue":e[7]||(e[7]=a=>o(i).date=a),disabled:o($),clearable:"",placeholder:"请选择日期",type:"date","value-format":"YYYY-MM-DD",onChange:e[8]||(e[8]=a=>ae(o(i)))},null,8,["modelValue","disabled"])]),_:1}),l(d,{label:"集中站名称",prop:"disposeId"},{default:t(()=>[l(re,{modelValue:o(i).disposeId,"onUpdate:modelValue":e[9]||(e[9]=a=>o(i).disposeId=a),disabled:o($),clearable:"",placeholder:"请选择集中站名称",onChange:e[10]||(e[10]=a=>ae(o(i)))},{default:t(()=>[(f(!0),J(be,null,we(o(T),a=>(f(),w(ue,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),l(d,{label:"泥浆处理量",prop:"slurryDisposeAmount"},{default:t(()=>[l(D,{modelValue:o(i).slurryDisposeAmount,"onUpdate:modelValue":e[11]||(e[11]=a=>o(i).slurryDisposeAmount=a),placeholder:"请输入泥浆处理量"},{append:t(()=>e[29]||(e[29]=[p("方")])),_:1},8,["modelValue"])]),_:1}),l(d,{label:"泥浆暂存量",prop:"slurryStagingAmount"},{default:t(()=>[l(D,{modelValue:o(i).slurryStagingAmount,"onUpdate:modelValue":e[12]||(e[12]=a=>o(i).slurryStagingAmount=a),placeholder:"请输入泥浆暂存量"},{append:t(()=>e[30]||(e[30]=[p("方")])),_:1},8,["modelValue"])]),_:1}),l(d,{label:"泥饼拉运量",prop:"mudPullingAmount"},{default:t(()=>[l(D,{modelValue:o(i).mudPullingAmount,"onUpdate:modelValue":e[13]||(e[13]=a=>o(i).mudPullingAmount=a),placeholder:"请输入泥饼拉运量",disabled:""},{append:t(()=>e[31]||(e[31]=[p("方")])),_:1},8,["modelValue"])]),_:1}),l(d,{label:"泥饼暂存量",prop:"mudStagingAmount"},{default:t(()=>[l(D,{modelValue:o(i).mudStagingAmount,"onUpdate:modelValue":e[14]||(e[14]=a=>o(i).mudStagingAmount=a),placeholder:"请输入泥饼暂存量"},{append:t(()=>e[32]||(e[32]=[p("方")])),_:1},8,["modelValue"])]),_:1}),l(d,{label:"滤水拉运量",prop:"waterPullingAmount"},{default:t(()=>[l(D,{modelValue:o(i).waterPullingAmount,"onUpdate:modelValue":e[15]||(e[15]=a=>o(i).waterPullingAmount=a),placeholder:"请输入滤水拉运量",disabled:""},{append:t(()=>e[33]||(e[33]=[p("方")])),_:1},8,["modelValue"])]),_:1}),l(d,{label:"滤水暂存量",prop:"waterStagingAmount"},{default:t(()=>[l(D,{modelValue:o(i).waterStagingAmount,"onUpdate:modelValue":e[16]||(e[16]=a=>o(i).waterStagingAmount=a),placeholder:"请输入滤水暂存量"},{append:t(()=>e[34]||(e[34]=[p("方")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(q,{title:"数据统计",modelValue:o(z).visible,"onUpdate:modelValue":e[20]||(e[20]=a=>o(z).visible=a),width:"1200px","append-to-body":""},{default:t(()=>[l(ge,{stripe:"",data:o(ee)},{default:t(()=>[l(s,{align:"center",type:"selection",width:"55"}),l(s,{label:"拉运时间",align:"center",prop:"transportTime",width:"140"},{default:t(a=>[I("span",null,C(n.parseTime(a.row.transportTime,"{y}-{m}-{d}")),1)]),_:1}),l(s,{label:"分类",align:"center",prop:"mediumCategory"},{default:t(a=>[l(j,{options:o(Ce),value:a.row.mediumCategory},null,8,["options","value"])]),_:1}),l(s,{label:"介质类型",align:"center",prop:"mediumType"},{default:t(a=>[l(j,{options:o(Se),value:a.row.mediumType},null,8,["options","value"])]),_:1}),l(s,{align:"center",label:"起运点",prop:"departurePoint"}),l(s,{align:"center",label:"数量(方)",prop:"number"}),l(s,{align:"center",label:"拉运人",prop:"transporter"}),l(s,{align:"center",label:"车牌号",prop:"licensePlate"}),l(s,{align:"center",label:"接收点",prop:"arrivalPoint"}),l(s,{align:"center",label:"接收人",prop:"receiver"}),l(s,{align:"center",label:"备注信息",prop:"remark"})]),_:1},8,["data"]),c(l(ve,{limit:o(m).pageSize,"onUpdate:limit":e[18]||(e[18]=a=>o(m).pageSize=a),page:o(m).pageNum,"onUpdate:page":e[19]||(e[19]=a=>o(m).pageNum=a),total:o(B),onPagination:N},null,8,["limit","page","total"]),[[K,o(B)>0]])]),_:1},8,["modelValue"]),l(q,{modelValue:o(k).visible,"onUpdate:modelValue":e[22]||(e[22]=a=>o(k).visible=a),width:"400px","append-to-body":""},{footer:t(()=>[I("div",Dl,[l(v,{loading:o(S),type:"primary",onClick:Re},{default:t(()=>e[37]||(e[37]=[p("计 算")])),_:1},8,["loading"]),l(v,{onClick:oe},{default:t(()=>e[38]||(e[38]=[p("取 消")])),_:1})])]),default:t(()=>[l(Q,{model:o(L)},{default:t(()=>[l(d,{label:"选择日期进行计算",prop:"date"},{default:t(()=>[l(y,{modelValue:o(L).date,"onUpdate:modelValue":e[21]||(e[21]=a=>o(L).date=a),clearable:"",placeholder:"请选择日期",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),$l=il(Cl,[["__scopeId","data-v-130857e1"]]);export{$l as default};
