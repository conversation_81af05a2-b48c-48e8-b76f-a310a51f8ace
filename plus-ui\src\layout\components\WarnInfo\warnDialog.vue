<template>
  <div class="warnDialog">
    <el-dialog :title="warnDialog.title" v-model="warnDialog.visible" width="1000px" append-to-body>
      <el-table v-loading="loading" :data="warnList" :rowStyle="rowStyle" border stripe style="width: 100%">
        <el-table-column :index="indexMethod" align="center" label="序号" type="index" width="100px" />
        <el-table-column align="center" label="告警名称" prop="warnName">
          <template #default="scope">
            <el-tooltip :content="scope.row.warnName" placement="top">
              <span class="descStyle" @click="toWarnPage(scope.row)">{{ scope.row.warnName }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" label="异常发生时间" prop="occurTime" width="200px">
          <template #default="scope">
            <div>{{ scope.row.occurTime }}</div>
          </template>
        </el-table-column>
        <el-table-column label="异常级别" align="center" prop="warnLevel" width="200px">
          <template #default="scope">
            <dict-tag :options="warn_level" :value="scope.row.warnLevel" />
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        background
        class="mt-4 page"
        layout="total, sizes, prev, pager, next"
        size="small"
        @size-change="getList"
        @current-change="getList"
      />
      <template #footer>
        <el-button type="primary" @click="handleClose">我已知晓，本次不再弹出</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { listWarnRecord } from '@/api/comm/warn/warnRecord/warnRecord';
import { getRoutePathWarnConfig, WarnSystemTypeConfig } from '@/layout/components/WarnInfo/warnSystemType';
import router from '@/router';
import { ref } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { warn_level } = toRefs<any>(proxy?.useDict('warn_level'));

const route = useRoute();
const warnDialog = ref({
  title: '',
  visible: false
});
const loading = ref(false);
const total = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  systemType: -1,
  warnStatus: 1
});
const map = reactive([true, true, true, true, true]);
const flag = ref(0);
const warnConfig = ref<WarnSystemTypeConfig>(null);
const warnList = ref([]);
const handleClose = () => {
  map[flag.value] = false;
  warnDialog.value.visible = false;
};
const getList = async () => {
  loading.value = true;
  const response = await listWarnRecord(queryParams.value);
  warnList.value = response.rows;
  total.value = response.total;
  if (total.value > 0 && map[flag.value]) {
    warnDialog.value.visible = true;
  }
  loading.value = false;
};
const toWarnPage = (row: any) => {
  if (row.jumpUrl) {
    router.push({
      path: row.jumpUrl
    });
  } else {
    warnConfig.value.toWarnRecordPage();
  }
  handleClose();
};

const rowStyle = (row, index) => {
  return {
    cursor: 'pointer'
  };
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};

watch(
  () => route.path,
  (path) => {
    warnConfig.value = getRoutePathWarnConfig(path);
    if (warnConfig.value) {
      flag.value = warnConfig.value.systemType; // 匹配到取水
      queryParams.value.systemType = warnConfig.value.systemType;
      warnDialog.value.title = warnConfig.value.title;
      getList();
    }
  },
  { immediate: true, deep: true }
);
</script>

<style scoped lang="scss">
.warnDialog {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 1000px;
}
.descStyle {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.2;
}
</style>
