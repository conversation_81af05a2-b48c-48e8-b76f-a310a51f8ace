<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="拉运时间" prop="monthQuery">
              <el-date-picker v-model="queryParams.monthQuery" clearable placeholder="请选择拉运时间" type="date" value-format="YYYY-MM-DD" />
            </el-form-item>
            <!--  <el-form-item label="分类" prop="mediumCategory">
              <el-select v-model="queryParams.mediumCategory" placeholder="请选择分类" clearable disabled>
                <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>-->
            <el-form-item label="介质类型" prop="mediumType">
              <el-select v-model="queryParams.mediumType" placeholder="请选择介质类型" clearable>
                <el-option v-for="dict in epfy_yexiang_medium_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="起运点" prop="departurePoint">
              <el-input v-model="queryParams.departurePoint" clearable placeholder="请输入起运点" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="车牌号" prop="licensePlate">
              <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌号" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="接收点" prop="arrivalPoint">
              <el-input v-model="queryParams.arrivalPoint" clearable placeholder="请输入接收点" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卸液点负责人" prop="receiver">
              <el-input v-model="queryParams.receiver" clearable placeholder="请输入卸液点负责人" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="拉运申请名称" prop="appName">
              <el-input v-model="queryParams.appName" clearable placeholder="请输入拉运申请名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['epfy:transportRecord:add']" icon="Plus" plain type="primary" @click="handleAdd"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epfy:transportRecord:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epfy:transportRecord:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epfy:transportRecord:export']" icon="Download" plain type="warning" @click="handleExport">导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="transportRecordList" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column
          :index="(index) => (queryParams.pageNum - 1) * queryParams.pageSize + index + 1"
          align="center"
          label="序号"
          type="index"
          width="50"
        />
        <el-table-column align="center" label="拉运申请" prop="applicationId" width="300">
          <template #default="scope">
            {{ toApplicationName[scope.row.applicationId] || '未知' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="装液地点" prop="departurePoint" width="110" />
        <el-table-column align="center" label="分类" prop="mediumCategory" width="60">
          <template #default="scope">
            <dict-tag :options="epfy_medium_category" :value="scope.row.mediumCategory" />
          </template>
        </el-table-column>
        <el-table-column label="介质类型" align="center" width="120" prop="mediumType">
          <template #default="scope">
            <dict-tag :options="epfy_yexiang_medium_type" :value="scope.row.mediumType" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="装液量(方)" prop="number" />
        <el-table-column align="center" label="开始装液时间" prop="transportTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.transportTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="结束装液时间" prop="transportEndTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.transportEndTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="司机" prop="transporter" width="80" />
        <el-table-column align="center" label="车牌号" prop="licensePlate" width="100" />
        <el-table-column label="施工进度" align="center" prop="progress">
          <template #default="scope">
            <dict-tag :options="epfy_liquid_progress" :value="scope.row.progress" />
          </template>
        </el-table-column>
        <el-table-column label="审核信息" align="center" min-width="230px">
          <template #default="scope">
            现场监督:
            <span v-if="[2].includes(scope.row.superintendentApproval)">
              {{ scope.row.superintendent }}
              {{ parseTime(scope.row.superintendentApprovalTime, '{y}-{m}-{d}') }}
            </span>
            <span v-else style="display: inline-flex"> <dict-tag :options="epfy_application_status" :value="scope.row.senderApproval" /> </span><br />
            施工负责人:
            <span v-if="[2].includes(scope.row.senderApproval)">
              {{ scope.row.sender }}
              {{ parseTime(scope.row.senderApprovalTime, '{y}-{m}-{d}') }}
            </span>
            <span v-else style="display: inline-flex; margin-top: 5px">
              <dict-tag :options="epfy_application_status" :value="scope.row.senderApproval" />
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="备注信息" prop="remark" width="100" />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="附件预览" width="220">
          <template #default="scope">
            <el-tooltip content="装液图片" placement="top">
              <el-button
                v-hasPermi="['epfy:transportRecord:preview']"
                icon="Document"
                link
                type="primary"
                @click="handlePreview(scope.row, 'photo', scope.row.photo)"
                >装液图片
              </el-button>
            </el-tooltip>
            <el-tooltip content="量尺图片" placement="top">
              <el-button
                v-hasPermi="['epfy:transportRecord:preview']"
                icon="Document"
                link
                type="primary"
                @click="handlePreview(scope.row, 'measuringPhoto', scope.row.measuringPhoto)"
                >量尺图片
              </el-button>
            </el-tooltip>
            <el-tooltip content="罐车方量表" placement="top">
              <el-button
                v-hasPermi="['epfy:transportRecord:preview']"
                icon="Document"
                link
                type="primary"
                @click="handlePreview(scope.row, 'tankerSquare', scope.row.tankerSquare)"
                >罐车方量表
              </el-button>
            </el-tooltip>
            <el-tooltip content="铅封" placement="top">
              <el-button
                v-hasPermi="['epfy:transportRecord:preview']"
                icon="Document"
                link
                type="primary"
                @click="handlePreview(scope.row, 'accessories', scope.row.accessories)"
                >铅封
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="160">
          <template #default="scope">
            <el-tooltip v-if="scope.row.superintendent && scope.row.superintendentApproval !== 2" content="现场监督审核" placement="top">
              <el-button v-hasPermi="['epfy:transportRecord:super']" icon="Coordinate" link type="primary" @click="handleCoordinate(scope.row, 1)">
                现场监督审核
              </el-button>
            </el-tooltip>
            <el-tooltip v-if="scope.row.sender && scope.row.senderApproval !== 2" content="施工负责人审核" placement="top">
              <el-button v-hasPermi="['epfy:transportRecord:send']" icon="Coordinate" link type="primary" @click="handleCoordinate(scope.row, 2)">
                施工负责人审核
              </el-button>
            </el-tooltip>
            <el-tooltip v-if="scope.row.superintendentApproval === 2 && scope.row.senderApproval === 2" content="计量凭证下载" placement="top">
              <el-button link type="primary" icon="Download" @click="handleDownload(scope.row)">计量凭证下载</el-button>
            </el-tooltip>
            <el-tooltip content="复制" placement="top">
              <el-button v-hasPermi="['epfy:transportRecord:copy']" icon="Edit" link type="primary" @click="handleCopy(scope.row)">复制 </el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epfy:transportRecord:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)">修改 </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epfy:transportRecord:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改拉运记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="transportRecordFormRef" :model="form" :rules="rules" label-width="130px">
        <el-form-item v-if="isUpdate == false" label="拉运申请" prop="applicationId">
          <el-select v-model="form.applicationId" filterable placeholder="请选择拉运申请" @change="setApplication">
            <el-option v-for="dict in unSportList" :key="dict.appId" :label="dict.appName" :value="dict.appId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-else label="拉运申请" prop="applicationId">
          <el-select v-model="form.applicationId" disabled filterable placeholder="请选择拉运申请">
            <el-option v-for="dict in applicationList" :key="dict.appId" :label="dict.appName" :value="dict.appId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="装液地点" prop="departurePoint">
          <el-input v-model="form.departurePoint" disabled placeholder="请输入起运点" />
        </el-form-item>
        <el-form-item label="分类" prop="mediumCategory">
          <el-select v-model="form.mediumCategory" placeholder="请选择分类" disabled>
            <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="介质类型" prop="mediumType">
          <el-select v-model="form.mediumType" placeholder="请选择介质类型">
            <el-option v-for="dict in epfy_yexiang_medium_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="装液量" prop="number">
          <el-input v-model="form.number" placeholder="请输入装液量">
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <el-form-item label="开始装液时间" prop="transportTime">
          <el-date-picker v-model="form.transportTime" clearable placeholder="请选择拉运时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束装液时间" prop="transportEndTime">
          <el-date-picker v-model="form.transportEndTime" clearable placeholder="请选择拉运时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="司机" prop="transporter">
          <el-select filterable v-model="form.transporter" placeholder="选择司机" allow-create default-first-option>
            <el-option v-for="item in epfy_driver" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="车牌号" prop="licensePlate">
          <el-input v-model="form.licensePlate" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="施工进度" prop="progress">
          <el-select v-model="form.progress" placeholder="请选择施工进度">
            <el-option v-for="dict in epfy_liquid_progress" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="现场监督审核人" prop="superintendent">
          <!--          <el-input v-model="form.superintendent" disabled placeholder="请输入监督人"/>-->
          <el-select filterable v-model="form.superintendent" placeholder="选择施工负责人" allow-create default-first-option>
            <el-option v-for="item in epfy_superintendent" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="施工负责人" prop="sender">
          <!--          <el-input v-model="form.sender" placeholder="请输入施工负责人"/>-->
          <el-select filterable v-model="form.sender" placeholder="选择施工负责人" allow-create default-first-option>
            <el-option v-for="item in epfy_sender" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="审核意见" prop="rejectSuggestion">-->
        <!--          <el-input v-model="form.rejectSuggestion" type="textarea" placeholder="请输入内容"/>-->
        <!--        </el-form-item>-->
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注信息" />
        </el-form-item>
        <el-form-item label="装液照片" prop="photo">
          <attachFileUpload
            v-model="form.photo"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="photo"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>
        <el-form-item label="量尺照片" prop="measuringPhoto">
          <attachFileUpload
            v-model="form.measuringPhoto"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="measuringPhoto"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>
        <el-form-item label="罐车方量表" prop="tankerSquare">
          <attachFileUpload
            v-model="form.tankerSquare"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="tankerSquare"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>
        <el-form-item label="铅封" prop="accessories">
          <attachFileUpload
            v-model="form.accessories"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="accessories"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>
        <el-form-item label="计量凭证或转运单" prop="measurementVoucher">
          <attachFileUpload
            v-model="form.measurementVoucher"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="transportMeasurement"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="warning" @click="submitForm(true)">重新提交审核</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm(false)">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审批拉运记录对话框 -->
    <el-dialog :title="cooDialog.title" v-model="cooDialog.visible" width="500px" append-to-body>
      <el-form ref="transportRecordFormRef" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="拉运申请" prop="applicationId">
          <el-select
            v-model="form.applicationId"
            disabled
            filterable
            placeholder="请选择拉运申请"
            @change="handleApplicationChange(form.applicationId)"
          >
            <el-option v-for="dict in applicationList" :key="dict.appId" :label="dict.appName" :value="dict.appId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="装液地点" prop="departurePoint">
          <el-input v-model="form.departurePoint" disabled placeholder="请输入起运点" />
        </el-form-item>
        <el-form-item label="分类" prop="mediumCategory">
          <el-select v-model="form.mediumCategory" disabled placeholder="请选择分类">
            <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="介质类型" prop="mediumType">
          <el-select v-model="form.mediumType" disabled placeholder="请选择介质类型">
            <el-option v-for="dict in epfy_yexiang_medium_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="装液量" prop="number">
          <el-input v-model="form.number" disabled placeholder="请输入装液量">
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <el-form-item label="开始装液时间" prop="transportTime">
          <el-date-picker
            v-model="form.transportTime"
            clearable
            disabled
            placeholder="请选择拉运时间"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束装液时间" prop="transportEndTime">
          <el-date-picker
            v-model="form.transportEndTime"
            clearable
            disabled
            placeholder="请选择拉运时间"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="司机" prop="transporter">
          <el-input v-model="form.transporter" disabled placeholder="请输入司机" />
        </el-form-item>
        <el-form-item label="车牌号" prop="licensePlate">
          <el-input v-model="form.licensePlate" disabled placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="施工进度" prop="progress">
          <el-select v-model="form.progress" disabled placeholder="请选择施工进度">
            <el-option v-for="dict in epfy_liquid_progress" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="现场监督审核人" prop="superintendent">
          <!--          <el-input v-model="form.superintendent" placeholder="请输入监督人" disabled/>-->
          <el-select filterable v-model="form.superintendent" disabled placeholder="选择现场监督审核人" allow-create default-first-option>
            <el-option v-for="item in epfy_superintendent" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="现场监督审核状态" prop="superintendentApproval">
          <el-radio-group v-model="form.superintendentApproval" :disabled="approveType != 1">
            <el-radio v-for="dict in epfy_application_status" :key="dict.value" :value="parseInt(dict.value)">{{ dict.label }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="现场监督审核时间" prop="superintendentApprovalTime">
          <el-date-picker
            v-model="form.superintendentApprovalTime"
            clearable
            disabled
            placeholder=""
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="施工负责人" prop="sender">
          <!--          <el-input v-model="form.sender" disabled placeholder="请输入施工负责人"/>-->
          <el-select filterable v-model="form.sender" disabled placeholder="选择施工负责人" allow-create default-first-option>
            <el-option v-for="item in epfy_sender" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="施工负责人审核状态" prop="senderApproval">
          <el-radio-group v-model="form.senderApproval" :disabled="approveType != 2">
            <el-radio v-for="dict in epfy_application_status" :key="dict.value" :value="parseInt(dict.value)">{{ dict.label }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="施工负责人审核时间" prop="senderApprovalTime">
          <el-date-picker v-model="form.senderApprovalTime" clearable disabled placeholder="" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <!--        <el-form-item label="审核意见" prop="rejectSuggestion">-->
        <!--          <el-input v-model="form.rejectSuggestion" type="textarea" placeholder="请输入内容"/>-->
        <!--        </el-form-item>-->
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" disabled placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm(false)">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="copyDialog.title" v-model="copyDialog.visible" width="500px" append-to-body>
      <el-form ref="copyFormRef" :model="copyForm" label-width="100px">
        <el-form-item label="拉运申请" prop="applicationId">
          <el-select v-model="copyForm.applicationId" filterable placeholder="请选择拉运申请" @change="getApplicationDetail">
            <el-option v-for="dict in applicationList" :key="dict.appId" :label="dict.appName" :value="dict.appId"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmCopy">确 定</el-button>
          <el-button @click="cancelCopy">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TransportRecord" lang="ts">
import {
  addTransportRecord,
  copyTransportRecord,
  delTransportRecord,
  getTransportRecord,
  listTransportRecord,
  updateTransportRecord
} from '@/api/epms/epfy/transportRecord';
import { TransportRecordForm, TransportRecordQuery, TransportRecordVO } from '@/api/epms/epfy/transportRecord/types';
import { listAllTransportApplication } from '@/api/epms/epfy/transportApplication';
import { TransportApplicationQuery, TransportApplicationVO } from '@/api/epms/epfy/transportApplication/types';
import dayjs from 'dayjs';
import { getInfo } from '@/api/login';

const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  epfy_medium_category,
  epfy_yexiang_medium_type,
  epfy_liquid_progress,
  epfy_driver,
  epfy_application_status,
  epfy_sender,
  epfy_superintendent
} = toRefs<any>(
  proxy?.useDict(
    'epfy_medium_category',
    'epfy_yexiang_medium_type',
    'epfy_liquid_progress',
    'epfy_driver',
    'epfy_application_status',
    'epfy_sender',
    'epfy_superintendent'
  )
);
const transportRecordList = ref<TransportRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const tranQuery = reactive<TransportApplicationQuery>({});
const single = ref(true);
const multiple = ref(true);
const copyFormRef = ref<ElFormInstance>();
const total = ref(0);
const userName = ref('');
const applicationList = ref<TransportApplicationVO[]>([]);
const unSportList = ref<TransportApplicationVO[]>([]);
const queryFormRef = ref<ElFormInstance>();
const transportRecordFormRef = ref<ElFormInstance>();
const toApplicationName = ref({});
const approveType = ref<number>(1);
const suggestion = ref(false); //拉运申请审核意见是否显示
const baseURL = import.meta.env.VITE_APP_BASE_API;
const isUpdate = ref(false);
const currentRecord = ref<TransportRecordForm | null>(null);
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const cooDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const copyDialog = reactive<DialogOption>({
  visible: false,
  title: '选择拉运申请'
});

const copyForm = reactive<{
  applicationId: number | undefined;
  departurePoint: string | undefined;
}>({
  applicationId: undefined,
  departurePoint: undefined
});

const initFormData: TransportRecordForm = {
  transportId: undefined,
  applicationId: undefined,
  transportTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  mediumCategory: 2,
  mediumType: undefined,
  departurePoint: undefined,
  number: undefined,
  sender: undefined,
  transporter: undefined,
  licensePlate: undefined,
  arrivalPoint: undefined,
  receiver: undefined,
  remark: undefined,
  measurementVoucher: undefined,
  flowType: 1,
  photo: undefined,
  unloadApproval: 1,
  superintendentApproval: 1,
  senderApproval: 1
};
const data = reactive<PageData<TransportRecordForm, TransportRecordQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    applicationId: undefined,
    transportTime: undefined,
    mediumCategory: 2,
    mediumType: undefined,
    departurePoint: undefined,
    number: undefined,
    sender: undefined,
    transporter: undefined,
    licensePlate: undefined,
    arrivalPoint: undefined,
    receiver: undefined,
    measurementVoucher: undefined,
    photo: undefined,
    flowType: 1,
    unloadLocationType: 1, // 卸车地点 作业区
    appName: undefined,
    params: {}
  },
  rules: {
    applicationId: [{ required: true, message: '拉运申请不能为空', trigger: 'change' }],
    transportTime: [{ required: true, message: '拉运时间不能为空', trigger: 'blur' }],
    mediumCategory: [{ required: true, message: '分类不能为空', trigger: 'change' }],
    mediumType: [{ required: true, message: '介质类型不能为空', trigger: 'change' }],
    departurePoint: [{ required: true, message: '起运点不能为空', trigger: 'blur' }],
    number: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
    sender: [{ required: true, message: '施工单位负责人不能为空', trigger: 'blur' }],
    transporter: [{ required: true, message: '司机不能为空', trigger: 'blur' }],
    licensePlate: [{ required: true, message: '车牌号不能为空', trigger: 'blur' }],
    arrivalPoint: [{ required: true, message: '接收点不能为空', trigger: 'blur' }],
    receiver: [{ required: true, message: '卸液点负责人不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
const getUserInfo = () => {
  getInfo().then((res) => {
    userName.value = res.data.user.nickName;
  });
};
/** 查询拉运记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTransportRecord(queryParams.value);
  getApplicationList();

  transportRecordList.value = res.rows;

  const appIdList = transportRecordList.value.map((item) => item.applicationId);
  const resApp = await listAllTransportApplication({
    appIds: appIdList.join(',')
  });

  toApplicationName.value = resApp.rows.reduce((map, item) => {
    map[item.appId] = `${item.appName}-${item.wellName}-${item.applicationDate}`; // 使用模板字符串拼接
    return map;
  }, {});

  total.value = res.total;
  loading.value = false;
};

const getApproverName = (dict, key) => {
  return dict?.find((item) => {
    return item.value == key;
  })?.label;
};

const getApplicationDetail = () => {
  if (copyForm.applicationId) {
    const application = applicationList.value.find((item) => item.appId === copyForm.applicationId);
    if (application) {
      copyForm.departurePoint = application.loadingLocation;
    }
  }
};

const confirmCopy = async () => {
  // 验证表单
  copyFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const row = currentRecord.value;
      if (!row) {
        proxy?.$modal.msgError('复制失败：未找到原始记录');
        return;
      }
      row.applicationId = copyForm.applicationId;
      row.departurePoint = copyForm.departurePoint;
      try {
        // 调用复制API
        const res = await copyTransportRecord(row);
        copyDialog.visible = false;
        proxy?.$modal.msgSuccess('复制成功，请在最新记录中编辑');
        // 刷新列表
        await getList();
      } catch (error) {
        proxy?.$modal.msgError('复制失败');
      }
    }
  });
};

const cancelCopy = () => {
  copyDialog.visible = false;
};
/**
 * 获取废液申请列表
 */
const getApplicationList = () => {
  tranQuery.flowType = 1;
  tranQuery.unloadLocationType = 1; // 作业区收
  tranQuery.mediumCategory = 2;
  tranQuery.applicationStatus = 2; // 归口审批通过
  applicationList.value = [];
  listAllTransportApplication(tranQuery).then((res) => {
    res.rows.forEach((item) => {
      item.appName = `${item.appName}-${item.wellName}-${item.applicationDate}`; // 使用模板字符串拼接
    });
    applicationList.value = res.rows;
  });
  let tranQuery2 = { ...tranQuery };
  tranQuery2.transportStatus = 0;
  listAllTransportApplication(tranQuery2).then((res) => {
    res.rows.forEach((item) => {
      item.appName = `${item.appName}-${item.wellName}-${item.applicationDate}`; // 使用模板字符串拼接
    });
    unSportList.value = res.rows;
  });
};
/** 取消按钮 */
const cancel = () => {
  reset();
  cooDialog.visible = false;
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  transportRecordFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};
/** 同步介质类型 */
const handleApplicationChange = (value: any) => {
  applicationList.value.forEach((item) => {
    if (item.appId === value) {
      form.value.mediumType = item.mediumType ? item.mediumType : undefined;
    }
  });
};
/** 多选框选中数据 */
const handleSelectionChange = (selection: TransportRecordVO[]) => {
  ids.value = selection.map((item) => item.transportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  isUpdate.value = false;
  form.value.superintendent = userName.value;
  dialog.visible = true;
  dialog.title = '添加装液记录';
};

// todo 修改参数
const handleDownload = async (row?: TransportRecordVO) => {
  proxy?.download(
    'epfy/transportApplication/downloadMeasurementVoucher',
    {
      appId: row?.applicationId
    },
    '二连分公司废液收集计量凭证.xls'
  );
};
/** 下载按钮操作 */

/** 修改按钮操作 */
const handleUpdate = async (row?: TransportRecordVO) => {
  reset();
  isUpdate.value = true;
  const _transportId = row?.transportId || ids.value[0];
  const res = await getTransportRecord(_transportId);
  Object.assign(form.value, res.data);
  form.value.superintendent = userName.value;
  dialog.visible = true;
  dialog.title = '修改装液记录';
};

/** 复制按钮操作 */
const handleCopy = async (row?: TransportRecordForm) => {
  // 打开选择拉运申请对话框
  currentRecord.value = row;
  copyDialog.visible = true;
  // 清空表单
  copyForm.applicationId = undefined;
  copyForm.departurePoint = undefined;
};

/** 审核按钮操作 */
const handleCoordinate = async (row?: TransportRecordVO, type) => {
  reset();
  const _transportId = row?.transportId || ids.value[0];
  const res = await getTransportRecord(_transportId);
  Object.assign(form.value, res.data);
  // await changeSuoShuDi(res.data.workAreaId);
  approveType.value = type;
  suggestion.value = true;
  cooDialog.visible = true;
  cooDialog.title = '审核装液记录';
};

const handlePreview = async (row?: TransportRecordVO, type?: string, ids?: string) => {
  proxy.showAttachPreview({
    attachSourceId: ids ? null : row?.transportId,
    attachSourceType: 'yxTransportRecord',
    attachCategory: ids ? null : type,
    ids: ids
  });
};

/** 提交按钮 */
const submitForm = (flag?: boolean) => {
  if (flag) {
    form.value.senderApproval = 1;
    form.value.superintendentApproval = 1;
  } else {
    form.value.senderApproval = form.value.senderApproval ? form.value.senderApproval : 1;
    form.value.superintendentApproval = form.value.superintendentApproval ? form.value.superintendentApproval : 1;
  }
  if (form.value.senderApproval == 2 && !form.value.senderApprovalTime) {
    form.value.senderApprovalTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  }
  if (form.value.superintendentApproval == 2 && !form.value.superintendentApprovalTime) {
    form.value.superintendentApprovalTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  }
  transportRecordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.transportId) {
        await updateTransportRecord(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTransportRecord(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      cooDialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: TransportRecordVO) => {
  const _transportIds = row?.transportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除拉运记录编号为"' + _transportIds + '"的数据项？').finally(() => (loading.value = false));
  await delTransportRecord(_transportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epfy/transportRecord/export',
    {
      ...queryParams.value
    },
    `拉运记录_${new Date().getTime()}.xlsx`
  );
};
const setApplication = () => {
  let app = applicationList.value.find((item) => {
    return item.appId === form.value.applicationId;
  });
  form.value.departurePoint = app.loadingLocation;
};

onMounted(() => {
  getList();
  getUserInfo();
});
</script>
