<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="文档名称" prop="documentName">
              <el-input v-model="queryParams.documentName" placeholder="请输入文档名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文档类型" prop="documentType">
              <el-select v-model="queryParams.documentType" clearable placeholder="请选择文档类型">
                <el-option v-for="dict in eptw_standard_document_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="类别" prop="category">
              <el-select v-model="queryParams.category" clearable disabled placeholder="请选择类别">
                <el-option v-for="dict in eptw_standard_document_category" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epms:standardDocument:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:standardDocument:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:standardDocument:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:standardDocument:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="standardDocumentList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="文档名称" min-width="200" prop="documentName" />
        <el-table-column align="center" label="文档版本" prop="version" />
        <el-table-column label="文档类型" align="center" prop="documentType">
          <template #default="scope">
            <dict-tag :options="eptw_standard_document_type" :value="scope.row.documentType" />
          </template>
        </el-table-column>
        <el-table-column label="类别" align="center" prop="category">
          <template #default="scope">
            <dict-tag :options="eptw_standard_document_category" :value="scope.row.category" />
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="305">
          <template #default="scope">
            <el-tooltip content="管理依据" placement="top">
              <el-button v-hasPermi="['epms:standardDocument:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >文档预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:standardDocument:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epms:standardDocument:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:standardDocument:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改管理依据对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="standardDocumentFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="文档名称" prop="documentName">
          <el-input v-model="form.documentName" placeholder="请输入文档名称" />
        </el-form-item>
        <el-form-item label="文档版本" prop="version">
          <el-input v-model="form.version" placeholder="请输入文档版本" />
        </el-form-item>
        <el-form-item label="文档类型" prop="documentType">
          <el-select v-model="form.documentType" placeholder="请选择文档类型">
            <el-option v-for="dict in eptw_standard_document_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类别" prop="category">
          <el-select v-model="form.category" disabled placeholder="请选择类别">
            <el-option v-for="dict in eptw_standard_document_category" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动附件" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.documentId"
            :disabled="false"
            attach-category="standardDocument"
            attach-source-type="elCompany"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="standardDocumentFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <el-form-item label="文档名称" prop="documentName">
          <el-input v-model="form.documentName" placeholder="请输入文档名称" />
        </el-form-item>
        <el-form-item label="文档版本" prop="version">
          <el-input v-model="form.version" placeholder="请输入文档版本" />
        </el-form-item>
        <el-form-item label="文档类型" prop="documentType">
          <el-select v-model="form.documentType" placeholder="请选择文档类型">
            <el-option v-for="dict in eptw_standard_document_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类别" prop="category">
          <el-select v-model="form.category" placeholder="请选择类别">
            <el-option
              v-for="dict in eptw_standard_document_category"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="StandardDocument" lang="ts">
import {
  addStandardDocument,
  delStandardDocument,
  getStandardDocument,
  listStandardDocument,
  updateStandardDocument
} from '@/api/epms/epcom/standardDocument';
import { StandardDocumentForm, StandardDocumentQuery, StandardDocumentVO } from '@/api/epms/epcom/standardDocument/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_standard_document_category, eptw_standard_document_type } = toRefs<any>(
  proxy?.useDict('eptw_standard_document_category', 'eptw_standard_document_type')
);

const standardDocumentList = ref<StandardDocumentVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const router = useRouter();
const route = useRoute();
const queryFormRef = ref<ElFormInstance>();
const standardDocumentFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: StandardDocumentForm = {
  documentId: undefined,
  documentName: undefined,
  version: undefined,
  documentType: undefined,
  file: undefined,
  category: undefined
};
const data = reactive<PageData<StandardDocumentForm, StandardDocumentQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    documentName: undefined,
    documentType: undefined,
    file: undefined,
    category: undefined,
    params: {}
  },
  rules: {
    documentId: [{ required: true, message: '规范文档id不能为空', trigger: 'blur' }],
    documentName: [{ required: true, message: '文档名称不能为空', trigger: 'blur' }],
    version: [{ required: true, message: '文档版本不能为空', trigger: 'blur' }],
    documentType: [{ required: true, message: '文档类型不能为空', trigger: 'change' }],
    category: [{ required: true, message: '类别不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询管理依据列表 */
const getList = async () => {
  loading.value = true;
  const res = await listStandardDocument(queryParams.value);
  standardDocumentList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  standardDocumentFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: StandardDocumentVO[]) => {
  ids.value = selection.map((item) => item.documentId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  form.value.category = queryParams.value.category;
  dialog.visible = true;
  dialog.title = '添加管理依据';
};

const handlePreview = async (row?: StandardDocumentVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.documentId,
    attachSourceType: 'elCompany',
    attachCategory: 'standardDocument'
  });
};
const handleDetail = async (row?: StandardDocumentVO) => {
  reset();
  const _documentId = row?.documentId || ids.value[0];
  const res = await getStandardDocument(_documentId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '管理依据详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: StandardDocumentVO) => {
  reset();
  const _documentId = row?.documentId || ids.value[0];
  const res = await getStandardDocument(_documentId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改管理依据';
};

/** 提交按钮 */
const submitForm = () => {
  standardDocumentFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.documentId) {
        await updateStandardDocument(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addStandardDocument(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: StandardDocumentVO) => {
  const _documentIds = row?.documentId || ids.value;
  await proxy?.$modal.confirm('是否确认删除管理依据编号为"' + _documentIds + '"的数据项？').finally(() => (loading.value = false));
  await delStandardDocument(_documentIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/standardDocument/export',
    {
      ...queryParams.value
    },
    `管理依据_${new Date().getTime()}.xlsx`
  );
};

const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
  if (form.value.documentName == undefined) {
    form.value.documentName = fielInfo.name;
  }
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  if (route.query.category !== undefined) {
    queryParams.value.category = route.query.category;
  }
  getList();
});
</script>
