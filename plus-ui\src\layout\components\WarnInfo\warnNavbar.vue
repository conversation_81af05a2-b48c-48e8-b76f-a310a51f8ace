<script lang="ts" setup>
import { listWarnRecord } from '@/api/comm/warn/warnRecord/warnRecord';

import { getRoutePathWarnConfig, WarnSystemTypeConfig } from '@/layout/components/WarnInfo/warnSystemType';
import { useRoute } from 'vue-router';

const route = useRoute();
const warn = reactive({
  low: 0,
  mid: 0,
  top: 0
});
const warnQueryParams = ref({
  pageNum: -1,
  warnStatus: 1,
  systemType: 999
});
const warnConfig = ref<WarnSystemTypeConfig>(null);

const getWarnNum = async () => {
  //查询未处理状态的告警
  const res = await listWarnRecord(warnQueryParams.value);
  warn.low = res.rows.filter((obj) => obj.warnLevel === 1).length;
  warn.mid = res.rows.filter((obj) => obj.warnLevel === 2).length;
  warn.top = res.rows.filter((obj) => obj.warnLevel === 3).length;
};

const toWarn = async () => {
  if (!warnConfig.value) return;
  warnConfig.value.toWarnRecordPage();
};

watch(
  () => route.path,
  (path) => {
    warnConfig.value = getRoutePathWarnConfig(path);
    if (warnConfig.value) {
      warnQueryParams.value.systemType = warnConfig.value.systemType;
      getWarnNum();
    }
  },
  { immediate: true, deep: true }
);
</script>

<template>
  <div class="warn-navbar">
    <el-badge :max="99" :value="warn.low" type="success">
      <el-button size="small" type="success" @click="toWarn">低</el-button>
    </el-badge>
    <el-badge :max="99" :value="warn.mid" style="margin-left: 10px" type="warning">
      <el-button size="small" type="warning" @click="toWarn">中</el-button>
    </el-badge>
    <el-badge :max="99" :value="warn.top" style="margin-left: 10px" type="danger">
      <el-button size="small" type="danger" @click="toWarn">高</el-button>
    </el-badge>
  </div>
</template>

<style lang="scss" scoped>
.warn-navbar {
  width: 100%;
}
</style>
