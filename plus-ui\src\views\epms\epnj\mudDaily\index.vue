<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeDate"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>

            <!--            <el-form-item label="处理类型" prop="disposeType">-->
            <!--              <el-select v-model="queryParams.disposeType" placeholder="请选择处理方式" clearable-->
            <!--                         @change="resetRelatd(1)">-->
            <!--                <el-option v-for="dict in epnj_handling_type" :key="dict.value" :label="dict.label"-->
            <!--                           :value="parseInt(dict.value)"/>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <!--            <el-form-item v-if="queryParams.disposeType == 2" label-width="90" label="单井名称" prop="disposeId">-->
            <!--              <el-select-->
            <!--                v-model="queryParams.disposeId"-->
            <!--                filterable-->
            <!--                clearable-->
            <!--                placeholder="请选择单井"-->
            <!--                style="width: 240px"-->
            <!--              >-->
            <!--                <el-option-->
            <!--                  v-for="item in allList"-->
            <!--                  :key="item.id"-->
            <!--                  :label="item.label"-->
            <!--                  :value="item.id"-->
            <!--                />-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="集中站名称" label-width="90" prop="disposeId">
              <el-select v-model="queryParams.disposeId" clearable placeholder="请选择集中站名称">
                <el-option v-for="item in areaList" :key="item.operationAreaId" :label="item.operationAreaName" :value="item.operationAreaId" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!--          <el-col :span="1.5">-->
          <!--            <el-button v-hasPermi="['epms:mudDaily:add']" icon="Plus" plain type="primary" @click="handleAdd">新增 </el-button>-->
          <!--          </el-col>-->
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:mudDaily:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:mudDaily:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:mudDaily:export']" icon="Download" plain type="warning" @click="handleExport"> 导出 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:mudDaily:reCompute']" icon="RefreshRight" plain type="primary" @click="handleClick">重新计算 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="mudDailyList" stripe @cell-click="handleCellClick" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column label="日期" align="center" prop="date" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.date, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="处理类型" align="center" prop="disposeType">
          <template #default="scope">
            <dict-tag :options="epnj_handling_type" :value="scope.row.disposeType" />
          </template>
        </el-table-column>
        <el-table-column label="处理地点" align="center" prop="disposeId">
          <template #default="scope">
            {{ getKayName(scope.row.disposeId) }}
          </template>
        </el-table-column>
        <el-table-column label="泥浆拉运量（方）" align="center" prop="slurryDisposeAmount">
          <template #default="scope">
            <el-tag type="success" class="allowClick">{{ scope.row.slurryDisposeAmount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="泥浆暂存量（方）" prop="slurryStagingAmount" />
        <el-table-column label="泥饼拉运量（方）" align="center" prop="mudPullingAmount">
          <template #default="scope">
            <el-tag type="success" class="allowClick">{{ scope.row.mudPullingAmount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="泥饼暂存量（方）" prop="mudStagingAmount" />
        <el-table-column label="滤水拉运量（方）" align="center" prop="waterPullingAmount">
          <template #default="scope">
            <el-tag type="success" class="allowClick">{{ scope.row.waterPullingAmount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="滤水暂存量（方）" prop="waterStagingAmount" />
        <el-table-column align="center" label="总拉运量（方）" prop="totalPullingAmount" />
        <!--        <el-table-column label="损耗（方）" align="center" prop="loss"/>-->
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epms:mudDaily:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:mudDaily:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改泥浆日报对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="mudDailyFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="日期" prop="date">
          <el-date-picker
            v-model="form.date"
            :disabled="isEdit"
            clearable
            placeholder="请选择日期"
            type="date"
            value-format="YYYY-MM-DD"
            @change="getValue(form)"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="集中站名称" prop="disposeId">
          <el-select v-model="form.disposeId" :disabled="isEdit" clearable placeholder="请选择集中站名称" @change="getValue(form)">
            <el-option v-for="item in areaList" :key="item.operationAreaId" :label="item.operationAreaName" :value="item.operationAreaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="泥浆处理量" prop="slurryDisposeAmount">
          <el-input v-model="form.slurryDisposeAmount" placeholder="请输入泥浆处理量">
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <el-form-item label="泥浆暂存量" prop="slurryStagingAmount">
          <el-input v-model="form.slurryStagingAmount" placeholder="请输入泥浆暂存量">
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <el-form-item label="泥饼拉运量" prop="mudPullingAmount">
          <el-input v-model="form.mudPullingAmount" placeholder="请输入泥饼拉运量" disabled>
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <el-form-item label="泥饼暂存量" prop="mudStagingAmount">
          <el-input v-model="form.mudStagingAmount" placeholder="请输入泥饼暂存量">
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <el-form-item label="滤水拉运量" prop="waterPullingAmount">
          <el-input v-model="form.waterPullingAmount" placeholder="请输入滤水拉运量" disabled>
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <el-form-item label="滤水暂存量" prop="waterStagingAmount">
          <el-input v-model="form.waterStagingAmount" placeholder="请输入滤水暂存量">
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <!--        <el-form-item label="损耗" prop="loss">-->
        <!--          <el-input v-model="form.loss" placeholder="请输入损耗">-->
        <!--            <template #append>方</template>-->
        <!--          </el-input>-->
        <!--        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog title="数据统计" v-model="recordDialog.visible" width="1200px" append-to-body>
      <el-table stripe :data="transportRecordList">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column label="拉运时间" align="center" prop="transportTime" width="140">
          <template #default="scope">
            <span>{{ parseTime(scope.row.transportTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分类" align="center" prop="mediumCategory">
          <template #default="scope">
            <dict-tag :options="epfy_medium_category" :value="scope.row.mediumCategory" />
          </template>
        </el-table-column>
        <el-table-column label="介质类型" align="center" prop="mediumType">
          <template #default="scope">
            <dict-tag :options="epnj_transport_hunhe_medium_type" :value="scope.row.mediumType" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="起运点" prop="departurePoint" />
        <el-table-column align="center" label="数量(方)" prop="number" />
        <!--<el-table-column label="发送人" align="center" prop="sender" />-->
        <el-table-column align="center" label="拉运人" prop="transporter" />
        <el-table-column align="center" label="车牌号" prop="licensePlate" />
        <el-table-column align="center" label="接收点" prop="arrivalPoint" />
        <el-table-column align="center" label="接收人" prop="receiver" />
        <el-table-column align="center" label="备注信息" prop="remark" />
      </el-table>

      <pagination
        v-show="transportTotal > 0"
        v-model:limit="recordQuery.pageSize"
        v-model:page="recordQuery.pageNum"
        :total="transportTotal"
        @pagination="getAppList"
      />
    </el-dialog>

    <el-dialog v-model="computedDialog.visible" width="400px" append-to-body>
      <el-form :model="dateParams">
        <el-form-item label="选择日期进行计算" prop="date">
          <el-date-picker v-model="dateParams.date" clearable placeholder="请选择日期" type="date" value-format="YYYY-MM-DD"> </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="handleCompute">计 算</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MudDaily" lang="ts">
import { addMudDaily, delMudDaily, getMudDaily, listMudDaily, reCompute, updateMudDaily } from '@/api/epms/epnj/mudDaily';
import { MudDailyForm, MudDailyQuery, MudDailyVO } from '@/api/epms/epnj/mudDaily/types';
import { WellPreparationVO } from '@/api/epms/epnj/wellPreparation/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { listWellPreparation } from '@/api/epms/epnj/wellPreparation';
import { TransportRecordQuery, TransportRecordVO } from '@/api/epms/epfy/transportRecord/types';
import { getListByDaliy } from '@/api/epms/epfy/transportRecord';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epnj_handling_type, epfy_medium_category, epnj_transport_hunhe_medium_type } = toRefs<any>(
  proxy?.useDict('epnj_handling_type', 'epfy_medium_category', 'epnj_transport_hunhe_medium_type')
);

const mudDailyList = ref<MudDailyVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeDate = ref<[DateModelType, DateModelType]>(['', '']);
const isEdit = ref(false);

const wellList = ref<WellPreparationVO[]>([]);
const areaQuery = ref<OperationAreaQuery>({});
const areaList = ref<OperationAreaVO[]>([]);

const queryFormRef = ref<ElFormInstance>();
const mudDailyFormRef = ref<ElFormInstance>();
const allList = ref([]);
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const computedDialog = reactive({
  visible: false,
  title: ''
});
const recordDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const recordQuery = ref<TransportRecordQuery>({
  pageNum: 1,
  pageSize: 10
});
const transportTotal = ref(0);
const transportRecordList = ref<TransportRecordVO[]>([]);
const dateParams = ref({
  date: ''
});
const initFormData: MudDailyForm = {
  id: undefined,
  date: undefined,
  disposeType: 1,
  disposeId: undefined,
  slurryDisposeAmount: undefined,
  mudPullingAmount: undefined,
  mudStagingAmount: undefined,
  waterPullingAmount: undefined,
  waterStagingAmount: undefined,
  loss: undefined
};
const data = reactive<PageData<MudDailyForm, MudDailyQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    disposeType: 1,
    disposeId: undefined,
    slurryDisposeAmount: undefined,
    mudPullingAmount: undefined,
    mudStagingAmount: undefined,
    waterPullingAmount: undefined,
    waterStagingAmount: undefined,
    loss: undefined,
    params: {
      date: undefined
    }
  },
  rules: {
    id: [{ required: true, message: '主键ID不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const resetRelatd = (type: string | number) => {
  if (type === 1) {
    queryParams.value.disposeId = undefined;
  } else if (type === 2) {
    form.value.disposeId = undefined;
  }
};

const getWellList = async () => {
  const res = await listWellPreparation();
  wellList.value = res.rows;
  wellList.value.forEach((item) => {
    item.id = item.prepId;
    item.label = item.wellName;
    allList.value.push(item);
  });
};

/** 获取集中站列表 */
const queryAreaList = async () => {
  areaQuery.value.operationAreaType = 2;
  const res = await listOperationArea(areaQuery.value);
  areaList.value = res.rows;
  areaList.value.forEach((item) => {
    item.id = item.operationAreaId;
    item.label = item.operationAreaName;
    allList.value.push(item);
  });
};

const getValue = async (query: any, type?: string | number) => {
  if (type === 1) {
    form.value.disposeId = undefined;
  }
  if (query.date && query.disposeType && query.disposeId) {
    let newQyery = {
      disposeType: query.disposeType,
      disposeId: query.disposeId,
      date: query.date
    };
    const res = await listMudDaily(newQyery);
    if (res.total > 0) {
      form.value.id = res.rows[0].id;
      form.value.mudPullingAmount = res.rows[0].mudPullingAmount;
      form.value.mudStagingAmount = res.rows[0].mudStagingAmount;
      form.value.slurryDisposeAmount = res.rows[0].slurryDisposeAmount;
      form.value.waterPullingAmount = res.rows[0].waterPullingAmount;
      form.value.waterStagingAmount = res.rows[0].waterStagingAmount;
      form.value.loss = res.rows[0].loss;
    } else {
      form.value.id = undefined;
      form.value.mudPullingAmount = undefined;
      form.value.mudStagingAmount = undefined;
      form.value.slurryDisposeAmount = undefined;
      form.value.waterPullingAmount = undefined;
      form.value.waterStagingAmount = undefined;
      form.value.loss = undefined;
    }
  }
};

const getKayName = (id: string) => {
  // let name = areaList.value.find((item: any) => item.operationAreaId == id) ? areaList.value.find((item: any) => item.operationAreaId == id).operationAreaName : null
  return allList.value.find((item: any) => item.id == id) ? allList.value.find((item: any) => item.id == id).label : '未知';
  // return name != null ? name : name1
};
/** 查询泥浆日报列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeDate.value, 'Date');
  const res = await listMudDaily(queryParams.value);
  mudDailyList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  computedDialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  mudDailyFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeDate.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: MudDailyVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  isEdit.value = false;
  dialog.visible = true;
  dialog.title = '添加泥浆日报';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: MudDailyVO) => {
  reset();
  isEdit.value = true;
  const _id = row?.id || ids.value[0];
  const res = await getMudDaily(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改泥浆日报';
};

/** 提交按钮 */
const submitForm = () => {
  mudDailyFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMudDaily(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addMudDaily(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: MudDailyVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除泥浆日报编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delMudDaily(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/mudDaily/export',
    {
      ...queryParams.value
    },
    `mudDaily_${new Date().getTime()}.xlsx`
  );
};

const handleClick = () => {
  computedDialog.visible = true;
};
const handleCompute = async () => {
  computedDialog.visible = false;
  loading.value = true;
  await reCompute(dateParams.value);
  await getList();
  loading.value = false;
};
const handleCellClick = (row, column, cell, event) => {
  recordQuery.value.unloadLocation = undefined;
  recordQuery.value.relationId = undefined;
  if (column.property === 'mudPullingAmount') {
    recordQuery.value.mediumCategory = 1;
    recordQuery.value.relationId = row.disposeId;
    recordQuery.value.date = row.date;
    getAppList();
  } else if (column.property === 'waterPullingAmount') {
    recordQuery.value.mediumCategory = 2;
    recordQuery.value.relationId = row.disposeId;
    recordQuery.value.date = row.date;
    getAppList();
  } else if (column.property === 'slurryDisposeAmount') {
    recordQuery.value.mediumCategory = 3;
    recordQuery.value.unloadLocation = getKayName(row.disposeId);
    recordQuery.value.relationId = undefined;
    recordQuery.value.date = row.date;
    getAppList();
  }
};
const getAppList = () => {
  getListByDaliy(recordQuery.value).then((res) => {
    transportRecordList.value = res.rows;
    transportTotal.value = res.total;
    recordDialog.visible = true;
  });
};
onMounted(() => {
  getWellList();
  queryAreaList();
  getList();
});
</script>
<style scoped>
.allowClick {
  color: #1e9fff;
  background-color: #fdfdfd;
  cursor: pointer;
}
</style>
