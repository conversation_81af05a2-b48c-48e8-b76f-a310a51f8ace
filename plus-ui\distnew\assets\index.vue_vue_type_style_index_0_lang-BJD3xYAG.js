var pl=Object.defineProperty;var ml=(r,t,e)=>t in r?pl(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var $=(r,t,e)=>ml(r,typeof t!="symbol"?t+"":t,e);import{cn as oe,bO as Or,bG as Ir,co as ue,d as Ii,ap as bl,b as vl,ba as yl,r as Ve,k as ls,n as Gr,bl as Me,h as El,ai as Nl,aS as Al,X as wl,c as Tl,o as Zr,F as xl,q as rr,x as ql,y as Ll,t as Sl,e as ye,p as kl,a3 as _l,a8 as Cl,cp as Ol}from"./index-Bm6k27Yz.js";import{c as Ke,E as Il}from"./el-upload-CSOgWUCs.js";import"./el-progress-D8KwIucv.js";var Z=(r=>(r[r.TYPE=3]="TYPE",r[r.LEVEL=12]="LEVEL",r[r.ATTRIBUTE=13]="ATTRIBUTE",r[r.BLOT=14]="BLOT",r[r.INLINE=7]="INLINE",r[r.BLOCK=11]="BLOCK",r[r.BLOCK_BLOT=10]="BLOCK_BLOT",r[r.INLINE_BLOT=6]="INLINE_BLOT",r[r.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",r[r.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",r[r.ANY=15]="ANY",r))(Z||{});class Zt{constructor(t,e,n={}){this.attrName=t,this.keyName=e;const s=Z.TYPE&Z.ATTRIBUTE;this.scope=n.scope!=null?n.scope&Z.LEVEL|s:Z.ATTRIBUTE,n.whitelist!=null&&(this.whitelist=n.whitelist)}static keys(t){return Array.from(t.attributes).map(e=>e.name)}add(t,e){return this.canAdd(t,e)?(t.setAttribute(this.keyName,e),!0):!1}canAdd(t,e){return this.whitelist==null?!0:typeof e=="string"?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1}remove(t){t.removeAttribute(this.keyName)}value(t){const e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}}class Ge extends Error{constructor(t){t="[Parchment] "+t,super(t),this.message=t,this.name=this.constructor.name}}const Ri=class br{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(t==null)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let n=null;try{n=t.parentNode}catch{return null}return this.find(n,e)}return null}create(t,e,n){const s=this.query(e);if(s==null)throw new Ge(`Unable to create ${e} blot`);const i=s,o=e instanceof Node||e.nodeType===Node.TEXT_NODE?e:i.create(n),c=new i(t,o,n);return br.blots.set(c.domNode,c),c}find(t,e=!1){return br.find(t,e)}query(t,e=Z.ANY){let n;return typeof t=="string"?n=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?n=this.types.text:typeof t=="number"?t&Z.LEVEL&Z.BLOCK?n=this.types.block:t&Z.LEVEL&Z.INLINE&&(n=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some(s=>(n=this.classes[s],!!n)),n=n||this.tags[t.tagName]),n==null?null:"scope"in n&&e&Z.LEVEL&n.scope&&e&Z.TYPE&n.scope?n:null}register(...t){return t.map(e=>{const n="blotName"in e,s="attrName"in e;if(!n&&!s)throw new Ge("Invalid definition");if(n&&e.blotName==="abstract")throw new Ge("Cannot register abstract class");const i=n?e.blotName:s?e.attrName:void 0;return this.types[i]=e,s?typeof e.keyName=="string"&&(this.attributes[e.keyName]=e):n&&(e.className&&(this.classes[e.className]=e),e.tagName&&(Array.isArray(e.tagName)?e.tagName=e.tagName.map(o=>o.toUpperCase()):e.tagName=e.tagName.toUpperCase(),(Array.isArray(e.tagName)?e.tagName:[e.tagName]).forEach(o=>{(this.tags[o]==null||e.className==null)&&(this.tags[o]=e)}))),e})}};Ri.blots=new WeakMap;let We=Ri;function Wr(r,t){return(r.getAttribute("class")||"").split(/\s+/).filter(e=>e.indexOf(`${t}-`)===0)}class Rl extends Zt{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map(e=>e.split("-").slice(0,-1).join("-"))}add(t,e){return this.canAdd(t,e)?(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0):!1}remove(t){Wr(t,this.keyName).forEach(e=>{t.classList.remove(e)}),t.classList.length===0&&t.removeAttribute("class")}value(t){const e=(Wr(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}}const Ut=Rl;function ir(r){const t=r.split("-"),e=t.slice(1).map(n=>n[0].toUpperCase()+n.slice(1)).join("");return t[0]+e}class Ml extends Zt{static keys(t){return(t.getAttribute("style")||"").split(";").map(e=>e.split(":")[0].trim())}add(t,e){return this.canAdd(t,e)?(t.style[ir(this.keyName)]=e,!0):!1}remove(t){t.style[ir(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){const e=t.style[ir(this.keyName)];return this.canAdd(t,e)?e:""}}const he=Ml;class Bl{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(t.value(this.domNode)!=null?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};const t=We.find(this.domNode);if(t==null)return;const e=Zt.keys(this.domNode),n=Ut.keys(this.domNode),s=he.keys(this.domNode);e.concat(n).concat(s).forEach(i=>{const o=t.scroll.query(i,Z.ATTRIBUTE);o instanceof Zt&&(this.attributes[o.attrName]=o)})}copy(t){Object.keys(this.attributes).forEach(e=>{const n=this.attributes[e].value(this.domNode);t.format(e,n)})}move(t){this.copy(t),Object.keys(this.attributes).forEach(e=>{this.attributes[e].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t),{})}}const us=Bl,Mi=class{constructor(t,e){this.scroll=t,this.domNode=e,We.blots.set(e,this),this.prev=null,this.next=null}static create(t){if(this.tagName==null)throw new Ge("Blot definition missing tagName");let e,n;return Array.isArray(this.tagName)?(typeof t=="string"?(n=t.toUpperCase(),parseInt(n,10).toString()===n&&(n=parseInt(n,10))):typeof t=="number"&&(n=t),typeof n=="number"?e=document.createElement(this.tagName[n-1]):n&&this.tagName.indexOf(n)>-1?e=document.createElement(n):e=document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){const t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){this.parent!=null&&this.parent.removeChild(this),We.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,n,s){const i=this.isolate(t,e);if(this.scroll.query(n,Z.BLOT)!=null&&s)i.wrap(n,s);else if(this.scroll.query(n,Z.ATTRIBUTE)!=null){const o=this.scroll.create(this.statics.scope);i.wrap(o),o.format(n,s)}}insertAt(t,e,n){const s=n==null?this.scroll.create("text",e):this.scroll.create(e,n),i=this.split(t);this.parent.insertBefore(s,i||void 0)}isolate(t,e){const n=this.split(t);if(n==null)throw new Error("Attempt to isolate at end");return n.split(e),n}length(){return 1}offset(t=this.parent){return this.parent==null||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){const n=typeof t=="string"?this.scroll.create(t,e):t;return this.parent!=null&&(this.parent.insertBefore(n,this.next||void 0),this.remove()),n}split(t,e){return t===0?this:this.next}update(t,e){}wrap(t,e){const n=typeof t=="string"?this.scroll.create(t,e):t;if(this.parent!=null&&this.parent.insertBefore(n,this.next||void 0),typeof n.appendChild!="function")throw new Ge(`Cannot wrap ${t}`);return n.appendChild(this),n}};Mi.blotName="abstract";let Bi=Mi;const Di=class extends Bi{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let n=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(n+=1),[this.parent.domNode,n]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};Di.scope=Z.INLINE_BLOT;let Dl=Di;const Et=Dl;class jl{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){const e=t.slice(1);this.append(...e)}}at(t){const e=this.iterator();let n=e();for(;n&&t>0;)t-=1,n=e();return n}contains(t){const e=this.iterator();let n=e();for(;n;){if(n===t)return!0;n=e()}return!1}indexOf(t){const e=this.iterator();let n=e(),s=0;for(;n;){if(n===t)return s;s+=1,n=e()}return-1}insertBefore(t,e){t!=null&&(this.remove(t),t.next=e,e!=null?(t.prev=e.prev,e.prev!=null&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):this.tail!=null?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,n=this.head;for(;n!=null;){if(n===t)return e;e+=n.length(),n=n.next}return-1}remove(t){this.contains(t)&&(t.prev!=null&&(t.prev.next=t.next),t.next!=null&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{const e=t;return t!=null&&(t=t.next),e}}find(t,e=!1){const n=this.iterator();let s=n();for(;s;){const i=s.length();if(t<i||e&&t===i&&(s.next==null||s.next.length()!==0))return[s,t];t-=i,s=n()}return[null,0]}forEach(t){const e=this.iterator();let n=e();for(;n;)t(n),n=e()}forEachAt(t,e,n){if(e<=0)return;const[s,i]=this.find(t);let o=t-i;const c=this.iterator(s);let f=c();for(;f&&o<t+e;){const a=f.length();t>o?n(f,t-o,Math.min(e,o+a-t)):n(f,0,Math.min(a,t+e-o)),o+=a,f=c()}}map(t){return this.reduce((e,n)=>(e.push(t(n)),e),[])}reduce(t,e){const n=this.iterator();let s=n();for(;s;)e=t(e,s),s=n();return e}}function Xr(r,t){const e=t.find(r);if(e)return e;try{return t.create(r)}catch{const n=t.create(Z.INLINE);return Array.from(r.childNodes).forEach(s=>{n.domNode.appendChild(s)}),r.parentNode&&r.parentNode.replaceChild(n.domNode,r),n.attach(),n}}const ji=class le extends Bi{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach(t=>{t.attach()})}attachUI(t){this.uiNode!=null&&this.uiNode.remove(),this.uiNode=t,le.uiClass&&this.uiNode.classList.add(le.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new jl,Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode).reverse().forEach(t=>{try{const e=Xr(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(e){if(e instanceof Ge)return;throw e}})}deleteAt(t,e){if(t===0&&e===this.length())return this.remove();this.children.forEachAt(t,e,(n,s,i)=>{n.deleteAt(s,i)})}descendant(t,e=0){const[n,s]=this.children.find(e);return t.blotName==null&&t(n)||t.blotName!=null&&n instanceof t?[n,s]:n instanceof le?n.descendant(t,s):[null,-1]}descendants(t,e=0,n=Number.MAX_VALUE){let s=[],i=n;return this.children.forEachAt(e,n,(o,c,f)=>{(t.blotName==null&&t(o)||t.blotName!=null&&o instanceof t)&&s.push(o),o instanceof le&&(s=s.concat(o.descendants(t,c,i))),i-=f}),s}detach(){this.children.forEach(t=>{t.detach()}),super.detach()}enforceAllowedChildren(){let t=!1;this.children.forEach(e=>{t||this.statics.allowedChildren.some(n=>e instanceof n)||(e.statics.scope===Z.BLOCK_BLOT?(e.next!=null&&this.splitAfter(e),e.prev!=null&&this.splitAfter(e.prev),e.parent.unwrap(),t=!0):e instanceof le?e.unwrap():e.remove())})}formatAt(t,e,n,s){this.children.forEachAt(t,e,(i,o,c)=>{i.formatAt(o,c,n,s)})}insertAt(t,e,n){const[s,i]=this.children.find(t);if(s)s.insertAt(i,e,n);else{const o=n==null?this.scroll.create("text",e):this.scroll.create(e,n);this.appendChild(o)}}insertBefore(t,e){t.parent!=null&&t.parent.children.remove(t);let n=null;this.children.insertBefore(t,e||null),t.parent=this,e!=null&&(n=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==n)&&this.domNode.insertBefore(t.domNode,n),t.attach()}length(){return this.children.reduce((t,e)=>t+e.length(),0)}moveChildren(t,e){this.children.forEach(n=>{t.insertBefore(n,e)})}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),this.uiNode!=null&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),this.children.length===0)if(this.statics.defaultChild!=null){const e=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(e)}else this.remove()}path(t,e=!1){const[n,s]=this.children.find(t,e),i=[[this,t]];return n instanceof le?i.concat(n.path(s,e)):(n!=null&&i.push([n,s]),i)}removeChild(t){this.children.remove(t)}replaceWith(t,e){const n=typeof t=="string"?this.scroll.create(t,e):t;return n instanceof le&&this.moveChildren(n),super.replaceWith(n)}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const n=this.clone();return this.parent&&this.parent.insertBefore(n,this.next||void 0),this.children.forEachAt(t,this.length(),(s,i,o)=>{const c=s.split(i,e);c!=null&&n.appendChild(c)}),n}splitAfter(t){const e=this.clone();for(;t.next!=null;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){const n=[],s=[];t.forEach(i=>{i.target===this.domNode&&i.type==="childList"&&(n.push(...i.addedNodes),s.push(...i.removedNodes))}),s.forEach(i=>{if(i.parentNode!=null&&i.tagName!=="IFRAME"&&document.body.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;const o=this.scroll.find(i);o!=null&&(o.domNode.parentNode==null||o.domNode.parentNode===this.domNode)&&o.detach()}),n.filter(i=>i.parentNode===this.domNode&&i!==this.uiNode).sort((i,o)=>i===o?0:i.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(i=>{let o=null;i.nextSibling!=null&&(o=this.scroll.find(i.nextSibling));const c=Xr(i,this.scroll);(c.next!==o||c.next==null)&&(c.parent!=null&&c.parent.removeChild(this),this.insertBefore(c,o||void 0))}),this.enforceAllowedChildren()}};ji.uiClass="";let Ul=ji;const Dt=Ul;function Pl(r,t){if(Object.keys(r).length!==Object.keys(t).length)return!1;for(const e in r)if(r[e]!==t[e])return!1;return!0}const Ue=class Pe extends Dt{static create(t){return super.create(t)}static formats(t,e){const n=e.query(Pe.blotName);if(!(n!=null&&t.tagName===n.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new us(this.domNode)}format(t,e){if(t===this.statics.blotName&&!e)this.children.forEach(n=>{n instanceof Pe||(n=n.wrap(Pe.blotName,!0)),this.attributes.copy(n)}),this.unwrap();else{const n=this.scroll.query(t,Z.INLINE);if(n==null)return;n instanceof Zt?this.attributes.attribute(n,e):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e)}}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,n,s){this.formats()[n]!=null||this.scroll.query(n,Z.ATTRIBUTE)?this.isolate(t,e).format(n,s):super.formatAt(t,e,n,s)}optimize(t){super.optimize(t);const e=this.formats();if(Object.keys(e).length===0)return this.unwrap();const n=this.next;n instanceof Pe&&n.prev===this&&Pl(e,n.formats())&&(n.moveChildren(this),n.remove())}replaceWith(t,e){const n=super.replaceWith(t,e);return this.attributes.copy(n),n}update(t,e){super.update(t,e),t.some(n=>n.target===this.domNode&&n.type==="attributes")&&this.attributes.build()}wrap(t,e){const n=super.wrap(t,e);return n instanceof Pe&&this.attributes.move(n),n}};Ue.allowedChildren=[Ue,Et],Ue.blotName="inline",Ue.scope=Z.INLINE_BLOT,Ue.tagName="SPAN";let $l=Ue;const Rr=$l,$e=class vr extends Dt{static create(t){return super.create(t)}static formats(t,e){const n=e.query(vr.blotName);if(!(n!=null&&t.tagName===n.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new us(this.domNode)}format(t,e){const n=this.scroll.query(t,Z.BLOCK);n!=null&&(n instanceof Zt?this.attributes.attribute(n,e):t===this.statics.blotName&&!e?this.replaceWith(vr.blotName):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e))}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,n,s){this.scroll.query(n,Z.BLOCK)!=null?this.format(n,s):super.formatAt(t,e,n,s)}insertAt(t,e,n){if(n==null||this.scroll.query(e,Z.INLINE)!=null)super.insertAt(t,e,n);else{const s=this.split(t);if(s!=null){const i=this.scroll.create(e,n);s.parent.insertBefore(i,s)}else throw new Error("Attempt to insertAt after block boundaries")}}replaceWith(t,e){const n=super.replaceWith(t,e);return this.attributes.copy(n),n}update(t,e){super.update(t,e),t.some(n=>n.target===this.domNode&&n.type==="attributes")&&this.attributes.build()}};$e.blotName="block",$e.scope=Z.BLOCK_BLOT,$e.tagName="P",$e.allowedChildren=[Rr,$e,Et];let Fl=$e;const Tn=Fl,yr=class extends Dt{checkMerge(){return this.next!==null&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,n,s){super.formatAt(t,e,n,s),this.enforceAllowedChildren()}insertAt(t,e,n){super.insertAt(t,e,n),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&this.next!=null&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};yr.blotName="container",yr.scope=Z.BLOCK_BLOT;let Hl=yr;const hs=Hl;class zl extends Et{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,n,s){t===0&&e===this.length()?this.format(n,s):super.formatAt(t,e,n,s)}formats(){return this.statics.formats(this.domNode,this.scroll)}}const xt=zl,Vl={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Kl=100,Fe=class extends Dt{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver(n=>{this.update(n)}),this.observer.observe(this.domNode,Vl),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){const n=this.registry.find(t,e);return n?n.scroll===this?n:e?this.find(n.scroll.domNode.parentNode,!0):null:null}query(t,e=Z.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){this.scroll!=null&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),t===0&&e===this.length()?this.children.forEach(n=>{n.remove()}):super.deleteAt(t,e)}formatAt(t,e,n,s){this.update(),super.formatAt(t,e,n,s)}insertAt(t,e,n){this.update(),super.insertAt(t,e,n)}optimize(t=[],e={}){super.optimize(e);const n=e.mutationsMap||new WeakMap;let s=Array.from(this.observer.takeRecords());for(;s.length>0;)t.push(s.pop());const i=(f,a=!0)=>{f==null||f===this||f.domNode.parentNode!=null&&(n.has(f.domNode)||n.set(f.domNode,[]),a&&i(f.parent))},o=f=>{n.has(f.domNode)&&(f instanceof Dt&&f.children.forEach(o),n.delete(f.domNode),f.optimize(e))};let c=t;for(let f=0;c.length>0;f+=1){if(f>=Kl)throw new Error("[Parchment] Maximum optimize iterations reached");for(c.forEach(a=>{const g=this.find(a.target,!0);g!=null&&(g.domNode===a.target&&(a.type==="childList"?(i(this.find(a.previousSibling,!1)),Array.from(a.addedNodes).forEach(v=>{const d=this.find(v,!1);i(d,!1),d instanceof Dt&&d.children.forEach(p=>{i(p,!1)})})):a.type==="attributes"&&i(g.prev)),i(g))}),this.children.forEach(o),c=Array.from(this.observer.takeRecords()),s=c.slice();s.length>0;)t.push(s.pop())}}update(t,e={}){t=t||this.observer.takeRecords();const n=new WeakMap;t.map(s=>{const i=this.find(s.target,!0);return i==null?null:n.has(i.domNode)?(n.get(i.domNode).push(s),null):(n.set(i.domNode,[s]),i)}).forEach(s=>{s!=null&&s!==this&&n.has(s.domNode)&&s.update(n.get(s.domNode)||[],e)}),e.mutationsMap=n,n.has(this.domNode)&&super.update(n.get(this.domNode),e),this.optimize(t,e)}};Fe.blotName="scroll",Fe.defaultChild=Tn,Fe.allowedChildren=[Tn,hs],Fe.scope=Z.BLOCK_BLOT,Fe.tagName="DIV";let Gl=Fe;const Mr=Gl,Er=class Ui extends Et{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,n){n==null?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,n)}length(){return this.text.length}optimize(t){super.optimize(t),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof Ui&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const n=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(n,this.next||void 0),this.text=this.statics.value(this.domNode),n}update(t,e){t.some(n=>n.type==="characterData"&&n.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};Er.blotName="text",Er.scope=Z.INLINE_BLOT;let Zl=Er;const cs=Zl,Wl=Object.freeze(Object.defineProperty({__proto__:null,Attributor:Zt,AttributorStore:us,BlockBlot:Tn,ClassAttributor:Ut,ContainerBlot:hs,EmbedBlot:xt,InlineBlot:Rr,LeafBlot:Et,ParentBlot:Dt,Registry:We,Scope:Z,ScrollBlot:Mr,StyleAttributor:he,TextBlot:cs},Symbol.toStringTag,{value:"Module"}));var Yn={exports:{}},lr,Yr;function Xl(){if(Yr)return lr;Yr=1;var r=-1,t=1,e=0;function n(h,A,y,_,C){if(h===A)return h?[[e,h]]:[];if(y!=null){var x=F(h,A,y);if(x)return x}var O=c(h,A),B=h.substring(0,O);h=h.substring(O),A=A.substring(O),O=a(h,A);var P=h.substring(h.length-O);h=h.substring(0,h.length-O),A=A.substring(0,A.length-O);var I=s(h,A);return B&&I.unshift([e,B]),P&&I.push([e,P]),R(I,C),_&&v(I),I}function s(h,A){var y;if(!h)return[[t,A]];if(!A)return[[r,h]];var _=h.length>A.length?h:A,C=h.length>A.length?A:h,x=_.indexOf(C);if(x!==-1)return y=[[t,_.substring(0,x)],[e,C],[t,_.substring(x+C.length)]],h.length>A.length&&(y[0][0]=y[2][0]=r),y;if(C.length===1)return[[r,h],[t,A]];var O=g(h,A);if(O){var B=O[0],P=O[1],I=O[2],K=O[3],H=O[4],W=n(B,I),X=n(P,K);return W.concat([[e,H]],X)}return i(h,A)}function i(h,A){for(var y=h.length,_=A.length,C=Math.ceil((y+_)/2),x=C,O=2*C,B=new Array(O),P=new Array(O),I=0;I<O;I++)B[I]=-1,P[I]=-1;B[x+1]=0,P[x+1]=0;for(var K=y-_,H=K%2!==0,W=0,X=0,j=0,tt=0,et=0;et<C;et++){for(var J=-et+W;J<=et-X;J+=2){var it=x+J,rt;J===-et||J!==et&&B[it-1]<B[it+1]?rt=B[it+1]:rt=B[it-1]+1;for(var lt=rt-J;rt<y&&lt<_&&h.charAt(rt)===A.charAt(lt);)rt++,lt++;if(B[it]=rt,rt>y)X+=2;else if(lt>_)W+=2;else if(H){var at=x+K-J;if(at>=0&&at<O&&P[at]!==-1){var ct=y-P[at];if(rt>=ct)return o(h,A,rt,lt)}}}for(var ut=-et+j;ut<=et-tt;ut+=2){var at=x+ut,ct;ut===-et||ut!==et&&P[at-1]<P[at+1]?ct=P[at+1]:ct=P[at-1]+1;for(var mt=ct-ut;ct<y&&mt<_&&h.charAt(y-ct-1)===A.charAt(_-mt-1);)ct++,mt++;if(P[at]=ct,ct>y)tt+=2;else if(mt>_)j+=2;else if(!H){var it=x+K-ut;if(it>=0&&it<O&&B[it]!==-1){var rt=B[it],lt=x+rt-it;if(ct=y-ct,rt>=ct)return o(h,A,rt,lt)}}}}return[[r,h],[t,A]]}function o(h,A,y,_){var C=h.substring(0,y),x=A.substring(0,_),O=h.substring(y),B=A.substring(_),P=n(C,x),I=n(O,B);return P.concat(I)}function c(h,A){if(!h||!A||h.charAt(0)!==A.charAt(0))return 0;for(var y=0,_=Math.min(h.length,A.length),C=_,x=0;y<C;)h.substring(x,C)==A.substring(x,C)?(y=C,x=y):_=C,C=Math.floor((_-y)/2+y);return m(h.charCodeAt(C-1))&&C--,C}function f(h,A){var y=h.length,_=A.length;if(y==0||_==0)return 0;y>_?h=h.substring(y-_):y<_&&(A=A.substring(0,y));var C=Math.min(y,_);if(h==A)return C;for(var x=0,O=1;;){var B=h.substring(C-O),P=A.indexOf(B);if(P==-1)return x;O+=P,(P==0||h.substring(C-O)==A.substring(0,O))&&(x=O,O++)}}function a(h,A){if(!h||!A||h.slice(-1)!==A.slice(-1))return 0;for(var y=0,_=Math.min(h.length,A.length),C=_,x=0;y<C;)h.substring(h.length-C,h.length-x)==A.substring(A.length-C,A.length-x)?(y=C,x=y):_=C,C=Math.floor((_-y)/2+y);return S(h.charCodeAt(h.length-C))&&C--,C}function g(h,A){var y=h.length>A.length?h:A,_=h.length>A.length?A:h;if(y.length<4||_.length*2<y.length)return null;function C(X,j,tt){for(var et=X.substring(tt,tt+Math.floor(X.length/4)),J=-1,it="",rt,lt,at,ct;(J=j.indexOf(et,J+1))!==-1;){var ut=c(X.substring(tt),j.substring(J)),mt=a(X.substring(0,tt),j.substring(0,J));it.length<mt+ut&&(it=j.substring(J-mt,J)+j.substring(J,J+ut),rt=X.substring(0,tt-mt),lt=X.substring(tt+ut),at=j.substring(0,J-mt),ct=j.substring(J+ut))}return it.length*2>=X.length?[rt,lt,at,ct,it]:null}var x=C(y,_,Math.ceil(y.length/4)),O=C(y,_,Math.ceil(y.length/2)),B;if(!x&&!O)return null;O?x?B=x[4].length>O[4].length?x:O:B=O:B=x;var P,I,K,H;h.length>A.length?(P=B[0],I=B[1],K=B[2],H=B[3]):(K=B[0],H=B[1],P=B[2],I=B[3]);var W=B[4];return[P,I,K,H,W]}function v(h){for(var A=!1,y=[],_=0,C=null,x=0,O=0,B=0,P=0,I=0;x<h.length;)h[x][0]==e?(y[_++]=x,O=P,B=I,P=0,I=0,C=h[x][1]):(h[x][0]==t?P+=h[x][1].length:I+=h[x][1].length,C&&C.length<=Math.max(O,B)&&C.length<=Math.max(P,I)&&(h.splice(y[_-1],0,[r,C]),h[y[_-1]+1][0]=t,_--,_--,x=_>0?y[_-1]:-1,O=0,B=0,P=0,I=0,C=null,A=!0)),x++;for(A&&R(h),k(h),x=1;x<h.length;){if(h[x-1][0]==r&&h[x][0]==t){var K=h[x-1][1],H=h[x][1],W=f(K,H),X=f(H,K);W>=X?(W>=K.length/2||W>=H.length/2)&&(h.splice(x,0,[e,H.substring(0,W)]),h[x-1][1]=K.substring(0,K.length-W),h[x+1][1]=H.substring(W),x++):(X>=K.length/2||X>=H.length/2)&&(h.splice(x,0,[e,K.substring(0,X)]),h[x-1][0]=t,h[x-1][1]=H.substring(0,H.length-X),h[x+1][0]=r,h[x+1][1]=K.substring(X),x++),x++}x++}}var d=/[^a-zA-Z0-9]/,p=/\s/,E=/[\r\n]/,w=/\n\r?\n$/,q=/^\r?\n\r?\n/;function k(h){function A(X,j){if(!X||!j)return 6;var tt=X.charAt(X.length-1),et=j.charAt(0),J=tt.match(d),it=et.match(d),rt=J&&tt.match(p),lt=it&&et.match(p),at=rt&&tt.match(E),ct=lt&&et.match(E),ut=at&&X.match(w),mt=ct&&j.match(q);return ut||mt?5:at||ct?4:J&&!rt&&lt?3:rt||lt?2:J||it?1:0}for(var y=1;y<h.length-1;){if(h[y-1][0]==e&&h[y+1][0]==e){var _=h[y-1][1],C=h[y][1],x=h[y+1][1],O=a(_,C);if(O){var B=C.substring(C.length-O);_=_.substring(0,_.length-O),C=B+C.substring(0,C.length-O),x=B+x}for(var P=_,I=C,K=x,H=A(_,C)+A(C,x);C.charAt(0)===x.charAt(0);){_+=C.charAt(0),C=C.substring(1)+x.charAt(0),x=x.substring(1);var W=A(_,C)+A(C,x);W>=H&&(H=W,P=_,I=C,K=x)}h[y-1][1]!=P&&(P?h[y-1][1]=P:(h.splice(y-1,1),y--),h[y][1]=I,K?h[y+1][1]=K:(h.splice(y+1,1),y--))}y++}}function R(h,A){h.push([e,""]);for(var y=0,_=0,C=0,x="",O="",B;y<h.length;){if(y<h.length-1&&!h[y][1]){h.splice(y,1);continue}switch(h[y][0]){case t:C++,O+=h[y][1],y++;break;case r:_++,x+=h[y][1],y++;break;case e:var P=y-C-_-1;if(A){if(P>=0&&D(h[P][1])){var I=h[P][1].slice(-1);if(h[P][1]=h[P][1].slice(0,-1),x=I+x,O=I+O,!h[P][1]){h.splice(P,1),y--;var K=P-1;h[K]&&h[K][0]===t&&(C++,O=h[K][1]+O,K--),h[K]&&h[K][0]===r&&(_++,x=h[K][1]+x,K--),P=K}}if(N(h[y][1])){var I=h[y][1].charAt(0);h[y][1]=h[y][1].slice(1),x+=I,O+=I}}if(y<h.length-1&&!h[y][1]){h.splice(y,1);break}if(x.length>0||O.length>0){x.length>0&&O.length>0&&(B=c(O,x),B!==0&&(P>=0?h[P][1]+=O.substring(0,B):(h.splice(0,0,[e,O.substring(0,B)]),y++),O=O.substring(B),x=x.substring(B)),B=a(O,x),B!==0&&(h[y][1]=O.substring(O.length-B)+h[y][1],O=O.substring(0,O.length-B),x=x.substring(0,x.length-B)));var H=C+_;x.length===0&&O.length===0?(h.splice(y-H,H),y=y-H):x.length===0?(h.splice(y-H,H,[t,O]),y=y-H+1):O.length===0?(h.splice(y-H,H,[r,x]),y=y-H+1):(h.splice(y-H,H,[r,x],[t,O]),y=y-H+2)}y!==0&&h[y-1][0]===e?(h[y-1][1]+=h[y][1],h.splice(y,1)):y++,C=0,_=0,x="",O="";break}}h[h.length-1][1]===""&&h.pop();var W=!1;for(y=1;y<h.length-1;)h[y-1][0]===e&&h[y+1][0]===e&&(h[y][1].substring(h[y][1].length-h[y-1][1].length)===h[y-1][1]?(h[y][1]=h[y-1][1]+h[y][1].substring(0,h[y][1].length-h[y-1][1].length),h[y+1][1]=h[y-1][1]+h[y+1][1],h.splice(y-1,1),W=!0):h[y][1].substring(0,h[y+1][1].length)==h[y+1][1]&&(h[y-1][1]+=h[y+1][1],h[y][1]=h[y][1].substring(h[y+1][1].length)+h[y+1][1],h.splice(y+1,1),W=!0)),y++;W&&R(h,A)}function m(h){return h>=55296&&h<=56319}function S(h){return h>=56320&&h<=57343}function N(h){return S(h.charCodeAt(0))}function D(h){return m(h.charCodeAt(h.length-1))}function U(h){for(var A=[],y=0;y<h.length;y++)h[y][1].length>0&&A.push(h[y]);return A}function M(h,A,y,_){return D(h)||N(_)?null:U([[e,h],[r,A],[t,y],[e,_]])}function F(h,A,y){var _=typeof y=="number"?{index:y,length:0}:y.oldRange,C=typeof y=="number"?null:y.newRange,x=h.length,O=A.length;if(_.length===0&&(C===null||C.length===0)){var B=_.index,P=h.slice(0,B),I=h.slice(B),K=C?C.index:null;t:{var H=B+O-x;if(K!==null&&K!==H||H<0||H>O)break t;var W=A.slice(0,H),X=A.slice(H);if(X!==I)break t;var j=Math.min(B,H),tt=P.slice(0,j),et=W.slice(0,j);if(tt!==et)break t;var J=P.slice(j),it=W.slice(j);return M(tt,J,it,I)}t:{if(K!==null&&K!==B)break t;var rt=B,W=A.slice(0,rt),X=A.slice(rt);if(W!==P)break t;var lt=Math.min(x-rt,O-rt),at=I.slice(I.length-lt),ct=X.slice(X.length-lt);if(at!==ct)break t;var J=I.slice(0,I.length-lt),it=X.slice(0,X.length-lt);return M(P,J,it,at)}}if(_.length>0&&C&&C.length===0)t:{var tt=h.slice(0,_.index),at=h.slice(_.index+_.length),j=tt.length,lt=at.length;if(O<j+lt)break t;var et=A.slice(0,j),ct=A.slice(O-lt);if(tt!==et||at!==ct)break t;var J=h.slice(j,x-lt),it=A.slice(j,O-lt);return M(tt,J,it,at)}return null}function V(h,A,y,_){return n(h,A,y,_,!0)}return V.INSERT=t,V.DELETE=r,V.EQUAL=e,lr=V,lr}var vn={exports:{}};vn.exports;var Qr;function fs(){return Qr||(Qr=1,function(r,t){var e=200,n="__lodash_hash_undefined__",s=9007199254740991,i="[object Arguments]",o="[object Array]",c="[object Boolean]",f="[object Date]",a="[object Error]",g="[object Function]",v="[object GeneratorFunction]",d="[object Map]",p="[object Number]",E="[object Object]",w="[object Promise]",q="[object RegExp]",k="[object Set]",R="[object String]",m="[object Symbol]",S="[object WeakMap]",N="[object ArrayBuffer]",D="[object DataView]",U="[object Float32Array]",M="[object Float64Array]",F="[object Int8Array]",V="[object Int16Array]",h="[object Int32Array]",A="[object Uint8Array]",y="[object Uint8ClampedArray]",_="[object Uint16Array]",C="[object Uint32Array]",x=/[\\^$.*+?()[\]{}|]/g,O=/\w*$/,B=/^\[object .+?Constructor\]$/,P=/^(?:0|[1-9]\d*)$/,I={};I[i]=I[o]=I[N]=I[D]=I[c]=I[f]=I[U]=I[M]=I[F]=I[V]=I[h]=I[d]=I[p]=I[E]=I[q]=I[k]=I[R]=I[m]=I[A]=I[y]=I[_]=I[C]=!0,I[a]=I[g]=I[S]=!1;var K=typeof oe=="object"&&oe&&oe.Object===Object&&oe,H=typeof self=="object"&&self&&self.Object===Object&&self,W=K||H||Function("return this")(),X=t&&!t.nodeType&&t,j=X&&!0&&r&&!r.nodeType&&r,tt=j&&j.exports===X;function et(l,u){return l.set(u[0],u[1]),l}function J(l,u){return l.add(u),l}function it(l,u){for(var b=-1,T=l?l.length:0;++b<T&&u(l[b],b,l)!==!1;);return l}function rt(l,u){for(var b=-1,T=u.length,Q=l.length;++b<T;)l[Q+b]=u[b];return l}function lt(l,u,b,T){for(var Q=-1,Y=l?l.length:0;++Q<Y;)b=u(b,l[Q],Q,l);return b}function at(l,u){for(var b=-1,T=Array(l);++b<l;)T[b]=u(b);return T}function ct(l,u){return l==null?void 0:l[u]}function ut(l){var u=!1;if(l!=null&&typeof l.toString!="function")try{u=!!(l+"")}catch{}return u}function mt(l){var u=-1,b=Array(l.size);return l.forEach(function(T,Q){b[++u]=[Q,T]}),b}function Je(l,u){return function(b){return l(u(b))}}function On(l){var u=-1,b=Array(l.size);return l.forEach(function(T){b[++u]=T}),b}var bs=Array.prototype,vs=Function.prototype,Te=Object.prototype,tn=W["__core-js_shared__"],In=function(){var l=/[^.]+$/.exec(tn&&tn.keys&&tn.keys.IE_PROTO||"");return l?"Symbol(src)_1."+l:""}(),Rn=vs.toString,Ft=Te.hasOwnProperty,xe=Te.toString,ys=RegExp("^"+Rn.call(Ft).replace(x,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),fe=tt?W.Buffer:void 0,qe=W.Symbol,en=W.Uint8Array,qt=Je(Object.getPrototypeOf,Object),Mn=Object.create,Bn=Te.propertyIsEnumerable,Es=bs.splice,nn=Object.getOwnPropertySymbols,Le=fe?fe.isBuffer:void 0,Dn=Je(Object.keys,Object),Se=It(W,"DataView"),de=It(W,"Map"),Ot=It(W,"Promise"),ke=It(W,"Set"),sn=It(W,"WeakMap"),ge=It(Object,"create"),rn=At(Se),pe=At(de),ln=At(Ot),on=At(ke),an=At(sn),re=qe?qe.prototype:void 0,jn=re?re.valueOf:void 0;function Xt(l){var u=-1,b=l?l.length:0;for(this.clear();++u<b;){var T=l[u];this.set(T[0],T[1])}}function Ns(){this.__data__=ge?ge(null):{}}function As(l){return this.has(l)&&delete this.__data__[l]}function ws(l){var u=this.__data__;if(ge){var b=u[l];return b===n?void 0:b}return Ft.call(u,l)?u[l]:void 0}function Un(l){var u=this.__data__;return ge?u[l]!==void 0:Ft.call(u,l)}function cn(l,u){var b=this.__data__;return b[l]=ge&&u===void 0?n:u,this}Xt.prototype.clear=Ns,Xt.prototype.delete=As,Xt.prototype.get=ws,Xt.prototype.has=Un,Xt.prototype.set=cn;function dt(l){var u=-1,b=l?l.length:0;for(this.clear();++u<b;){var T=l[u];this.set(T[0],T[1])}}function Ts(){this.__data__=[]}function xs(l){var u=this.__data__,b=Ce(u,l);if(b<0)return!1;var T=u.length-1;return b==T?u.pop():Es.call(u,b,1),!0}function qs(l){var u=this.__data__,b=Ce(u,l);return b<0?void 0:u[b][1]}function Ls(l){return Ce(this.__data__,l)>-1}function Ss(l,u){var b=this.__data__,T=Ce(b,l);return T<0?b.push([l,u]):b[T][1]=u,this}dt.prototype.clear=Ts,dt.prototype.delete=xs,dt.prototype.get=qs,dt.prototype.has=Ls,dt.prototype.set=Ss;function bt(l){var u=-1,b=l?l.length:0;for(this.clear();++u<b;){var T=l[u];this.set(T[0],T[1])}}function ks(){this.__data__={hash:new Xt,map:new(de||dt),string:new Xt}}function _s(l){return be(this,l).delete(l)}function Cs(l){return be(this,l).get(l)}function Os(l){return be(this,l).has(l)}function Is(l,u){return be(this,l).set(l,u),this}bt.prototype.clear=ks,bt.prototype.delete=_s,bt.prototype.get=Cs,bt.prototype.has=Os,bt.prototype.set=Is;function wt(l){this.__data__=new dt(l)}function Rs(){this.__data__=new dt}function Ms(l){return this.__data__.delete(l)}function Bs(l){return this.__data__.get(l)}function Ds(l){return this.__data__.has(l)}function js(l,u){var b=this.__data__;if(b instanceof dt){var T=b.__data__;if(!de||T.length<e-1)return T.push([l,u]),this;b=this.__data__=new bt(T)}return b.set(l,u),this}wt.prototype.clear=Rs,wt.prototype.delete=Ms,wt.prototype.get=Bs,wt.prototype.has=Ds,wt.prototype.set=js;function _e(l,u){var b=dn(l)||Ie(l)?at(l.length,String):[],T=b.length,Q=!!T;for(var Y in l)Ft.call(l,Y)&&!(Q&&(Y=="length"||Qs(Y,T)))&&b.push(Y);return b}function Pn(l,u,b){var T=l[u];(!(Ft.call(l,u)&&Vn(T,b))||b===void 0&&!(u in l))&&(l[u]=b)}function Ce(l,u){for(var b=l.length;b--;)if(Vn(l[b][0],u))return b;return-1}function Ht(l,u){return l&&fn(u,pn(u),l)}function un(l,u,b,T,Q,Y,st){var nt;if(T&&(nt=Y?T(l,Q,Y,st):T(l)),nt!==void 0)return nt;if(!Vt(l))return l;var ht=dn(l);if(ht){if(nt=Xs(l),!u)return Gs(l,nt)}else{var ot=Qt(l),vt=ot==g||ot==v;if(Kn(l))return Oe(l,u);if(ot==E||ot==i||vt&&!Y){if(ut(l))return Y?l:{};if(nt=zt(vt?{}:l),!u)return Zs(l,Ht(nt,l))}else{if(!I[ot])return Y?l:{};nt=Ys(l,ot,un,u)}}st||(st=new wt);var Tt=st.get(l);if(Tt)return Tt;if(st.set(l,nt),!ht)var ft=b?Ws(l):pn(l);return it(ft||l,function(yt,gt){ft&&(gt=yt,yt=l[gt]),Pn(nt,gt,un(yt,u,b,T,gt,l,st))}),nt}function Us(l){return Vt(l)?Mn(l):{}}function Ps(l,u,b){var T=u(l);return dn(l)?T:rt(T,b(l))}function $s(l){return xe.call(l)}function Fs(l){if(!Vt(l)||tr(l))return!1;var u=gn(l)||ut(l)?ys:B;return u.test(At(l))}function Hs(l){if(!Hn(l))return Dn(l);var u=[];for(var b in Object(l))Ft.call(l,b)&&b!="constructor"&&u.push(b);return u}function Oe(l,u){if(u)return l.slice();var b=new l.constructor(l.length);return l.copy(b),b}function hn(l){var u=new l.constructor(l.byteLength);return new en(u).set(new en(l)),u}function me(l,u){var b=u?hn(l.buffer):l.buffer;return new l.constructor(b,l.byteOffset,l.byteLength)}function $n(l,u,b){var T=u?b(mt(l),!0):mt(l);return lt(T,et,new l.constructor)}function Fn(l){var u=new l.constructor(l.source,O.exec(l));return u.lastIndex=l.lastIndex,u}function zs(l,u,b){var T=u?b(On(l),!0):On(l);return lt(T,J,new l.constructor)}function Vs(l){return jn?Object(jn.call(l)):{}}function Ks(l,u){var b=u?hn(l.buffer):l.buffer;return new l.constructor(b,l.byteOffset,l.length)}function Gs(l,u){var b=-1,T=l.length;for(u||(u=Array(T));++b<T;)u[b]=l[b];return u}function fn(l,u,b,T){b||(b={});for(var Q=-1,Y=u.length;++Q<Y;){var st=u[Q],nt=void 0;Pn(b,st,nt===void 0?l[st]:nt)}return b}function Zs(l,u){return fn(l,Yt(l),u)}function Ws(l){return Ps(l,pn,Yt)}function be(l,u){var b=l.__data__;return Js(u)?b[typeof u=="string"?"string":"hash"]:b.map}function It(l,u){var b=ct(l,u);return Fs(b)?b:void 0}var Yt=nn?Je(nn,Object):nr,Qt=$s;(Se&&Qt(new Se(new ArrayBuffer(1)))!=D||de&&Qt(new de)!=d||Ot&&Qt(Ot.resolve())!=w||ke&&Qt(new ke)!=k||sn&&Qt(new sn)!=S)&&(Qt=function(l){var u=xe.call(l),b=u==E?l.constructor:void 0,T=b?At(b):void 0;if(T)switch(T){case rn:return D;case pe:return d;case ln:return w;case on:return k;case an:return S}return u});function Xs(l){var u=l.length,b=l.constructor(u);return u&&typeof l[0]=="string"&&Ft.call(l,"index")&&(b.index=l.index,b.input=l.input),b}function zt(l){return typeof l.constructor=="function"&&!Hn(l)?Us(qt(l)):{}}function Ys(l,u,b,T){var Q=l.constructor;switch(u){case N:return hn(l);case c:case f:return new Q(+l);case D:return me(l,T);case U:case M:case F:case V:case h:case A:case y:case _:case C:return Ks(l,T);case d:return $n(l,T,b);case p:case R:return new Q(l);case q:return Fn(l);case k:return zs(l,T,b);case m:return Vs(l)}}function Qs(l,u){return u=u??s,!!u&&(typeof l=="number"||P.test(l))&&l>-1&&l%1==0&&l<u}function Js(l){var u=typeof l;return u=="string"||u=="number"||u=="symbol"||u=="boolean"?l!=="__proto__":l===null}function tr(l){return!!In&&In in l}function Hn(l){var u=l&&l.constructor,b=typeof u=="function"&&u.prototype||Te;return l===b}function At(l){if(l!=null){try{return Rn.call(l)}catch{}try{return l+""}catch{}}return""}function zn(l){return un(l,!0,!0)}function Vn(l,u){return l===u||l!==l&&u!==u}function Ie(l){return er(l)&&Ft.call(l,"callee")&&(!Bn.call(l,"callee")||xe.call(l)==i)}var dn=Array.isArray;function Re(l){return l!=null&&Gn(l.length)&&!gn(l)}function er(l){return Zn(l)&&Re(l)}var Kn=Le||sr;function gn(l){var u=Vt(l)?xe.call(l):"";return u==g||u==v}function Gn(l){return typeof l=="number"&&l>-1&&l%1==0&&l<=s}function Vt(l){var u=typeof l;return!!l&&(u=="object"||u=="function")}function Zn(l){return!!l&&typeof l=="object"}function pn(l){return Re(l)?_e(l):Hs(l)}function nr(){return[]}function sr(){return!1}r.exports=zn}(vn,vn.exports)),vn.exports}var yn={exports:{}};yn.exports;var Jr;function ds(){return Jr||(Jr=1,function(r,t){var e=200,n="__lodash_hash_undefined__",s=1,i=2,o=9007199254740991,c="[object Arguments]",f="[object Array]",a="[object AsyncFunction]",g="[object Boolean]",v="[object Date]",d="[object Error]",p="[object Function]",E="[object GeneratorFunction]",w="[object Map]",q="[object Number]",k="[object Null]",R="[object Object]",m="[object Promise]",S="[object Proxy]",N="[object RegExp]",D="[object Set]",U="[object String]",M="[object Symbol]",F="[object Undefined]",V="[object WeakMap]",h="[object ArrayBuffer]",A="[object DataView]",y="[object Float32Array]",_="[object Float64Array]",C="[object Int8Array]",x="[object Int16Array]",O="[object Int32Array]",B="[object Uint8Array]",P="[object Uint8ClampedArray]",I="[object Uint16Array]",K="[object Uint32Array]",H=/[\\^$.*+?()[\]{}|]/g,W=/^\[object .+?Constructor\]$/,X=/^(?:0|[1-9]\d*)$/,j={};j[y]=j[_]=j[C]=j[x]=j[O]=j[B]=j[P]=j[I]=j[K]=!0,j[c]=j[f]=j[h]=j[g]=j[A]=j[v]=j[d]=j[p]=j[w]=j[q]=j[R]=j[N]=j[D]=j[U]=j[V]=!1;var tt=typeof oe=="object"&&oe&&oe.Object===Object&&oe,et=typeof self=="object"&&self&&self.Object===Object&&self,J=tt||et||Function("return this")(),it=t&&!t.nodeType&&t,rt=it&&!0&&r&&!r.nodeType&&r,lt=rt&&rt.exports===it,at=lt&&tt.process,ct=function(){try{return at&&at.binding&&at.binding("util")}catch{}}(),ut=ct&&ct.isTypedArray;function mt(l,u){for(var b=-1,T=l==null?0:l.length,Q=0,Y=[];++b<T;){var st=l[b];u(st,b,l)&&(Y[Q++]=st)}return Y}function Je(l,u){for(var b=-1,T=u.length,Q=l.length;++b<T;)l[Q+b]=u[b];return l}function On(l,u){for(var b=-1,T=l==null?0:l.length;++b<T;)if(u(l[b],b,l))return!0;return!1}function bs(l,u){for(var b=-1,T=Array(l);++b<l;)T[b]=u(b);return T}function vs(l){return function(u){return l(u)}}function Te(l,u){return l.has(u)}function tn(l,u){return l==null?void 0:l[u]}function In(l){var u=-1,b=Array(l.size);return l.forEach(function(T,Q){b[++u]=[Q,T]}),b}function Rn(l,u){return function(b){return l(u(b))}}function Ft(l){var u=-1,b=Array(l.size);return l.forEach(function(T){b[++u]=T}),b}var xe=Array.prototype,ys=Function.prototype,fe=Object.prototype,qe=J["__core-js_shared__"],en=ys.toString,qt=fe.hasOwnProperty,Mn=function(){var l=/[^.]+$/.exec(qe&&qe.keys&&qe.keys.IE_PROTO||"");return l?"Symbol(src)_1."+l:""}(),Bn=fe.toString,Es=RegExp("^"+en.call(qt).replace(H,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),nn=lt?J.Buffer:void 0,Le=J.Symbol,Dn=J.Uint8Array,Se=fe.propertyIsEnumerable,de=xe.splice,Ot=Le?Le.toStringTag:void 0,ke=Object.getOwnPropertySymbols,sn=nn?nn.isBuffer:void 0,ge=Rn(Object.keys,Object),rn=Yt(J,"DataView"),pe=Yt(J,"Map"),ln=Yt(J,"Promise"),on=Yt(J,"Set"),an=Yt(J,"WeakMap"),re=Yt(Object,"create"),jn=At(rn),Xt=At(pe),Ns=At(ln),As=At(on),ws=At(an),Un=Le?Le.prototype:void 0,cn=Un?Un.valueOf:void 0;function dt(l){var u=-1,b=l==null?0:l.length;for(this.clear();++u<b;){var T=l[u];this.set(T[0],T[1])}}function Ts(){this.__data__=re?re(null):{},this.size=0}function xs(l){var u=this.has(l)&&delete this.__data__[l];return this.size-=u?1:0,u}function qs(l){var u=this.__data__;if(re){var b=u[l];return b===n?void 0:b}return qt.call(u,l)?u[l]:void 0}function Ls(l){var u=this.__data__;return re?u[l]!==void 0:qt.call(u,l)}function Ss(l,u){var b=this.__data__;return this.size+=this.has(l)?0:1,b[l]=re&&u===void 0?n:u,this}dt.prototype.clear=Ts,dt.prototype.delete=xs,dt.prototype.get=qs,dt.prototype.has=Ls,dt.prototype.set=Ss;function bt(l){var u=-1,b=l==null?0:l.length;for(this.clear();++u<b;){var T=l[u];this.set(T[0],T[1])}}function ks(){this.__data__=[],this.size=0}function _s(l){var u=this.__data__,b=Oe(u,l);if(b<0)return!1;var T=u.length-1;return b==T?u.pop():de.call(u,b,1),--this.size,!0}function Cs(l){var u=this.__data__,b=Oe(u,l);return b<0?void 0:u[b][1]}function Os(l){return Oe(this.__data__,l)>-1}function Is(l,u){var b=this.__data__,T=Oe(b,l);return T<0?(++this.size,b.push([l,u])):b[T][1]=u,this}bt.prototype.clear=ks,bt.prototype.delete=_s,bt.prototype.get=Cs,bt.prototype.has=Os,bt.prototype.set=Is;function wt(l){var u=-1,b=l==null?0:l.length;for(this.clear();++u<b;){var T=l[u];this.set(T[0],T[1])}}function Rs(){this.size=0,this.__data__={hash:new dt,map:new(pe||bt),string:new dt}}function Ms(l){var u=It(this,l).delete(l);return this.size-=u?1:0,u}function Bs(l){return It(this,l).get(l)}function Ds(l){return It(this,l).has(l)}function js(l,u){var b=It(this,l),T=b.size;return b.set(l,u),this.size+=b.size==T?0:1,this}wt.prototype.clear=Rs,wt.prototype.delete=Ms,wt.prototype.get=Bs,wt.prototype.has=Ds,wt.prototype.set=js;function _e(l){var u=-1,b=l==null?0:l.length;for(this.__data__=new wt;++u<b;)this.add(l[u])}function Pn(l){return this.__data__.set(l,n),this}function Ce(l){return this.__data__.has(l)}_e.prototype.add=_e.prototype.push=Pn,_e.prototype.has=Ce;function Ht(l){var u=this.__data__=new bt(l);this.size=u.size}function un(){this.__data__=new bt,this.size=0}function Us(l){var u=this.__data__,b=u.delete(l);return this.size=u.size,b}function Ps(l){return this.__data__.get(l)}function $s(l){return this.__data__.has(l)}function Fs(l,u){var b=this.__data__;if(b instanceof bt){var T=b.__data__;if(!pe||T.length<e-1)return T.push([l,u]),this.size=++b.size,this;b=this.__data__=new wt(T)}return b.set(l,u),this.size=b.size,this}Ht.prototype.clear=un,Ht.prototype.delete=Us,Ht.prototype.get=Ps,Ht.prototype.has=$s,Ht.prototype.set=Fs;function Hs(l,u){var b=Ie(l),T=!b&&Vn(l),Q=!b&&!T&&Re(l),Y=!b&&!T&&!Q&&Zn(l),st=b||T||Q||Y,nt=st?bs(l.length,String):[],ht=nt.length;for(var ot in l)qt.call(l,ot)&&!(st&&(ot=="length"||Q&&(ot=="offset"||ot=="parent")||Y&&(ot=="buffer"||ot=="byteLength"||ot=="byteOffset")||Ys(ot,ht)))&&nt.push(ot);return nt}function Oe(l,u){for(var b=l.length;b--;)if(zn(l[b][0],u))return b;return-1}function hn(l,u,b){var T=u(l);return Ie(l)?T:Je(T,b(l))}function me(l){return l==null?l===void 0?F:k:Ot&&Ot in Object(l)?Qt(l):Hn(l)}function $n(l){return Vt(l)&&me(l)==c}function Fn(l,u,b,T,Q){return l===u?!0:l==null||u==null||!Vt(l)&&!Vt(u)?l!==l&&u!==u:zs(l,u,b,T,Fn,Q)}function zs(l,u,b,T,Q,Y){var st=Ie(l),nt=Ie(u),ht=st?f:zt(l),ot=nt?f:zt(u);ht=ht==c?R:ht,ot=ot==c?R:ot;var vt=ht==R,Tt=ot==R,ft=ht==ot;if(ft&&Re(l)){if(!Re(u))return!1;st=!0,vt=!1}if(ft&&!vt)return Y||(Y=new Ht),st||Zn(l)?fn(l,u,b,T,Q,Y):Zs(l,u,ht,b,T,Q,Y);if(!(b&s)){var yt=vt&&qt.call(l,"__wrapped__"),gt=Tt&&qt.call(u,"__wrapped__");if(yt||gt){var ie=yt?l.value():l,Jt=gt?u.value():u;return Y||(Y=new Ht),Q(ie,Jt,b,T,Y)}}return ft?(Y||(Y=new Ht),Ws(l,u,b,T,Q,Y)):!1}function Vs(l){if(!Gn(l)||Js(l))return!1;var u=Kn(l)?Es:W;return u.test(At(l))}function Ks(l){return Vt(l)&&gn(l.length)&&!!j[me(l)]}function Gs(l){if(!tr(l))return ge(l);var u=[];for(var b in Object(l))qt.call(l,b)&&b!="constructor"&&u.push(b);return u}function fn(l,u,b,T,Q,Y){var st=b&s,nt=l.length,ht=u.length;if(nt!=ht&&!(st&&ht>nt))return!1;var ot=Y.get(l);if(ot&&Y.get(u))return ot==u;var vt=-1,Tt=!0,ft=b&i?new _e:void 0;for(Y.set(l,u),Y.set(u,l);++vt<nt;){var yt=l[vt],gt=u[vt];if(T)var ie=st?T(gt,yt,vt,u,l,Y):T(yt,gt,vt,l,u,Y);if(ie!==void 0){if(ie)continue;Tt=!1;break}if(ft){if(!On(u,function(Jt,ve){if(!Te(ft,ve)&&(yt===Jt||Q(yt,Jt,b,T,Y)))return ft.push(ve)})){Tt=!1;break}}else if(!(yt===gt||Q(yt,gt,b,T,Y))){Tt=!1;break}}return Y.delete(l),Y.delete(u),Tt}function Zs(l,u,b,T,Q,Y,st){switch(b){case A:if(l.byteLength!=u.byteLength||l.byteOffset!=u.byteOffset)return!1;l=l.buffer,u=u.buffer;case h:return!(l.byteLength!=u.byteLength||!Y(new Dn(l),new Dn(u)));case g:case v:case q:return zn(+l,+u);case d:return l.name==u.name&&l.message==u.message;case N:case U:return l==u+"";case w:var nt=In;case D:var ht=T&s;if(nt||(nt=Ft),l.size!=u.size&&!ht)return!1;var ot=st.get(l);if(ot)return ot==u;T|=i,st.set(l,u);var vt=fn(nt(l),nt(u),T,Q,Y,st);return st.delete(l),vt;case M:if(cn)return cn.call(l)==cn.call(u)}return!1}function Ws(l,u,b,T,Q,Y){var st=b&s,nt=be(l),ht=nt.length,ot=be(u),vt=ot.length;if(ht!=vt&&!st)return!1;for(var Tt=ht;Tt--;){var ft=nt[Tt];if(!(st?ft in u:qt.call(u,ft)))return!1}var yt=Y.get(l);if(yt&&Y.get(u))return yt==u;var gt=!0;Y.set(l,u),Y.set(u,l);for(var ie=st;++Tt<ht;){ft=nt[Tt];var Jt=l[ft],ve=u[ft];if(T)var Kr=st?T(ve,Jt,ft,u,l,Y):T(Jt,ve,ft,l,u,Y);if(!(Kr===void 0?Jt===ve||Q(Jt,ve,b,T,Y):Kr)){gt=!1;break}ie||(ie=ft=="constructor")}if(gt&&!ie){var Wn=l.constructor,Xn=u.constructor;Wn!=Xn&&"constructor"in l&&"constructor"in u&&!(typeof Wn=="function"&&Wn instanceof Wn&&typeof Xn=="function"&&Xn instanceof Xn)&&(gt=!1)}return Y.delete(l),Y.delete(u),gt}function be(l){return hn(l,pn,Xs)}function It(l,u){var b=l.__data__;return Qs(u)?b[typeof u=="string"?"string":"hash"]:b.map}function Yt(l,u){var b=tn(l,u);return Vs(b)?b:void 0}function Qt(l){var u=qt.call(l,Ot),b=l[Ot];try{l[Ot]=void 0;var T=!0}catch{}var Q=Bn.call(l);return T&&(u?l[Ot]=b:delete l[Ot]),Q}var Xs=ke?function(l){return l==null?[]:(l=Object(l),mt(ke(l),function(u){return Se.call(l,u)}))}:nr,zt=me;(rn&&zt(new rn(new ArrayBuffer(1)))!=A||pe&&zt(new pe)!=w||ln&&zt(ln.resolve())!=m||on&&zt(new on)!=D||an&&zt(new an)!=V)&&(zt=function(l){var u=me(l),b=u==R?l.constructor:void 0,T=b?At(b):"";if(T)switch(T){case jn:return A;case Xt:return w;case Ns:return m;case As:return D;case ws:return V}return u});function Ys(l,u){return u=u??o,!!u&&(typeof l=="number"||X.test(l))&&l>-1&&l%1==0&&l<u}function Qs(l){var u=typeof l;return u=="string"||u=="number"||u=="symbol"||u=="boolean"?l!=="__proto__":l===null}function Js(l){return!!Mn&&Mn in l}function tr(l){var u=l&&l.constructor,b=typeof u=="function"&&u.prototype||fe;return l===b}function Hn(l){return Bn.call(l)}function At(l){if(l!=null){try{return en.call(l)}catch{}try{return l+""}catch{}}return""}function zn(l,u){return l===u||l!==l&&u!==u}var Vn=$n(function(){return arguments}())?$n:function(l){return Vt(l)&&qt.call(l,"callee")&&!Se.call(l,"callee")},Ie=Array.isArray;function dn(l){return l!=null&&gn(l.length)&&!Kn(l)}var Re=sn||sr;function er(l,u){return Fn(l,u)}function Kn(l){if(!Gn(l))return!1;var u=me(l);return u==p||u==E||u==a||u==S}function gn(l){return typeof l=="number"&&l>-1&&l%1==0&&l<=o}function Gn(l){var u=typeof l;return l!=null&&(u=="object"||u=="function")}function Vt(l){return l!=null&&typeof l=="object"}var Zn=ut?vs(ut):Ks;function pn(l){return dn(l)?Hs(l):Gs(l)}function nr(){return[]}function sr(){return!1}r.exports=er}(yn,yn.exports)),yn.exports}var Qn={},ti;function Yl(){if(ti)return Qn;ti=1,Object.defineProperty(Qn,"__esModule",{value:!0});const r=fs(),t=ds();var e;return function(n){function s(f={},a={},g=!1){typeof f!="object"&&(f={}),typeof a!="object"&&(a={});let v=r(a);g||(v=Object.keys(v).reduce((d,p)=>(v[p]!=null&&(d[p]=v[p]),d),{}));for(const d in f)f[d]!==void 0&&a[d]===void 0&&(v[d]=f[d]);return Object.keys(v).length>0?v:void 0}n.compose=s;function i(f={},a={}){typeof f!="object"&&(f={}),typeof a!="object"&&(a={});const g=Object.keys(f).concat(Object.keys(a)).reduce((v,d)=>(t(f[d],a[d])||(v[d]=a[d]===void 0?null:a[d]),v),{});return Object.keys(g).length>0?g:void 0}n.diff=i;function o(f={},a={}){f=f||{};const g=Object.keys(a).reduce((v,d)=>(a[d]!==f[d]&&f[d]!==void 0&&(v[d]=a[d]),v),{});return Object.keys(f).reduce((v,d)=>(f[d]!==a[d]&&a[d]===void 0&&(v[d]=null),v),g)}n.invert=o;function c(f,a,g=!1){if(typeof f!="object")return a;if(typeof a!="object")return;if(!g)return a;const v=Object.keys(a).reduce((d,p)=>(f[p]===void 0&&(d[p]=a[p]),d),{});return Object.keys(v).length>0?v:void 0}n.transform=c}(e||(e={})),Qn.default=e,Qn}var Jn={},ei;function Pi(){if(ei)return Jn;ei=1,Object.defineProperty(Jn,"__esModule",{value:!0});var r;return function(t){function e(n){return typeof n.delete=="number"?n.delete:typeof n.retain=="number"?n.retain:typeof n.retain=="object"&&n.retain!==null?1:typeof n.insert=="string"?n.insert.length:1}t.length=e}(r||(r={})),Jn.default=r,Jn}var ts={},ni;function Ql(){if(ni)return ts;ni=1,Object.defineProperty(ts,"__esModule",{value:!0});const r=Pi();class t{constructor(n){this.ops=n,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(n){n||(n=1/0);const s=this.ops[this.index];if(s){const i=this.offset,o=r.default.length(s);if(n>=o-i?(n=o-i,this.index+=1,this.offset=0):this.offset+=n,typeof s.delete=="number")return{delete:n};{const c={};return s.attributes&&(c.attributes=s.attributes),typeof s.retain=="number"?c.retain=n:typeof s.retain=="object"&&s.retain!==null?c.retain=s.retain:typeof s.insert=="string"?c.insert=s.insert.substr(i,n):c.insert=s.insert,c}}else return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?r.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const n=this.ops[this.index];return n?typeof n.delete=="number"?"delete":typeof n.retain=="number"||typeof n.retain=="object"&&n.retain!==null?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);{const n=this.offset,s=this.index,i=this.next(),o=this.ops.slice(this.index);return this.offset=n,this.index=s,[i].concat(o)}}else return[]}}return ts.default=t,ts}var si;function Jl(){return si||(si=1,function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.AttributeMap=t.OpIterator=t.Op=void 0;const e=Xl(),n=fs(),s=ds(),i=Yl();t.AttributeMap=i.default;const o=Pi();t.Op=o.default;const c=Ql();t.OpIterator=c.default;const f="\0",a=(v,d)=>{if(typeof v!="object"||v===null)throw new Error(`cannot retain a ${typeof v}`);if(typeof d!="object"||d===null)throw new Error(`cannot retain a ${typeof d}`);const p=Object.keys(v)[0];if(!p||p!==Object.keys(d)[0])throw new Error(`embed types not matched: ${p} != ${Object.keys(d)[0]}`);return[p,v[p],d[p]]};class g{constructor(d){Array.isArray(d)?this.ops=d:d!=null&&Array.isArray(d.ops)?this.ops=d.ops:this.ops=[]}static registerEmbed(d,p){this.handlers[d]=p}static unregisterEmbed(d){delete this.handlers[d]}static getHandler(d){const p=this.handlers[d];if(!p)throw new Error(`no handlers for embed type "${d}"`);return p}insert(d,p){const E={};return typeof d=="string"&&d.length===0?this:(E.insert=d,p!=null&&typeof p=="object"&&Object.keys(p).length>0&&(E.attributes=p),this.push(E))}delete(d){return d<=0?this:this.push({delete:d})}retain(d,p){if(typeof d=="number"&&d<=0)return this;const E={retain:d};return p!=null&&typeof p=="object"&&Object.keys(p).length>0&&(E.attributes=p),this.push(E)}push(d){let p=this.ops.length,E=this.ops[p-1];if(d=n(d),typeof E=="object"){if(typeof d.delete=="number"&&typeof E.delete=="number")return this.ops[p-1]={delete:E.delete+d.delete},this;if(typeof E.delete=="number"&&d.insert!=null&&(p-=1,E=this.ops[p-1],typeof E!="object"))return this.ops.unshift(d),this;if(s(d.attributes,E.attributes)){if(typeof d.insert=="string"&&typeof E.insert=="string")return this.ops[p-1]={insert:E.insert+d.insert},typeof d.attributes=="object"&&(this.ops[p-1].attributes=d.attributes),this;if(typeof d.retain=="number"&&typeof E.retain=="number")return this.ops[p-1]={retain:E.retain+d.retain},typeof d.attributes=="object"&&(this.ops[p-1].attributes=d.attributes),this}}return p===this.ops.length?this.ops.push(d):this.ops.splice(p,0,d),this}chop(){const d=this.ops[this.ops.length-1];return d&&typeof d.retain=="number"&&!d.attributes&&this.ops.pop(),this}filter(d){return this.ops.filter(d)}forEach(d){this.ops.forEach(d)}map(d){return this.ops.map(d)}partition(d){const p=[],E=[];return this.forEach(w=>{(d(w)?p:E).push(w)}),[p,E]}reduce(d,p){return this.ops.reduce(d,p)}changeLength(){return this.reduce((d,p)=>p.insert?d+o.default.length(p):p.delete?d-p.delete:d,0)}length(){return this.reduce((d,p)=>d+o.default.length(p),0)}slice(d=0,p=1/0){const E=[],w=new c.default(this.ops);let q=0;for(;q<p&&w.hasNext();){let k;q<d?k=w.next(d-q):(k=w.next(p-q),E.push(k)),q+=o.default.length(k)}return new g(E)}compose(d){const p=new c.default(this.ops),E=new c.default(d.ops),w=[],q=E.peek();if(q!=null&&typeof q.retain=="number"&&q.attributes==null){let R=q.retain;for(;p.peekType()==="insert"&&p.peekLength()<=R;)R-=p.peekLength(),w.push(p.next());q.retain-R>0&&E.next(q.retain-R)}const k=new g(w);for(;p.hasNext()||E.hasNext();)if(E.peekType()==="insert")k.push(E.next());else if(p.peekType()==="delete")k.push(p.next());else{const R=Math.min(p.peekLength(),E.peekLength()),m=p.next(R),S=E.next(R);if(S.retain){const N={};if(typeof m.retain=="number")N.retain=typeof S.retain=="number"?R:S.retain;else if(typeof S.retain=="number")m.retain==null?N.insert=m.insert:N.retain=m.retain;else{const U=m.retain==null?"insert":"retain",[M,F,V]=a(m[U],S.retain),h=g.getHandler(M);N[U]={[M]:h.compose(F,V,U==="retain")}}const D=i.default.compose(m.attributes,S.attributes,typeof m.retain=="number");if(D&&(N.attributes=D),k.push(N),!E.hasNext()&&s(k.ops[k.ops.length-1],N)){const U=new g(p.rest());return k.concat(U).chop()}}else typeof S.delete=="number"&&(typeof m.retain=="number"||typeof m.retain=="object"&&m.retain!==null)&&k.push(S)}return k.chop()}concat(d){const p=new g(this.ops.slice());return d.ops.length>0&&(p.push(d.ops[0]),p.ops=p.ops.concat(d.ops.slice(1))),p}diff(d,p){if(this.ops===d.ops)return new g;const E=[this,d].map(m=>m.map(S=>{if(S.insert!=null)return typeof S.insert=="string"?S.insert:f;const N=m===d?"on":"with";throw new Error("diff() called "+N+" non-document")}).join("")),w=new g,q=e(E[0],E[1],p,!0),k=new c.default(this.ops),R=new c.default(d.ops);return q.forEach(m=>{let S=m[1].length;for(;S>0;){let N=0;switch(m[0]){case e.INSERT:N=Math.min(R.peekLength(),S),w.push(R.next(N));break;case e.DELETE:N=Math.min(S,k.peekLength()),k.next(N),w.delete(N);break;case e.EQUAL:N=Math.min(k.peekLength(),R.peekLength(),S);const D=k.next(N),U=R.next(N);s(D.insert,U.insert)?w.retain(N,i.default.diff(D.attributes,U.attributes)):w.push(U).delete(N);break}S-=N}}),w.chop()}eachLine(d,p=`
`){const E=new c.default(this.ops);let w=new g,q=0;for(;E.hasNext();){if(E.peekType()!=="insert")return;const k=E.peek(),R=o.default.length(k)-E.peekLength(),m=typeof k.insert=="string"?k.insert.indexOf(p,R)-R:-1;if(m<0)w.push(E.next());else if(m>0)w.push(E.next(m));else{if(d(w,E.next(1).attributes||{},q)===!1)return;q+=1,w=new g}}w.length()>0&&d(w,{},q)}invert(d){const p=new g;return this.reduce((E,w)=>{if(w.insert)p.delete(o.default.length(w));else{if(typeof w.retain=="number"&&w.attributes==null)return p.retain(w.retain),E+w.retain;if(w.delete||typeof w.retain=="number"){const q=w.delete||w.retain;return d.slice(E,E+q).forEach(R=>{w.delete?p.push(R):w.retain&&w.attributes&&p.retain(o.default.length(R),i.default.invert(w.attributes,R.attributes))}),E+q}else if(typeof w.retain=="object"&&w.retain!==null){const q=d.slice(E,E+1),k=new c.default(q.ops).next(),[R,m,S]=a(w.retain,k.insert),N=g.getHandler(R);return p.retain({[R]:N.invert(m,S)},i.default.invert(w.attributes,k.attributes)),E+1}}return E},0),p.chop()}transform(d,p=!1){if(p=!!p,typeof d=="number")return this.transformPosition(d,p);const E=d,w=new c.default(this.ops),q=new c.default(E.ops),k=new g;for(;w.hasNext()||q.hasNext();)if(w.peekType()==="insert"&&(p||q.peekType()!=="insert"))k.retain(o.default.length(w.next()));else if(q.peekType()==="insert")k.push(q.next());else{const R=Math.min(w.peekLength(),q.peekLength()),m=w.next(R),S=q.next(R);if(m.delete)continue;if(S.delete)k.push(S);else{const N=m.retain,D=S.retain;let U=typeof D=="object"&&D!==null?D:R;if(typeof N=="object"&&N!==null&&typeof D=="object"&&D!==null){const M=Object.keys(N)[0];if(M===Object.keys(D)[0]){const F=g.getHandler(M);F&&(U={[M]:F.transform(N[M],D[M],p)})}}k.retain(U,i.default.transform(m.attributes,S.attributes,p))}}return k.chop()}transformPosition(d,p=!1){p=!!p;const E=new c.default(this.ops);let w=0;for(;E.hasNext()&&w<=d;){const q=E.peekLength(),k=E.peekType();if(E.next(),k==="delete"){d-=Math.min(q,d-w);continue}else k==="insert"&&(w<d||!p)&&(d+=q);w+=q}return d}}g.Op=o.default,g.OpIterator=c.default,g.AttributeMap=i.default,g.handlers={},t.default=g,r.exports=g,r.exports.default=g}(Yn,Yn.exports)),Yn.exports}var Ct=Jl();const G=Or(Ct);class Pt extends xt{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}Pt.blotName="break";Pt.tagName="BR";let jt=class extends cs{};function gs(r){return r.replace(/[&<>"']/g,t=>({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"})[t])}const Kt=class Kt extends Rr{static compare(t,e){const n=Kt.order.indexOf(t),s=Kt.order.indexOf(e);return n>=0||s>=0?n-s:t===e?0:t<e?-1:1}formatAt(t,e,n,s){if(Kt.compare(this.statics.blotName,n)<0&&this.scroll.query(n,Z.BLOT)){const i=this.isolate(t,e);s&&i.wrap(n,s)}else super.formatAt(t,e,n,s)}optimize(t){if(super.optimize(t),this.parent instanceof Kt&&Kt.compare(this.statics.blotName,this.parent.statics.blotName)>0){const e=this.parent.isolate(this.offset(),this.length());this.moveChildren(e),e.wrap(this)}}};$(Kt,"allowedChildren",[Kt,Pt,xt,jt]),$(Kt,"order",["cursor","inline","link","underline","strike","italic","bold","script","code"]);let Wt=Kt;const ri=1;class pt extends Tn{constructor(){super(...arguments);$(this,"cache",{})}delta(){return this.cache.delta==null&&(this.cache.delta=$i(this)),this.cache.delta}deleteAt(e,n){super.deleteAt(e,n),this.cache={}}formatAt(e,n,s,i){n<=0||(this.scroll.query(s,Z.BLOCK)?e+n===this.length()&&this.format(s,i):super.formatAt(e,Math.min(n,this.length()-e-1),s,i),this.cache={})}insertAt(e,n,s){if(s!=null){super.insertAt(e,n,s),this.cache={};return}if(n.length===0)return;const i=n.split(`
`),o=i.shift();o.length>0&&(e<this.length()-1||this.children.tail==null?super.insertAt(Math.min(e,this.length()-1),o):this.children.tail.insertAt(this.children.tail.length(),o),this.cache={});let c=this;i.reduce((f,a)=>(c=c.split(f,!0),c.insertAt(0,a),a.length),e+o.length)}insertBefore(e,n){const{head:s}=this.children;super.insertBefore(e,n),s instanceof Pt&&s.remove(),this.cache={}}length(){return this.cache.length==null&&(this.cache.length=super.length()+ri),this.cache.length}moveChildren(e,n){super.moveChildren(e,n),this.cache={}}optimize(e){super.optimize(e),this.cache={}}path(e){return super.path(e,!0)}removeChild(e){super.removeChild(e),this.cache={}}split(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(n&&(e===0||e>=this.length()-ri)){const i=this.clone();return e===0?(this.parent.insertBefore(i,this),this):(this.parent.insertBefore(i,this.next),i)}const s=super.split(e,n);return this.cache={},s}}pt.blotName="block";pt.tagName="P";pt.defaultChild=Pt;pt.allowedChildren=[Pt,Wt,xt,jt];class _t extends xt{attach(){super.attach(),this.attributes=new us(this.domNode)}delta(){return new G().insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){const n=this.scroll.query(t,Z.BLOCK_ATTRIBUTE);n!=null&&this.attributes.attribute(n,e)}formatAt(t,e,n,s){this.format(n,s)}insertAt(t,e,n){if(n!=null){super.insertAt(t,e,n);return}const s=e.split(`
`),i=s.pop(),o=s.map(f=>{const a=this.scroll.create(pt.blotName);return a.insertAt(0,f),a}),c=this.split(t);o.forEach(f=>{this.parent.insertBefore(f,c)}),i&&this.parent.insertBefore(this.scroll.create("text",i),c)}}_t.scope=Z.BLOCK_BLOT;function $i(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return r.descendants(Et).reduce((e,n)=>n.length()===0?e:e.insert(n.value(),St(n,{},t)),new G).insert(`
`,St(r))}function St(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return r==null||("formats"in r&&typeof r.formats=="function"&&(t={...t,...r.formats()},e&&delete t["code-token"]),r.parent==null||r.parent.statics.blotName==="scroll"||r.parent.statics.scope!==r.statics.scope)?t:St(r.parent,t,e)}const Lt=class Lt extends xt{static value(){}constructor(t,e,n){super(t,e),this.selection=n,this.textNode=document.createTextNode(Lt.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){this.parent!=null&&this.parent.removeChild(this)}format(t,e){if(this.savedLength!==0){super.format(t,e);return}let n=this,s=0;for(;n!=null&&n.statics.scope!==Z.BLOCK_BLOT;)s+=n.offset(n.parent),n=n.parent;n!=null&&(this.savedLength=Lt.CONTENTS.length,n.optimize(),n.formatAt(s,Lt.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||this.parent==null)return null;const t=this.selection.getNativeRange();for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);const e=this.prev instanceof jt?this.prev:null,n=e?e.length():0,s=this.next instanceof jt?this.next:null,i=s?s.text:"",{textNode:o}=this,c=o.data.split(Lt.CONTENTS).join("");o.data=Lt.CONTENTS;let f;if(e)f=e,(c||s)&&(e.insertAt(e.length(),c+i),s&&s.remove());else if(s)f=s,s.insertAt(0,c);else{const a=document.createTextNode(c);f=this.scroll.create(a),this.parent.insertBefore(f,this)}if(this.remove(),t){const a=(d,p)=>e&&d===e.domNode?p:d===o?n+p-1:s&&d===s.domNode?n+c.length+p:null,g=a(t.start.node,t.start.offset),v=a(t.end.node,t.end.offset);if(g!==null&&v!==null)return{startNode:f.domNode,startOffset:g,endNode:f.domNode,endOffset:v}}return null}update(t,e){if(t.some(n=>n.type==="characterData"&&n.target===this.textNode)){const n=this.restore();n&&(e.range=n)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if(e.domNode.tagName==="A"){this.savedLength=Lt.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return""}};$(Lt,"blotName","cursor"),$(Lt,"className","ql-cursor"),$(Lt,"tagName","span"),$(Lt,"CONTENTS","\uFEFF");let Xe=Lt;var or={exports:{}},ii;function to(){return ii||(ii=1,function(r){var t=Object.prototype.hasOwnProperty,e="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(e=!1));function s(f,a,g){this.fn=f,this.context=a,this.once=g||!1}function i(f,a,g,v,d){if(typeof g!="function")throw new TypeError("The listener must be a function");var p=new s(g,v||f,d),E=e?e+a:a;return f._events[E]?f._events[E].fn?f._events[E]=[f._events[E],p]:f._events[E].push(p):(f._events[E]=p,f._eventsCount++),f}function o(f,a){--f._eventsCount===0?f._events=new n:delete f._events[a]}function c(){this._events=new n,this._eventsCount=0}c.prototype.eventNames=function(){var a=[],g,v;if(this._eventsCount===0)return a;for(v in g=this._events)t.call(g,v)&&a.push(e?v.slice(1):v);return Object.getOwnPropertySymbols?a.concat(Object.getOwnPropertySymbols(g)):a},c.prototype.listeners=function(a){var g=e?e+a:a,v=this._events[g];if(!v)return[];if(v.fn)return[v.fn];for(var d=0,p=v.length,E=new Array(p);d<p;d++)E[d]=v[d].fn;return E},c.prototype.listenerCount=function(a){var g=e?e+a:a,v=this._events[g];return v?v.fn?1:v.length:0},c.prototype.emit=function(a,g,v,d,p,E){var w=e?e+a:a;if(!this._events[w])return!1;var q=this._events[w],k=arguments.length,R,m;if(q.fn){switch(q.once&&this.removeListener(a,q.fn,void 0,!0),k){case 1:return q.fn.call(q.context),!0;case 2:return q.fn.call(q.context,g),!0;case 3:return q.fn.call(q.context,g,v),!0;case 4:return q.fn.call(q.context,g,v,d),!0;case 5:return q.fn.call(q.context,g,v,d,p),!0;case 6:return q.fn.call(q.context,g,v,d,p,E),!0}for(m=1,R=new Array(k-1);m<k;m++)R[m-1]=arguments[m];q.fn.apply(q.context,R)}else{var S=q.length,N;for(m=0;m<S;m++)switch(q[m].once&&this.removeListener(a,q[m].fn,void 0,!0),k){case 1:q[m].fn.call(q[m].context);break;case 2:q[m].fn.call(q[m].context,g);break;case 3:q[m].fn.call(q[m].context,g,v);break;case 4:q[m].fn.call(q[m].context,g,v,d);break;default:if(!R)for(N=1,R=new Array(k-1);N<k;N++)R[N-1]=arguments[N];q[m].fn.apply(q[m].context,R)}}return!0},c.prototype.on=function(a,g,v){return i(this,a,g,v,!1)},c.prototype.once=function(a,g,v){return i(this,a,g,v,!0)},c.prototype.removeListener=function(a,g,v,d){var p=e?e+a:a;if(!this._events[p])return this;if(!g)return o(this,p),this;var E=this._events[p];if(E.fn)E.fn===g&&(!d||E.once)&&(!v||E.context===v)&&o(this,p);else{for(var w=0,q=[],k=E.length;w<k;w++)(E[w].fn!==g||d&&!E[w].once||v&&E[w].context!==v)&&q.push(E[w]);q.length?this._events[p]=q.length===1?q[0]:q:o(this,p)}return this},c.prototype.removeAllListeners=function(a){var g;return a?(g=e?e+a:a,this._events[g]&&o(this,g)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=e,c.EventEmitter=c,r.exports=c}(or)),or.exports}var eo=to();const no=Or(eo),Nr=new WeakMap,Ar=["error","warn","log","info"];let wr="warn";function Fi(r){if(wr&&Ar.indexOf(r)<=Ar.indexOf(wr)){for(var t=arguments.length,e=new Array(t>1?t-1:0),n=1;n<t;n++)e[n-1]=arguments[n];console[r](...e)}}function se(r){return Ar.reduce((t,e)=>(t[e]=Fi.bind(console,e,r),t),{})}se.level=r=>{wr=r};Fi.level=se.level;const ar=se("quill:events"),so=["selectionchange","mousedown","mouseup","click"];so.forEach(r=>{document.addEventListener(r,function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];Array.from(document.querySelectorAll(".ql-container")).forEach(s=>{const i=Nr.get(s);i&&i.emitter&&i.emitter.handleDOM(...e)})})});class z extends no{constructor(){super(),this.domListeners={},this.on("error",ar.error)}emit(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return ar.log.call(ar,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),s=1;s<e;s++)n[s-1]=arguments[s];(this.domListeners[t.type]||[]).forEach(i=>{let{node:o,handler:c}=i;(t.target===o||o.contains(t.target))&&c(t,...n)})}listenDOM(t,e,n){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:n})}}$(z,"events",{EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"}),$(z,"sources",{API:"api",SILENT:"silent",USER:"user"});const cr=se("quill:selection");class Ee{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;this.index=t,this.length=e}}class ro{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new Ee(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{!this.mouseDown&&!this.composing&&setTimeout(this.update.bind(this,z.sources.USER),1)}),this.emitter.on(z.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;const n=this.getNativeRange();n!=null&&n.start.node!==this.cursor.textNode&&this.emitter.once(z.events.SCROLL_UPDATE,(s,i)=>{try{this.root.contains(n.start.node)&&this.root.contains(n.end.node)&&this.setNativeRange(n.start.node,n.start.offset,n.end.node,n.end.offset);const o=i.some(c=>c.type==="characterData"||c.type==="childList"||c.type==="attributes"&&c.target===this.root);this.update(o?z.sources.SILENT:s)}catch{}})}),this.emitter.on(z.events.SCROLL_OPTIMIZE,(n,s)=>{if(s.range){const{startNode:i,startOffset:o,endNode:c,endOffset:f}=s.range;this.setNativeRange(i,o,c,f),this.update(z.sources.SILENT)}}),this.update(z.sources.SILENT)}handleComposition(){this.emitter.on(z.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(z.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){const t=this.cursor.restore();if(!t)return;setTimeout(()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(z.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();const n=this.getNativeRange();if(!(n==null||!n.native.collapsed||this.scroll.query(t,Z.BLOCK))){if(n.start.node!==this.cursor.textNode){const s=this.scroll.find(n.start.node,!1);if(s==null)return;if(s instanceof Et){const i=s.split(n.start.offset);s.parent.insertBefore(this.cursor,i)}else s.insertBefore(this.cursor,n.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;const n=this.scroll.length();t=Math.min(t,n-1),e=Math.min(t+e,n-1)-t;let s,[i,o]=this.scroll.leaf(t);if(i==null)return null;if(e>0&&o===i.length()){const[g]=this.scroll.leaf(t+1);if(g){const[v]=this.scroll.line(t),[d]=this.scroll.line(t+1);v===d&&(i=g,o=0)}}[s,o]=i.position(o,!0);const c=document.createRange();if(e>0)return c.setStart(s,o),[i,o]=this.scroll.leaf(t+e),i==null?null:([s,o]=i.position(o,!0),c.setEnd(s,o),c.getBoundingClientRect());let f="left",a;if(s instanceof Text){if(!s.data.length)return null;o<s.data.length?(c.setStart(s,o),c.setEnd(s,o+1)):(c.setStart(s,o-1),c.setEnd(s,o),f="right"),a=c.getBoundingClientRect()}else{if(!(i.domNode instanceof Element))return null;a=i.domNode.getBoundingClientRect(),o>0&&(f="right")}return{bottom:a.top+a.height,height:a.height,left:a[f],right:a[f],top:a.top,width:0}}getNativeRange(){const t=document.getSelection();if(t==null||t.rangeCount<=0)return null;const e=t.getRangeAt(0);if(e==null)return null;const n=this.normalizeNative(e);return cr.info("getNativeRange",n),n}getRange(){const t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];const e=this.getNativeRange();return e==null?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||document.activeElement!=null&&ur(this.root,document.activeElement)}normalizedToRange(t){const e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);const n=e.map(o=>{const[c,f]=o,a=this.scroll.find(c,!0),g=a.offset(this.scroll);return f===0?g:a instanceof Et?g+a.index(c,f):g+a.length()}),s=Math.min(Math.max(...n),this.scroll.length()-1),i=Math.min(s,...n);return new Ee(i,s-i)}normalizeNative(t){if(!ur(this.root,t.startContainer)||!t.collapsed&&!ur(this.root,t.endContainer))return null;const e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(n=>{let{node:s,offset:i}=n;for(;!(s instanceof Text)&&s.childNodes.length>0;)if(s.childNodes.length>i)s=s.childNodes[i],i=0;else if(s.childNodes.length===i)s=s.lastChild,s instanceof Text?i=s.data.length:s.childNodes.length>0?i=s.childNodes.length:i=s.childNodes.length+1;else break;n.node=s,n.offset=i}),e}rangeToNative(t){const e=this.scroll.length(),n=(s,i)=>{s=Math.min(e-1,s);const[o,c]=this.scroll.leaf(s);return o?o.position(c,i):[null,-1]};return[...n(t.index,!1),...n(t.index+t.length,!0)]}setNativeRange(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t,s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:e,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(cr.info("setNativeRange",t,e,n,s),t!=null&&(this.root.parentNode==null||t.parentNode==null||n.parentNode==null))return;const o=document.getSelection();if(o!=null)if(t!=null){this.hasFocus()||this.root.focus({preventScroll:!0});const{native:c}=this.getNativeRange()||{};if(c==null||i||t!==c.startContainer||e!==c.startOffset||n!==c.endContainer||s!==c.endOffset){t instanceof Element&&t.tagName==="BR"&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),n instanceof Element&&n.tagName==="BR"&&(s=Array.from(n.parentNode.childNodes).indexOf(n),n=n.parentNode);const f=document.createRange();f.setStart(t,e),f.setEnd(n,s),o.removeAllRanges(),o.addRange(f)}}else o.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:z.sources.API;if(typeof e=="string"&&(n=e,e=!1),cr.info("setRange",t),t!=null){const s=this.rangeToNative(t);this.setNativeRange(...s,e)}else this.setNativeRange(null);this.update(n)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:z.sources.USER;const e=this.lastRange,[n,s]=this.getRange();if(this.lastRange=n,this.lastNative=s,this.lastRange!=null&&(this.savedRange=this.lastRange),!Ir(e,this.lastRange)){if(!this.composing&&s!=null&&s.native.collapsed&&s.start.node!==this.cursor.textNode){const o=this.cursor.restore();o&&this.setNativeRange(o.startNode,o.startOffset,o.endNode,o.endOffset)}const i=[z.events.SELECTION_CHANGE,Ke(this.lastRange),Ke(e),t];this.emitter.emit(z.events.EDITOR_CHANGE,...i),t!==z.sources.SILENT&&this.emitter.emit(...i)}}}function ur(r,t){try{t.parentNode}catch{return!1}return r.contains(t)}const io=/^[ -~]*$/;class lo{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();const n=li(t),s=new G;return ao(n.ops.slice()).reduce((o,c)=>{const f=Ct.Op.length(c);let a=c.attributes||{},g=!1,v=!1;if(c.insert!=null){if(s.retain(f),typeof c.insert=="string"){const E=c.insert;v=!E.endsWith(`
`)&&(e<=o||!!this.scroll.descendant(_t,o)[0]),this.scroll.insertAt(o,E);const[w,q]=this.scroll.line(o);let k=ue({},St(w));if(w instanceof pt){const[R]=w.descendant(Et,q);R&&(k=ue(k,St(R)))}a=Ct.AttributeMap.diff(k,a)||{}}else if(typeof c.insert=="object"){const E=Object.keys(c.insert)[0];if(E==null)return o;const w=this.scroll.query(E,Z.INLINE)!=null;if(w)(e<=o||this.scroll.descendant(_t,o)[0])&&(v=!0);else if(o>0){const[q,k]=this.scroll.descendant(Et,o-1);q instanceof jt?q.value()[k]!==`
`&&(g=!0):q instanceof xt&&q.statics.scope===Z.INLINE_BLOT&&(g=!0)}if(this.scroll.insertAt(o,E,c.insert[E]),w){const[q]=this.scroll.descendant(Et,o);if(q){const k=ue({},St(q));a=Ct.AttributeMap.diff(k,a)||{}}}}e+=f}else if(s.push(c),c.retain!==null&&typeof c.retain=="object"){const E=Object.keys(c.retain)[0];if(E==null)return o;this.scroll.updateEmbedAt(o,E,c.retain[E])}Object.keys(a).forEach(E=>{this.scroll.formatAt(o,f,E,a[E])});const d=g?1:0,p=v?1:0;return e+=d+p,s.retain(d),s.delete(p),o+f+d+p},0),s.reduce((o,c)=>typeof c.delete=="number"?(this.scroll.deleteAt(o,c.delete),o):o+Ct.Op.length(c),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(n)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update(new G().retain(t).delete(e))}formatLine(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.scroll.update(),Object.keys(n).forEach(i=>{this.scroll.lines(t,Math.max(e,1)).forEach(o=>{o.format(i,n[i])})}),this.scroll.optimize();const s=new G().retain(t).retain(e,Ke(n));return this.update(s)}formatText(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Object.keys(n).forEach(i=>{this.scroll.formatAt(t,e,i,n[i])});const s=new G().retain(t).retain(e,Ke(n));return this.update(s)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce((t,e)=>t.concat(e.delta()),new G)}getFormat(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=[],s=[];e===0?this.scroll.path(t).forEach(c=>{const[f]=c;f instanceof pt?n.push(f):f instanceof Et&&s.push(f)}):(n=this.scroll.lines(t,e),s=this.scroll.descendants(Et,t,e));const[i,o]=[n,s].map(c=>{const f=c.shift();if(f==null)return{};let a=St(f);for(;Object.keys(a).length>0;){const g=c.shift();if(g==null)return a;a=oo(St(g),a)}return a});return{...i,...o}}getHTML(t,e){const[n,s]=this.scroll.line(t);if(n){const i=n.length();return n.length()>=s+e&&!(s===0&&e===i)?xn(n,s,e,!0):xn(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter(n=>typeof n.insert=="string").map(n=>n.insert).join("")}insertContents(t,e){const n=li(e),s=new G().retain(t).concat(n);return this.scroll.insertContents(t,n),this.update(s)}insertEmbed(t,e,n){return this.scroll.insertAt(t,e,n),this.update(new G().retain(t).insert({[e]:n}))}insertText(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return e=e.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(t,e),Object.keys(n).forEach(s=>{this.scroll.formatAt(t,e.length,s,n[s])}),this.update(new G().retain(t).insert(e,Ke(n)))}isBlank(){if(this.scroll.children.length===0)return!0;if(this.scroll.children.length>1)return!1;const t=this.scroll.children.head;if((t==null?void 0:t.statics.blotName)!==pt.blotName)return!1;const e=t;return e.children.length>1?!1:e.children.head instanceof Pt}removeFormat(t,e){const n=this.getText(t,e),[s,i]=this.scroll.line(t+e);let o=0,c=new G;s!=null&&(o=s.length()-i,c=s.delta().slice(i,i+o-1).insert(`
`));const a=this.getContents(t,e+o).diff(new G().insert(n).concat(c)),g=new G().retain(t).concat(a);return this.applyDelta(g)}update(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;const s=this.delta;if(e.length===1&&e[0].type==="characterData"&&e[0].target.data.match(io)&&this.scroll.find(e[0].target)){const i=this.scroll.find(e[0].target),o=St(i),c=i.offset(this.scroll),f=e[0].oldValue.replace(Xe.CONTENTS,""),a=new G().insert(f),g=new G().insert(i.value()),v=n&&{oldRange:oi(n.oldRange,-c),newRange:oi(n.newRange,-c)};t=new G().retain(c).concat(a.diff(g,v)).reduce((p,E)=>E.insert?p.insert(E.insert,o):p.push(E),new G),this.delta=s.compose(t)}else this.delta=this.getDelta(),(!t||!Ir(s.compose(t),this.delta))&&(t=s.diff(this.delta,n));return t}}function He(r,t,e){if(r.length===0){const[p]=hr(e.pop());return t<=0?`</li></${p}>`:`</li></${p}>${He([],t-1,e)}`}const[{child:n,offset:s,length:i,indent:o,type:c},...f]=r,[a,g]=hr(c);if(o>t)return e.push(c),o===t+1?`<${a}><li${g}>${xn(n,s,i)}${He(f,o,e)}`:`<${a}><li>${He(r,t+1,e)}`;const v=e[e.length-1];if(o===t&&c===v)return`</li><li${g}>${xn(n,s,i)}${He(f,o,e)}`;const[d]=hr(e.pop());return`</li></${d}>${He(r,t-1,e)}`}function xn(r,t,e){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if("html"in r&&typeof r.html=="function")return r.html(t,e);if(r instanceof jt)return gs(r.value().slice(t,t+e));if(r instanceof Dt){if(r.statics.blotName==="list-container"){const a=[];return r.children.forEachAt(t,e,(g,v,d)=>{const p="formats"in g&&typeof g.formats=="function"?g.formats():{};a.push({child:g,offset:v,length:d,indent:p.indent||0,type:p.list})}),He(a,-1,[])}const s=[];if(r.children.forEachAt(t,e,(a,g,v)=>{s.push(xn(a,g,v))}),n||r.statics.blotName==="list")return s.join("");const{outerHTML:i,innerHTML:o}=r.domNode,[c,f]=i.split(`>${o}<`);return c==="<table"?`<table style="border: 1px solid #000;">${s.join("")}<${f}`:`${c}>${s.join("")}<${f}`}return r.domNode instanceof Element?r.domNode.outerHTML:""}function oo(r,t){return Object.keys(t).reduce((e,n)=>{if(r[n]==null)return e;const s=t[n];return s===r[n]?e[n]=s:Array.isArray(s)?s.indexOf(r[n])<0?e[n]=s.concat([r[n]]):e[n]=s:e[n]=[s,r[n]],e},{})}function hr(r){const t=r==="ordered"?"ol":"ul";switch(r){case"checked":return[t,' data-list="checked"'];case"unchecked":return[t,' data-list="unchecked"'];default:return[t,""]}}function li(r){return r.reduce((t,e)=>{if(typeof e.insert=="string"){const n=e.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return t.insert(n,e.attributes)}return t.push(e)},new G)}function oi(r,t){let{index:e,length:n}=r;return new Ee(e+t,n)}function ao(r){const t=[];return r.forEach(e=>{typeof e.insert=="string"?e.insert.split(`
`).forEach((s,i)=>{i&&t.push({insert:`
`,attributes:e.attributes}),s&&t.push({insert:s,attributes:e.attributes})}):t.push(e)}),t}class $t{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.quill=t,this.options=e}}$($t,"DEFAULTS",{});const es="\uFEFF";class Br extends xt{constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(n=>{this.contentNode.appendChild(n)}),this.leftGuard=document.createTextNode(es),this.rightGuard=document.createTextNode(es),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e=null,n;const s=t.data.split(es).join("");if(t===this.leftGuard)if(this.prev instanceof jt){const i=this.prev.length();this.prev.insertAt(i,s),e={startNode:this.prev.domNode,startOffset:i+s.length}}else n=document.createTextNode(s),this.parent.insertBefore(this.scroll.create(n),this),e={startNode:n,startOffset:s.length};else t===this.rightGuard&&(this.next instanceof jt?(this.next.insertAt(0,s),e={startNode:this.next.domNode,startOffset:s.length}):(n=document.createTextNode(s),this.parent.insertBefore(this.scroll.create(n),this.next),e={startNode:n,startOffset:s.length}));return t.data=es,e}update(t,e){t.forEach(n=>{if(n.type==="characterData"&&(n.target===this.leftGuard||n.target===this.rightGuard)){const s=this.restore(n.target);s&&(e.range=s)}})}}class co{constructor(t,e){$(this,"isComposing",!1);this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",t=>{this.isComposing||this.handleCompositionStart(t)}),this.scroll.domNode.addEventListener("compositionend",t=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(t)})})}handleCompositionStart(t){const e=t.target instanceof Node?this.scroll.find(t.target,!0):null;e&&!(e instanceof Br)&&(this.emitter.emit(z.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(z.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(z.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(z.events.COMPOSITION_END,t),this.isComposing=!1}}const An=class An{constructor(t,e){$(this,"modules",{});this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach(t=>{this.modules[t]==null&&this.addModule(t)})}addModule(t){const e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}};$(An,"DEFAULTS",{modules:{}}),$(An,"themes",{default:An});let Ye=An;const uo=r=>r.parentElement||r.getRootNode().host||null,ho=r=>{const t=r.getBoundingClientRect(),e="offsetWidth"in r&&Math.abs(t.width)/r.offsetWidth||1,n="offsetHeight"in r&&Math.abs(t.height)/r.offsetHeight||1;return{top:t.top,right:t.left+r.clientWidth*e,bottom:t.top+r.clientHeight*n,left:t.left}},ns=r=>{const t=parseInt(r,10);return Number.isNaN(t)?0:t},ai=(r,t,e,n,s,i)=>r<e&&t>n?0:r<e?-(e-r+s):t>n?t-r>n-e?r+s-e:t-n+i:0,fo=(r,t)=>{var i,o,c;const e=r.ownerDocument;let n=t,s=r;for(;s;){const f=s===e.body,a=f?{top:0,right:((i=window.visualViewport)==null?void 0:i.width)??e.documentElement.clientWidth,bottom:((o=window.visualViewport)==null?void 0:o.height)??e.documentElement.clientHeight,left:0}:ho(s),g=getComputedStyle(s),v=ai(n.left,n.right,a.left,a.right,ns(g.scrollPaddingLeft),ns(g.scrollPaddingRight)),d=ai(n.top,n.bottom,a.top,a.bottom,ns(g.scrollPaddingTop),ns(g.scrollPaddingBottom));if(v||d)if(f)(c=e.defaultView)==null||c.scrollBy(v,d);else{const{scrollLeft:p,scrollTop:E}=s;d&&(s.scrollTop+=d),v&&(s.scrollLeft+=v);const w=s.scrollLeft-p,q=s.scrollTop-E;n={left:n.left-w,top:n.top-q,right:n.right-w,bottom:n.bottom-q}}s=f||g.position==="fixed"?null:uo(s)}},go=100,po=["block","break","cursor","inline","scroll","text"],mo=(r,t,e)=>{const n=new We;return po.forEach(s=>{const i=t.query(s);i&&n.register(i)}),r.forEach(s=>{let i=t.query(s);i||e.error(`Cannot register "${s}" specified in "formats" config. Are you sure it was registered?`);let o=0;for(;i;)if(n.register(i),i="blotName"in i?i.requiredContainer??null:null,o+=1,o>go){e.error(`Cycle detected in registering blot requiredContainer: "${s}"`);break}}),n},Ze=se("quill"),ss=new We;Dt.uiClass="ql-ui";const Mt=class Mt{static debug(t){t===!0&&(t="log"),se.level(t)}static find(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return Nr.get(t)||ss.find(t,e)}static import(t){return this.imports[t]==null&&Ze.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if(typeof(arguments.length<=0?void 0:arguments[0])!="string"){const t=arguments.length<=0?void 0:arguments[0],e=!!(!(arguments.length<=1)&&arguments[1]),n="attrName"in t?t.attrName:t.blotName;typeof n=="string"?this.register(`formats/${n}`,t,e):Object.keys(t).forEach(s=>{this.register(s,t[s],e)})}else{const t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],n=!!(!(arguments.length<=2)&&arguments[2]);this.imports[t]!=null&&!n&&Ze.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&typeof e!="boolean"&&e.blotName!=="abstract"&&ss.register(e),typeof e.register=="function"&&e.register(ss)}}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.options=bo(t,e),this.container=this.options.container,this.container==null){Ze.error("Invalid Quill container",t);return}this.options.debug&&Mt.debug(this.options.debug);const n=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",Nr.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new z;const s=Mr.blotName,i=this.options.registry.query(s);if(!i||!("blotName"in i))throw new Error(`Cannot initialize Quill without "${s}" blot`);if(this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new lo(this.scroll),this.selection=new ro(this.scroll,this.emitter),this.composition=new co(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(z.events.EDITOR_CHANGE,o=>{o===z.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(z.events.SCROLL_UPDATE,(o,c)=>{const f=this.selection.lastRange,[a]=this.selection.getRange(),g=f&&a?{oldRange:f,newRange:a}:void 0;Rt.call(this,()=>this.editor.update(null,c,g),o)}),this.emitter.on(z.events.SCROLL_EMBED_UPDATE,(o,c)=>{const f=this.selection.lastRange,[a]=this.selection.getRange(),g=f&&a?{oldRange:f,newRange:a}:void 0;Rt.call(this,()=>{const v=new G().retain(o.offset(this)).retain({[o.statics.blotName]:c});return this.editor.update(v,[],g)},Mt.sources.USER)}),n){const o=this.clipboard.convert({html:`${n}<p><br></p>`,text:`
`});this.setContents(o)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof t=="string"){const n=t;t=document.createElement("div"),t.classList.add(n)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,n){return[t,e,,n]=te(t,e,n),Rt.call(this,()=>this.editor.deleteText(t,e),n,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;const e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:z.sources.API;return Rt.call(this,()=>{const s=this.getSelection(!0);let i=new G;if(s==null)return i;if(this.scroll.query(t,Z.BLOCK))i=this.editor.formatLine(s.index,s.length,{[t]:e});else{if(s.length===0)return this.selection.format(t,e),i;i=this.editor.formatText(s.index,s.length,{[t]:e})}return this.setSelection(s,z.sources.SILENT),i},n)}formatLine(t,e,n,s,i){let o;return[t,e,o,i]=te(t,e,n,s,i),Rt.call(this,()=>this.editor.formatLine(t,e,o),i,t,0)}formatText(t,e,n,s,i){let o;return[t,e,o,i]=te(t,e,n,s,i),Rt.call(this,()=>this.editor.formatText(t,e,o),i,t,0)}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=null;if(typeof t=="number"?n=this.selection.getBounds(t,e):n=this.selection.getBounds(t.index,t.length),!n)return null;const s=this.container.getBoundingClientRect();return{bottom:n.bottom-s.top,height:n.height,left:n.left-s.left,right:n.right-s.left,top:n.top-s.top,width:n.width}}getContents(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-t;return[t,e]=te(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof t=="number"?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof t!="number"?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=te(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=te(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:Mt.sources.API;return Rt.call(this,()=>this.editor.insertEmbed(t,e,n),s,t)}insertText(t,e,n,s,i){let o;return[t,,o,i]=te(t,0,n,s,i),Rt.call(this,()=>this.editor.insertText(t,e,o),i,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,n){return[t,e,,n]=te(t,e,n),Rt.call(this,()=>this.editor.removeFormat(t,e),n,t)}scrollRectIntoView(t){fo(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){const t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:z.sources.API;return Rt.call(this,()=>{t=new G(t);const n=this.getLength(),s=this.editor.deleteText(0,n),i=this.editor.insertContents(0,t),o=this.editor.deleteText(this.getLength()-1,1);return s.compose(i).compose(o)},e)}setSelection(t,e,n){t==null?this.selection.setRange(null,e||Mt.sources.API):([t,e,,n]=te(t,e,n),this.selection.setRange(new Ee(Math.max(0,t),e),n),n!==z.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:z.sources.API;const n=new G().insert(t);return this.setContents(n,e)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:z.sources.USER;const e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:z.sources.API;return Rt.call(this,()=>(t=new G(t),this.editor.applyDelta(t)),e,!0)}};$(Mt,"DEFAULTS",{bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:ss,theme:"default"}),$(Mt,"events",z.events),$(Mt,"sources",z.sources),$(Mt,"version","2.0.2"),$(Mt,"imports",{delta:G,parchment:Wl,"core/module":$t,"core/theme":Ye});let L=Mt;function ci(r){return typeof r=="string"?document.querySelector(r):r}function fr(r){return Object.entries(r??{}).reduce((t,e)=>{let[n,s]=e;return{...t,[n]:s===!0?{}:s}},{})}function ui(r){return Object.fromEntries(Object.entries(r).filter(t=>t[1]!==void 0))}function bo(r,t){const e=ci(r);if(!e)throw new Error("Invalid Quill container");const s=!t.theme||t.theme===L.DEFAULTS.theme?Ye:L.import(`themes/${t.theme}`);if(!s)throw new Error(`Invalid theme ${t.theme}. Did you register it?`);const{modules:i,...o}=L.DEFAULTS,{modules:c,...f}=s.DEFAULTS;let a=fr(t.modules);a!=null&&a.toolbar&&a.toolbar.constructor!==Object&&(a={...a,toolbar:{container:a.toolbar}});const g=ue({},fr(i),fr(c),a),v={...o,...ui(f),...ui(t)};let d=t.registry;return d?t.formats&&Ze.warn('Ignoring "formats" option because "registry" is specified'):d=t.formats?mo(t.formats,v.registry,Ze):v.registry,{...v,registry:d,container:e,theme:s,modules:Object.entries(g).reduce((p,E)=>{let[w,q]=E;if(!q)return p;const k=L.import(`modules/${w}`);return k==null?(Ze.error(`Cannot load ${w} module. Are you sure you registered it?`),p):{...p,[w]:ue({},k.DEFAULTS||{},q)}},{}),bounds:ci(v.bounds)}}function Rt(r,t,e,n){if(!this.isEnabled()&&t===z.sources.USER&&!this.allowReadOnlyEdits)return new G;let s=e==null?null:this.getSelection();const i=this.editor.delta,o=r();if(s!=null&&(e===!0&&(e=s.index),n==null?s=hi(s,o,t):n!==0&&(s=hi(s,e,n,t)),this.setSelection(s,z.sources.SILENT)),o.length()>0){const c=[z.events.TEXT_CHANGE,o,i,t];this.emitter.emit(z.events.EDITOR_CHANGE,...c),t!==z.sources.SILENT&&this.emitter.emit(...c)}return o}function te(r,t,e,n,s){let i={};return typeof r.index=="number"&&typeof r.length=="number"?typeof t!="number"?(s=n,n=e,e=t,t=r.length,r=r.index):(t=r.length,r=r.index):typeof t!="number"&&(s=n,n=e,e=t,t=0),typeof e=="object"?(i=e,s=n):typeof e=="string"&&(n!=null?i[e]=n:s=e),s=s||z.sources.API,[r,t,i,s]}function hi(r,t,e,n){const s=typeof e=="number"?e:0;if(r==null)return null;let i,o;return t&&typeof t.transformPosition=="function"?[i,o]=[r.index,r.index+r.length].map(c=>t.transformPosition(c,n!==z.sources.USER)):[i,o]=[r.index,r.index+r.length].map(c=>c<t||c===t&&n===z.sources.USER?c:s>=0?c+s:Math.max(t,c+s)),new Ee(i,o-i)}class Ne extends hs{}function fi(r){return r instanceof pt||r instanceof _t}function di(r){return typeof r.updateContent=="function"}class ze extends Mr{constructor(t,e,n){let{emitter:s}=n;super(t,e),this.emitter=s,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",i=>this.handleDragStart(i))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;const t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(z.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(z.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit(z.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){const[n,s]=this.line(t),[i]=this.line(t+e);if(super.deleteAt(t,e),i!=null&&n!==i&&s>0){if(n instanceof _t||i instanceof _t){this.optimize();return}const o=i.children.head instanceof Pt?null:i.children.head;n.moveChildren(i,o),n.remove()}this.optimize()}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,e,n,s){super.formatAt(t,e,n,s),this.optimize()}insertAt(t,e,n){if(t>=this.length())if(n==null||this.scroll.query(e,Z.BLOCK)==null){const s=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(s),n==null&&e.endsWith(`
`)?s.insertAt(0,e.slice(0,-1),n):s.insertAt(0,e,n)}else{const s=this.scroll.create(e,n);this.appendChild(s)}else super.insertAt(t,e,n);this.optimize()}insertBefore(t,e){if(t.statics.scope===Z.INLINE_BLOT){const n=this.scroll.create(this.statics.defaultChild.blotName);n.appendChild(t),super.insertBefore(n,e)}else super.insertBefore(t,e)}insertContents(t,e){const n=this.deltaToRenderBlocks(e.concat(new G().insert(`
`))),s=n.pop();if(s==null)return;this.batchStart();const i=n.shift();if(i){const f=i.type==="block"&&(i.delta.length()===0||!this.descendant(_t,t)[0]&&t<this.length()),a=i.type==="block"?i.delta:new G().insert({[i.key]:i.value});dr(this,t,a);const g=i.type==="block"?1:0,v=t+a.length()+g;f&&this.insertAt(v-1,`
`);const d=St(this.line(t)[0]),p=Ct.AttributeMap.diff(d,i.attributes)||{};Object.keys(p).forEach(E=>{this.formatAt(v-1,1,E,p[E])}),t=v}let[o,c]=this.children.find(t);if(n.length&&(o&&(o=o.split(c),c=0),n.forEach(f=>{if(f.type==="block"){const a=this.createBlock(f.attributes,o||void 0);dr(a,0,f.delta)}else{const a=this.create(f.key,f.value);this.insertBefore(a,o||void 0),Object.keys(f.attributes).forEach(g=>{a.format(g,f.attributes[g])})}})),s.type==="block"&&s.delta.length()){const f=o?o.offset(o.scroll)+c:this.length();dr(this,f,s.delta)}this.batchEnd(),this.optimize()}isEnabled(){return this.domNode.getAttribute("contenteditable")==="true"}leaf(t){const e=this.path(t).pop();if(!e)return[null,-1];const[n,s]=e;return n instanceof Et?[n,s]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(fi,t)}lines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;const n=(s,i,o)=>{let c=[],f=o;return s.children.forEachAt(i,o,(a,g,v)=>{fi(a)?c.push(a):a instanceof hs&&(c=c.concat(n(a,g,f))),f-=v}),c};return n(this,t,e)}optimize(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch||(super.optimize(t,e),t.length>0&&this.emitter.emit(z.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch){Array.isArray(t)&&(this.batch=this.batch.concat(t));return}let e=z.sources.USER;typeof t=="string"&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),t=t.filter(n=>{let{target:s}=n;const i=this.find(s,!0);return i&&!di(i)}),t.length>0&&this.emitter.emit(z.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(z.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,n){const[s]=this.descendant(i=>i instanceof _t,t);s&&s.statics.blotName===e&&di(s)&&s.updateContent(n)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){const e=[];let n=new G;return t.forEach(s=>{const i=s==null?void 0:s.insert;if(i)if(typeof i=="string"){const o=i.split(`
`);o.slice(0,-1).forEach(f=>{n.insert(f,s.attributes),e.push({type:"block",delta:n,attributes:s.attributes??{}}),n=new G});const c=o[o.length-1];c&&n.insert(c,s.attributes)}else{const o=Object.keys(i)[0];if(!o)return;this.query(o,Z.INLINE)?n.push(s):(n.length()&&e.push({type:"block",delta:n,attributes:{}}),n=new G,e.push({type:"blockEmbed",key:o,value:i[o],attributes:s.attributes??{}}))}}),n.length()&&e.push({type:"block",delta:n,attributes:{}}),e}createBlock(t,e){let n;const s={};Object.entries(t).forEach(c=>{let[f,a]=c;this.query(f,Z.BLOCK&Z.BLOT)!=null?n=f:s[f]=a});const i=this.create(n||this.statics.defaultChild.blotName,n?t[n]:void 0);this.insertBefore(i,e||void 0);const o=i.length();return Object.entries(s).forEach(c=>{let[f,a]=c;i.formatAt(0,o,f,a)}),i}}$(ze,"blotName","scroll"),$(ze,"className","ql-editor"),$(ze,"tagName","DIV"),$(ze,"defaultChild",pt),$(ze,"allowedChildren",[pt,_t,Ne]);function dr(r,t,e){e.reduce((n,s)=>{const i=Ct.Op.length(s);let o=s.attributes||{};if(s.insert!=null){if(typeof s.insert=="string"){const c=s.insert;r.insertAt(n,c);const[f]=r.descendant(Et,n),a=St(f);o=Ct.AttributeMap.diff(a,o)||{}}else if(typeof s.insert=="object"){const c=Object.keys(s.insert)[0];if(c==null)return n;if(r.insertAt(n,c,s.insert[c]),r.scroll.query(c,Z.INLINE)!=null){const[a]=r.descendant(Et,n),g=St(a);o=Ct.AttributeMap.diff(g,o)||{}}}}return Object.keys(o).forEach(c=>{r.formatAt(n,i,c,o[c])}),n+i},t)}const Dr={scope:Z.BLOCK,whitelist:["right","center","justify"]},vo=new Zt("align","align",Dr),Hi=new Ut("align","ql-align",Dr),zi=new he("align","text-align",Dr);class Vi extends he{value(t){let e=super.value(t);return e.startsWith("rgb(")?(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${e.split(",").map(s=>`00${parseInt(s,10).toString(16)}`.slice(-2)).join("")}`):e}}const yo=new Ut("color","ql-color",{scope:Z.INLINE}),jr=new Vi("color","color",{scope:Z.INLINE}),Eo=new Ut("background","ql-bg",{scope:Z.INLINE}),Ur=new Vi("background","background-color",{scope:Z.INLINE});class Ae extends Ne{static create(t){const e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map(n=>n.length()<=1?"":n.domNode.innerText).join(`
`).slice(t,t+e)}html(t,e){return`<pre>
${gs(this.code(t,e))}
</pre>`}}class Nt extends pt{static register(){L.register(Ae)}}$(Nt,"TAB","  ");class Pr extends Wt{}Pr.blotName="code";Pr.tagName="CODE";Nt.blotName="code-block";Nt.className="ql-code-block";Nt.tagName="DIV";Ae.blotName="code-block-container";Ae.className="ql-code-block-container";Ae.tagName="DIV";Ae.allowedChildren=[Nt];Nt.allowedChildren=[jt,Pt,Xe];Nt.requiredContainer=Ae;const $r={scope:Z.BLOCK,whitelist:["rtl"]},Ki=new Zt("direction","dir",$r),Gi=new Ut("direction","ql-direction",$r),Zi=new he("direction","direction",$r),Wi={scope:Z.INLINE,whitelist:["serif","monospace"]},Xi=new Ut("font","ql-font",Wi);class No extends he{value(t){return super.value(t).replace(/["']/g,"")}}const Yi=new No("font","font-family",Wi),Qi=new Ut("size","ql-size",{scope:Z.INLINE,whitelist:["small","large","huge"]}),Ji=new he("size","font-size",{scope:Z.INLINE,whitelist:["10px","18px","32px"]}),Ao=se("quill:keyboard"),wo=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class ps extends $t{static match(t,e){return["altKey","ctrlKey","metaKey","shiftKey"].some(n=>!!e[n]!==t[n]&&e[n]!==null)?!1:e.key===t.key||e.key===t.which}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach(n=>{this.options.bindings[n]&&this.addBinding(this.options.bindings[n])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const s=xo(t);if(s==null){Ao.warn("Attempted to add invalid keyboard binding",s);return}typeof e=="function"&&(e={handler:e}),typeof n=="function"&&(n={handler:n}),(Array.isArray(s.key)?s.key:[s.key]).forEach(o=>{const c={...s,key:o,...e,...n};this.bindings[c.key]=this.bindings[c.key]||[],this.bindings[c.key].push(c)})}listen(){this.quill.root.addEventListener("keydown",t=>{if(t.defaultPrevented||t.isComposing||t.keyCode===229&&(t.key==="Enter"||t.key==="Backspace"))return;const s=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter(k=>ps.match(t,k));if(s.length===0)return;const i=L.find(t.target,!0);if(i&&i.scroll!==this.quill.scroll)return;const o=this.quill.getSelection();if(o==null||!this.quill.hasFocus())return;const[c,f]=this.quill.getLine(o.index),[a,g]=this.quill.getLeaf(o.index),[v,d]=o.length===0?[a,g]:this.quill.getLeaf(o.index+o.length),p=a instanceof cs?a.value().slice(0,g):"",E=v instanceof cs?v.value().slice(d):"",w={collapsed:o.length===0,empty:o.length===0&&c.length()<=1,format:this.quill.getFormat(o),line:c,offset:f,prefix:p,suffix:E,event:t};s.some(k=>{if(k.collapsed!=null&&k.collapsed!==w.collapsed||k.empty!=null&&k.empty!==w.empty||k.offset!=null&&k.offset!==w.offset)return!1;if(Array.isArray(k.format)){if(k.format.every(R=>w.format[R]==null))return!1}else if(typeof k.format=="object"&&!Object.keys(k.format).every(R=>k.format[R]===!0?w.format[R]!=null:k.format[R]===!1?w.format[R]==null:Ir(k.format[R],w.format[R])))return!1;return k.prefix!=null&&!k.prefix.test(w.prefix)||k.suffix!=null&&!k.suffix.test(w.suffix)?!1:k.handler.call(this,o,w,k)!==!0})&&t.preventDefault()})}handleBackspace(t,e){const n=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(t.index===0||this.quill.getLength()<=1)return;let s={};const[i]=this.quill.getLine(t.index);let o=new G().retain(t.index-n).delete(n);if(e.offset===0){const[c]=this.quill.getLine(t.index-1);if(c&&!(c.statics.blotName==="block"&&c.length()<=1)){const a=i.formats(),g=this.quill.getFormat(t.index-1,1);if(s=Ct.AttributeMap.diff(a,g)||{},Object.keys(s).length>0){const v=new G().retain(t.index+i.length()-2).retain(1,s);o=o.compose(v)}}}this.quill.updateContents(o,L.sources.USER),this.quill.focus()}handleDelete(t,e){const n=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-n)return;let s={};const[i]=this.quill.getLine(t.index);let o=new G().retain(t.index).delete(n);if(e.offset>=i.length()-1){const[c]=this.quill.getLine(t.index+1);if(c){const f=i.formats(),a=this.quill.getFormat(t.index,1);s=Ct.AttributeMap.diff(f,a)||{},Object.keys(s).length>0&&(o=o.retain(c.length()-1).retain(1,s))}}this.quill.updateContents(o,L.sources.USER),this.quill.focus()}handleDeleteRange(t){Fr({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){const n=Object.keys(e.format).reduce((i,o)=>(this.quill.scroll.query(o,Z.BLOCK)&&!Array.isArray(e.format[o])&&(i[o]=e.format[o]),i),{}),s=new G().retain(t.index).delete(t.length).insert(`
`,n);this.quill.updateContents(s,L.sources.USER),this.quill.setSelection(t.index+1,L.sources.SILENT),this.quill.focus()}}const To={bindings:{bold:gr("bold"),italic:gr("italic"),underline:gr("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(r,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","+1",L.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(r,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","-1",L.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(r,t){t.format.indent!=null?this.quill.format("indent","-1",L.sources.USER):t.format.list!=null&&this.quill.format("list",!1,L.sources.USER)}},"indent code-block":gi(!0),"outdent code-block":gi(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(r){this.quill.deleteText(r.index-1,1,L.sources.USER)}},tab:{key:"Tab",handler(r,t){if(t.format.table)return!0;this.quill.history.cutoff();const e=new G().retain(r.index).delete(r.length).insert("	");return this.quill.updateContents(e,L.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(r.index+1,L.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,L.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(r,t){const e={list:!1};t.format.indent&&(e.indent=!1),this.quill.formatLine(r.index,r.length,e,L.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(r){const[t,e]=this.quill.getLine(r.index),n={...t.formats(),list:"checked"},s=new G().retain(r.index).insert(`
`,n).retain(t.length()-e-1).retain(1,{list:"unchecked"});this.quill.updateContents(s,L.sources.USER),this.quill.setSelection(r.index+1,L.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(r,t){const[e,n]=this.quill.getLine(r.index),s=new G().retain(r.index).insert(`
`,t.format).retain(e.length()-n-1).retain(1,{header:null});this.quill.updateContents(s,L.sources.USER),this.quill.setSelection(r.index+1,L.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(r){const t=this.quill.getModule("table");if(t){const[e,n,s,i]=t.getTable(r),o=qo(e,n,s,i);if(o==null)return;let c=e.offset();if(o<0){const f=new G().retain(c).insert(`
`);this.quill.updateContents(f,L.sources.USER),this.quill.setSelection(r.index+1,r.length,L.sources.SILENT)}else if(o>0){c+=e.length();const f=new G().retain(c).insert(`
`);this.quill.updateContents(f,L.sources.USER),this.quill.setSelection(c,L.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(r,t){const{event:e,line:n}=t,s=n.offset(this.quill.scroll);e.shiftKey?this.quill.setSelection(s-1,L.sources.USER):this.quill.setSelection(s+n.length(),L.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(r,t){if(this.quill.scroll.query("list")==null)return!0;const{length:e}=t.prefix,[n,s]=this.quill.getLine(r.index);if(s>e)return!0;let i;switch(t.prefix.trim()){case"[]":case"[ ]":i="unchecked";break;case"[x]":i="checked";break;case"-":case"*":i="bullet";break;default:i="ordered"}this.quill.insertText(r.index," ",L.sources.USER),this.quill.history.cutoff();const o=new G().retain(r.index-s).delete(e+1).retain(n.length()-2-s).retain(1,{list:i});return this.quill.updateContents(o,L.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(r.index-e,L.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(r){const[t,e]=this.quill.getLine(r.index);let n=2,s=t;for(;s!=null&&s.length()<=1&&s.formats()["code-block"];)if(s=s.prev,n-=1,n<=0){const i=new G().retain(r.index+t.length()-e-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(i,L.sources.USER),this.quill.setSelection(r.index-1,L.sources.SILENT),!1}return!0}},"embed left":rs("ArrowLeft",!1),"embed left shift":rs("ArrowLeft",!0),"embed right":rs("ArrowRight",!1),"embed right shift":rs("ArrowRight",!0),"table down":pi(!1),"table up":pi(!0)}};ps.DEFAULTS=To;function gi(r){return{key:"Tab",shiftKey:!r,format:{"code-block":!0},handler(t,e){let{event:n}=e;const s=this.quill.scroll.query("code-block"),{TAB:i}=s;if(t.length===0&&!n.shiftKey){this.quill.insertText(t.index,i,L.sources.USER),this.quill.setSelection(t.index+i.length,L.sources.SILENT);return}const o=t.length===0?this.quill.getLines(t.index,1):this.quill.getLines(t);let{index:c,length:f}=t;o.forEach((a,g)=>{r?(a.insertAt(0,i),g===0?c+=i.length:f+=i.length):a.domNode.textContent.startsWith(i)&&(a.deleteAt(0,i.length),g===0?c-=i.length:f-=i.length)}),this.quill.update(L.sources.USER),this.quill.setSelection(c,f,L.sources.SILENT)}}}function rs(r,t){return{key:r,shiftKey:t,altKey:null,[r==="ArrowLeft"?"prefix":"suffix"]:/^$/,handler(n){let{index:s}=n;r==="ArrowRight"&&(s+=n.length+1);const[i]=this.quill.getLeaf(s);return i instanceof xt?(r==="ArrowLeft"?t?this.quill.setSelection(n.index-1,n.length+1,L.sources.USER):this.quill.setSelection(n.index-1,L.sources.USER):t?this.quill.setSelection(n.index,n.length+1,L.sources.USER):this.quill.setSelection(n.index+n.length+1,L.sources.USER),!1):!0}}}function gr(r){return{key:r[0],shortKey:!0,handler(t,e){this.quill.format(r,!e.format[r],L.sources.USER)}}}function pi(r){return{key:r?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(t,e){const n=r?"prev":"next",s=e.line,i=s.parent[n];if(i!=null){if(i.statics.blotName==="table-row"){let o=i.children.head,c=s;for(;c.prev!=null;)c=c.prev,o=o.next;const f=o.offset(this.quill.scroll)+Math.min(e.offset,o.length()-1);this.quill.setSelection(f,0,L.sources.USER)}}else{const o=s.table()[n];o!=null&&(r?this.quill.setSelection(o.offset(this.quill.scroll)+o.length()-1,0,L.sources.USER):this.quill.setSelection(o.offset(this.quill.scroll),0,L.sources.USER))}return!1}}}function xo(r){if(typeof r=="string"||typeof r=="number")r={key:r};else if(typeof r=="object")r=Ke(r);else return null;return r.shortKey&&(r[wo]=r.shortKey,delete r.shortKey),r}function Fr(r){let{quill:t,range:e}=r;const n=t.getLines(e);let s={};if(n.length>1){const i=n[0].formats(),o=n[n.length-1].formats();s=Ct.AttributeMap.diff(o,i)||{}}t.deleteText(e,L.sources.USER),Object.keys(s).length>0&&t.formatLine(e.index,1,s,L.sources.USER),t.setSelection(e.index,L.sources.SILENT)}function qo(r,t,e,n){return t.prev==null&&t.next==null?e.prev==null&&e.next==null?n===0?-1:1:e.prev==null?-1:1:t.prev==null?-1:t.next==null?1:null}const Lo=/font-weight:\s*normal/,So=["P","OL","UL"],mi=r=>r&&So.includes(r.tagName),ko=r=>{Array.from(r.querySelectorAll("br")).filter(t=>mi(t.previousElementSibling)&&mi(t.nextElementSibling)).forEach(t=>{var e;(e=t.parentNode)==null||e.removeChild(t)})},_o=r=>{Array.from(r.querySelectorAll('b[style*="font-weight"]')).filter(t=>{var e;return(e=t.getAttribute("style"))==null?void 0:e.match(Lo)}).forEach(t=>{var n;const e=r.createDocumentFragment();e.append(...t.childNodes),(n=t.parentNode)==null||n.replaceChild(e,t)})};function Co(r){r.querySelector('[id^="docs-internal-guid-"]')&&(_o(r),ko(r))}const Oo=/\bmso-list:[^;]*ignore/i,Io=/\bmso-list:[^;]*\bl(\d+)/i,Ro=/\bmso-list:[^;]*\blevel(\d+)/i,Mo=(r,t)=>{const e=r.getAttribute("style"),n=e==null?void 0:e.match(Io);if(!n)return null;const s=Number(n[1]),i=e==null?void 0:e.match(Ro),o=i?Number(i[1]):1,c=new RegExp(`@list l${s}:level${o}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),f=t.match(c),a=f&&f[1]==="bullet"?"bullet":"ordered";return{id:s,indent:o,type:a,element:r}},Bo=r=>{var o,c;const t=Array.from(r.querySelectorAll("[style*=mso-list]")),e=[],n=[];t.forEach(f=>{(f.getAttribute("style")||"").match(Oo)?e.push(f):n.push(f)}),e.forEach(f=>{var a;return(a=f.parentNode)==null?void 0:a.removeChild(f)});const s=r.documentElement.innerHTML,i=n.map(f=>Mo(f,s)).filter(f=>f);for(;i.length;){const f=[];let a=i.shift();for(;a;)f.push(a),a=i.length&&((o=i[0])==null?void 0:o.element)===a.element.nextElementSibling&&i[0].id===a.id?i.shift():null;const g=document.createElement("ul");f.forEach(p=>{const E=document.createElement("li");E.setAttribute("data-list",p.type),p.indent>1&&E.setAttribute("class",`ql-indent-${p.indent-1}`),E.innerHTML=p.element.innerHTML,g.appendChild(E)});const v=(c=f[0])==null?void 0:c.element,{parentNode:d}=v??{};v&&(d==null||d.replaceChild(g,v)),f.slice(1).forEach(p=>{let{element:E}=p;d==null||d.removeChild(E)})}};function Do(r){r.documentElement.getAttribute("xmlns:w")==="urn:schemas-microsoft-com:office:word"&&Bo(r)}const jo=[Do,Co],Uo=r=>{r.documentElement&&jo.forEach(t=>{t(r)})},Po=se("quill:clipboard"),$o=[[Node.TEXT_NODE,Jo],[Node.TEXT_NODE,vi],["br",Ko],[Node.ELEMENT_NODE,vi],[Node.ELEMENT_NODE,Vo],[Node.ELEMENT_NODE,zo],[Node.ELEMENT_NODE,Yo],["li",Wo],["ol, ul",Xo],["pre",Go],["tr",Qo],["b",pr("bold")],["i",pr("italic")],["strike",pr("strike")],["style",Zo]],Fo=[vo,Ki].reduce((r,t)=>(r[t.keyName]=t,r),{}),bi=[zi,Ur,jr,Zi,Yi,Ji].reduce((r,t)=>(r[t.keyName]=t,r),{});class tl extends $t{constructor(t,e){super(t,e),this.quill.root.addEventListener("copy",n=>this.onCaptureCopy(n,!1)),this.quill.root.addEventListener("cut",n=>this.onCaptureCopy(n,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],$o.concat(this.options.matchers??[]).forEach(n=>{let[s,i]=n;this.addMatcher(s,i)})}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:n}=t,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(s[Nt.blotName])return new G().insert(n||"",{[Nt.blotName]:s[Nt.blotName]});if(!e)return new G().insert(n||"",s);const i=this.convertHTML(e);return Sn(i,`
`)&&(i.ops[i.ops.length-1].attributes==null||s.table)?i.compose(new G().retain(i.length()-1).delete(1)):i}normalizeHTML(t){Uo(t)}convertHTML(t){const e=new DOMParser().parseFromString(t,"text/html");this.normalizeHTML(e);const n=e.body,s=new WeakMap,[i,o]=this.prepareMatching(n,s);return Hr(this.quill.scroll,n,i,o,s)}dangerouslyPasteHTML(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:L.sources.API;if(typeof t=="string"){const s=this.convert({html:t,text:""});this.quill.setContents(s,e),this.quill.setSelection(0,L.sources.SILENT)}else{const s=this.convert({html:e,text:""});this.quill.updateContents(new G().retain(t).concat(s),n),this.quill.setSelection(t+s.length(),L.sources.SILENT)}}onCaptureCopy(t){var o,c;let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(t.defaultPrevented)return;t.preventDefault();const[n]=this.quill.selection.getRange();if(n==null)return;const{html:s,text:i}=this.onCopy(n,e);(o=t.clipboardData)==null||o.setData("text/plain",i),(c=t.clipboardData)==null||c.setData("text/html",s),e&&Fr({range:n,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(e=>e[0]!=="#").join(`
`)}onCapturePaste(t){var o,c,f,a,g;if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();const e=this.quill.getSelection(!0);if(e==null)return;const n=(o=t.clipboardData)==null?void 0:o.getData("text/html");let s=(c=t.clipboardData)==null?void 0:c.getData("text/plain");if(!n&&!s){const v=(f=t.clipboardData)==null?void 0:f.getData("text/uri-list");v&&(s=this.normalizeURIList(v))}const i=Array.from(((a=t.clipboardData)==null?void 0:a.files)||[]);if(!n&&i.length>0){this.quill.uploader.upload(e,i);return}if(n&&i.length>0){const v=new DOMParser().parseFromString(n,"text/html");if(v.body.childElementCount===1&&((g=v.body.firstElementChild)==null?void 0:g.tagName)==="IMG"){this.quill.uploader.upload(e,i);return}}this.onPaste(e,{html:n,text:s})}onCopy(t){const e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:n,html:s}=e;const i=this.quill.getFormat(t.index),o=this.convert({text:n,html:s},i);Po.log("onPaste",o,{text:n,html:s});const c=new G().retain(t.index).delete(t.length).concat(o);this.quill.updateContents(c,L.sources.USER),this.quill.setSelection(c.length()-t.length,L.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){const n=[],s=[];return this.matchers.forEach(i=>{const[o,c]=i;switch(o){case Node.TEXT_NODE:s.push(c);break;case Node.ELEMENT_NODE:n.push(c);break;default:Array.from(t.querySelectorAll(o)).forEach(f=>{if(e.has(f)){const a=e.get(f);a==null||a.push(c)}else e.set(f,[c])});break}}),[n,s]}}$(tl,"DEFAULTS",{matchers:[]});function we(r,t,e,n){return n.query(t)?r.reduce((s,i)=>{if(!i.insert)return s;if(i.attributes&&i.attributes[t])return s.push(i);const o=e?{[t]:e}:{};return s.insert(i.insert,{...o,...i.attributes})},new G):r}function Sn(r,t){let e="";for(let n=r.ops.length-1;n>=0&&e.length<t.length;--n){const s=r.ops[n];if(typeof s.insert!="string")break;e=s.insert+e}return e.slice(-1*t.length)===t}function ae(r,t){if(!(r instanceof Element))return!1;const e=t.query(r);return e&&e.prototype instanceof xt?!1:["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(r.tagName.toLowerCase())}function Ho(r,t){return r.previousElementSibling&&r.nextElementSibling&&!ae(r.previousElementSibling,t)&&!ae(r.nextElementSibling,t)}const is=new WeakMap;function el(r){return r==null?!1:(is.has(r)||(r.tagName==="PRE"?is.set(r,!0):is.set(r,el(r.parentNode))),is.get(r))}function Hr(r,t,e,n,s){return t.nodeType===t.TEXT_NODE?n.reduce((i,o)=>o(t,i,r),new G):t.nodeType===t.ELEMENT_NODE?Array.from(t.childNodes||[]).reduce((i,o)=>{let c=Hr(r,o,e,n,s);return o.nodeType===t.ELEMENT_NODE&&(c=e.reduce((f,a)=>a(o,f,r),c),c=(s.get(o)||[]).reduce((f,a)=>a(o,f,r),c)),i.concat(c)},new G):new G}function pr(r){return(t,e,n)=>we(e,r,!0,n)}function zo(r,t,e){const n=Zt.keys(r),s=Ut.keys(r),i=he.keys(r),o={};return n.concat(s).concat(i).forEach(c=>{let f=e.query(c,Z.ATTRIBUTE);f!=null&&(o[f.attrName]=f.value(r),o[f.attrName])||(f=Fo[c],f!=null&&(f.attrName===c||f.keyName===c)&&(o[f.attrName]=f.value(r)||void 0),f=bi[c],f!=null&&(f.attrName===c||f.keyName===c)&&(f=bi[c],o[f.attrName]=f.value(r)||void 0))}),Object.entries(o).reduce((c,f)=>{let[a,g]=f;return we(c,a,g,e)},t)}function Vo(r,t,e){const n=e.query(r);if(n==null)return t;if(n.prototype instanceof xt){const s={},i=n.value(r);if(i!=null)return s[n.blotName]=i,new G().insert(s,n.formats(r,e))}else if(n.prototype instanceof Tn&&!Sn(t,`
`)&&t.insert(`
`),"blotName"in n&&"formats"in n&&typeof n.formats=="function")return we(t,n.blotName,n.formats(r,e),e);return t}function Ko(r,t){return Sn(t,`
`)||t.insert(`
`),t}function Go(r,t,e){const n=e.query("code-block"),s=n&&"formats"in n&&typeof n.formats=="function"?n.formats(r,e):!0;return we(t,"code-block",s,e)}function Zo(){return new G}function Wo(r,t,e){const n=e.query(r);if(n==null||n.blotName!=="list"||!Sn(t,`
`))return t;let s=-1,i=r.parentNode;for(;i!=null;)["OL","UL"].includes(i.tagName)&&(s+=1),i=i.parentNode;return s<=0?t:t.reduce((o,c)=>c.insert?c.attributes&&typeof c.attributes.indent=="number"?o.push(c):o.insert(c.insert,{indent:s,...c.attributes||{}}):o,new G)}function Xo(r,t,e){const n=r;let s=n.tagName==="OL"?"ordered":"bullet";const i=n.getAttribute("data-checked");return i&&(s=i==="true"?"checked":"unchecked"),we(t,"list",s,e)}function vi(r,t,e){if(!Sn(t,`
`)){if(ae(r,e)&&(r.childNodes.length>0||r instanceof HTMLParagraphElement))return t.insert(`
`);if(t.length()>0&&r.nextSibling){let n=r.nextSibling;for(;n!=null;){if(ae(n,e))return t.insert(`
`);const s=e.query(n);if(s&&s.prototype instanceof _t)return t.insert(`
`);n=n.firstChild}}}return t}function Yo(r,t,e){var i;const n={},s=r.style||{};return s.fontStyle==="italic"&&(n.italic=!0),s.textDecoration==="underline"&&(n.underline=!0),s.textDecoration==="line-through"&&(n.strike=!0),((i=s.fontWeight)!=null&&i.startsWith("bold")||parseInt(s.fontWeight,10)>=700)&&(n.bold=!0),t=Object.entries(n).reduce((o,c)=>{let[f,a]=c;return we(o,f,a,e)},t),parseFloat(s.textIndent||0)>0?new G().insert("	").concat(t):t}function Qo(r,t,e){var s,i;const n=((s=r.parentElement)==null?void 0:s.tagName)==="TABLE"?r.parentElement:(i=r.parentElement)==null?void 0:i.parentElement;if(n!=null){const c=Array.from(n.querySelectorAll("tr")).indexOf(r)+1;return we(t,"table",c,e)}return t}function Jo(r,t,e){var s;let n=r.data;if(((s=r.parentElement)==null?void 0:s.tagName)==="O:P")return t.insert(n.trim());if(!el(r)){if(n.trim().length===0&&n.includes(`
`)&&!Ho(r,e))return t;const i=(o,c)=>{const f=c.replace(/[^\u00a0]/g,"");return f.length<1&&o?" ":f};n=n.replace(/\r\n/g," ").replace(/\n/g," "),n=n.replace(/\s\s+/g,i.bind(i,!0)),(r.previousSibling==null&&r.parentElement!=null&&ae(r.parentElement,e)||r.previousSibling instanceof Element&&ae(r.previousSibling,e))&&(n=n.replace(/^\s+/,i.bind(i,!1))),(r.nextSibling==null&&r.parentElement!=null&&ae(r.parentElement,e)||r.nextSibling instanceof Element&&ae(r.nextSibling,e))&&(n=n.replace(/\s+$/,i.bind(i,!1)))}return t.insert(n)}class nl extends $t{constructor(e,n){super(e,n);$(this,"lastRecorded",0);$(this,"ignoreChange",!1);$(this,"stack",{undo:[],redo:[]});$(this,"currentRange",null);this.quill.on(L.events.EDITOR_CHANGE,(s,i,o,c)=>{s===L.events.SELECTION_CHANGE?i&&c!==L.sources.SILENT&&(this.currentRange=i):s===L.events.TEXT_CHANGE&&(this.ignoreChange||(!this.options.userOnly||c===L.sources.USER?this.record(i,o):this.transform(i)),this.currentRange=Tr(this.currentRange,i))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",s=>{s.inputType==="historyUndo"?(this.undo(),s.preventDefault()):s.inputType==="historyRedo"&&(this.redo(),s.preventDefault())})}change(e,n){if(this.stack[e].length===0)return;const s=this.stack[e].pop();if(!s)return;const i=this.quill.getContents(),o=s.delta.invert(i);this.stack[n].push({delta:o,range:Tr(s.range,o)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(s.delta,L.sources.USER),this.ignoreChange=!1,this.restoreSelection(s)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(e,n){if(e.ops.length===0)return;this.stack.redo=[];let s=e.invert(n),i=this.currentRange;const o=Date.now();if(this.lastRecorded+this.options.delay>o&&this.stack.undo.length>0){const c=this.stack.undo.pop();c&&(s=s.compose(c.delta),i=c.range)}else this.lastRecorded=o;s.length()!==0&&(this.stack.undo.push({delta:s,range:i}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(e){yi(this.stack.undo,e),yi(this.stack.redo,e)}undo(){this.change("undo","redo")}restoreSelection(e){if(e.range)this.quill.setSelection(e.range,L.sources.USER);else{const n=ea(this.quill.scroll,e.delta);this.quill.setSelection(n,L.sources.USER)}}}$(nl,"DEFAULTS",{delay:1e3,maxStack:100,userOnly:!1});function yi(r,t){let e=t;for(let n=r.length-1;n>=0;n-=1){const s=r[n];r[n]={delta:e.transform(s.delta,!0),range:s.range&&Tr(s.range,e)},e=s.delta.transform(e),r[n].delta.length()===0&&r.splice(n,1)}}function ta(r,t){const e=t.ops[t.ops.length-1];return e==null?!1:e.insert!=null?typeof e.insert=="string"&&e.insert.endsWith(`
`):e.attributes!=null?Object.keys(e.attributes).some(n=>r.query(n,Z.BLOCK)!=null):!1}function ea(r,t){const e=t.reduce((s,i)=>s+(i.delete||0),0);let n=t.length()-e;return ta(r,t)&&(n-=1),n}function Tr(r,t){if(!r)return r;const e=t.transformPosition(r.index),n=t.transformPosition(r.index+r.length);return{index:e,length:n-e}}class sl extends $t{constructor(t,e){super(t,e),t.root.addEventListener("drop",n=>{var o;n.preventDefault();let s=null;if(document.caretRangeFromPoint)s=document.caretRangeFromPoint(n.clientX,n.clientY);else if(document.caretPositionFromPoint){const c=document.caretPositionFromPoint(n.clientX,n.clientY);s=document.createRange(),s.setStart(c.offsetNode,c.offset),s.setEnd(c.offsetNode,c.offset)}const i=s&&t.selection.normalizeNative(s);if(i){const c=t.selection.normalizedToRange(i);(o=n.dataTransfer)!=null&&o.files&&this.upload(c,n.dataTransfer.files)}})}upload(t,e){const n=[];Array.from(e).forEach(s=>{var i;s&&((i=this.options.mimetypes)!=null&&i.includes(s.type))&&n.push(s)}),n.length>0&&this.options.handler.call(this,t,n)}}sl.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(r,t){if(!this.quill.scroll.query("image"))return;const e=t.map(n=>new Promise(s=>{const i=new FileReader;i.onload=()=>{s(i.result)},i.readAsDataURL(n)}));Promise.all(e).then(n=>{const s=n.reduce((i,o)=>i.insert({image:o}),new G().retain(r.index).delete(r.length));this.quill.updateContents(s,z.sources.USER),this.quill.setSelection(r.index+n.length,z.sources.SILENT)})}};const na=["insertText","insertReplacementText"];class sa extends $t{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",n=>{this.handleBeforeInput(n)}),/Android/i.test(navigator.userAgent)||t.on(L.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(t){Fr({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(t.length===0)return!1;if(e){const n=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents(new G().retain(t.index).insert(e,n),L.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,L.sources.SILENT),!0}handleBeforeInput(t){if(this.quill.composition.isComposing||t.defaultPrevented||!na.includes(t.inputType))return;const e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!e||e.collapsed===!0)return;const n=ra(t);if(n==null)return;const s=this.quill.selection.normalizeNative(e),i=s?this.quill.selection.normalizedToRange(s):null;i&&this.replaceText(i,n)&&t.preventDefault()}handleCompositionStart(){const t=this.quill.getSelection();t&&this.replaceText(t)}}function ra(r){var t;return typeof r.data=="string"?r.data:(t=r.dataTransfer)!=null&&t.types.includes("text/plain")?r.dataTransfer.getData("text/plain"):null}const ia=/Mac/i.test(navigator.platform),la=100,oa=r=>!!(r.key==="ArrowLeft"||r.key==="ArrowRight"||r.key==="ArrowUp"||r.key==="ArrowDown"||r.key==="Home"||ia&&r.key==="a"&&r.ctrlKey===!0);class aa extends $t{constructor(e,n){super(e,n);$(this,"isListening",!1);$(this,"selectionChangeDeadline",0);this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(e,n){let{line:s,event:i}=n;if(!(s instanceof Dt)||!s.uiNode)return!0;const o=getComputedStyle(s.domNode).direction==="rtl";return o&&i.key!=="ArrowRight"||!o&&i.key!=="ArrowLeft"?!0:(this.quill.setSelection(e.index-1,e.length+(i.shiftKey?1:0),L.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",e=>{!e.defaultPrevented&&oa(e)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){if(this.selectionChangeDeadline=Date.now()+la,this.isListening)return;this.isListening=!0;const e=()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()};document.addEventListener("selectionchange",e,{once:!0})}handleSelectionChange(){const e=document.getSelection();if(!e)return;const n=e.getRangeAt(0);if(n.collapsed!==!0||n.startOffset!==0)return;const s=this.quill.scroll.find(n.startContainer);if(!(s instanceof Dt)||!s.uiNode)return;const i=document.createRange();i.setStartAfter(s.uiNode),i.setEndAfter(s.uiNode),e.removeAllRanges(),e.addRange(i)}}L.register({"blots/block":pt,"blots/block/embed":_t,"blots/break":Pt,"blots/container":Ne,"blots/cursor":Xe,"blots/embed":Br,"blots/inline":Wt,"blots/scroll":ze,"blots/text":jt,"modules/clipboard":tl,"modules/history":nl,"modules/keyboard":ps,"modules/uploader":sl,"modules/input":sa,"modules/uiNode":aa});class ca extends Ut{add(t,e){let n=0;if(e==="+1"||e==="-1"){const s=this.value(t)||0;n=e==="+1"?s+1:s-1}else typeof e=="number"&&(n=e);return n===0?(this.remove(t),!0):super.add(t,n.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}const ua=new ca("indent","ql-indent",{scope:Z.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});class xr extends pt{}$(xr,"blotName","blockquote"),$(xr,"tagName","blockquote");class qr extends pt{static formats(t){return this.tagName.indexOf(t.tagName)+1}}$(qr,"blotName","header"),$(qr,"tagName",["H1","H2","H3","H4","H5","H6"]);class kn extends Ne{}kn.blotName="list-container";kn.tagName="OL";class _n extends pt{static create(t){const e=super.create();return e.setAttribute("data-list",t),e}static formats(t){return t.getAttribute("data-list")||void 0}static register(){L.register(kn)}constructor(t,e){super(t,e);const n=e.ownerDocument.createElement("span"),s=i=>{if(!t.isEnabled())return;const o=this.statics.formats(e,t);o==="checked"?(this.format("list","unchecked"),i.preventDefault()):o==="unchecked"&&(this.format("list","checked"),i.preventDefault())};n.addEventListener("mousedown",s),n.addEventListener("touchstart",s),this.attachUI(n)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):super.format(t,e)}}_n.blotName="list";_n.tagName="LI";kn.allowedChildren=[_n];_n.requiredContainer=kn;class qn extends Wt{static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}$(qn,"blotName","bold"),$(qn,"tagName",["STRONG","B"]);class Lr extends qn{}$(Lr,"blotName","italic"),$(Lr,"tagName",["EM","I"]);class ce extends Wt{static create(t){const e=super.create(t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}static formats(t){return t.getAttribute("href")}static sanitize(t){return rl(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t!==this.statics.blotName||!e?super.format(t,e):this.domNode.setAttribute("href",this.constructor.sanitize(e))}}$(ce,"blotName","link"),$(ce,"tagName","A"),$(ce,"SANITIZED_URL","about:blank"),$(ce,"PROTOCOL_WHITELIST",["http","https","mailto","tel","sms"]);function rl(r,t){const e=document.createElement("a");e.href=r;const n=e.href.slice(0,e.href.indexOf(":"));return t.indexOf(n)>-1}class Sr extends Wt{static create(t){return t==="super"?document.createElement("sup"):t==="sub"?document.createElement("sub"):super.create(t)}static formats(t){if(t.tagName==="SUB")return"sub";if(t.tagName==="SUP")return"super"}}$(Sr,"blotName","script"),$(Sr,"tagName",["SUB","SUP"]);class kr extends qn{}$(kr,"blotName","strike"),$(kr,"tagName",["S","STRIKE"]);class _r extends Wt{}$(_r,"blotName","underline"),$(_r,"tagName","U");class os extends Br{static create(t){if(window.katex==null)throw new Error("Formula module requires KaTeX.");const e=super.create(t);return typeof t=="string"&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}static value(t){return t.getAttribute("data-value")}html(){const{formula:t}=this.value();return`<span>${t}</span>`}}$(os,"blotName","formula"),$(os,"className","ql-formula"),$(os,"tagName","SPAN");const Ei=["alt","height","width"];class Cr extends xt{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Ei.reduce((e,n)=>(t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return rl(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,e){Ei.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}}$(Cr,"blotName","image"),$(Cr,"tagName","IMG");const Ni=["height","width"];class as extends _t{static create(t){const e=super.create(t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen","true"),e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Ni.reduce((e,n)=>(t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e),{})}static sanitize(t){return ce.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,e){Ni.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){const{video:t}=this.value();return`<a href="${t}">${t}</a>`}}$(as,"blotName","video"),$(as,"className","ql-video"),$(as,"tagName","IFRAME");const En=new Ut("code-token","hljs",{scope:Z.INLINE});class ee extends Wt{static formats(t,e){for(;t!=null&&t!==e.domNode;){if(t.classList&&t.classList.contains(Nt.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,n){super(t,e,n),En.add(this.domNode,n)}format(t,e){t!==ee.blotName?super.format(t,e):e?En.add(this.domNode,e):(En.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),En.value(this.domNode)||this.unwrap()}}ee.blotName="code-token";ee.className="ql-token";class kt extends Nt{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),ee.blotName,!1),super.replaceWith(t,e)}}class Nn extends Ae{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===kt.blotName&&(this.forceNext=!0,this.children.forEach(n=>{n.format(t,e)}))}formatAt(t,e,n,s){n===kt.blotName&&(this.forceNext=!0),super.formatAt(t,e,n,s)}highlight(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.children.head==null)return;const s=`${Array.from(this.domNode.childNodes).filter(o=>o!==this.uiNode).map(o=>o.textContent).join(`
`)}
`,i=kt.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==s){if(s.trim().length>0||this.cachedText==null){const o=this.children.reduce((f,a)=>f.concat($i(a,!1)),new G),c=t(s,i);o.diff(c).reduce((f,a)=>{let{retain:g,attributes:v}=a;return g?(v&&Object.keys(v).forEach(d=>{[kt.blotName,ee.blotName].includes(d)&&this.formatAt(f,g,d,v[d])}),f+g):f},0)}this.cachedText=s,this.forceNext=!1}}html(t,e){const[n]=this.children.find(t);return`<pre data-language="${n?kt.formats(n.domNode):"plain"}">
${gs(this.code(t,e))}
</pre>`}optimize(t){if(super.optimize(t),this.parent!=null&&this.children.head!=null&&this.uiNode!=null){const e=kt.formats(this.children.head.domNode);e!==this.uiNode.value&&(this.uiNode.value=e)}}}Nn.allowedChildren=[kt];kt.requiredContainer=Nn;kt.allowedChildren=[ee,Xe,jt,Pt];const ha=(r,t,e)=>{if(typeof r.versionString=="string"){const n=r.versionString.split(".")[0];if(parseInt(n,10)>=11)return r.highlight(e,{language:t}).value}return r.highlight(t,e).value};class il extends $t{static register(){L.register(ee,!0),L.register(kt,!0),L.register(Nn,!0)}constructor(t,e){if(super(t,e),this.options.hljs==null)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((n,s)=>{let{key:i}=s;return n[i]=!0,n},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(L.events.SCROLL_BLOT_MOUNT,t=>{if(!(t instanceof Nn))return;const e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(n=>{let{key:s,label:i}=n;const o=e.ownerDocument.createElement("option");o.textContent=i,o.setAttribute("value",s),e.appendChild(o)}),e.addEventListener("change",()=>{t.format(kt.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)}),t.uiNode==null&&(t.attachUI(e),t.children.head&&(e.value=kt.formats(t.children.head.domNode)))})}initTimer(){let t=null;this.quill.on(L.events.SCROLL_OPTIMIZE,()=>{t&&clearTimeout(t),t=setTimeout(()=>{this.highlight(),t=null},this.options.interval)})}highlight(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.quill.selection.composing)return;this.quill.update(L.sources.USER);const n=this.quill.getSelection();(t==null?this.quill.scroll.descendants(Nn):[t]).forEach(i=>{i.highlight(this.highlightBlot,e)}),this.quill.update(L.sources.SILENT),n!=null&&this.quill.setSelection(n,L.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"plain";if(e=this.languages[e]?e:"plain",e==="plain")return gs(t).split(`
`).reduce((s,i,o)=>(o!==0&&s.insert(`
`,{[Nt.blotName]:e}),s.insert(i)),new G);const n=this.quill.root.ownerDocument.createElement("div");return n.classList.add(Nt.className),n.innerHTML=ha(this.options.hljs,e,t),Hr(this.quill.scroll,n,[(s,i)=>{const o=En.value(s);return o?i.compose(new G().retain(i.length(),{[ee.blotName]:o})):i}],[(s,i)=>s.data.split(`
`).reduce((o,c,f)=>(f!==0&&o.insert(`
`,{[Nt.blotName]:e}),o.insert(c)),i)],new WeakMap)}}il.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};const wn=class wn extends pt{static create(t){const e=super.create();return t?e.setAttribute("data-row",t):e.setAttribute("data-row",zr()),e}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===wn.blotName&&e?this.domNode.setAttribute("data-row",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}};$(wn,"blotName","table"),$(wn,"tagName","TD");let Bt=wn;class ne extends Ne{checkMerge(){if(super.checkMerge()&&this.next.children.head!=null){const t=this.children.head.formats(),e=this.children.tail.formats(),n=this.next.children.head.formats(),s=this.next.children.tail.formats();return t.table===e.table&&t.table===n.table&&t.table===s.table}return!1}optimize(t){super.optimize(t),this.children.forEach(e=>{if(e.next==null)return;const n=e.formats(),s=e.next.formats();if(n.table!==s.table){const i=this.splitAfter(e);i&&i.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}$(ne,"blotName","table-row"),$(ne,"tagName","TR");class Gt extends Ne{}$(Gt,"blotName","table-body"),$(Gt,"tagName","TBODY");class Qe extends Ne{balanceCells(){const t=this.descendants(ne),e=t.reduce((n,s)=>Math.max(s.children.length,n),0);t.forEach(n=>{new Array(e-n.children.length).fill(0).forEach(()=>{let s;n.children.head!=null&&(s=Bt.formats(n.children.head.domNode));const i=this.scroll.create(Bt.blotName,s);n.appendChild(i),i.optimize()})})}cells(t){return this.rows().map(e=>e.children.at(t))}deleteColumn(t){const[e]=this.descendant(Gt);e==null||e.children.head==null||e.children.forEach(n=>{const s=n.children.at(t);s!=null&&s.remove()})}insertColumn(t){const[e]=this.descendant(Gt);e==null||e.children.head==null||e.children.forEach(n=>{const s=n.children.at(t),i=Bt.formats(n.children.head.domNode),o=this.scroll.create(Bt.blotName,i);n.insertBefore(o,s)})}insertRow(t){const[e]=this.descendant(Gt);if(e==null||e.children.head==null)return;const n=zr(),s=this.scroll.create(ne.blotName);e.children.head.children.forEach(()=>{const o=this.scroll.create(Bt.blotName,n);s.appendChild(o)});const i=e.children.at(t);e.insertBefore(s,i)}rows(){const t=this.children.head;return t==null?[]:t.children.map(e=>e)}}$(Qe,"blotName","table-container"),$(Qe,"tagName","TABLE");Qe.allowedChildren=[Gt];Gt.requiredContainer=Qe;Gt.allowedChildren=[ne];ne.requiredContainer=Gt;ne.allowedChildren=[Bt];Bt.requiredContainer=ne;function zr(){return`row-${Math.random().toString(36).slice(2,6)}`}class fa extends $t{static register(){L.register(Bt),L.register(ne),L.register(Gt),L.register(Qe)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(Qe).forEach(t=>{t.balanceCells()})}deleteColumn(){const[t,,e]=this.getTable();e!=null&&(t.deleteColumn(e.cellOffset()),this.quill.update(L.sources.USER))}deleteRow(){const[,t]=this.getTable();t!=null&&(t.remove(),this.quill.update(L.sources.USER))}deleteTable(){const[t]=this.getTable();if(t==null)return;const e=t.offset();t.remove(),this.quill.update(L.sources.USER),this.quill.setSelection(e,L.sources.SILENT)}getTable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.quill.getSelection();if(t==null)return[null,null,null,-1];const[e,n]=this.quill.getLine(t.index);if(e==null||e.statics.blotName!==Bt.blotName)return[null,null,null,-1];const s=e.parent;return[s.parent.parent,s,e,n]}insertColumn(t){const e=this.quill.getSelection();if(!e)return;const[n,s,i]=this.getTable(e);if(i==null)return;const o=i.cellOffset();n.insertColumn(o+t),this.quill.update(L.sources.USER);let c=s.rowOffset();t===0&&(c+=1),this.quill.setSelection(e.index+c,e.length,L.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){const e=this.quill.getSelection();if(!e)return;const[n,s,i]=this.getTable(e);if(i==null)return;const o=s.rowOffset();n.insertRow(o+t),this.quill.update(L.sources.USER),t>0?this.quill.setSelection(e,L.sources.SILENT):this.quill.setSelection(e.index+s.children.length,e.length,L.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){const n=this.quill.getSelection();if(n==null)return;const s=new Array(t).fill(0).reduce(i=>{const o=new Array(e).fill(`
`).join("");return i.insert(o,{table:zr()})},new G().retain(n.index));this.quill.updateContents(s,L.sources.USER),this.quill.setSelection(n.index,L.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(L.events.SCROLL_OPTIMIZE,t=>{t.some(e=>["TD","TR","TBODY","TABLE"].includes(e.target.tagName)?(this.quill.once(L.events.TEXT_CHANGE,(n,s,i)=>{i===L.sources.USER&&this.balanceTables()}),!0):!1)})}}const Ai=se("quill:toolbar");class Vr extends $t{constructor(t,e){var n,s;if(super(t,e),Array.isArray(this.options.container)){const i=document.createElement("div");i.setAttribute("role","toolbar"),da(i,this.options.container),(s=(n=t.container)==null?void 0:n.parentNode)==null||s.insertBefore(i,t.container),this.container=i}else typeof this.options.container=="string"?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement)){Ai.error("Container required for toolbar",this.options);return}this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(i=>{var c;const o=(c=this.options.handlers)==null?void 0:c[i];o&&this.addHandler(i,o)}),Array.from(this.container.querySelectorAll("button, select")).forEach(i=>{this.attach(i)}),this.quill.on(L.events.EDITOR_CHANGE,()=>{const[i]=this.quill.selection.getRange();this.update(i)})}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find(s=>s.indexOf("ql-")===0);if(!e)return;if(e=e.slice(3),t.tagName==="BUTTON"&&t.setAttribute("type","button"),this.handlers[e]==null&&this.quill.scroll.query(e)==null){Ai.warn("ignoring attaching to nonexistent format",e,t);return}const n=t.tagName==="SELECT"?"change":"click";t.addEventListener(n,s=>{let i;if(t.tagName==="SELECT"){if(t.selectedIndex<0)return;const c=t.options[t.selectedIndex];c.hasAttribute("selected")?i=!1:i=c.value||!1}else t.classList.contains("ql-active")?i=!1:i=t.value||!t.hasAttribute("value"),s.preventDefault();this.quill.focus();const[o]=this.quill.selection.getRange();if(this.handlers[e]!=null)this.handlers[e].call(this,i);else if(this.quill.scroll.query(e).prototype instanceof xt){if(i=prompt(`Enter ${e}`),!i)return;this.quill.updateContents(new G().retain(o.index).delete(o.length).insert({[e]:i}),L.sources.USER)}else this.quill.format(e,i,L.sources.USER);this.update(o)}),this.controls.push([e,t])}update(t){const e=t==null?{}:this.quill.getFormat(t);this.controls.forEach(n=>{const[s,i]=n;if(i.tagName==="SELECT"){let o=null;if(t==null)o=null;else if(e[s]==null)o=i.querySelector("option[selected]");else if(!Array.isArray(e[s])){let c=e[s];typeof c=="string"&&(c=c.replace(/"/g,'\\"')),o=i.querySelector(`option[value="${c}"]`)}o==null?(i.value="",i.selectedIndex=-1):o.selected=!0}else if(t==null)i.classList.remove("ql-active"),i.setAttribute("aria-pressed","false");else if(i.hasAttribute("value")){const o=e[s],c=o===i.getAttribute("value")||o!=null&&o.toString()===i.getAttribute("value")||o==null&&!i.getAttribute("value");i.classList.toggle("ql-active",c),i.setAttribute("aria-pressed",c.toString())}else{const o=e[s]!=null;i.classList.toggle("ql-active",o),i.setAttribute("aria-pressed",o.toString())}})}}Vr.DEFAULTS={};function wi(r,t,e){const n=document.createElement("button");n.setAttribute("type","button"),n.classList.add(`ql-${t}`),n.setAttribute("aria-pressed","false"),e!=null?(n.value=e,n.setAttribute("aria-label",`${t}: ${e}`)):n.setAttribute("aria-label",t),r.appendChild(n)}function da(r,t){Array.isArray(t[0])||(t=[t]),t.forEach(e=>{const n=document.createElement("span");n.classList.add("ql-formats"),e.forEach(s=>{if(typeof s=="string")wi(n,s);else{const i=Object.keys(s)[0],o=s[i];Array.isArray(o)?ga(n,i,o):wi(n,i,o)}}),r.appendChild(n)})}function ga(r,t,e){const n=document.createElement("select");n.classList.add(`ql-${t}`),e.forEach(s=>{const i=document.createElement("option");s!==!1?i.setAttribute("value",String(s)):i.setAttribute("selected","selected"),n.appendChild(i)}),r.appendChild(n)}Vr.DEFAULTS={container:null,handlers:{clean(){const r=this.quill.getSelection();if(r!=null)if(r.length===0){const t=this.quill.getFormat();Object.keys(t).forEach(e=>{this.quill.scroll.query(e,Z.INLINE)!=null&&this.quill.format(e,!1,L.sources.USER)})}else this.quill.removeFormat(r.index,r.length,L.sources.USER)},direction(r){const{align:t}=this.quill.getFormat();r==="rtl"&&t==null?this.quill.format("align","right",L.sources.USER):!r&&t==="right"&&this.quill.format("align",!1,L.sources.USER),this.quill.format("direction",r,L.sources.USER)},indent(r){const t=this.quill.getSelection(),e=this.quill.getFormat(t),n=parseInt(e.indent||0,10);if(r==="+1"||r==="-1"){let s=r==="+1"?1:-1;e.direction==="rtl"&&(s*=-1),this.quill.format("indent",n+s,L.sources.USER)}},link(r){r===!0&&(r=prompt("Enter link URL:")),this.quill.format("link",r,L.sources.USER)},list(r){const t=this.quill.getSelection(),e=this.quill.getFormat(t);r==="check"?e.list==="checked"||e.list==="unchecked"?this.quill.format("list",!1,L.sources.USER):this.quill.format("list","unchecked",L.sources.USER):this.quill.format("list",r,L.sources.USER)}}};const pa='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',ma='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',ba='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',va='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>',ya='<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',Ea='<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',Na='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',Aa='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',Ti='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',wa='<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',Ta='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',xa='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>',qa='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',La='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',Sa='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',ka='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',_a='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',Ca='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Oa='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>',Ia='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',Ra='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',Ma='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',Ba='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>',Da='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',ja='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',Ua='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',Pa='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>',$a='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',Fa='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>',Ha='<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',za='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',Va='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',Ka='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>',Ln={align:{"":pa,center:ma,right:ba,justify:va},background:ya,blockquote:Ea,bold:Na,clean:Aa,code:Ti,"code-block":Ti,color:wa,direction:{"":Ta,rtl:xa},formula:qa,header:{1:La,2:Sa,3:ka,4:_a,5:Ca,6:Oa},italic:Ia,image:Ra,indent:{"+1":Ma,"-1":Ba},link:Da,list:{bullet:ja,check:Ua,ordered:Pa},script:{sub:$a,super:Fa},strike:Ha,table:za,underline:Va,video:Ka},Ga='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>';let xi=0;function qi(r,t){r.setAttribute(t,`${r.getAttribute(t)!=="true"}`)}class ms{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",e=>{switch(e.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),e.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),qi(this.label,"aria-expanded"),qi(this.options,"aria-hidden")}buildItem(t){const e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");const n=t.getAttribute("value");return n&&e.setAttribute("data-value",n),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",()=>{this.selectItem(e,!0)}),e.addEventListener("keydown",s=>{switch(s.key){case"Enter":this.selectItem(e,!0),s.preventDefault();break;case"Escape":this.escape(),s.preventDefault();break}}),e}buildLabel(){const t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=Ga,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){const t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${xi}`,xi+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach(e=>{const n=this.buildItem(e);t.appendChild(n),e.selected===!0&&this.selectItem(n)}),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach(t=>{this.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const n=this.container.querySelector(".ql-selected");t!==n&&(n!=null&&n.classList.remove("ql-selected"),t!=null&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){const n=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(n)}else this.selectItem(null);const e=t!=null&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}}class ll extends ms{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(n=>{n.classList.add("ql-primary")})}buildItem(t){const e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);const n=this.label.querySelector(".ql-color-label"),s=t&&t.getAttribute("data-value")||"";n&&(n.tagName==="line"?n.style.stroke=s:n.style.fill=s)}}class ol extends ms{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(n=>{n.innerHTML=e[n.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);const n=t||this.defaultItem;if(n!=null){if(this.label.innerHTML===n.innerHTML)return;this.label.innerHTML=n.innerHTML}}}const Za=r=>{const{overflowY:t}=getComputedStyle(r,null);return t!=="visible"&&t!=="clip"};class al{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,Za(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){const e=t.left+t.width/2-this.root.offsetWidth/2,n=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${n}px`,this.root.classList.remove("ql-flip");const s=this.boundsContainer.getBoundingClientRect(),i=this.root.getBoundingClientRect();let o=0;if(i.right>s.right&&(o=s.right-i.right,this.root.style.left=`${e+o}px`),i.left<s.left&&(o=s.left-i.left,this.root.style.left=`${e+o}px`),i.bottom>s.bottom){const c=i.bottom-i.top,f=t.bottom-t.top+c;this.root.style.top=`${n-f}px`,this.root.classList.add("ql-flip")}return o}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}const Wa=[!1,"center","right","justify"],Xa=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],Ya=[!1,"serif","monospace"],Qa=["1","2","3",!1],Ja=["small",!1,"large","huge"];class Cn extends Ye{constructor(t,e){super(t,e);const n=s=>{if(!document.body.contains(t.root)){document.body.removeEventListener("click",n);return}this.tooltip!=null&&!this.tooltip.root.contains(s.target)&&document.activeElement!==this.tooltip.textbox&&!this.quill.hasFocus()&&this.tooltip.hide(),this.pickers!=null&&this.pickers.forEach(i=>{i.container.contains(s.target)||i.close()})};t.emitter.listenDOM("click",document.body,n)}addModule(t){const e=super.addModule(t);return t==="toolbar"&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach(n=>{(n.getAttribute("class")||"").split(/\s+/).forEach(i=>{if(i.startsWith("ql-")&&(i=i.slice(3),e[i]!=null))if(i==="direction")n.innerHTML=e[i][""]+e[i].rtl;else if(typeof e[i]=="string")n.innerHTML=e[i];else{const o=n.value||"";o!=null&&e[i][o]&&(n.innerHTML=e[i][o])}})})}buildPickers(t,e){this.pickers=Array.from(t).map(s=>{if(s.classList.contains("ql-align")&&(s.querySelector("option")==null&&mn(s,Wa),typeof e.align=="object"))return new ol(s,e.align);if(s.classList.contains("ql-background")||s.classList.contains("ql-color")){const i=s.classList.contains("ql-background")?"background":"color";return s.querySelector("option")==null&&mn(s,Xa,i==="background"?"#ffffff":"#000000"),new ll(s,e[i])}return s.querySelector("option")==null&&(s.classList.contains("ql-font")?mn(s,Ya):s.classList.contains("ql-header")?mn(s,Qa):s.classList.contains("ql-size")&&mn(s,Ja)),new ms(s)});const n=()=>{this.pickers.forEach(s=>{s.update()})};this.quill.on(z.events.EDITOR_CHANGE,n)}}Cn.DEFAULTS=ue({},Ye.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let r=this.container.querySelector("input.ql-image[type=file]");r==null&&(r=document.createElement("input"),r.setAttribute("type","file"),r.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),r.classList.add("ql-image"),r.addEventListener("change",()=>{const t=this.quill.getSelection(!0);this.quill.uploader.upload(t,r.files),r.value=""}),this.container.appendChild(r)),r.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class cl extends al{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",t=>{t.key==="Enter"?(this.save(),t.preventDefault()):t.key==="Escape"&&(this.cancel(),t.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),this.textbox==null)return;e!=null?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");const n=this.quill.getBounds(this.quill.selection.savedRange);n!=null&&this.position(n),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{const{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,z.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,z.sources.USER)),this.quill.root.scrollTop=e;break}case"video":t=tc(t);case"formula":{if(!t)break;const e=this.quill.getSelection(!0);if(e!=null){const n=e.index+e.length;this.quill.insertEmbed(n,this.root.getAttribute("data-mode"),t,z.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(n+1," ",z.sources.USER),this.quill.setSelection(n+2,z.sources.USER)}break}}this.textbox.value="",this.hide()}}function tc(r){let t=r.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||r.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return t?`${t[1]||"https"}://www.youtube.com/embed/${t[2]}?showinfo=0`:(t=r.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${t[1]||"https"}://player.vimeo.com/video/${t[2]}/`:r}function mn(r,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;t.forEach(n=>{const s=document.createElement("option");n===e?s.setAttribute("selected","selected"):s.setAttribute("value",String(n)),r.appendChild(s)})}const ec=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class ul extends cl{constructor(t,e){super(t,e),this.quill.on(z.events.EDITOR_CHANGE,(n,s,i,o)=>{if(n===z.events.SELECTION_CHANGE)if(s!=null&&s.length>0&&o===z.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;const c=this.quill.getLines(s.index,s.length);if(c.length===1){const f=this.quill.getBounds(s);f!=null&&this.position(f)}else{const f=c[c.length-1],a=this.quill.getIndex(f),g=Math.min(f.length()-1,s.index+s.length-a),v=this.quill.getBounds(new Ee(a,g));v!=null&&this.position(v)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(z.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;const t=this.quill.getSelection();if(t!=null){const e=this.quill.getBounds(t);e!=null&&this.position(e)}},1)})}cancel(){this.show()}position(t){const e=super.position(t),n=this.root.querySelector(".ql-tooltip-arrow");return n.style.marginLeft="",e!==0&&(n.style.marginLeft=`${-1*e-n.offsetWidth/2}px`),e}}$(ul,"TEMPLATE",['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""));class hl extends Cn{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=ec),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new ul(this.quill,this.options.bounds),t.container!=null&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),Ln),this.buildPickers(t.container.querySelectorAll("select"),Ln))}}hl.DEFAULTS=ue({},Cn.DEFAULTS,{modules:{toolbar:{handlers:{link(r){r?this.quill.theme.tooltip.edit():this.quill.format("link",!1,L.sources.USER)}}}}});const nc=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class fl extends cl{constructor(){super(...arguments);$(this,"preview",this.root.querySelector("a.ql-preview"))}listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",e=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",e=>{if(this.linkRange!=null){const n=this.linkRange;this.restoreFocus(),this.quill.formatText(n,"link",!1,z.sources.USER),delete this.linkRange}e.preventDefault(),this.hide()}),this.quill.on(z.events.SELECTION_CHANGE,(e,n,s)=>{if(e!=null){if(e.length===0&&s===z.sources.USER){const[i,o]=this.quill.scroll.descendant(ce,e.index);if(i!=null){this.linkRange=new Ee(e.index-o,i.length());const c=ce.formats(i.domNode);this.preview.textContent=c,this.preview.setAttribute("href",c),this.show();const f=this.quill.getBounds(this.linkRange);f!=null&&this.position(f);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}}$(fl,"TEMPLATE",['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""));class dl extends Cn{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=nc),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){t.container!=null&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),Ln),this.buildPickers(t.container.querySelectorAll("select"),Ln),this.tooltip=new fl(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(e,n)=>{t.handlers.link.call(t,!n.format.link)}))}}dl.DEFAULTS=ue({},Cn.DEFAULTS,{modules:{toolbar:{handlers:{link(r){if(r){const t=this.quill.getSelection();if(t==null||t.length===0)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&e.indexOf("mailto:")!==0&&(e=`mailto:${e}`);const{tooltip:n}=this.quill.theme;n.edit("link",e)}else this.quill.format("link",!1,L.sources.USER)}}}}});L.register({"attributors/attribute/direction":Ki,"attributors/class/align":Hi,"attributors/class/background":Eo,"attributors/class/color":yo,"attributors/class/direction":Gi,"attributors/class/font":Xi,"attributors/class/size":Qi,"attributors/style/align":zi,"attributors/style/background":Ur,"attributors/style/color":jr,"attributors/style/direction":Zi,"attributors/style/font":Yi,"attributors/style/size":Ji},!0);L.register({"formats/align":Hi,"formats/direction":Gi,"formats/indent":ua,"formats/background":Ur,"formats/color":jr,"formats/font":Xi,"formats/size":Qi,"formats/blockquote":xr,"formats/code-block":Nt,"formats/header":qr,"formats/list":_n,"formats/bold":qn,"formats/code":Pr,"formats/italic":Lr,"formats/link":ce,"formats/script":Sr,"formats/strike":kr,"formats/underline":_r,"formats/formula":os,"formats/image":Cr,"formats/video":as,"modules/syntax":il,"modules/table":fa,"modules/toolbar":Vr,"themes/bubble":hl,"themes/snow":dl,"ui/icons":Ln,"ui/picker":ms,"ui/icon-picker":ol,"ui/color-picker":ll,"ui/tooltip":al},!0);var mr,Li;function sc(){if(Li)return mr;Li=1;var r=-1,t=1,e=0;function n(m,S,N,D){if(m===S)return m?[[e,m]]:[];if(N!=null){var U=k(m,S,N);if(U)return U}var M=c(m,S),F=m.substring(0,M);m=m.substring(M),S=S.substring(M),M=f(m,S);var V=m.substring(m.length-M);m=m.substring(0,m.length-M),S=S.substring(0,S.length-M);var h=s(m,S);return F&&h.unshift([e,F]),V&&h.push([e,V]),g(h,D),h}function s(m,S){var N;if(!m)return[[t,S]];if(!S)return[[r,m]];var D=m.length>S.length?m:S,U=m.length>S.length?S:m,M=D.indexOf(U);if(M!==-1)return N=[[t,D.substring(0,M)],[e,U],[t,D.substring(M+U.length)]],m.length>S.length&&(N[0][0]=N[2][0]=r),N;if(U.length===1)return[[r,m],[t,S]];var F=a(m,S);if(F){var V=F[0],h=F[1],A=F[2],y=F[3],_=F[4],C=n(V,A),x=n(h,y);return C.concat([[e,_]],x)}return i(m,S)}function i(m,S){for(var N=m.length,D=S.length,U=Math.ceil((N+D)/2),M=U,F=2*U,V=new Array(F),h=new Array(F),A=0;A<F;A++)V[A]=-1,h[A]=-1;V[M+1]=0,h[M+1]=0;for(var y=N-D,_=y%2!==0,C=0,x=0,O=0,B=0,P=0;P<U;P++){for(var I=-P+C;I<=P-x;I+=2){var K=M+I,H;I===-P||I!==P&&V[K-1]<V[K+1]?H=V[K+1]:H=V[K-1]+1;for(var W=H-I;H<N&&W<D&&m.charAt(H)===S.charAt(W);)H++,W++;if(V[K]=H,H>N)x+=2;else if(W>D)C+=2;else if(_){var X=M+y-I;if(X>=0&&X<F&&h[X]!==-1){var j=N-h[X];if(H>=j)return o(m,S,H,W)}}}for(var tt=-P+O;tt<=P-B;tt+=2){var X=M+tt,j;tt===-P||tt!==P&&h[X-1]<h[X+1]?j=h[X+1]:j=h[X-1]+1;for(var et=j-tt;j<N&&et<D&&m.charAt(N-j-1)===S.charAt(D-et-1);)j++,et++;if(h[X]=j,j>N)B+=2;else if(et>D)O+=2;else if(!_){var K=M+y-tt;if(K>=0&&K<F&&V[K]!==-1){var H=V[K],W=M+H-K;if(j=N-j,H>=j)return o(m,S,H,W)}}}}return[[r,m],[t,S]]}function o(m,S,N,D){var U=m.substring(0,N),M=S.substring(0,D),F=m.substring(N),V=S.substring(D),h=n(U,M),A=n(F,V);return h.concat(A)}function c(m,S){if(!m||!S||m.charAt(0)!==S.charAt(0))return 0;for(var N=0,D=Math.min(m.length,S.length),U=D,M=0;N<U;)m.substring(M,U)==S.substring(M,U)?(N=U,M=N):D=U,U=Math.floor((D-N)/2+N);return v(m.charCodeAt(U-1))&&U--,U}function f(m,S){if(!m||!S||m.slice(-1)!==S.slice(-1))return 0;for(var N=0,D=Math.min(m.length,S.length),U=D,M=0;N<U;)m.substring(m.length-U,m.length-M)==S.substring(S.length-U,S.length-M)?(N=U,M=N):D=U,U=Math.floor((D-N)/2+N);return d(m.charCodeAt(m.length-U))&&U--,U}function a(m,S){var N=m.length>S.length?m:S,D=m.length>S.length?S:m;if(N.length<4||D.length*2<N.length)return null;function U(x,O,B){for(var P=x.substring(B,B+Math.floor(x.length/4)),I=-1,K="",H,W,X,j;(I=O.indexOf(P,I+1))!==-1;){var tt=c(x.substring(B),O.substring(I)),et=f(x.substring(0,B),O.substring(0,I));K.length<et+tt&&(K=O.substring(I-et,I)+O.substring(I,I+tt),H=x.substring(0,B-et),W=x.substring(B+tt),X=O.substring(0,I-et),j=O.substring(I+tt))}return K.length*2>=x.length?[H,W,X,j,K]:null}var M=U(N,D,Math.ceil(N.length/4)),F=U(N,D,Math.ceil(N.length/2)),V;if(!M&&!F)return null;F?M?V=M[4].length>F[4].length?M:F:V=F:V=M;var h,A,y,_;m.length>S.length?(h=V[0],A=V[1],y=V[2],_=V[3]):(y=V[0],_=V[1],h=V[2],A=V[3]);var C=V[4];return[h,A,y,_,C]}function g(m,S){m.push([e,""]);for(var N=0,D=0,U=0,M="",F="",V;N<m.length;){if(N<m.length-1&&!m[N][1]){m.splice(N,1);continue}switch(m[N][0]){case t:U++,F+=m[N][1],N++;break;case r:D++,M+=m[N][1],N++;break;case e:var h=N-U-D-1;if(S){if(h>=0&&E(m[h][1])){var A=m[h][1].slice(-1);if(m[h][1]=m[h][1].slice(0,-1),M=A+M,F=A+F,!m[h][1]){m.splice(h,1),N--;var y=h-1;m[y]&&m[y][0]===t&&(U++,F=m[y][1]+F,y--),m[y]&&m[y][0]===r&&(D++,M=m[y][1]+M,y--),h=y}}if(p(m[N][1])){var A=m[N][1].charAt(0);m[N][1]=m[N][1].slice(1),M+=A,F+=A}}if(N<m.length-1&&!m[N][1]){m.splice(N,1);break}if(M.length>0||F.length>0){M.length>0&&F.length>0&&(V=c(F,M),V!==0&&(h>=0?m[h][1]+=F.substring(0,V):(m.splice(0,0,[e,F.substring(0,V)]),N++),F=F.substring(V),M=M.substring(V)),V=f(F,M),V!==0&&(m[N][1]=F.substring(F.length-V)+m[N][1],F=F.substring(0,F.length-V),M=M.substring(0,M.length-V)));var _=U+D;M.length===0&&F.length===0?(m.splice(N-_,_),N=N-_):M.length===0?(m.splice(N-_,_,[t,F]),N=N-_+1):F.length===0?(m.splice(N-_,_,[r,M]),N=N-_+1):(m.splice(N-_,_,[r,M],[t,F]),N=N-_+2)}N!==0&&m[N-1][0]===e?(m[N-1][1]+=m[N][1],m.splice(N,1)):N++,U=0,D=0,M="",F="";break}}m[m.length-1][1]===""&&m.pop();var C=!1;for(N=1;N<m.length-1;)m[N-1][0]===e&&m[N+1][0]===e&&(m[N][1].substring(m[N][1].length-m[N-1][1].length)===m[N-1][1]?(m[N][1]=m[N-1][1]+m[N][1].substring(0,m[N][1].length-m[N-1][1].length),m[N+1][1]=m[N-1][1]+m[N+1][1],m.splice(N-1,1),C=!0):m[N][1].substring(0,m[N+1][1].length)==m[N+1][1]&&(m[N-1][1]+=m[N+1][1],m[N][1]=m[N][1].substring(m[N+1][1].length)+m[N+1][1],m.splice(N+1,1),C=!0)),N++;C&&g(m,S)}function v(m){return m>=55296&&m<=56319}function d(m){return m>=56320&&m<=57343}function p(m){return d(m.charCodeAt(0))}function E(m){return v(m.charCodeAt(m.length-1))}function w(m){for(var S=[],N=0;N<m.length;N++)m[N][1].length>0&&S.push(m[N]);return S}function q(m,S,N,D){return E(m)||p(D)?null:w([[e,m],[r,S],[t,N],[e,D]])}function k(m,S,N){var D=typeof N=="number"?{index:N,length:0}:N.oldRange,U=typeof N=="number"?null:N.newRange,M=m.length,F=S.length;if(D.length===0&&(U===null||U.length===0)){var V=D.index,h=m.slice(0,V),A=m.slice(V),y=U?U.index:null;t:{var _=V+F-M;if(y!==null&&y!==_||_<0||_>F)break t;var C=S.slice(0,_),x=S.slice(_);if(x!==A)break t;var O=Math.min(V,_),B=h.slice(0,O),P=C.slice(0,O);if(B!==P)break t;var I=h.slice(O),K=C.slice(O);return q(B,I,K,A)}t:{if(y!==null&&y!==V)break t;var H=V,C=S.slice(0,H),x=S.slice(H);if(C!==h)break t;var W=Math.min(M-H,F-H),X=A.slice(A.length-W),j=x.slice(x.length-W);if(X!==j)break t;var I=A.slice(0,A.length-W),K=x.slice(0,x.length-W);return q(h,I,K,X)}}if(D.length>0&&U&&U.length===0)t:{var B=m.slice(0,D.index),X=m.slice(D.index+D.length),O=B.length,W=X.length;if(F<O+W)break t;var P=S.slice(0,O),j=S.slice(F-W);if(B!==P||X!==j)break t;var I=m.slice(O,M-W),K=S.slice(O,F-W);return q(B,I,K,X)}return null}function R(m,S,N){return n(m,S,N,!0)}return R.INSERT=t,R.DELETE=r,R.EQUAL=e,mr=R,mr}var Be={},Si;function rc(){if(Si)return Be;Si=1;var r=Be&&Be.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(Be,"__esModule",{value:!0});var t=r(fs()),e=r(ds()),n;return function(s){function i(a,g,v){a===void 0&&(a={}),g===void 0&&(g={}),typeof a!="object"&&(a={}),typeof g!="object"&&(g={});var d=t.default(g);v||(d=Object.keys(d).reduce(function(E,w){return d[w]!=null&&(E[w]=d[w]),E},{}));for(var p in a)a[p]!==void 0&&g[p]===void 0&&(d[p]=a[p]);return Object.keys(d).length>0?d:void 0}s.compose=i;function o(a,g){a===void 0&&(a={}),g===void 0&&(g={}),typeof a!="object"&&(a={}),typeof g!="object"&&(g={});var v=Object.keys(a).concat(Object.keys(g)).reduce(function(d,p){return e.default(a[p],g[p])||(d[p]=g[p]===void 0?null:g[p]),d},{});return Object.keys(v).length>0?v:void 0}s.diff=o;function c(a,g){a===void 0&&(a={}),g===void 0&&(g={}),a=a||{};var v=Object.keys(g).reduce(function(d,p){return g[p]!==a[p]&&a[p]!==void 0&&(d[p]=g[p]),d},{});return Object.keys(a).reduce(function(d,p){return a[p]!==g[p]&&g[p]===void 0&&(d[p]=null),d},v)}s.invert=c;function f(a,g,v){if(v===void 0&&(v=!1),typeof a!="object")return g;if(typeof g=="object"){if(!v)return g;var d=Object.keys(g).reduce(function(p,E){return a[E]===void 0&&(p[E]=g[E]),p},{});return Object.keys(d).length>0?d:void 0}}s.transform=f}(n||(n={})),Be.default=n,Be}var De={},je={},ki;function ic(){if(ki)return je;ki=1;var r=je&&je.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(je,"__esModule",{value:!0});var t=r(gl()),e=function(){function n(s){this.ops=s,this.index=0,this.offset=0}return n.prototype.hasNext=function(){return this.peekLength()<1/0},n.prototype.next=function(s){s||(s=1/0);var i=this.ops[this.index];if(i){var o=this.offset,c=t.default.length(i);if(s>=c-o?(s=c-o,this.index+=1,this.offset=0):this.offset+=s,typeof i.delete=="number")return{delete:s};var f={};return i.attributes&&(f.attributes=i.attributes),typeof i.retain=="number"?f.retain=s:typeof i.insert=="string"?f.insert=i.insert.substr(o,s):f.insert=i.insert,f}else return{retain:1/0}},n.prototype.peek=function(){return this.ops[this.index]},n.prototype.peekLength=function(){return this.ops[this.index]?t.default.length(this.ops[this.index])-this.offset:1/0},n.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},n.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var s=this.offset,i=this.index,o=this.next(),c=this.ops.slice(this.index);return this.offset=s,this.index=i,[o].concat(c)}else return[]},n}();return je.default=e,je}var _i;function gl(){if(_i)return De;_i=1;var r=De&&De.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(De,"__esModule",{value:!0});var t=r(ic()),e;return function(n){function s(o){return new t.default(o)}n.iterator=s;function i(o){return typeof o.delete=="number"?o.delete:typeof o.retain=="number"?o.retain:typeof o.insert=="string"?o.insert.length:1}n.length=i}(e||(e={})),De.default=e,De}var bn,Ci;function lc(){if(Ci)return bn;Ci=1;var r=bn&&bn.__importDefault||function(f){return f&&f.__esModule?f:{default:f}},t=r(sc()),e=r(fs()),n=r(ds()),s=r(rc()),i=r(gl()),o="\0",c=function(){function f(a){Array.isArray(a)?this.ops=a:a!=null&&Array.isArray(a.ops)?this.ops=a.ops:this.ops=[]}return f.prototype.insert=function(a,g){var v={};return typeof a=="string"&&a.length===0?this:(v.insert=a,g!=null&&typeof g=="object"&&Object.keys(g).length>0&&(v.attributes=g),this.push(v))},f.prototype.delete=function(a){return a<=0?this:this.push({delete:a})},f.prototype.retain=function(a,g){if(a<=0)return this;var v={retain:a};return g!=null&&typeof g=="object"&&Object.keys(g).length>0&&(v.attributes=g),this.push(v)},f.prototype.push=function(a){var g=this.ops.length,v=this.ops[g-1];if(a=e.default(a),typeof v=="object"){if(typeof a.delete=="number"&&typeof v.delete=="number")return this.ops[g-1]={delete:v.delete+a.delete},this;if(typeof v.delete=="number"&&a.insert!=null&&(g-=1,v=this.ops[g-1],typeof v!="object"))return this.ops.unshift(a),this;if(n.default(a.attributes,v.attributes)){if(typeof a.insert=="string"&&typeof v.insert=="string")return this.ops[g-1]={insert:v.insert+a.insert},typeof a.attributes=="object"&&(this.ops[g-1].attributes=a.attributes),this;if(typeof a.retain=="number"&&typeof v.retain=="number")return this.ops[g-1]={retain:v.retain+a.retain},typeof a.attributes=="object"&&(this.ops[g-1].attributes=a.attributes),this}}return g===this.ops.length?this.ops.push(a):this.ops.splice(g,0,a),this},f.prototype.chop=function(){var a=this.ops[this.ops.length-1];return a&&a.retain&&!a.attributes&&this.ops.pop(),this},f.prototype.filter=function(a){return this.ops.filter(a)},f.prototype.forEach=function(a){this.ops.forEach(a)},f.prototype.map=function(a){return this.ops.map(a)},f.prototype.partition=function(a){var g=[],v=[];return this.forEach(function(d){var p=a(d)?g:v;p.push(d)}),[g,v]},f.prototype.reduce=function(a,g){return this.ops.reduce(a,g)},f.prototype.changeLength=function(){return this.reduce(function(a,g){return g.insert?a+i.default.length(g):g.delete?a-g.delete:a},0)},f.prototype.length=function(){return this.reduce(function(a,g){return a+i.default.length(g)},0)},f.prototype.slice=function(a,g){a===void 0&&(a=0),g===void 0&&(g=1/0);for(var v=[],d=i.default.iterator(this.ops),p=0;p<g&&d.hasNext();){var E=void 0;p<a?E=d.next(a-p):(E=d.next(g-p),v.push(E)),p+=i.default.length(E)}return new f(v)},f.prototype.compose=function(a){var g=i.default.iterator(this.ops),v=i.default.iterator(a.ops),d=[],p=v.peek();if(p!=null&&typeof p.retain=="number"&&p.attributes==null){for(var E=p.retain;g.peekType()==="insert"&&g.peekLength()<=E;)E-=g.peekLength(),d.push(g.next());p.retain-E>0&&v.next(p.retain-E)}for(var w=new f(d);g.hasNext()||v.hasNext();)if(v.peekType()==="insert")w.push(v.next());else if(g.peekType()==="delete")w.push(g.next());else{var q=Math.min(g.peekLength(),v.peekLength()),k=g.next(q),R=v.next(q);if(typeof R.retain=="number"){var m={};typeof k.retain=="number"?m.retain=q:m.insert=k.insert;var S=s.default.compose(k.attributes,R.attributes,typeof k.retain=="number");if(S&&(m.attributes=S),w.push(m),!v.hasNext()&&n.default(w.ops[w.ops.length-1],m)){var N=new f(g.rest());return w.concat(N).chop()}}else typeof R.delete=="number"&&typeof k.retain=="number"&&w.push(R)}return w.chop()},f.prototype.concat=function(a){var g=new f(this.ops.slice());return a.ops.length>0&&(g.push(a.ops[0]),g.ops=g.ops.concat(a.ops.slice(1))),g},f.prototype.diff=function(a,g){if(this.ops===a.ops)return new f;var v=[this,a].map(function(q){return q.map(function(k){if(k.insert!=null)return typeof k.insert=="string"?k.insert:o;var R=q===a?"on":"with";throw new Error("diff() called "+R+" non-document")}).join("")}),d=new f,p=t.default(v[0],v[1],g),E=i.default.iterator(this.ops),w=i.default.iterator(a.ops);return p.forEach(function(q){for(var k=q[1].length;k>0;){var R=0;switch(q[0]){case t.default.INSERT:R=Math.min(w.peekLength(),k),d.push(w.next(R));break;case t.default.DELETE:R=Math.min(k,E.peekLength()),E.next(R),d.delete(R);break;case t.default.EQUAL:R=Math.min(E.peekLength(),w.peekLength(),k);var m=E.next(R),S=w.next(R);n.default(m.insert,S.insert)?d.retain(R,s.default.diff(m.attributes,S.attributes)):d.push(S).delete(R);break}k-=R}}),d.chop()},f.prototype.eachLine=function(a,g){g===void 0&&(g=`
`);for(var v=i.default.iterator(this.ops),d=new f,p=0;v.hasNext();){if(v.peekType()!=="insert")return;var E=v.peek(),w=i.default.length(E)-v.peekLength(),q=typeof E.insert=="string"?E.insert.indexOf(g,w)-w:-1;if(q<0)d.push(v.next());else if(q>0)d.push(v.next(q));else{if(a(d,v.next(1).attributes||{},p)===!1)return;p+=1,d=new f}}d.length()>0&&a(d,{},p)},f.prototype.invert=function(a){var g=new f;return this.reduce(function(v,d){if(d.insert)g.delete(i.default.length(d));else{if(d.retain&&d.attributes==null)return g.retain(d.retain),v+d.retain;if(d.delete||d.retain&&d.attributes){var p=d.delete||d.retain,E=a.slice(v,v+p);return E.forEach(function(w){d.delete?g.push(w):d.retain&&d.attributes&&g.retain(i.default.length(w),s.default.invert(d.attributes,w.attributes))}),v+p}}return v},0),g.chop()},f.prototype.transform=function(a,g){if(g===void 0&&(g=!1),g=!!g,typeof a=="number")return this.transformPosition(a,g);for(var v=a,d=i.default.iterator(this.ops),p=i.default.iterator(v.ops),E=new f;d.hasNext()||p.hasNext();)if(d.peekType()==="insert"&&(g||p.peekType()!=="insert"))E.retain(i.default.length(d.next()));else if(p.peekType()==="insert")E.push(p.next());else{var w=Math.min(d.peekLength(),p.peekLength()),q=d.next(w),k=p.next(w);if(q.delete)continue;k.delete?E.push(k):E.retain(w,s.default.transform(q.attributes,k.attributes,g))}return E.chop()},f.prototype.transformPosition=function(a,g){g===void 0&&(g=!1),g=!!g;for(var v=i.default.iterator(this.ops),d=0;v.hasNext()&&d<=a;){var p=v.peekLength(),E=v.peekType();if(v.next(),E==="delete"){a-=Math.min(p,a-d);continue}else E==="insert"&&(d<a||!g)&&(a+=p);d+=p}return a},f.Op=i.default,f.AttributeMap=s.default,f}();return bn=c,bn}var oc=lc();const ac=Or(oc);/*!
 * VueQuill @vueup/vue-quill v1.2.0
 * https://vueup.github.io/vue-quill/
 * 
 * Includes quill v1.3.7
 * https://quilljs.com/
 * 
 * Copyright (c) 2023 Ahmad Luthfi Masruri
 * Released under the MIT license
 * Date: 2023-05-12T08:44:03.742Z
 */const Oi={essential:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}],["blockquote","code-block","link"],[{color:[]},"clean"]],minimal:[[{header:1},{header:2}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}]],full:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["link","video","image"],["clean"]]},cc=Ii({name:"QuillEditor",inheritAttrs:!1,props:{content:{type:[String,Object]},contentType:{type:String,default:"delta",validator:r=>["delta","html","text"].includes(r)},enable:{type:Boolean,default:!0},readOnly:{type:Boolean,default:!1},placeholder:{type:String,required:!1},theme:{type:String,default:"snow",validator:r=>["snow","bubble",""].includes(r)},toolbar:{type:[String,Array,Object],required:!1,validator:r=>typeof r=="string"&&r!==""?r.charAt(0)==="#"?!0:Object.keys(Oi).indexOf(r)!==-1:!0},modules:{type:Object,required:!1},options:{type:Object,required:!1},globalOptions:{type:Object,required:!1}},emits:["textChange","selectionChange","editorChange","update:content","focus","blur","ready"],setup:(r,t)=>{vl(()=>{i()}),yl(()=>{e=null});let e,n;const s=Ve(),i=()=>{var h;if(s.value){if(n=o(),r.modules)if(Array.isArray(r.modules))for(const A of r.modules)L.register(`modules/${A.name}`,A.module);else L.register(`modules/${r.modules.name}`,r.modules.module);e=new L(s.value,n),m(r.content),e.on("text-change",v),e.on("selection-change",p),e.on("editor-change",E),r.theme!=="bubble"&&s.value.classList.remove("ql-bubble"),r.theme!=="snow"&&s.value.classList.remove("ql-snow"),(h=e.getModule("toolbar"))===null||h===void 0||h.container.addEventListener("mousedown",A=>{A.preventDefault()}),t.emit("ready",e)}},o=()=>{const h={};if(r.theme!==""&&(h.theme=r.theme),r.readOnly&&(h.readOnly=r.readOnly),r.placeholder&&(h.placeholder=r.placeholder),r.toolbar&&r.toolbar!==""&&(h.modules={toolbar:(()=>{if(typeof r.toolbar=="object")return r.toolbar;if(typeof r.toolbar=="string")return r.toolbar.charAt(0)==="#"?r.toolbar:Oi[r.toolbar]})()}),r.modules){const A=(()=>{var y,_;const C={};if(Array.isArray(r.modules))for(const x of r.modules)C[x.name]=(y=x.options)!==null&&y!==void 0?y:{};else C[r.modules.name]=(_=r.modules.options)!==null&&_!==void 0?_:{};return C})();h.modules=Object.assign({},h.modules,A)}return Object.assign({},r.globalOptions,r.options,h)},c=h=>typeof h=="object"&&h?h.slice():h,f=h=>Object.values(h.ops).some(A=>!A.retain||Object.keys(A).length!==1);let a;const g=h=>{if(typeof a==typeof h){if(h===a)return!0;if(typeof h=="object"&&h&&typeof a=="object"&&a)return!f(a.diff(h))}return!1},v=(h,A,y)=>{a=c(R()),g(r.content)||t.emit("update:content",a),t.emit("textChange",{delta:h,oldContents:A,source:y})},d=Ve(),p=(h,A,y)=>{d.value=!!(e!=null&&e.hasFocus()),t.emit("selectionChange",{range:h,oldRange:A,source:y})};ls(d,h=>{h?t.emit("focus",s):t.emit("blur",s)});const E=(...h)=>{h[0]==="text-change"&&t.emit("editorChange",{name:h[0],delta:h[1],oldContents:h[2],source:h[3]}),h[0]==="selection-change"&&t.emit("editorChange",{name:h[0],range:h[1],oldRange:h[2],source:h[3]})},w=()=>s.value,q=()=>{var h;return(h=e==null?void 0:e.getModule("toolbar"))===null||h===void 0?void 0:h.container},k=()=>{if(e)return e;throw`The quill editor hasn't been instantiated yet,
                  make sure to call this method when the editor ready
                  or use v-on:ready="onReady(quill)" event instead.`},R=(h,A)=>r.contentType==="html"?D():r.contentType==="text"?S(h,A):e==null?void 0:e.getContents(h,A),m=(h,A="api")=>{const y=h||(r.contentType==="delta"?new ac:"");r.contentType==="html"?U(y):r.contentType==="text"?N(y,A):e==null||e.setContents(y,A),a=c(y)},S=(h,A)=>{var y;return(y=e==null?void 0:e.getText(h,A))!==null&&y!==void 0?y:""},N=(h,A="api")=>{e==null||e.setText(h,A)},D=()=>{var h;return(h=e==null?void 0:e.root.innerHTML)!==null&&h!==void 0?h:""},U=h=>{e&&(e.root.innerHTML=h)},M=(h,A="api")=>{const y=e==null?void 0:e.clipboard.convert(h);y&&(e==null||e.setContents(y,A))},F=()=>{e==null||e.focus()},V=()=>{Gr(()=>{var h;!t.slots.toolbar&&e&&((h=e.getModule("toolbar"))===null||h===void 0||h.container.remove()),i()})};return ls(()=>r.content,h=>{if(!e||!h||g(h))return;const A=e.getSelection();A&&Gr(()=>e==null?void 0:e.setSelection(A)),m(h)},{deep:!0}),ls(()=>r.enable,h=>{e&&e.enable(h)}),{editor:s,getEditor:w,getToolbar:q,getQuill:k,getContents:R,setContents:m,getHTML:D,setHTML:U,pasteHTML:M,focus:F,getText:S,setText:N,reinit:V}},render(){var r,t;return[(t=(r=this.$slots).toolbar)===null||t===void 0?void 0:t.call(r),bl("div",{ref:"editor",...this.$attrs})]}}),uc={class:"editor"},Ec=Ii({__name:"index",props:{modelValue:Me.string,height:Me.number.def(400),minHeight:Me.number.def(400),readOnly:Me.bool.def(!1),fileSize:Me.number.def(5),type:Me.string.def("url")},emits:["update:modelValue"],setup(r){const t=r,{proxy:e}=El(),n=Nl({headers:Al(),url:"/prod-api/resource/oss/upload"}),s=Ve(),i=Ve(),o=Ve({theme:"snow",bounds:document.body,debug:"warn",modules:{toolbar:{container:[["bold","italic","underline","strike"],["blockquote","code-block"],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{align:[]}],["clean"],["link","image","video"]],handlers:{image:d=>{d?i.value.click():L.format("image",!0)}}}},placeholder:"请输入内容",readOnly:t.readOnly}),c=wl(()=>{const d={};return t.minHeight&&(d.minHeight=`${t.minHeight}px`),t.height&&(d.height=`${t.height}px`),d}),f=Ve("");ls(()=>t.modelValue,d=>{d!==f.value&&(f.value=d||"<p></p>")},{immediate:!0});const a=d=>{if(d.code===200){const p=Ol(s.value).getQuill(),E=p.selection.savedRange.index;p.insertEmbed(E,"image",d.data.url),p.setSelection(E+1),e==null||e.$modal.closeLoading()}else e==null||e.$modal.msgError("图片插入失败"),e==null||e.$modal.closeLoading()},g=d=>["image/jpeg","image/jpg","image/png","image/svg"].includes(d.type)?t.fileSize&&!(d.size/1024/1024<t.fileSize)?(e==null||e.$modal.msgError(`上传文件大小不能超过 ${t.fileSize} MB!`),!1):(e==null||e.$modal.loading("正在上传文件，请稍候..."),!0):(e==null||e.$modal.msgError("图片格式错误!"),!1),v=d=>{e==null||e.$modal.msgError("上传文件失败")};return(d,p)=>{const E=Il;return Zr(),Tl(xl,null,[rr("div",null,[r.type==="url"?(Zr(),ql(E,{key:0,action:ye(n).url,"before-upload":g,"on-success":a,"on-error":v,class:"editor-img-uploader",name:"file","show-file-list":!1,headers:ye(n).headers},{default:Sl(()=>[rr("i",{ref_key:"uploadRef",ref:i},null,512)]),_:1},8,["action","headers"])):Ll("",!0)]),rr("div",uc,[kl(ye(cc),{ref_key:"quillEditorRef",ref:s,content:ye(f),"onUpdate:content":p[0]||(p[0]=w=>Cl(f)?f.value=w:null),"content-type":"html",options:ye(o),style:_l(ye(c)),onTextChange:p[1]||(p[1]=w=>d.$emit("update:modelValue",ye(f)))},null,8,["content","options","style"])])],64)}}});export{Ec as _};
