import{d as ue,a as De,h as Ne,ak as ie,r as v,ai as z,X as Ye,b as Se,aH as $e,c as P,o as p,p as l,t as o,w as y,q as $,a7 as qe,M as Le,e as t,A as Fe,B as Me,F as E,C as R,x as c,D as xe,G as Ae,H as U,K as He,J as f,am as de,aI as Qe,ay as ze,ax as Ke,z as K,aJ as Be,aL as je,v as Je,az as Oe,a8 as Ge,aA as Xe}from"./index-D07cMzhp.js";import{_ as We}from"./index-DVHplxfU.js";import{E as Ze}from"./el-row-CikYE3zA.js";import{_ as ea}from"./index-BWMgqvQ9.js";import{E as aa}from"./el-col-BaG5Rg5z.js";import{E as la}from"./el-date-picker-HyhB9X9n.js";import{l as ta,g as oa,u as ra,a as na,d as ia}from"./index-DAD2mDUi.js";import{l as da}from"./index-DpFzHGDz.js";import{e as pe}from"./index-M7LxucW_.js";import{m as pa}from"./dayjs.min-Brw96_N0.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./el-tree-DW6MoFaI.js";import"./index-VIEDZI2D.js";const ua={class:"p-2"},sa={class:"mb-[10px]"},ma={class:"dialog-footer"},ca=ue({name:"TransportRecord"}),Ua=ue({...ca,setup(va){De();const{proxy:u}=Ne(),{epfy_medium_category:q,epnj_suizuan_yexiang_medium_type:se}=ie(u==null?void 0:u.useDict("epfy_medium_category","epnj_suizuan_yexiang_medium_type")),L=v([]),B=v([]),D=v(!1),N=v(!0),Y=v(!0),F=v([]),j=v(!0),J=v(!0),I=z({}),M=v(0),k=v([]),O=v([]),G=v(),x=v(),A=v(!1),_=z({visible:!1,title:""}),X={transportId:void 0,applicationId:void 0,transportTime:pa().format("YYYY-MM-DD HH:mm:ss"),mediumCategory:2,mediumType:8,departurePoint:void 0,number:void 0,sender:"-",transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,remark:void 0,measurementVoucher:void 0,photo:void 0,flowType:2},me=z({form:{...X},queryParams:{pageNum:1,pageSize:10,applicationId:void 0,transportTime:void 0,mediumCategory:2,mediumType:void 0,departurePoint:void 0,number:void 0,sender:void 0,transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,measurementVoucher:void 0,photo:void 0,flowType:2,unloadLocationType:1,monthQuery:void 0,appName:void 0,params:{}},rules:{applicationId:[{required:!0,message:"拉运申请不能为空",trigger:"change"}],transportTime:[{required:!0,message:"装车时间不能为空",trigger:"blur"}],transportEndTime:[{required:!0,message:"拉运时间不能为空",trigger:"blur"}],mediumCategory:[{required:!0,message:"分类不能为空",trigger:"change"}],departurePoint:[{required:!0,message:"起运点不能为空",trigger:"blur"}],number:[{required:!0,message:"数量不能为空",trigger:"blur"}],transporter:[{required:!0,message:"拉运人不能为空",trigger:"blur"}],licensePlate:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],arrivalPoint:[{required:!0,message:"接收点不能为空",trigger:"blur"}],receiver:[{required:!0,message:"接收人不能为空",trigger:"blur"}]}}),{queryParams:d,form:r,rules:ce}=ie(me),T=async()=>{N.value=!0;const n=await ta(d.value);B.value=n.rows,M.value=n.total,N.value=!1},ve=()=>{I.flowType=2,I.unloadLocationType=1,I.mediumCategory=2,k.value=[],pe(I).then(e=>{e.rows.forEach(s=>{s.appName=`${s.appName}-${s.wellName}-${s.applicationDate}`}),k.value=e.rows});let n={...I};n.transportStatus=0,pe(n).then(e=>{e.rows.forEach(s=>{s.appName=`${s.appName}-${s.wellName}-${s.applicationDate}`}),O.value=e.rows})},fe=()=>{H(),_.visible=!1},ge=async()=>{const n=await da();L.value=n.rows,console.log(L)},be=Ye(()=>k.value.reduce((n,e)=>(n[e.appId]=e.appName,n),{})),H=()=>{var n;r.value={...X},(n=x.value)==null||n.resetFields(),ge()},V=()=>{d.value.pageNum=1,T()},ye=()=>{var n;(n=G.value)==null||n.resetFields(),V()},_e=n=>{F.value=n.map(e=>e.transportId),j.value=n.length!=1,J.value=!n.length},Ve=()=>{H(),A.value=!1,_.visible=!0,_.title="添加拉运记录"},W=async n=>{H(),A.value=!0;const e=(n==null?void 0:n.transportId)||F.value[0],s=await oa(e);Object.assign(r.value,s.data),_.visible=!0,_.title="修改拉运记录"},we=async n=>{u.showAttachPreview({attachSourceId:n.transportId,attachSourceType:"yxTransportRecord"})},Te=()=>{var n;(n=x.value)==null||n.validate(async e=>{e&&(D.value=!0,r.value.transportId?await ra(r.value).finally(()=>D.value=!1):await na(r.value).finally(()=>D.value=!1),u==null||u.$modal.msgSuccess("操作成功"),_.visible=!1,await T())})},Z=async n=>{const e=(n==null?void 0:n.transportId)||F.value;await(u==null?void 0:u.$modal.confirm('是否确认删除拉运记录编号为"'+e+'"的数据项？').finally(()=>N.value=!1)),await ia(e),u==null||u.$modal.msgSuccess("删除成功"),await T()},Pe=()=>{u==null||u.download("epfy/transportRecord/export",{...d.value},`拉运记录_${new Date().getTime()}.xlsx`)},Ie=()=>{let n=k.value.find(e=>e.appId===r.value.applicationId);r.value.departurePoint=n.loadingLocation,r.value.arrivalPoint=n.unloadLocation};return Se(()=>{T(),ve()}),(n,e)=>{var oe,re;const s=la,i=Fe,C=xe,h=Me,g=Ae,b=He,ee=Le,ae=qe,S=aa,ke=ea,Ce=Ze,m=Ke,le=Be,Q=je,he=ze,Ee=Oe,te=We,Re=Xe,w=$e("hasPermi"),Ue=Je;return p(),P("div",ua,[l(Qe,{"enter-active-class":(oe=t(u))==null?void 0:oe.animate.searchAnimate.enter,"leave-active-class":(re=t(u))==null?void 0:re.animate.searchAnimate.leave},{default:o(()=>[y($("div",sa,[l(ae,{shadow:"hover"},{default:o(()=>[l(ee,{ref_key:"queryFormRef",ref:G,model:t(d),inline:!0,"label-width":"100px"},{default:o(()=>[l(i,{label:"装车时间",prop:"monthQuery"},{default:o(()=>[l(s,{modelValue:t(d).monthQuery,"onUpdate:modelValue":e[0]||(e[0]=a=>t(d).monthQuery=a),clearable:"",placeholder:"请选择装车时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),l(i,{label:"分类",prop:"mediumCategory"},{default:o(()=>[l(h,{modelValue:t(d).mediumCategory,"onUpdate:modelValue":e[1]||(e[1]=a=>t(d).mediumCategory=a),disabled:"",placeholder:"请选择分类"},{default:o(()=>[(p(!0),P(E,null,R(t(q),a=>(p(),c(C,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"起运点",prop:"departurePoint"},{default:o(()=>[l(g,{modelValue:t(d).departurePoint,"onUpdate:modelValue":e[2]||(e[2]=a=>t(d).departurePoint=a),placeholder:"请输入起运点",clearable:"",onKeyup:U(V,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"车牌号",prop:"licensePlate"},{default:o(()=>[l(g,{modelValue:t(d).licensePlate,"onUpdate:modelValue":e[3]||(e[3]=a=>t(d).licensePlate=a),placeholder:"请输入车牌号",clearable:"",onKeyup:U(V,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"接收点",prop:"arrivalPoint"},{default:o(()=>[l(g,{modelValue:t(d).arrivalPoint,"onUpdate:modelValue":e[4]||(e[4]=a=>t(d).arrivalPoint=a),placeholder:"请输入接收点",clearable:"",onKeyup:U(V,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"接收人",prop:"receiver"},{default:o(()=>[l(g,{modelValue:t(d).receiver,"onUpdate:modelValue":e[5]||(e[5]=a=>t(d).receiver=a),placeholder:"请输入接收人",clearable:"",onKeyup:U(V,["enter"])},null,8,["modelValue"])]),_:1}),l(i,{label:"拉运申请名称",prop:"appName"},{default:o(()=>[l(g,{modelValue:t(d).appName,"onUpdate:modelValue":e[6]||(e[6]=a=>t(d).appName=a),placeholder:"请输入拉运申请名称",clearable:"",onKeyup:U(V,["enter"])},null,8,["modelValue"])]),_:1}),l(i,null,{default:o(()=>[l(b,{type:"primary",icon:"Search",onClick:V},{default:o(()=>e[28]||(e[28]=[f("搜索")])),_:1}),l(b,{icon:"Refresh",onClick:ye},{default:o(()=>e[29]||(e[29]=[f("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[de,t(Y)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(ae,{shadow:"never"},{header:o(()=>[l(Ce,{gutter:10,class:"mb8"},{default:o(()=>[l(S,{span:1.5},{default:o(()=>[y((p(),c(b,{type:"primary",plain:"",icon:"Plus",onClick:Ve},{default:o(()=>e[30]||(e[30]=[f("新增")])),_:1})),[[w,["epfy:transportRecord:add"]]])]),_:1}),l(S,{span:1.5},{default:o(()=>[y((p(),c(b,{disabled:t(j),icon:"Edit",plain:"",type:"success",onClick:e[7]||(e[7]=a=>W())},{default:o(()=>e[31]||(e[31]=[f("修改")])),_:1},8,["disabled"])),[[w,["epfy:transportRecord:edit"]]])]),_:1}),l(S,{span:1.5},{default:o(()=>[y((p(),c(b,{disabled:t(J),icon:"Delete",plain:"",type:"danger",onClick:e[8]||(e[8]=a=>Z())},{default:o(()=>e[32]||(e[32]=[f("删除")])),_:1},8,["disabled"])),[[w,["epfy:transportRecord:remove"]]])]),_:1}),l(S,{span:1.5},{default:o(()=>[y((p(),c(b,{type:"warning",plain:"",icon:"Download",onClick:Pe},{default:o(()=>e[33]||(e[33]=[f("导出")])),_:1})),[[w,["epfy:transportRecord:export"]]])]),_:1}),l(ke,{showSearch:t(Y),"onUpdate:showSearch":e[9]||(e[9]=a=>Ge(Y)?Y.value=a:null),onQueryTable:T},null,8,["showSearch"])]),_:1})]),default:o(()=>[y((p(),c(he,{stripe:"",data:t(B),onSelectionChange:_e},{default:o(()=>[l(m,{type:"selection",width:"55",align:"center"}),l(m,{align:"center",label:"拉运申请",prop:"applicationId",width:"400"},{default:o(a=>[f(K(t(be)[a.row.applicationId]||"未知"),1)]),_:1}),l(m,{align:"center",label:"装车时间",prop:"transportTime",width:"110"},{default:o(a=>[$("span",null,K(n.parseTime(a.row.transportTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(m,{align:"center",label:"拉运时间",prop:"transportEndTime",width:"110"},{default:o(a=>[$("span",null,K(n.parseTime(a.row.transportEndTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(m,{label:"分类",align:"center",prop:"mediumCategory"},{default:o(a=>[l(le,{options:t(q),value:a.row.mediumCategory},null,8,["options","value"])]),_:1}),l(m,{label:"介质类型",align:"center",prop:"mediumType"},{default:o(a=>[l(le,{options:t(se),value:a.row.mediumType},null,8,["options","value"])]),_:1}),l(m,{align:"center",label:"起运点",prop:"departurePoint",width:"110"}),l(m,{label:"数量(方)",align:"center",prop:"number"}),l(m,{label:"拉运人",align:"center",prop:"transporter"}),l(m,{align:"center",label:"车牌号",prop:"licensePlate",width:"100"}),l(m,{align:"center",label:"接收点","min-width":"135",prop:"arrivalPoint"}),l(m,{label:"接收人",align:"center",prop:"receiver"}),l(m,{align:"center",label:"卸车时间",prop:"unloadTime","min-width":"105"}),l(m,{label:"备注信息",align:"center",prop:"remark"}),l(m,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作",width:"240"},{default:o(a=>[l(Q,{content:"拉运材料",placement:"top"},{default:o(()=>[y((p(),c(b,{icon:"Document",link:"",type:"primary",onClick:ne=>we(a.row)},{default:o(()=>e[34]||(e[34]=[f("拉运材料")])),_:2},1032,["onClick"])),[[w,["epfy:transportRecord:preview"]]])]),_:2},1024),l(Q,{content:"修改",placement:"top"},{default:o(()=>[y((p(),c(b,{link:"",type:"primary",icon:"Edit",onClick:ne=>W(a.row)},{default:o(()=>e[35]||(e[35]=[f("修改")])),_:2},1032,["onClick"])),[[w,["epfy:transportRecord:edit"]]])]),_:2},1024),l(Q,{content:"删除",placement:"top"},{default:o(()=>[y((p(),c(b,{icon:"Delete",link:"",type:"primary",onClick:ne=>Z(a.row)},{default:o(()=>e[36]||(e[36]=[f("删除")])),_:2},1032,["onClick"])),[[w,["epfy:transportRecord:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Ue,t(N)]]),y(l(Ee,{total:t(M),page:t(d).pageNum,"onUpdate:page":e[10]||(e[10]=a=>t(d).pageNum=a),limit:t(d).pageSize,"onUpdate:limit":e[11]||(e[11]=a=>t(d).pageSize=a),onPagination:T},null,8,["total","page","limit"]),[[de,t(M)>0]])]),_:1}),l(Re,{title:t(_).title,modelValue:t(_).visible,"onUpdate:modelValue":e[27]||(e[27]=a=>t(_).visible=a),width:"500px","append-to-body":""},{footer:o(()=>[$("div",ma,[l(b,{loading:t(D),type:"primary",onClick:Te},{default:o(()=>e[38]||(e[38]=[f("确 定")])),_:1},8,["loading"]),l(b,{onClick:fe},{default:o(()=>e[39]||(e[39]=[f("取 消")])),_:1})])]),default:o(()=>[l(ee,{ref_key:"transportRecordFormRef",ref:x,model:t(r),rules:t(ce),"label-width":"130px"},{default:o(()=>[t(A)==!1?(p(),c(i,{key:0,label:"拉运申请",prop:"applicationId"},{default:o(()=>[l(h,{modelValue:t(r).applicationId,"onUpdate:modelValue":e[12]||(e[12]=a=>t(r).applicationId=a),filterable:"",placeholder:"请选择拉运申请",onChange:Ie},{default:o(()=>[(p(!0),P(E,null,R(t(O),a=>(p(),c(C,{key:a.appId,label:a.appName,value:a.appId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):(p(),c(i,{key:1,label:"拉运申请",prop:"applicationId"},{default:o(()=>[l(h,{modelValue:t(r).applicationId,"onUpdate:modelValue":e[13]||(e[13]=a=>t(r).applicationId=a),disabled:"",filterable:"",placeholder:"请选择拉运申请"},{default:o(()=>[(p(!0),P(E,null,R(t(k),a=>(p(),c(C,{key:a.appId,label:a.appName,value:a.appId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),l(i,{label:"装车时间",prop:"transportTime"},{default:o(()=>[l(s,{modelValue:t(r).transportTime,"onUpdate:modelValue":e[14]||(e[14]=a=>t(r).transportTime=a),clearable:"",placeholder:"请选择装车时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(i,{label:"拉运时间",prop:"transportEndTime"},{default:o(()=>[l(s,{modelValue:t(r).transportEndTime,"onUpdate:modelValue":e[15]||(e[15]=a=>t(r).transportEndTime=a),clearable:"",placeholder:"请选择拉运时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(i,{label:"分类",prop:"mediumCategory"},{default:o(()=>[l(h,{modelValue:t(r).mediumCategory,"onUpdate:modelValue":e[16]||(e[16]=a=>t(r).mediumCategory=a),placeholder:"请选择分类",disabled:""},{default:o(()=>[(p(!0),P(E,null,R(t(q),a=>(p(),c(C,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"起运点",prop:"departurePoint"},{default:o(()=>[l(g,{modelValue:t(r).departurePoint,"onUpdate:modelValue":e[17]||(e[17]=a=>t(r).departurePoint=a),disabled:"",placeholder:"请输入起运点"},null,8,["modelValue"])]),_:1}),l(i,{label:"数量",prop:"number"},{default:o(()=>[l(g,{modelValue:t(r).number,"onUpdate:modelValue":e[18]||(e[18]=a=>t(r).number=a),placeholder:"请输入数量"},{append:o(()=>e[37]||(e[37]=[f("方")])),_:1},8,["modelValue"])]),_:1}),l(i,{label:"拉运人",prop:"transporter"},{default:o(()=>[l(g,{modelValue:t(r).transporter,"onUpdate:modelValue":e[19]||(e[19]=a=>t(r).transporter=a),placeholder:"请输入拉运人"},null,8,["modelValue"])]),_:1}),l(i,{label:"车牌号",prop:"licensePlate"},{default:o(()=>[l(h,{modelValue:t(r).licensePlate,"onUpdate:modelValue":e[20]||(e[20]=a=>t(r).licensePlate=a),placeholder:"请选择车牌号"},{default:o(()=>[(p(!0),P(E,null,R(t(L),a=>(p(),c(C,{key:a.carNumber,label:a.carNumber,value:a.carNumber},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"接收点",prop:"arrivalPoint"},{default:o(()=>[l(g,{modelValue:t(r).arrivalPoint,"onUpdate:modelValue":e[21]||(e[21]=a=>t(r).arrivalPoint=a),disabled:"",placeholder:"请输入接收点"},null,8,["modelValue"])]),_:1}),l(i,{label:"接收人",prop:"receiver"},{default:o(()=>[l(g,{modelValue:t(r).receiver,"onUpdate:modelValue":e[22]||(e[22]=a=>t(r).receiver=a),placeholder:"请输入接收人"},null,8,["modelValue"])]),_:1}),l(i,{label:"卸车时间",prop:"unloadTime"},{default:o(()=>[l(s,{modelValue:t(r).unloadTime,"onUpdate:modelValue":e[23]||(e[23]=a=>t(r).unloadTime=a),clearable:"",placeholder:"请选择卸车时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(i,{label:"备注信息",prop:"remark"},{default:o(()=>[l(g,{modelValue:t(r).remark,"onUpdate:modelValue":e[24]||(e[24]=a=>t(r).remark=a),placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1}),l(i,{label:"计量凭证或转运单",prop:"measurementVoucher"},{default:o(()=>[l(te,{modelValue:t(r).measurementVoucher,"onUpdate:modelValue":e[25]||(e[25]=a=>t(r).measurementVoucher=a),"attach-source-id":t(r).transportId,disabled:!1,"attach-category":"transportMeasurement","attach-source-type":"yxTransportRecord"},null,8,["modelValue","attach-source-id"])]),_:1}),l(i,{label:"装卸照片",prop:"photo"},{default:o(()=>[l(te,{modelValue:t(r).photo,"onUpdate:modelValue":e[26]||(e[26]=a=>t(r).photo=a),"attach-source-id":t(r).transportId,disabled:!1,"attach-category":"transportPhoto","attach-source-type":"yxTransportRecord"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ua as default};
