import{d as j,a as le,h as ne,ak as z,r as c,ai as B,X as se,b as ie,c as $,o as k,p as a,t as u,w as V,q as N,a7 as ue,M as me,e as i,A as pe,J as q,B as de,F as ce,C as ge,x as U,D as ve,G as fe,K as _e,am as W,aI as ye,ay as be,ax as we,z as Y,aJ as he,v as Te,az as qe,cq as Se}from"./index-Bm6k27Yz.js";import{E as ke}from"./el-date-picker-CIMm2f24.js";import{m as De}from"./index-DVbnU4Hd.js";import{e as Ae}from"./index-BwNwGGl7.js";import{u as D,X as Ce}from"./xlsx-DixkZmcl.js";import{l as Ee}from"./index-vmEtPepd.js";import{m as X}from"./dayjs.min-Dqr9lGM-.js";import"./index-Cf_aQ1ZR.js";const Ve={class:"p-2"},Ne={class:"mb-[10px]"},Ye=j({name:"TransportRecord"}),je=j({...Ye,setup(Pe){le();const{proxy:b}=ne(),{epfy_medium_category:P,epfy_medium_type:O,epnj_handling_type:xe}=z(b==null?void 0:b.useDict("epfy_medium_category","epfy_medium_type","epnj_handling_type"));`${new Date().getFullYear()}${String(new Date().getMonth()+1).padStart(2,"0")}`;const w=c([]);c(!1);const A=c(!0),Q=c(!0);c([]);const x=c(null);c(!0),c(!0);const C=c(0),F=c([]),M=c();c();const I=c([]),J=c({operationAreaType:0});B({visible:!1,title:""});const G=B({form:{...{transportId:void 0,applicationId:void 0,transportTime:void 0,mediumCategory:void 0,mediumType:void 0,departurePoint:void 0,number:void 0,sender:void 0,transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,remark:void 0,measurementVoucher:void 0,photo:void 0}},queryParams:{pageNum:-1,applicationId:void 0,mediumCategory:void 0,mediumType:void 0,departurePoint:void 0,number:void 0,sender:void 0,transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,measurementVoucher:void 0,photo:void 0,unloadLocationType:1,flowType:2,queryStartTime:X().subtract(1,"months").format("YYYY-MM-DD"),queryEndTime:X().format("YYYY-MM-DD"),params:{}},rules:{transportId:[{required:!0,message:"拉运ID不能为空",trigger:"blur"}],applicationId:[{required:!0,message:"拉运申请不能为空",trigger:"change"}],transportTime:[{required:!0,message:"拉运时间不能为空",trigger:"blur"}],mediumCategory:[{required:!0,message:"分类不能为空",trigger:"change"}],mediumType:[{required:!0,message:"介质类型不能为空",trigger:"change"}],departurePoint:[{required:!0,message:"起运点不能为空",trigger:"blur"}],number:[{required:!0,message:"数量不能为空",trigger:"blur"}],sender:[{required:!0,message:"发送人不能为空",trigger:"blur"}],transporter:[{required:!0,message:"拉运人不能为空",trigger:"blur"}],licensePlate:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],arrivalPoint:[{required:!0,message:"接收点不能为空",trigger:"blur"}],receiver:[{required:!0,message:"接收人不能为空",trigger:"blur"}]}}),{queryParams:m,form:Me,rules:Ie}=z(G),E=async()=>{A.value=!0;const r=await De(m.value),e=r.rows.map(o=>{var n;return{...o,transportTime:((n=o.transportTime)==null?void 0:n.replace(/(\d{4}-\d{2})-\d{2}/,"$1"))||""}});w.value=e,C.value=r.total,A.value=!1},K=()=>{F.value=[],Ae().then(r=>{F.value=r.rows})},H=async()=>{const r=await Ee(J.value);I.value=r.rows},Z=({columns:r,data:e})=>{const o=[];return r.forEach((n,s)=>{if(s===0){o[s]="合计";return}if(n.property==="number"){const g=e.reduce((v,p)=>{const f=Number(p[n.property]);return v+(isNaN(f)?0:f)},0);o[s]=Number(g.toFixed(2))}else o[s]=""}),o},ee=({row:r,column:e,rowIndex:o,columnIndex:n})=>{const s=["workArea","wellName","mediumCategory","mediumType","transportTime"],g=e.property;if(s.includes(g)){const v=r[g];let p=o;for(;p>0&&w.value[p-1][g]===v;)p--;if(p===o){let f=1;for(;o+f<w.value.length&&w.value[o+f][g]===v;)f++;return{rowspan:f,colspan:1}}else return{rowspan:0,colspan:0}}return{rowspan:1,colspan:1}},te=se(()=>I.value.reduce((r,e)=>(r[e.operationAreaId]=e.operationAreaName,r),{})),L=()=>{m.value.pageNum=1,E()},re=()=>{var r;(r=M.value)==null||r.resetFields(),L()},ae=()=>{var r;try{const e=(r=x.value)==null?void 0:r.$el;let o=e.querySelector(".el-table__fixed");o||(o=e);const n=D.table_to_book(o,{raw:!0}),s=n.Sheets[n.SheetNames[0]],g=[],v=D.decode_range(s["!ref"]);for(let d=v.s.c;d<=v.e.c;d++){let l=0;for(let _=v.s.r;_<=v.e.r;_++){const y=D.encode_cell({r:_,c:d}),h=s[y];if(h&&h.v){const T=String(h.v).split("").reduce((S,t)=>S+(t.charCodeAt(0)>255?2:1),0);T>l&&(l=T)}}g.push({wch:Math.min(l+2,60)})}s["!cols"]=g;const p={alignment:{horizontal:"center",vertical:"center",wrapText:!0},border:{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},font:{sz:11,name:"宋体",color:{rgb:"000000"}}};Object.keys(s).forEach(d=>{if(!d.startsWith("!")){const l=s[d];l.s=l.s?{...l.s,...p}:{...p},typeof l.v=="number"&&(l.z=l.z||"0.00")}}),s["!merges"]&&s["!merges"].forEach(d=>{for(let l=d.s.r;l<=d.e.r;l++)for(let _=d.s.c;_<=d.e.c;_++){const y=D.encode_cell({r:l,c:_});s[y]||(s[y]={t:"s",v:""}),s[y].s={...p}}});const f=Ce.write(n,{bookType:"xlsx",bookSST:!1,type:"binary",cellStyles:!0});Se.saveAs(new Blob([oe(f)],{type:"application/octet-stream"}),`拉运台账_${new Date().getTime()}.xlsx`)}catch(e){typeof console<"u"&&console.error(e)}},oe=r=>{const e=new ArrayBuffer(r.length),o=new Uint8Array(e);for(let n=0;n<r.length;n++)o[n]=r.charCodeAt(n)&255;return e};return ie(()=>{E(),K(),H()}),(r,e)=>{var T,S;const o=ke,n=pe,s=ve,g=de,v=fe,p=_e,f=me,d=ue,l=we,_=he,y=be,h=qe,R=Te;return k(),$("div",Ve,[a(ye,{"enter-active-class":(T=i(b))==null?void 0:T.animate.searchAnimate.enter,"leave-active-class":(S=i(b))==null?void 0:S.animate.searchAnimate.leave},{default:u(()=>[V(N("div",Ne,[a(d,{shadow:"hover"},{default:u(()=>[a(f,{ref_key:"queryFormRef",ref:M,model:i(m),inline:!0},{default:u(()=>[a(n,{label:"时间"},{default:u(()=>[a(o,{modelValue:i(m).queryStartTime,"onUpdate:modelValue":e[0]||(e[0]=t=>i(m).queryStartTime=t),type:"date","value-format":"YYYY-MM-DD",clearable:!1,class:"searchDate",placeholder:"开始日期"},null,8,["modelValue"]),e[6]||(e[6]=q(" 至 ")),a(o,{modelValue:i(m).queryEndTime,"onUpdate:modelValue":e[1]||(e[1]=t=>i(m).queryEndTime=t),type:"date","value-format":"YYYY-MM-DD",clearable:!1,class:"searchDate",placeholder:"结束日期"},null,8,["modelValue"])]),_:1}),a(n,{label:"分类",prop:"mediumCategory"},{default:u(()=>[a(g,{modelValue:i(m).mediumCategory,"onUpdate:modelValue":e[2]||(e[2]=t=>i(m).mediumCategory=t),clearable:"",placeholder:"请选择分类"},{default:u(()=>[(k(!0),$(ce,null,ge(i(P),t=>(k(),U(s,{key:t.value,label:t.label,value:parseInt(t.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"井名称",prop:"wellName"},{default:u(()=>[a(v,{modelValue:i(m).departurePoint,"onUpdate:modelValue":e[3]||(e[3]=t=>i(m).departurePoint=t),clearable:"",placeholder:"请输入井名称"},null,8,["modelValue"])]),_:1}),a(n,null,{default:u(()=>[a(p,{type:"primary",icon:"Search",onClick:L},{default:u(()=>e[7]||(e[7]=[q("搜索")])),_:1}),a(p,{icon:"Refresh",onClick:re},{default:u(()=>e[8]||(e[8]=[q("重置")])),_:1}),a(p,{type:"warning",plain:"",icon:"Download",onClick:ae},{default:u(()=>e[9]||(e[9]=[q("导出")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[W,Q.value]])]),_:1},8,["enter-active-class","leave-active-class"]),a(d,{shadow:"never"},{default:u(()=>[V((k(),U(y,{ref_key:"reportTable",ref:x,data:w.value,"show-summary":!0,"span-method":ee,"summary-method":Z,border:"",stripe:""},{default:u(()=>[a(l,{label:"日期",align:"center",prop:"transportTime",width:"180"},{default:u(t=>[N("span",null,Y(r.parseTime(t.row.transportTime,"{y}-{m}")),1)]),_:1}),a(l,{label:"作业区",align:"center",prop:"workArea"},{default:u(t=>[q(Y(i(te)[t.row.workArea]||"未知"),1)]),_:1}),a(l,{align:"center",label:"井",prop:"wellName"}),a(l,{label:"分类",align:"center",prop:"mediumCategory"},{default:u(t=>[a(_,{options:i(P),value:t.row.mediumCategory},null,8,["options","value"])]),_:1}),a(l,{label:"介质类型",align:"center",prop:"mediumType"},{default:u(t=>[a(_,{options:i(O),value:t.row.mediumType},null,8,["options","value"])]),_:1}),a(l,{label:"数量（方）",align:"center",prop:"number"},{default:u(t=>[N("span",null,Y(t.row.number),1)]),_:1})]),_:1},8,["data"])),[[R,A.value]]),V(a(h,{total:C.value,page:i(m).pageNum,"onUpdate:page":e[4]||(e[4]=t=>i(m).pageNum=t),limit:i(m).pageSize,"onUpdate:limit":e[5]||(e[5]=t=>i(m).pageSize=t),onPagination:E},null,8,["total","page","limit"]),[[W,C.value>0]])]),_:1})])}}});export{je as default};
