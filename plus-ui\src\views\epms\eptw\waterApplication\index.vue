<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="行政区域" prop="administrativeArea">
              <el-select v-model="queryParams.administrativeArea" clearable placeholder="请选择行政区域" @keyup.enter="handleQuery">
                <el-option v-for="dict in eptw_administrative_area" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="所属地" prop="operationAreaParentId">
              <el-select
                v-model="queryParams.operationAreaParentId"
                clearable
                placeholder="请选择所属地"
                @change="queryByParentId(queryParams.operationAreaParentId as number)"
                @keyup.enter="handleQuery"
              >
                <el-option
                  v-for="dict in operationAreaList"
                  :key="dict.operationAreaId"
                  :label="dict.operationAreaName"
                  :value="dict.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="取水区块" prop="operationAreaId">
              <el-select v-model="queryParams.operationAreaId" clearable placeholder="请选择取水区块" @keyup.enter="handleQuery">
                <el-option v-for="dict in queryList" :key="dict.operationAreaId" :label="dict.operationAreaName" :value="dict.operationAreaId" />
              </el-select>
            </el-form-item>
            <el-form-item label="取水证编号" prop="waterDrawingLicenseCode">
              <el-input v-model="queryParams.waterDrawingLicenseCode" placeholder="请输入取水证编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="过期状态" prop="timeStatus">
              <el-select v-model="queryParams.timeStatus" placeholder="请选择过期状态">
                <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="申请年度" prop="year">
              <el-date-picker
                v-model="queryParams.yearArray"
                type="yearrange"
                value-format="YYYY"
                range-separator="至"
                start-placeholder="开始年度"
                end-placeholder="结束年度"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epms:waterApplication:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterApplication:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterApplication:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:waterApplication:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="waterApplicationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column label="行政区域" align="center" prop="administrativeArea">
          <template #default="scope">
            {{ getAdministrativeAreaText(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="所属地" align="center" prop="operationAreaName">
          <template #default="scope">
            {{ getAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="取水区块" align="center" prop="blockName">
          <template #default="scope">
            {{ getBlockName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="取水许可证号" prop="waterDrawingLicenseCode" width="190" />
        <el-table-column align="center" label="申请年度" prop="year" width="110" />
        <el-table-column align="center" label="取水量(立方米)" prop="waterDrawingAmount" width="150" />
        <el-table-column label="申请状态" align="center" prop="status" width="110">
          <template #default="scope">
            <dict-tag :options="eptw_file_application_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="过期状态" align="center" prop="timeStatus" width="110">
          <template #default="scope">
            <dict-tag :options="eptw_file_status" :value="scope.row.timeStatus" />
          </template>
        </el-table-column>
        <el-table-column label="上传时间" align="center" prop="uploadTime" width="155">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="305">
          <template #default="scope">
            <el-tooltip content="用水申请" placement="top">
              <el-button v-hasPermi="['epms:waterApplication:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >申请预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:waterApplication:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epms:waterApplication:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:waterApplication:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改用水申请对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="waterApplicationFormRef" :model="form" :rules="rules" label-width="100px">
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" filterable placeholder="请选择行政区域" >-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaParentId">
          <el-select
            v-model="form.operationAreaParentId"
            filterable
            placeholder="请选择所属地"
            @change="getOperationAreaList(form.operationAreaParentId as number, 'operationAreaParentId')"
          >
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水区块" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择取水区块">
            <el-option
              v-for="dict in blockQueryList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水年度" prop="year">
          <el-input v-model="form.year" placeholder="输入取水年度" />
        </el-form-item>
        <el-form-item label="取水证编号" prop="waterDrawingLicenseId">
          <el-select v-model="form.waterDrawingLicenseId" filterable placeholder="请选择取水证">
            <el-option
              v-for="dict in licenseList"
              :key="dict.waterDrawingLicenseId"
              :label="dict.waterDrawingLicenseCode"
              :value="dict.waterDrawingLicenseId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水量" prop="waterDrawingAmount">
          <el-input v-model="form.waterDrawingAmount" placeholder="请输入取水量">
            <template #append>立方米</template>
          </el-input>
        </el-form-item>
        <el-form-item label="申请状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择申请状态">
            <el-option v-for="dict in eptw_file_application_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="过期状态" prop="timeStatus">
          <el-select v-model="form.timeStatus" placeholder="请选择过期状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件列表" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.waterApplicationId"
            :disabled="false"
            attach-category="waterApplication"
            attach-source-type="waterBlock"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="waterApplicationFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" filterable placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaParentId">
          <el-select
            v-model="form.operationAreaParentId"
            filterable
            placeholder="请选择所属地"
            @change="getOperationAreaList(form.operationAreaParentId as number, 'operationAreaParentId')"
          >
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水区块" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择取水区块">
            <el-option
              v-for="dict in blockQueryList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="取水证编号" prop="waterDrawingLicenseCode">
          <el-select v-model="form.waterDrawingLicenseCode" placeholder="请选择取水证编号" />
        </el-form-item>
        <el-form-item label="取水量" prop="waterDrawingAmount">
          <el-input v-model.number="form.waterDrawingAmount" placeholder="请输入取水量">
            <template #append>立方米</template>
          </el-input>
        </el-form-item>
        <el-form-item label="申请状态" prop="status">
          <el-select v-model="form.status" placeholder="请输入取水量">
            <el-option v-for="dict in eptw_file_application_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="过期状态" prop="timeStatus">
          <el-select v-model="form.timeStatus" placeholder="请选择过期状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WaterApplication" lang="ts">
import { listLicense } from '@/api/epms/eptw/license';
import { LicenseVO } from '@/api/epms/eptw/license/types';
import {
  addWaterApplication,
  delWaterApplication,
  getWaterApplication,
  listWaterApplication,
  updateWaterApplication
} from '@/api/epms/eptw/waterApplication';
import { WaterApplicationForm, WaterApplicationQuery, WaterApplicationVO } from '@/api/epms/eptw/waterApplication/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_file_status, eptw_administrative_area, eptw_file_application_status } = toRefs<any>(
  proxy?.useDict('eptw_file_status', 'eptw_administrative_area', 'eptw_file_application_status')
);

const waterApplicationList = ref<WaterApplicationVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const router = useRouter();
const queryFormRef = ref<ElFormInstance>();
const waterApplicationFormRef = ref<ElFormInstance>();

const operationAreaQuery = reactive<OperationAreaQuery>({});
// 渲染列表
const operationAreaList = ref([]);
const blockList = ref<OperationAreaVO[]>([]);
const licenseList = ref<LicenseVO[]>([]);
const queryList = ref([]);
// 参数列表
const blockQueryList = ref([]);
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WaterApplicationForm = {
  waterApplicationId: undefined,
  waterDrawingLicenseId: undefined,
  operationAreaId: undefined,
  administrativeArea: undefined,
  waterDrawingAmount: undefined,
  uploadTime: undefined,
  file: undefined
};
const data = reactive<PageData<WaterApplicationForm, WaterApplicationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    waterDrawingLicenseId: undefined,
    waterDrawingAmount: undefined,
    operationAreaId: undefined,
    waterDrawingLicenseCode: undefined,
    year: undefined,
    startYear: undefined,
    endYear: undefined,
    params: {}
  },
  rules: {
    operationAreaParentId: [{ required: true, message: '所属地不能为空', trigger: 'change' }],
    waterApplicationId: [{ required: true, message: '用水申请id不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '取水区块不能为空', trigger: 'change' }],
    waterDrawingLicenseId: [{ required: true, message: '取水证编号不能为空', trigger: 'change' }],
    waterDrawingAmount: [{ required: true, message: '取水量不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '申请状态不能为空', trigger: 'change' }],
    year: [{ required: true, message: '取水年度不能为空', trigger: 'blur' }],
    timeStatus: [{ required: true, message: '过期状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用水申请列表 */
const getList = async () => {
  loading.value = true;

  if (typeof queryParams.value.yearArray != 'undefined' && queryParams.value.yearArray !== null) {
    queryParams.value.startYear = queryParams.value.yearArray[0];
    queryParams.value.endYear = queryParams.value.yearArray[1];
    // queryParams.value.yearArray = undefined;
  } else {
    queryParams.value.startYear = undefined;
    queryParams.value.endYear = undefined;
  }
  console.log(queryParams.value);
  const res = await listWaterApplication(queryParams.value);
  waterApplicationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
};
/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  waterApplicationFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.operationAreaId = null;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WaterApplicationVO[]) => {
  ids.value = selection.map((item) => item.waterApplicationId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  operationAreaQuery.operationAreaParentId = null;
  getOperationAreaList();
  reset();
  dialog.visible = true;
  dialog.title = '添加用水申请';
};

/** 预览按钮操作 */
const handlePreview = async (row?: WaterApplicationVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.waterApplicationId,
    attachSourceType: 'waterBlock',
    attachCategory: 'waterApplication'
  });
};
/** 详情按钮操作 */
const handleDetail = async (row?: WaterApplicationVO) => {
  reset();
  const _waterApplicationId = row?.waterApplicationId || ids.value[0];
  const res = await getWaterApplication(_waterApplicationId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '用水申请详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: WaterApplicationVO) => {
  reset();
  const _waterApplicationId = row?.waterApplicationId || ids.value[0];
  const res = await getWaterApplication(_waterApplicationId);
  Object.assign(form.value, res.data);
  console.log(form.value);
  await getOperationAreaList(form.value.operationAreaParentId, 'operationAreaParentId');
  dialog.visible = true;
  dialog.title = '修改用水申请';
};

/** 提交按钮 */
const submitForm = () => {
  waterApplicationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.waterApplicationId) {
        await updateWaterApplication(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWaterApplication(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WaterApplicationVO) => {
  const _waterApplicationIds = row?.waterApplicationId || ids.value;
  await proxy?.$modal.confirm('是否确认删除用水申请编号为"' + _waterApplicationIds + '"的数据项？').finally(() => (loading.value = false));
  await delWaterApplication(_waterApplicationIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/waterApplication/export',
    {
      ...queryParams.value
    },
    `用水申请_${new Date().getTime()}.xlsx`
  );
};

const getAdministrativeAreaText = (blockId: number) => {
  if (!blockId) {
    return '未知';
  }
  const block = blockList.value.find((item) => item.operationAreaId === blockId);
  if (block) {
    const administrativeAreaItem = eptw_administrative_area.value.find((item) => item.value == block.administrativeArea);
    return administrativeAreaItem ? administrativeAreaItem.label : '未知';
  } else {
    return '未知';
  }
};

const getBlockName = (blockId: number) => {
  if (!blockId) {
    return '未知';
  }
  const block = blockList.value.find((item) => item.operationAreaId === blockId);
  return block ? block.operationAreaName : '未知';
};

const getAreaName = (blockId: number) => {
  if (!blockId) {
    return '未知';
  }
  const block = blockList.value.find((item) => item.operationAreaId === blockId);
  if (block) {
    const area = blockList.value.find((item) => item.operationAreaId === block.operationAreaParentId);
    return area ? area.operationAreaName : '未知';
  } else {
    return '未知';
  }
};

/**
 * 获取所属地下取水区块列表
 */
const getOperationAreaList = async (operationAreaId?: number, type?: string) => {
  if (type === 'operationAreaParentId') {
    queryParams.value.operationAreaId = null;
    operationAreaQuery.operationAreaParentId = operationAreaId;
  }
  blockQueryList.value = [];
  operationAreaQuery.operationAreaType = 1;
  listOperationArea(operationAreaQuery).then((res) => {
    blockQueryList.value = res.rows;
  });
};

const queryByParentId = async (operationAreaId?: number) => {
  operationAreaQuery.operationAreaParentId = operationAreaId;
  operationAreaQuery.operationAreaType = 1;
  listOperationArea(operationAreaQuery).then((res) => {
    queryList.value = res.rows;
  });
};

/**
 * 获取所属地列表
 */
const getAreaList = async () => {
  operationAreaList.value = [];
  operationAreaQuery.operationAreaType = 0;
  const resOperationArea = await listOperationArea(operationAreaQuery);
  operationAreaList.value = resOperationArea.rows;
};

/**
 * 获取全部取水区块列表
 */
const getBlockList = () => {
  blockList.value = [];
  listOperationArea().then((res) => {
    blockList.value = res.rows;
  });
};

const getLicenseList = async () => {
  licenseList.value = [];
  listLicense().then((res) => {
    licenseList.value = res.rows;
  });
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
  getAreaList();
  getBlockList();
  getLicenseList();
  queryByParentId();
  getOperationAreaList();
});
</script>
