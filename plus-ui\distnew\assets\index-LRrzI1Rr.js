import{aC as t}from"./index-D07cMzhp.js";const r=e=>t({url:"/tool/gen/list",method:"get",params:e}),n=e=>t({url:"/tool/gen/db/list",method:"get",params:e}),l=e=>t({url:"/tool/gen/"+e,method:"get"}),s=e=>t({url:"/tool/gen",method:"put",data:e}),a=e=>t({url:"/tool/gen/importTable",method:"post",params:e}),g=e=>t({url:"/tool/gen/preview/"+e,method:"get"}),u=e=>t({url:"/tool/gen/"+e,method:"delete"}),m=e=>t({url:"/tool/gen/genCode/"+e,method:"get"}),d=e=>t({url:"/tool/gen/synchDb/"+e,method:"get"}),c=()=>t({url:"/tool/gen/getDataNames",method:"get"});export{c as a,r as b,m as c,u as d,l as g,a as i,n as l,g as p,d as s,s as u};
