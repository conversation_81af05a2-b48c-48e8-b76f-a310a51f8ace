<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="集中站名称" label-width="90" prop="relatedId">
              <el-select v-model="queryParams.relatedId" clearable placeholder="请选择集中站名称">
                <el-option v-for="item in areaList" :key="item.operationAreaId" :label="item.operationAreaName" :value="item.operationAreaId" />
              </el-select>
            </el-form-item>
            <el-form-item label="处理时间" prop="timeRange">
              <el-date-picker
                v-model="queryParams.timeRange"
                clearable
                end-placeholder="结束日期"
                placeholder="请选择处理时间"
                range-separator="至"
                start-placeholder="开始日期"
                type="daterange"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epnj:mudHandling:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epnj:mudHandling:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epnj:mudHandling:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epnj:mudHandling:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="mudHandlingList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="名称" align="center" prop="relatedName" />
        <el-table-column label="处理时间" align="center" prop="handlingTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.handlingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <!--<el-table-column label="药剂检验合格证" align="center" prop="chemicalCertificateName" />-->
        <!--<el-table-column label="药剂检测报告" align="center" prop="chemicalReportName" />-->
        <el-table-column label="药剂使用台账" align="center" prop="chemicalLedgerName" />
        <el-table-column label="处理方式" align="center" prop="handlingType">
          <template #default="scope">
            <dict-tag :options="epnj_handling_type" :value="scope.row.handlingType" />
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="305">
          <template #default="scope">
            <el-tooltip content="附件预览" placement="top">
              <el-button v-hasPermi="['epnj:mudHandling:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >附件预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epnj:mudHandling:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['epnj:mudHandling:remove']">删除</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改泥浆处理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="mudHandlingFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="集中站名称" prop="relatedId">
          <el-select v-model="form.relatedId" clearable placeholder="请选择集中站名称">
            <el-option v-for="item in areaList" :key="item.operationAreaId" :label="item.operationAreaName" :value="item.operationAreaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理时间" prop="handlingTime">
          <el-date-picker v-model="form.handlingTime" clearable placeholder="请选择处理时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <!--        <el-form-item label="药剂检验合格证" prop="chemicalCertificate">
          <attachFileUpload v-model="form.chemicalCertificate"
                            :attach-source-id="form.handlingId"
                            @upload-success="chemicalCertificateSuccess"
                            attach-category="chemicalCertificate"
                            attach-source-type="mudHandling"
                            :disabled="false" />
        </el-form-item>
        <el-form-item label="药剂检测报告" prop="chemicalReport">
          <attachFileUpload v-model="form.chemicalReport"
                            :attach-source-id="form.handlingId"
                            @upload-success="chemicalReportSuccess"
                            attach-category="chemicalReport"
                            attach-source-type="mudHandling"
                            :disabled="false" />
        </el-form-item>-->
        <el-form-item label="药剂使用台账" prop="chemicalLedger">
          <attachFileUpload
            v-model="form.chemicalLedger"
            :attach-source-id="form.handlingId"
            :disabled="false"
            attach-category="chemicalLedger"
            attach-source-type="mudHandling"
            @upload-success="chemicalLedgerSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MudHandlingjzz" lang="ts">
import { addMudHandling, delMudHandling, getMudHandling, listMudHandling, updateMudHandling } from '@/api/epms/epnj/mudHandling';
import { MudHandlingForm, MudHandlingQuery, MudHandlingVO } from '@/api/epms/epnj/mudHandling/types';
import { WellPreparationVO } from '@/api/epms/epnj/wellPreparation/types';
import { getWellPreparation, listWellPreparation } from '@/api/epms/epnj/wellPreparation';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epnj_handling_type } = toRefs<any>(proxy?.useDict('epnj_handling_type'));

const mudHandlingList = ref<MudHandlingVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const mudHandlingFormRef = ref<ElFormInstance>();

const wellList = ref<WellPreparationVO[]>([]);

const areaQuery = ref<OperationAreaQuery>({});
const areaList = ref<OperationAreaVO[]>([]);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MudHandlingForm = {
  handlingId: undefined,
  relatedId: undefined,
  handlingTime: undefined,
  chemicalCertificate: undefined,
  chemicalReport: undefined,
  chemicalLedger: undefined,
  handlingType: 1
};
const data = reactive<PageData<MudHandlingForm, MudHandlingQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    relatedId: undefined,
    handlingTime: undefined,
    chemicalCertificate: undefined,
    chemicalReport: undefined,
    chemicalLedger: undefined,
    handlingType: 1,
    params: {}
  },
  rules: {
    handlingId: [{ required: true, message: '处理ID不能为空', trigger: 'blur' }],
    relatedId: [{ required: true, message: '集中站不能为空', trigger: 'blur' }],
    handlingTime: [{ required: true, message: '处理时间不能为空', trigger: 'blur' }],
    /*chemicalCertificate: [
      { required: true, message: "药剂检验合格证不能为空", trigger: "blur" }
    ],
    chemicalReport: [
      { required: true, message: "药剂检测报告不能为空", trigger: "blur" }
    ],*/
    chemicalLedger: [{ required: true, message: '药剂使用台账不能为空', trigger: 'blur' }],
    handlingType: [{ required: true, message: '类型不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询泥浆处理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMudHandling(queryParams.value);
  mudHandlingList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  wellList.value = [];
  form.value = { ...initFormData };
  mudHandlingFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.startTime = queryParams.value.timeRange?.[0];
  queryParams.value.endTime = queryParams.value.timeRange?.[1];
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: MudHandlingVO[]) => {
  ids.value = selection.map((item) => item.handlingId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加泥浆处理';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: MudHandlingVO) => {
  reset();
  const _handlingId = row?.handlingId || ids.value[0];
  const res = await getMudHandling(_handlingId);
  Object.assign(form.value, res.data);
  await getWellPreparation(res.data.relatedId).then((res: any) => {
    wellList.value.push(res.data);
    loading.value = false;
  });
  dialog.visible = true;
  dialog.title = '修改泥浆处理';
};

/** 跳转预览 */
const handlePreview = async (row?: MudHandlingVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.handlingId,
    attachSourceType: 'mudHandling'
    // attachCategory: "wellPreparation"
  });
};

/** 获取集中站列表 */
const queryAreaList = async () => {
  areaQuery.value.operationAreaType = 2;
  const res = await listOperationArea(areaQuery.value);
  areaList.value = res.rows;
};

/** 提交按钮 */
const submitForm = () => {
  mudHandlingFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      if (form.value.handlingType == 1) {
        form.value.relatedName = areaList.value.find((item: any) => item.operationAreaId == form.value.relatedId)?.operationAreaName;
      } else if (form.value.handlingType == 2) {
        form.value.relatedName = wellList.value.find((item: any) => item.prepId == form.value.relatedId)?.wellName;
      }
      buttonLoading.value = true;
      if (form.value.handlingId) {
        await updateMudHandling(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addMudHandling(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

const resetRelatd = (type: string | number) => {
  wellList.value = [];
  if (type == 1) {
    queryParams.value.relatedId = undefined;
  } else if (type == 2) {
    form.value.relatedId = undefined;
  }
};

const getWellList = (query: string) => {
  if (query) {
    loading.value = true;
    listWellPreparation({
      wellName: query
    }).then((res: any) => {
      wellList.value = res.rows;
      loading.value = false;
    });
  } else {
    wellList.value = [];
    loading.value = false;
  }
};

/** 上传成功回调 */
const chemicalCertificateSuccess = (fielInfo: any) => {
  // form.value.uploadDate = fielInfo.uploadTime;
  form.value.chemicalCertificateName = fielInfo.name;
};
/** 上传成功回调 */
const chemicalReportSuccess = (fielInfo: any) => {
  // form.value.uploadDate = fielInfo.uploadTime;
  form.value.chemicalReportName = fielInfo.name;
};
/** 上传成功回调 */
const chemicalLedgerSuccess = (fielInfo: any) => {
  // form.value.uploadDate = fielInfo.uploadTime;
  form.value.chemicalLedgerName = fielInfo.name;
};

/** 删除按钮操作 */
const handleDelete = async (row?: MudHandlingVO) => {
  const _handlingIds = row?.handlingId || ids.value;
  await proxy?.$modal.confirm('是否确认删除泥浆处理编号为"' + _handlingIds + '"的数据项？').finally(() => (loading.value = false));
  await delMudHandling(_handlingIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epnj/mudHandling/export',
    {
      ...queryParams.value
    },
    `mudHandling_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
  queryAreaList();
});
</script>
