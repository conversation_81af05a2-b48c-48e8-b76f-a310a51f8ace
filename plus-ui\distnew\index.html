<!doctype html>
<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <link rel="icon" href="/favicon.ico" />
    <title>环保信息化平台</title>
    <!--[if lt IE 11
      ]><script>
        window.location.href = '/html/ie.html';
      </script><!
    [endif]-->
    <style>
        html,
        body,
        #app {
            height: 100%;
            margin: 0px;
            padding: 0px;
        }

        .chromeframe {
            margin: 0.2em 0;
            background: #ccc;
            color: #000;
            padding: 0.2em 0;
        }

        @keyframes gradient {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        #loader-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 999999;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            background: linear-gradient(90deg, #0e1952,#5d7ae6,#0081e1);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }

        /* 新增竖状长条动画样式 */
        .bars-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            height: 60px;
        }

        .bar {
            width: 10px;
            height: 60px;
            background: #fff;
            border-radius: 4px;
            animation: jump 1.2s infinite;
        }

        .bar:nth-child(1) { animation-delay: 0s; }
        .bar:nth-child(2) { animation-delay: 0.2s; }
        .bar:nth-child(3) { animation-delay: 0.4s; }
        .bar:nth-child(4) { animation-delay: 0.6s; }
        .bar:nth-child(5) { animation-delay: 0.8s; }

        @keyframes jump {
            0%, 100% {
                transform: translateY(0) scaleY(1);
                opacity: 0.8;
            }
            50% {
                transform: translateY(-30px) scaleY(1.2);
                opacity: 1;
            }
        }

        /* 增加动态文字效果 */
        #loader-wrapper .load_title {
            font-family: 'Open Sans', sans-serif;
            color: #fff;
            font-size: 24px;
            text-align: center;
            margin-top: 20px;
            animation: fadeInOut 2s infinite;
        }

        @keyframes fadeInOut {
            0%, 100% {
                opacity: 0.5;
            }
            50% {
                opacity: 1;
            }
        }

        #loader-wrapper .load_title span {
            font-weight: normal;
            font-style: italic;
            font-size: 16px;
            color: #fff;
            opacity: 0.5;
        }
    </style>

<script type="module" crossorigin src="/assets/index-D07cMzhp.js"></script>
<link rel="stylesheet" crossorigin href="/assets/index-CKMMv2ef.css">

<body>
<div id="app">
    <div id="loader-wrapper">
        <!-- 替换为竖状长条结构 -->
        <div class="bars-container">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
        </div>
        <div class="load_title">正在加载系统资源，请耐心等待</div>
    </div>
</div>

</body>
</html>