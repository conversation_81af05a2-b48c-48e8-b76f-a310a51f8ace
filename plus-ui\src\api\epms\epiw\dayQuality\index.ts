import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DayQualityForm, DayQualityQuery, DayQualityVO } from '@/api/epms/epiw/dayQuality/types';

/**
 * 查询月度水质监测列表
 * @param query
 * @returns {*}
 */

export const listDayQuality = (query?: DayQualityQuery): AxiosPromise<DayQualityVO[]> => {
  return request({
    url: '/epms/dayQuality/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询月度水质监测详细
 * @param qualityReportId
 */
export const getDayQuality = (qualityReportId: string | number): AxiosPromise<DayQualityVO> => {
  return request({
    url: '/epms/dayQuality/' + qualityReportId,
    method: 'get'
  });
};

/**
 * 新增月度水质监测
 * @param data
 */
export const addDayQuality = (data: DayQualityForm) => {
  return request({
    url: '/epms/dayQuality',
    method: 'post',
    data: data
  });
};

/**
 * 修改月度水质监测
 * @param data
 */
export const updateDayQuality = (data: DayQualityForm) => {
  return request({
    url: '/epms/dayQuality',
    method: 'put',
    data: data
  });
};

/**
 * 删除月度水质监测
 * @param qualityReportId
 */
export const delDayQuality = (qualityReportId: string | number | Array<string | number>) => {
  return request({
    url: '/epms/dayQuality/' + qualityReportId,
    method: 'delete'
  });
};
