import{d as A,c as M,o as Y,y as E,q as m,w as k,am as T,a3 as S,a2 as R,z as N,i as D,h as F,r as I,ai as V,m as q,p as C,e as y,aA as j,a8 as G,t as x,x as Z,K as J,J as $,ar as K,Q}from"./index-D07cMzhp.js";import{E as tt}from"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import{E as et}from"./el-row-CikYE3zA.js";import{E as st}from"./el-col-BaG5Rg5z.js";import{u as it}from"./index-BdvXA74M.js";const z={};z.getData=t=>new Promise((e,s)=>{let i={};ot(t).then(o=>{i.arrayBuffer=o;try{i.orientation=ct(o)}catch{i.orientation=-1}e(i)}).catch(o=>{s(o)})});function ot(t){let e=null;return new Promise((s,i)=>{if(t.src)if(/^data\:/i.test(t.src))e=ht(t.src),s(e);else if(/^blob\:/i.test(t.src)){var o=new FileReader;o.onload=function(h){e=h.target.result,s(e)},rt(t.src,function(h){o.readAsArrayBuffer(h)})}else{var r=new XMLHttpRequest;r.onload=function(){if(this.status==200||this.status===0)e=r.response,s(e);else throw"Could not load image";r=null},r.open("GET",t.src,!0),r.responseType="arraybuffer",r.send(null)}else i("img error")})}function rt(t,e){var s=new XMLHttpRequest;s.open("GET",t,!0),s.responseType="blob",s.onload=function(i){(this.status==200||this.status===0)&&e(this.response)},s.send()}function ht(t,e){e=e||t.match(/^data\:([^\;]+)\;base64,/mi)[1]||"",t=t.replace(/^data\:([^\;]+)\;base64,/gmi,"");for(var s=atob(t),i=s.length%2==0?s.length:s.length+1,o=new ArrayBuffer(i),r=new Uint16Array(o),h=0;h<i;h++)r[h]=s.charCodeAt(h);return o}function at(t,e,s){var i="",o;for(o=e,s+=e;o<s;o++)i+=String.fromCharCode(t.getUint8(o));return i}function ct(t){var e=new DataView(t),s=e.byteLength,i,o,r,h,a,l,n,c,p,u;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(p=2;p<s;){if(e.getUint8(p)===255&&e.getUint8(p+1)===225){n=p;break}p++}if(n&&(o=n+4,r=n+10,at(e,o,4)==="Exif"&&(l=e.getUint16(r),a=l===18761,(a||l===19789)&&e.getUint16(r+2,a)===42&&(h=e.getUint32(r+4,a),h>=8&&(c=r+h)))),c){for(s=e.getUint16(c,a),u=0;u<s;u++)if(p=c+u*12+2,e.getUint16(p,a)===274){p+=8,i=e.getUint16(p,a);break}}return i}const nt=(t,e)=>{const s=t.__vccOpts||t;for(const[i,o]of e)s[i]=o;return s},lt=A({data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:()=>[1,1]},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:()=>10,validator:function(t){return Array.isArray(t)?Number(t[0])>=0&&Number(t[1])>=0:Number(t)>=0}},fillColor:{type:String,default:""}},computed:{cropInfo(){let t={};if(t.top=this.cropOffsertY>21?"-21px":"0px",t.width=this.cropW>0?this.cropW:0,t.height=this.cropH>0?this.cropH:0,this.infoTrue){let e=1;this.high&&!this.full&&(e=window.devicePixelRatio),this.enlarge!==1&!this.full&&(e=Math.abs(Number(this.enlarge))),t.width=t.width*e,t.height=t.height*e,this.full&&(t.width=t.width/this.scale,t.height=t.height/this.scale)}return t.width=t.width.toFixed(0),t.height=t.height.toFixed(0),t},isIE(){return!!window.ActiveXObject||"ActiveXObject"in window},passive(){return this.isIE?null:{passive:!1}}},watch:{img(){this.checkedImg()},imgs(t){t!==""&&this.reload()},cropW(){this.showPreview()},cropH(){this.showPreview()},cropOffsertX(){this.showPreview()},cropOffsertY(){this.showPreview()},scale(t,e){this.showPreview()},x(){this.showPreview()},y(){this.showPreview()},autoCrop(t){t&&this.goAutoCrop()},autoCropWidth(){this.autoCrop&&this.goAutoCrop()},autoCropHeight(){this.autoCrop&&this.goAutoCrop()},mode(){this.checkedImg()},rotate(){this.showPreview(),this.autoCrop?this.goAutoCrop(this.cropW,this.cropH):(this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion(t){var e=navigator.userAgent.split(" "),s="";let i=0;const o=new RegExp(t,"i");for(var r=0;r<e.length;r++)o.test(e[r])&&(s=e[r]);return s?i=s.split("/")[1].split("."):i=["0","0","0"],i},checkOrientationImage(t,e,s,i){if(this.getVersion("chrome")[0]>=81)e=-1;else if(this.getVersion("safari")[0]>=605){const h=this.getVersion("version");h[0]>13&&h[1]>1&&(e=-1)}else{const h=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(h){let a=h[1];a=a.split("_"),(a[0]>13||a[0]>=13&&a[1]>=4)&&(e=-1)}}let o=document.createElement("canvas"),r=o.getContext("2d");switch(r.save(),e){case 2:o.width=s,o.height=i,r.translate(s,0),r.scale(-1,1);break;case 3:o.width=s,o.height=i,r.translate(s/2,i/2),r.rotate(180*Math.PI/180),r.translate(-s/2,-i/2);break;case 4:o.width=s,o.height=i,r.translate(0,i),r.scale(1,-1);break;case 5:o.height=s,o.width=i,r.rotate(.5*Math.PI),r.scale(1,-1);break;case 6:o.width=i,o.height=s,r.translate(i/2,s/2),r.rotate(90*Math.PI/180),r.translate(-s/2,-i/2);break;case 7:o.height=s,o.width=i,r.rotate(.5*Math.PI),r.translate(s,-i),r.scale(-1,1);break;case 8:o.height=s,o.width=i,r.translate(i/2,s/2),r.rotate(-90*Math.PI/180),r.translate(-s/2,-i/2);break;default:o.width=s,o.height=i}r.drawImage(t,0,0,s,i),r.restore(),o.toBlob(h=>{let a=URL.createObjectURL(h);URL.revokeObjectURL(this.imgs),this.imgs=a},"image/"+this.outputType,1)},checkedImg(){if(this.img===null||this.img===""){this.imgs="",this.clearCrop();return}this.loading=!0,this.scale=1,this.rotate=0,this.clearCrop();let t=new Image;if(t.onload=()=>{if(this.img==="")return this.$emit("img-load",new Error("图片不能为空")),!1;let s=t.width,i=t.height;z.getData(t).then(o=>{this.orientation=o.orientation||1;let r=Number(this.maxImgSize);if(!this.orientation&&s<r&i<r){this.imgs=this.img;return}s>r&&(i=i/s*r,s=r),i>r&&(s=s/i*r,i=r),this.checkOrientationImage(t,this.orientation,s,i)}).catch(o=>{this.$emit("img-load","error"),this.$emit("img-load-error",o)})},t.onerror=s=>{this.$emit("img-load","error"),this.$emit("img-load-error",s)},this.img.substr(0,4)!=="data"&&(t.crossOrigin=""),this.isIE){var e=new XMLHttpRequest;e.onload=function(){var s=URL.createObjectURL(this.response);t.src=s},e.open("GET",this.img,!0),e.responseType="blob",e.send()}else t.src=this.img},startMove(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=("clientX"in t?t.clientX:t.touches[0].clientX)-this.x,this.moveY=("clientY"in t?t.clientY:t.touches[0].clientY)-this.y,t.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),t.touches.length==2&&(this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=t.offsetX?t.offsetX:t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY?t.offsetY:t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale(t){t.preventDefault();let e=this.scale;var s={x:this.touches[0].clientX,y:this.touches[0].clientY},i={x:t.touches[0].clientX,y:t.touches[0].clientY},o={x:this.touches[1].clientX,y:this.touches[1].clientY},r={x:t.touches[1].clientX,y:t.touches[1].clientY},h=Math.sqrt(Math.pow(s.x-o.x,2)+Math.pow(s.y-o.y,2)),a=Math.sqrt(Math.pow(i.x-r.x,2)+Math.pow(i.y-r.y,2)),l=a-h,n=1;n=n/this.trueWidth>n/this.trueHeight?n/this.trueHeight:n/this.trueWidth,n=n>.1?.1:n;var c=n*l;if(!this.touchNow){if(this.touchNow=!0,l>0?e+=Math.abs(c):l<0&&e>Math.abs(c)&&(e-=Math.abs(c)),this.touches=t.touches,setTimeout(()=>{this.touchNow=!1},8),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e}},cancelTouchScale(t){window.removeEventListener("touchmove",this.touchScale)},moveImg(t){if(t.preventDefault(),t.touches&&t.touches.length===2)return this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;let e="clientX"in t?t.clientX:t.touches[0].clientX,s="clientY"in t?t.clientY:t.touches[0].clientY,i,o;i=e-this.moveX,o=s-this.moveY,this.$nextTick(()=>{if(this.centerBox){let r=this.getImgAxis(i,o,this.scale),h=this.getCropAxis(),a=this.trueHeight*this.scale,l=this.trueWidth*this.scale,n,c,p,u;switch(this.rotate){case 1:case-1:case 3:case-3:n=this.cropOffsertX-this.trueWidth*(1-this.scale)/2+(a-l)/2,c=this.cropOffsertY-this.trueHeight*(1-this.scale)/2+(l-a)/2,p=n-a+this.cropW,u=c-l+this.cropH;break;default:n=this.cropOffsertX-this.trueWidth*(1-this.scale)/2,c=this.cropOffsertY-this.trueHeight*(1-this.scale)/2,p=n-l+this.cropW,u=c-a+this.cropH;break}r.x1>=h.x1&&(i=n),r.y1>=h.y1&&(o=c),r.x2<=h.x2&&(i=p),r.y2<=h.y2&&(o=u)}this.x=i,this.y=o,this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})})},leaveImg(t){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize(t){t.preventDefault();let e=this.scale;var s=t.deltaY||t.wheelDelta,i=navigator.userAgent.indexOf("Firefox");s=i>0?s*30:s,this.isIE&&(s=-s);var o=this.coe;o=o/this.trueWidth>o/this.trueHeight?o/this.trueHeight:o/this.trueWidth;var r=o*s;r<0?e+=Math.abs(r):e>Math.abs(r)&&(e-=Math.abs(r));let h=r<0?"add":"reduce";if(h!==this.coeStatus&&(this.coeStatus=h,this.coe=.2),this.scaling||(this.scalingSet=setTimeout(()=>{this.scaling=!1,this.coe=this.coe+=.01},50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},changeScale(t){let e=this.scale;t=t||1;var s=20;if(s=s/this.trueWidth>s/this.trueHeight?s/this.trueHeight:s/this.trueWidth,t=t*s,t>0?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop(t){t.preventDefault();var e="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,s="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick(()=>{var i=e-this.cropX,o=s-this.cropY;if(i>0?(this.cropW=i+this.cropChangeX>this.w?this.w-this.cropChangeX:i,this.cropOffsertX=this.cropChangeX):(this.cropW=this.w-this.cropChangeX+Math.abs(i)>this.w?this.cropChangeX:Math.abs(i),this.cropOffsertX=this.cropChangeX+i>0?this.cropChangeX+i:0),!this.fixed)o>0?(this.cropH=o+this.cropChangeY>this.h?this.h-this.cropChangeY:o,this.cropOffsertY=this.cropChangeY):(this.cropH=this.h-this.cropChangeY+Math.abs(o)>this.h?this.cropChangeY:Math.abs(o),this.cropOffsertY=this.cropChangeY+o>0?this.cropChangeY+o:0);else{var r=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];r+this.cropOffsertY>this.h?(this.cropH=this.h-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],i>0?this.cropOffsertX=this.cropChangeX:this.cropOffsertX=this.cropChangeX-this.cropW):this.cropH=r,this.cropOffsertY=this.cropOffsertY}})},changeCropSize(t,e,s,i,o){t.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=e,this.canChangeY=s,this.changeCropTypeX=i,this.changeCropTypeY=o,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow(t){t.preventDefault();var e="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,s="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;let i=this.w,o=this.h,r=0,h=0;if(this.centerBox){let n=this.getImgAxis(),c=n.x2,p=n.y2;r=n.x1>0?n.x1:0,h=n.y1>0?n.y1:0,i>c&&(i=c),o>p&&(o=p)}const[a,l]=this.checkCropLimitSize();this.$nextTick(()=>{var n=e-this.cropX,c=s-this.cropY;if(this.canChangeX&&(this.changeCropTypeX===1?this.cropOldW-n<a?(this.cropW=a,this.cropOffsertX=this.cropOldW+this.cropChangeX-r-a):this.cropOldW-n>0?(this.cropW=i-this.cropChangeX-n<=i-r?this.cropOldW-n:this.cropOldW+this.cropChangeX-r,this.cropOffsertX=i-this.cropChangeX-n<=i-r?this.cropChangeX+n:r):(this.cropW=Math.abs(n)+this.cropChangeX<=i?Math.abs(n)-this.cropOldW:i-this.cropOldW-this.cropChangeX,this.cropOffsertX=this.cropChangeX+this.cropOldW):this.changeCropTypeX===2&&(this.cropOldW+n<a?this.cropW=a:this.cropOldW+n>0?(this.cropW=this.cropOldW+n+this.cropOffsertX<=i?this.cropOldW+n:i-this.cropOffsertX,this.cropOffsertX=this.cropChangeX):(this.cropW=i-this.cropChangeX+Math.abs(n+this.cropOldW)<=i-r?Math.abs(n+this.cropOldW):this.cropChangeX-r,this.cropOffsertX=i-this.cropChangeX+Math.abs(n+this.cropOldW)<=i-r?this.cropChangeX-Math.abs(n+this.cropOldW):r))),this.canChangeY&&(this.changeCropTypeY===1?this.cropOldH-c<l?(this.cropH=l,this.cropOffsertY=this.cropOldH+this.cropChangeY-h-l):this.cropOldH-c>0?(this.cropH=o-this.cropChangeY-c<=o-h?this.cropOldH-c:this.cropOldH+this.cropChangeY-h,this.cropOffsertY=o-this.cropChangeY-c<=o-h?this.cropChangeY+c:h):(this.cropH=Math.abs(c)+this.cropChangeY<=o?Math.abs(c)-this.cropOldH:o-this.cropOldH-this.cropChangeY,this.cropOffsertY=this.cropChangeY+this.cropOldH):this.changeCropTypeY===2&&(this.cropOldH+c<l?this.cropH=l:this.cropOldH+c>0?(this.cropH=this.cropOldH+c+this.cropOffsertY<=o?this.cropOldH+c:o-this.cropOffsertY,this.cropOffsertY=this.cropChangeY):(this.cropH=o-this.cropChangeY+Math.abs(c+this.cropOldH)<=o-h?Math.abs(c+this.cropOldH):this.cropChangeY-h,this.cropOffsertY=o-this.cropChangeY+Math.abs(c+this.cropOldH)<=o-h?this.cropChangeY-Math.abs(c+this.cropOldH):h))),this.canChangeX&&this.fixed){var p=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];p<l?(this.cropH=l,this.cropW=this.fixedNumber[0]*l/this.fixedNumber[1],this.changeCropTypeX===1&&(this.cropOffsertX=this.cropChangeX+(this.cropOldW-this.cropW))):p+this.cropOffsertY>o?(this.cropH=o-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],this.changeCropTypeX===1&&(this.cropOffsertX=this.cropChangeX+(this.cropOldW-this.cropW))):this.cropH=p}if(this.canChangeY&&this.fixed){var u=this.cropH/this.fixedNumber[1]*this.fixedNumber[0];u<a?(this.cropW=a,this.cropH=this.fixedNumber[1]*a/this.fixedNumber[0],this.cropOffsertY=this.cropOldH+this.cropChangeY-this.cropH):u+this.cropOffsertX>i?(this.cropW=i-this.cropOffsertX,this.cropH=this.cropW/this.fixedNumber[0]*this.fixedNumber[1]):this.cropW=u}})},checkCropLimitSize(){let{cropW:t,cropH:e,limitMinSize:s}=this,i=new Array;return Array.isArray(s)?i=s:i=[s,s],t=parseFloat(i[0]),e=parseFloat(i[1]),[t,e]},changeCropEnd(t){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},calculateSize(t,e,s,i,o,r){const h=t/e;let a=o,l=r;return a<s&&(a=s,l=Math.ceil(a/h)),l<i&&(l=i,a=Math.ceil(l*h),a<s&&(a=s,l=Math.ceil(a/h))),a<o&&(a=o,l=Math.ceil(a/h)),l<r&&(l=r,a=Math.ceil(l*h)),{width:a,height:l}},endCrop(){this.cropW===0&&this.cropH===0&&(this.cropping=!1);let[t,e]=this.checkCropLimitSize();const{width:s,height:i}=this.fixed?this.calculateSize(this.fixedNumber[0],this.fixedNumber[1],t,e,this.cropW,this.cropH):{width:t,height:e};s>this.cropW&&(this.cropW=s,this.cropOffsertX+s>this.w&&(this.cropOffsertX=this.w-s)),i>this.cropH&&(this.cropH=i,this.cropOffsertY+i>this.h&&(this.cropOffsertY=this.h-i)),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop(){this.crop=!0},stopCrop(){this.crop=!1},clearCrop(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&t.touches.length===2)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);let e="clientX"in t?t.clientX:t.touches[0].clientX,s="clientY"in t?t.clientY:t.touches[0].clientY,i,o;i=e-this.cropOffsertX,o=s-this.cropOffsertY,this.cropX=i,this.cropY=o,this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop(t,e){let s=0,i=0;t&&(t.preventDefault(),s="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY),this.$nextTick(()=>{let o,r,h=s-this.cropX,a=i-this.cropY;if(e&&(h=this.cropOffsertX,a=this.cropOffsertY),h<=0?o=0:h+this.cropW>this.w?o=this.w-this.cropW:o=h,a<=0?r=0:a+this.cropH>this.h?r=this.h-this.cropH:r=a,this.centerBox){let l=this.getImgAxis();o<=l.x1&&(o=l.x1),o+this.cropW>l.x2&&(o=l.x2-this.cropW),r<=l.y1&&(r=l.y1),r+this.cropH>l.y2&&(r=l.y2-this.cropH)}this.cropOffsertX=o,this.cropOffsertY=r,this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})})},getImgAxis(t,e,s){t=t||this.x,e=e||this.y,s=s||this.scale;let i={x1:0,x2:0,y1:0,y2:0},o=this.trueWidth*s,r=this.trueHeight*s;switch(this.rotate){case 0:i.x1=t+this.trueWidth*(1-s)/2,i.x2=i.x1+this.trueWidth*s,i.y1=e+this.trueHeight*(1-s)/2,i.y2=i.y1+this.trueHeight*s;break;case 1:case-1:case 3:case-3:i.x1=t+this.trueWidth*(1-s)/2+(o-r)/2,i.x2=i.x1+this.trueHeight*s,i.y1=e+this.trueHeight*(1-s)/2+(r-o)/2,i.y2=i.y1+this.trueWidth*s;break;default:i.x1=t+this.trueWidth*(1-s)/2,i.x2=i.x1+this.trueWidth*s,i.y1=e+this.trueHeight*(1-s)/2,i.y2=i.y1+this.trueHeight*s;break}return i},getCropAxis(){let t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop(t){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked(t){let e=document.createElement("canvas"),s=new Image,i=this.rotate,o=this.trueWidth,r=this.trueHeight,h=this.cropOffsertX,a=this.cropOffsertY;s.onload=()=>{if(this.cropW!==0){let c=e.getContext("2d"),p=1;this.high&!this.full&&(p=window.devicePixelRatio),this.enlarge!==1&!this.full&&(p=Math.abs(Number(this.enlarge)));let u=this.cropW*p,X=this.cropH*p,g=o*this.scale*p,f=r*this.scale*p,v=(this.x-h+this.trueWidth*(1-this.scale)/2)*p,w=(this.y-a+this.trueHeight*(1-this.scale)/2)*p;switch(n(u,X),c.save(),this.fillColor&&(c.fillStyle=this.fillColor,c.fillRect(0,0,e.width,e.height)),i){case 0:this.full?(n(u/this.scale,X/this.scale),c.drawImage(s,v/this.scale,w/this.scale,g/this.scale,f/this.scale)):c.drawImage(s,v,w,g,f);break;case 1:case-3:this.full?(n(u/this.scale,X/this.scale),v=v/this.scale+(g/this.scale-f/this.scale)/2,w=w/this.scale+(f/this.scale-g/this.scale)/2,c.rotate(i*90*Math.PI/180),c.drawImage(s,w,-v-f/this.scale,g/this.scale,f/this.scale)):(v=v+(g-f)/2,w=w+(f-g)/2,c.rotate(i*90*Math.PI/180),c.drawImage(s,w,-v-f,g,f));break;case 2:case-2:this.full?(n(u/this.scale,X/this.scale),c.rotate(i*90*Math.PI/180),v=v/this.scale,w=w/this.scale,c.drawImage(s,-v-g/this.scale,-w-f/this.scale,g/this.scale,f/this.scale)):(c.rotate(i*90*Math.PI/180),c.drawImage(s,-v-g,-w-f,g,f));break;case 3:case-1:this.full?(n(u/this.scale,X/this.scale),v=v/this.scale+(g/this.scale-f/this.scale)/2,w=w/this.scale+(f/this.scale-g/this.scale)/2,c.rotate(i*90*Math.PI/180),c.drawImage(s,-w-g/this.scale,v,g/this.scale,f/this.scale)):(v=v+(g-f)/2,w=w+(f-g)/2,c.rotate(i*90*Math.PI/180),c.drawImage(s,-w-g,v,g,f));break;default:this.full?(n(u/this.scale,X/this.scale),c.drawImage(s,v/this.scale,w/this.scale,g/this.scale,f/this.scale)):c.drawImage(s,v,w,g,f)}c.restore()}else{let c=o*this.scale,p=r*this.scale,u=e.getContext("2d");switch(u.save(),this.fillColor&&(u.fillStyle=this.fillColor,u.fillRect(0,0,e.width,e.height)),i){case 0:n(c,p),u.drawImage(s,0,0,c,p);break;case 1:case-3:n(p,c),u.rotate(i*90*Math.PI/180),u.drawImage(s,0,-p,c,p);break;case 2:case-2:n(c,p),u.rotate(i*90*Math.PI/180),u.drawImage(s,-c,-p,c,p);break;case 3:case-1:n(p,c),u.rotate(i*90*Math.PI/180),u.drawImage(s,-c,0,c,p);break;default:n(c,p),u.drawImage(s,0,0,c,p)}u.restore()}t(e)};var l=this.img.substr(0,4);l!=="data"&&(s.crossOrigin="Anonymous"),s.src=this.imgs;function n(c,p){e.width=Math.round(c),e.height=Math.round(p)}},getCropData(t){this.getCropChecked(e=>{t(e.toDataURL("image/"+this.outputType,this.outputSize))})},getCropBlob(t){this.getCropChecked(e=>{e.toBlob(s=>t(s),"image/"+this.outputType,this.outputSize)})},showPreview(){if(this.isCanShow)this.isCanShow=!1,setTimeout(()=>{this.isCanShow=!0},16);else return!1;let t=this.cropW,e=this.cropH,s=this.scale;var i={};i.div={width:`${t}px`,height:`${e}px`};let o=(this.x-this.cropOffsertX)/s,r=(this.y-this.cropOffsertY)/s,h=0;i.w=t,i.h=e,i.url=this.imgs,i.img={width:`${this.trueWidth}px`,height:`${this.trueHeight}px`,transform:`scale(${s})translate3d(${o}px, ${r}px, ${h}px)rotateZ(${this.rotate*90}deg)`},i.html=`
      <div class="show-preview" style="width: ${i.w}px; height: ${i.h}px,; overflow: hidden">
        <div style="width: ${t}px; height: ${e}px">
          <img src=${i.url} style="width: ${this.trueWidth}px; height: ${this.trueHeight}px; transform:
          scale(${s})translate3d(${o}px, ${r}px, ${h}px)rotateZ(${this.rotate*90}deg)">
        </div>
      </div>`,this.$emit("real-time",i)},reload(){let t=new Image;t.onload=()=>{this.w=parseFloat(window.getComputedStyle(this.$refs.cropper).width),this.h=parseFloat(window.getComputedStyle(this.$refs.cropper).height),this.trueWidth=t.width,this.trueHeight=t.height,this.original?this.scale=1:this.scale=this.checkedMode(),this.$nextTick(()=>{this.x=-(this.trueWidth-this.trueWidth*this.scale)/2+(this.w-this.trueWidth*this.scale)/2,this.y=-(this.trueHeight-this.trueHeight*this.scale)/2+(this.h-this.trueHeight*this.scale)/2,this.loading=!1,this.autoCrop&&this.goAutoCrop(),this.$emit("img-load","success"),setTimeout(()=>{this.showPreview()},20)})},t.onerror=()=>{this.$emit("img-load","error")},t.src=this.imgs},checkedMode(){let t=1,e=this.trueWidth,s=this.trueHeight;const i=this.mode.split(" ");switch(i[0]){case"contain":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case"cover":e=this.w,t=e/this.trueWidth,s=s*t,s<this.h&&(s=this.h,t=s/this.trueHeight);break;default:try{let o=i[0];if(o.search("px")!==-1){o=o.replace("px",""),e=parseFloat(o);const r=e/this.trueWidth;let h=1,a=i[1];a.search("px")!==-1&&(a=a.replace("px",""),s=parseFloat(a),h=s/this.trueHeight),t=Math.min(r,h)}if(o.search("%")!==-1&&(o=o.replace("%",""),e=parseFloat(o)/100*this.w,t=e/this.trueWidth),i.length===2&&o==="auto"){let r=i[1];r.search("px")!==-1&&(r=r.replace("px",""),s=parseFloat(r),t=s/this.trueHeight),r.search("%")!==-1&&(r=r.replace("%",""),s=parseFloat(r)/100*this.h,t=s/this.trueHeight)}}catch{t=1}}return t},goAutoCrop(t,e){if(this.imgs===""||this.imgs===null)return;this.clearCrop(),this.cropping=!0;let s=this.w,i=this.h;if(this.centerBox){const h=Math.abs(this.rotate)%2>0;let a=(h?this.trueHeight:this.trueWidth)*this.scale,l=(h?this.trueWidth:this.trueHeight)*this.scale;s=a<s?a:s,i=l<i?l:i}var o=t||parseFloat(this.autoCropWidth),r=e||parseFloat(this.autoCropHeight);(o===0||r===0)&&(o=s*.8,r=i*.8),o=o>s?s:o,r=r>i?i:r,this.fixed&&(r=o/this.fixedNumber[0]*this.fixedNumber[1]),r>this.h&&(r=this.h,o=r/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(o,r)},changeCrop(t,e){if(this.centerBox){let s=this.getImgAxis();t>s.x2-s.x1&&(t=s.x2-s.x1,e=t/this.fixedNumber[0]*this.fixedNumber[1]),e>s.y2-s.y1&&(e=s.y2-s.y1,t=e/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick(()=>{this.cropOffsertX=(this.w-this.cropW)/2,this.cropOffsertY=(this.h-this.cropH)/2,this.centerBox&&this.moveCrop(null,!0)})},refresh(){this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.clearCrop(),this.$nextTick(()=>{this.checkedImg()})},rotateLeft(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear(){this.rotate=0},checkoutImgAxis(t,e,s){t=t||this.x,e=e||this.y,s=s||this.scale;let i=!0;if(this.centerBox){let o=this.getImgAxis(t,e,s),r=this.getCropAxis();o.x1>=r.x1&&(i=!1),o.x2<=r.x2&&(i=!1),o.y1>=r.y1&&(i=!1),o.y2<=r.y2&&(i=!1)}return i}},mounted(){this.support="onwheel"in document.createElement("div")?"wheel":document.onmousewheel!==void 0?"mousewheel":"DOMMouseScroll";let t=this;var e=navigator.userAgent;this.isIOS=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(s,i,o){for(var r=atob(this.toDataURL(i,o).split(",")[1]),h=r.length,a=new Uint8Array(h),l=0;l<h;l++)a[l]=r.charCodeAt(l);s(new Blob([a],{type:t.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},unmounted(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.cancelScale()}}),pt={key:0,class:"cropper-box"},ut=["src"],dt={class:"cropper-view-box"},gt=["src"],ft={key:1};function mt(t,e,s,i,o,r){return Y(),M("div",{class:"vue-cropper",ref:"cropper",onMouseover:e[28]||(e[28]=(...h)=>t.scaleImg&&t.scaleImg(...h)),onMouseout:e[29]||(e[29]=(...h)=>t.cancelScale&&t.cancelScale(...h))},[t.imgs?(Y(),M("div",pt,[k(m("div",{class:"cropper-box-canvas",style:S({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+t.x/t.scale+"px,"+t.y/t.scale+"px,0)rotateZ("+t.rotate*90+"deg)"})},[m("img",{src:t.imgs,alt:"cropper-img",ref:"cropperImg"},null,8,ut)],4),[[T,!t.loading]])])):E("",!0),m("div",{class:R(["cropper-drag-box",{"cropper-move":t.move&&!t.crop,"cropper-crop":t.crop,"cropper-modal":t.cropping}]),onMousedown:e[0]||(e[0]=(...h)=>t.startMove&&t.startMove(...h)),onTouchstart:e[1]||(e[1]=(...h)=>t.startMove&&t.startMove(...h))},null,34),k(m("div",{class:"cropper-crop-box",style:S({width:t.cropW+"px",height:t.cropH+"px",transform:"translate3d("+t.cropOffsertX+"px,"+t.cropOffsertY+"px,0)"})},[m("span",dt,[m("img",{style:S({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+(t.x-t.cropOffsertX)/t.scale+"px,"+(t.y-t.cropOffsertY)/t.scale+"px,0)rotateZ("+t.rotate*90+"deg)"}),src:t.imgs,alt:"cropper-img"},null,12,gt)]),m("span",{class:"cropper-face cropper-move",onMousedown:e[2]||(e[2]=(...h)=>t.cropMove&&t.cropMove(...h)),onTouchstart:e[3]||(e[3]=(...h)=>t.cropMove&&t.cropMove(...h))},null,32),t.info?(Y(),M("span",{key:0,class:"crop-info",style:S({top:t.cropInfo.top})},N(t.cropInfo.width)+" × "+N(t.cropInfo.height),5)):E("",!0),t.fixedBox?E("",!0):(Y(),M("span",ft,[m("span",{class:"crop-line line-w",onMousedown:e[4]||(e[4]=h=>t.changeCropSize(h,!1,!0,0,1)),onTouchstart:e[5]||(e[5]=h=>t.changeCropSize(h,!1,!0,0,1))},null,32),m("span",{class:"crop-line line-a",onMousedown:e[6]||(e[6]=h=>t.changeCropSize(h,!0,!1,1,0)),onTouchstart:e[7]||(e[7]=h=>t.changeCropSize(h,!0,!1,1,0))},null,32),m("span",{class:"crop-line line-s",onMousedown:e[8]||(e[8]=h=>t.changeCropSize(h,!1,!0,0,2)),onTouchstart:e[9]||(e[9]=h=>t.changeCropSize(h,!1,!0,0,2))},null,32),m("span",{class:"crop-line line-d",onMousedown:e[10]||(e[10]=h=>t.changeCropSize(h,!0,!1,2,0)),onTouchstart:e[11]||(e[11]=h=>t.changeCropSize(h,!0,!1,2,0))},null,32),m("span",{class:"crop-point point1",onMousedown:e[12]||(e[12]=h=>t.changeCropSize(h,!0,!0,1,1)),onTouchstart:e[13]||(e[13]=h=>t.changeCropSize(h,!0,!0,1,1))},null,32),m("span",{class:"crop-point point2",onMousedown:e[14]||(e[14]=h=>t.changeCropSize(h,!1,!0,0,1)),onTouchstart:e[15]||(e[15]=h=>t.changeCropSize(h,!1,!0,0,1))},null,32),m("span",{class:"crop-point point3",onMousedown:e[16]||(e[16]=h=>t.changeCropSize(h,!0,!0,2,1)),onTouchstart:e[17]||(e[17]=h=>t.changeCropSize(h,!0,!0,2,1))},null,32),m("span",{class:"crop-point point4",onMousedown:e[18]||(e[18]=h=>t.changeCropSize(h,!0,!1,1,0)),onTouchstart:e[19]||(e[19]=h=>t.changeCropSize(h,!0,!1,1,0))},null,32),m("span",{class:"crop-point point5",onMousedown:e[20]||(e[20]=h=>t.changeCropSize(h,!0,!1,2,0)),onTouchstart:e[21]||(e[21]=h=>t.changeCropSize(h,!0,!1,2,0))},null,32),m("span",{class:"crop-point point6",onMousedown:e[22]||(e[22]=h=>t.changeCropSize(h,!0,!0,1,2)),onTouchstart:e[23]||(e[23]=h=>t.changeCropSize(h,!0,!0,1,2))},null,32),m("span",{class:"crop-point point7",onMousedown:e[24]||(e[24]=h=>t.changeCropSize(h,!1,!0,0,2)),onTouchstart:e[25]||(e[25]=h=>t.changeCropSize(h,!1,!0,0,2))},null,32),m("span",{class:"crop-point point8",onMousedown:e[26]||(e[26]=h=>t.changeCropSize(h,!0,!0,2,2)),onTouchstart:e[27]||(e[27]=h=>t.changeCropSize(h,!0,!0,2,2))},null,32)]))],4),[[T,t.cropping]])],544)}const vt=nt(lt,[["render",mt],["__scopeId","data-v-69939069"]]),wt=["src"],Ct={class:"avatar-upload-preview"},xt=["src"],yt=A({__name:"userAvatar",setup(t){const e=D(),{proxy:s}=F(),i=I(!1),o=I(!1),r=I("修改头像"),h=I({}),a=V({img:e.avatar,autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0,outputType:"png",fileName:"",previews:{},visible:!1}),l=()=>{i.value=!0},n=()=>{o.value=!0},c=()=>{},p=()=>{h.value.rotateLeft()},u=()=>{h.value.rotateRight()},X=b=>{b=b||1,h.value.changeScale(b)},g=b=>{if(b.type.indexOf("image/")==-1)s==null||s.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");else{const d=new FileReader;d.readAsDataURL(b),d.onload=()=>{a.img=d.result,a.fileName=b.name}}},f=async()=>{h.value.getCropBlob(async b=>{const d=new FormData;d.append("avatarfile",b,a.fileName);const O=await it(d);i.value=!1,a.img=O.data.imgUrl,e.setAvatar(a.img),s==null||s.$modal.msgSuccess("修改成功"),o.value=!1})},v=b=>{a.previews=b},w=()=>{a.img=e.avatar,a.visible=!1};return(b,d)=>{const O=st,L=et,B=q("Upload"),_=K,W=J,P=tt,U=j;return Y(),M("div",{class:"user-info-head",onClick:d[6]||(d[6]=H=>l())},[m("img",{src:y(a).img,title:"点击上传头像",class:"img-circle img-lg"},null,8,wt),C(U,{modelValue:y(i),"onUpdate:modelValue":d[5]||(d[5]=H=>G(i)?i.value=H:null),title:y(r),width:"800px","append-to-body":"",onOpened:n,onClose:w},{default:x(()=>[C(L,null,{default:x(()=>[C(O,{xs:24,md:12,style:{height:"350px"}},{default:x(()=>[y(o)?(Y(),Z(y(vt),{key:0,ref_key:"cropper",ref:h,img:y(a).img,info:!0,"auto-crop":y(a).autoCrop,"auto-crop-width":y(a).autoCropWidth,"auto-crop-height":y(a).autoCropHeight,"fixed-box":y(a).fixedBox,"output-type":y(a).outputType,onRealTime:v},null,8,["img","auto-crop","auto-crop-width","auto-crop-height","fixed-box","output-type"])):E("",!0)]),_:1}),C(O,{xs:24,md:12,style:{height:"350px"}},{default:x(()=>[m("div",Ct,[m("img",{src:y(a).previews.url,style:S(y(a).previews.img)},null,12,xt)])]),_:1})]),_:1}),d[9]||(d[9]=m("br",null,null,-1)),C(L,null,{default:x(()=>[C(O,{lg:2,md:2},{default:x(()=>[C(P,{action:"#","http-request":c,"show-file-list":!1,"before-upload":g},{default:x(()=>[C(W,null,{default:x(()=>[d[7]||(d[7]=$(" 选择 ")),C(_,{class:"el-icon--right"},{default:x(()=>[C(B)]),_:1})]),_:1})]),_:1})]),_:1}),C(O,{lg:{span:1,offset:2},md:2},{default:x(()=>[C(W,{icon:"Plus",onClick:d[0]||(d[0]=H=>X(1))})]),_:1}),C(O,{lg:{span:1,offset:1},md:2},{default:x(()=>[C(W,{icon:"Minus",onClick:d[1]||(d[1]=H=>X(-1))})]),_:1}),C(O,{lg:{span:1,offset:1},md:2},{default:x(()=>[C(W,{icon:"RefreshLeft",onClick:d[2]||(d[2]=H=>p())})]),_:1}),C(O,{lg:{span:1,offset:1},md:2},{default:x(()=>[C(W,{icon:"RefreshRight",onClick:d[3]||(d[3]=H=>u())})]),_:1}),C(O,{lg:{span:2,offset:6},md:2},{default:x(()=>[C(W,{type:"primary",onClick:d[4]||(d[4]=H=>f())},{default:x(()=>d[8]||(d[8]=[$("提 交")])),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue","title"])])}}}),Mt=Q(yt,[["__scopeId","data-v-ece25be9"]]);export{Mt as default};
