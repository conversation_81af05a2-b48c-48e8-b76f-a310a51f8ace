package com.biz.epms.eppcs.domain.bo;

import com.biz.epms.eppcs.domain.EppcsDetectionResult;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 检测结果业务对象 eppcs_detection_result
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = EppcsDetectionResult.class, reverseConvertGenerate = false)
public class EppcsDetectionResultBo extends BaseEntity {

    /**
     * 检测结果id
     */
    private Long resultId;

    /**
     * 申请id
     */
    private Long applicationId;

    /**
     * 样品编号
     */
    private String sampleNumber;

    /**
     * 检测项目名称
     */
    private String projectName;

    /**
     * 检测标准
     */
    private Integer detectionStandard;

    /**
     * 检测日期
     */
    private Date detectionDate;

    /**
     * 报送时间
     */
    private Date submitTime;

    /**
     * 检测状态 (0草稿,1驳回,2待属地单位确认,3待煤层气管理中心确认,4检测完成)
     */
    private Integer detectionStatus;

    /**
     * 检测报告附件
     */
    private Long reportFile;

    /**
     * 检测结果数据 JSON格式
     */
    private String resultData;

    /**
     * 检测结果 (0达标,1不达标)
     */
    private Integer resultStatus;

    /**
     * 审批时间
     */
    private Date approvalTime;

    /**
     * 审批建议
     */
    private String approvalRemark;

    /**
     * 审批人
     */
    private String approver;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预警标记 (0无预警,1有预警)
     */
    private Integer warningFlag;

    /**
     * 申请ID列表（逗号分隔）
     */
    private String applicationIds;

    /**
     * 检测日期范围开始
     */
    private String detectionDateStart;

    /**
     * 检测日期范围结束
     */
    private String detectionDateEnd;

}
