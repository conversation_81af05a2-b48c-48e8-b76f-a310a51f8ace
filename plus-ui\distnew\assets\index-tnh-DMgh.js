import{aC as t}from"./index-D07cMzhp.js";const o=e=>t({url:"/epwf/wasteWarehouseApplication/list",method:"get",params:e}),p=e=>t({url:"/epwf/wasteWarehouseApplication/"+e,method:"get"}),u=e=>t({url:"/epwf/wasteWarehouseApplication",method:"post",data:e}),i=e=>t({url:"/epwf/wasteWarehouseApplication",method:"put",data:e}),n=(e,a)=>t({url:"/epwf/wasteWarehouseApplication/submitReview",method:"post",params:{recordId:e,action:a},data:{}}),c=(e,a,r)=>t({url:"/epwf/wasteWarehouseApplication/rejectReview",method:"post",params:{recordId:e,action:a},data:{approvalOpinions:r}}),w=(e,a)=>t({url:"/epwf/wasteWarehouseApplication/rejectReview",method:"post",params:{recordId:e,action:a},data:{}}),l=e=>t({url:"/epwf/wasteWarehouseApplication/getwfSum",method:"get",params:e});export{w as a,p as b,u as c,l as g,o as l,c as r,n as s,i as u};
