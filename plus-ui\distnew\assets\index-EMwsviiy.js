import{d as Z,h as Ce,r,ai as W,ak as he,b as we,aH as Ve,c as Pe,o as y,p as t,t as n,w as f,q as X,a7 as Ee,M as Ne,e as o,A as Te,G as Se,H as Ie,K as xe,J as m,am as Y,aI as Ue,x as w,ay as $e,y as Fe,ax as Re,cS as De,aL as Ae,v as Ke,az as qe,a8 as A,I as Be,aA as Le,N as Me,n as ze}from"./index-D07cMzhp.js";import{E as He}from"./el-tree-DW6MoFaI.js";import{E as Qe}from"./el-row-CikYE3zA.js";import{_ as Ge}from"./index-BWMgqvQ9.js";import{E as Je}from"./el-col-BaG5Rg5z.js";import{l as Oe,c as je,g as <PERSON>,u as Xe,a as Ye,d as Ze}from"./index-wKny4I4G.js";import{b as ea}from"./index-MyBaFeck.js";const aa={class:"p-2"},ta={class:"mb-[10px]"},la={class:"dialog-footer"},na=Z({name:"TenantPackage"}),pa=Z({...na,setup(oa){const{proxy:s}=Ce(),K=r([]),V=r(!1),b=r(!0),P=r(!0),x=r([]),q=r(!0),B=r(!0),U=r(0),E=r(!1),N=r(!1),T=r([]),k=r(),L=r(),$=r(),g=W({visible:!1,title:""}),M={packageId:void 0,packageName:"",menuIds:"",remark:"",menuCheckStrictly:!0},ee=W({form:{...M},queryParams:{pageNum:1,pageSize:10,packageName:""},rules:{packageId:[{required:!0,message:"租户套餐id不能为空",trigger:"blur"}],packageName:[{required:!0,message:"套餐名称不能为空",trigger:"blur"}]}}),{queryParams:p,form:d,rules:ae}=he(ee),te=()=>{var i,u;const a=(i=k.value)==null?void 0:i.getCheckedKeys(),e=(u=k.value)==null?void 0:u.getHalfCheckedKeys();return e&&(a==null||a.unshift(...e)),a},z=async a=>{const e=await ea(a);return T.value=e.data.menus,Promise.resolve(e)},v=async()=>{b.value=!0;const a=await Oe(p.value);K.value=a.rows,U.value=a.total,b.value=!1},le=async a=>{const e=a.status==="0"?"启用":"停用",[i]=await Me(s==null?void 0:s.$modal.confirm('确认要"'+e+'""'+a.packageName+'"套餐吗？'));i?a.status=a.status==="0"?"1":"0":(await je(a.packageId,a.status),s==null||s.$modal.msgSuccess(e+"成功"))},ne=()=>{F(),g.visible=!1},F=()=>{var a,e;(a=k.value)==null||a.setCheckedKeys([]),E.value=!1,N.value=!1,d.value={...M},(e=$.value)==null||e.resetFields()},R=()=>{p.value.pageNum=1,v()},oe=()=>{var a;(a=L.value)==null||a.resetFields(),R()},se=a=>{x.value=a.map(e=>e.packageId),q.value=a.length!=1,B.value=!a.length},ie=(a,e)=>{{const i=T.value;for(let u=0;u<i.length;u++)k.value&&(k.value.store.nodesMap[i[u].id].expanded=a)}},de=(a,e)=>{var i;(i=k.value)==null||i.setCheckedNodes(a?T.value:[])},ue=(a,e)=>{d.value.menuCheckStrictly=a},re=async()=>{F(),await z(0),g.visible=!0,g.title="添加租户套餐"},H=async a=>{F();const e=(a==null?void 0:a.packageId)||x.value[0],i=await We(e);d.value=i.data;const u=await z(e);g.visible=!0,g.title="修改租户套餐",u.data.checkedKeys.forEach(c=>{ze(()=>{var C;(C=k.value)==null||C.setChecked(c,!0,!1)})})},ce=()=>{var a;(a=$.value)==null||a.validate(async e=>{e&&(V.value=!0,d.value.menuIds=te(),d.value.packageId!=null?await Xe(d.value).finally(()=>V.value=!1):await Ye(d.value).finally(()=>V.value=!1),s==null||s.$modal.msgSuccess("操作成功"),g.visible=!1,await v())})},Q=async a=>{const e=(a==null?void 0:a.packageId)||x.value;await(s==null?void 0:s.$modal.confirm('是否确认删除租户套餐编号为"'+e+'"的数据项？').finally(()=>{b.value=!1})),await Ze(e),b.value=!0,await v(),s==null||s.$modal.msgSuccess("删除成功")},me=()=>{s==null||s.download("system/tenant/package/export",{...p.value},`tenantPackage_${new Date().getTime()}.xlsx`)};return we(()=>{v()}),(a,e)=>{var O,j;const i=Se,u=Te,c=xe,C=Ne,G=Ee,S=Je,pe=Ge,ge=Qe,h=Re,fe=De,J=Ae,ke=$e,ve=qe,D=Be,_e=He,ye=Le,_=Ve("hasPermi"),be=Ke;return y(),Pe("div",aa,[t(Ue,{"enter-active-class":(O=o(s))==null?void 0:O.animate.searchAnimate.enter,"leave-active-class":(j=o(s))==null?void 0:j.animate.searchAnimate.leave},{default:n(()=>[f(X("div",ta,[t(G,{shadow:"hover"},{default:n(()=>[t(C,{ref_key:"queryFormRef",ref:L,model:o(p),inline:!0},{default:n(()=>[t(u,{label:"套餐名称",prop:"packageName"},{default:n(()=>[t(i,{modelValue:o(p).packageName,"onUpdate:modelValue":e[0]||(e[0]=l=>o(p).packageName=l),placeholder:"请输入套餐名称",clearable:"",onKeyup:Ie(R,["enter"])},null,8,["modelValue"])]),_:1}),t(u,null,{default:n(()=>[t(c,{type:"primary",icon:"Search",onClick:R},{default:n(()=>e[15]||(e[15]=[m("搜索")])),_:1}),t(c,{icon:"Refresh",onClick:oe},{default:n(()=>e[16]||(e[16]=[m("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[Y,o(P)]])]),_:1},8,["enter-active-class","leave-active-class"]),t(G,{shadow:"hover"},{header:n(()=>[t(ge,{gutter:10,class:"mb8"},{default:n(()=>[t(S,{span:1.5},{default:n(()=>[f((y(),w(c,{type:"primary",plain:"",icon:"Plus",onClick:re},{default:n(()=>e[17]||(e[17]=[m(" 新增 ")])),_:1})),[[_,["system:tenantPackage:add"]]])]),_:1}),t(S,{span:1.5},{default:n(()=>[f((y(),w(c,{type:"success",plain:"",icon:"Edit",disabled:o(q),onClick:e[1]||(e[1]=l=>H())},{default:n(()=>e[18]||(e[18]=[m(" 修改 ")])),_:1},8,["disabled"])),[[_,["system:tenantPackage:edit"]]])]),_:1}),t(S,{span:1.5},{default:n(()=>[f((y(),w(c,{type:"danger",plain:"",icon:"Delete",disabled:o(B),onClick:e[2]||(e[2]=l=>Q())},{default:n(()=>e[19]||(e[19]=[m(" 删除 ")])),_:1},8,["disabled"])),[[_,["system:tenantPackage:remove"]]])]),_:1}),t(S,{span:1.5},{default:n(()=>[f((y(),w(c,{type:"warning",plain:"",icon:"Download",onClick:me},{default:n(()=>e[20]||(e[20]=[m("导出 ")])),_:1})),[[_,["system:tenantPackage:export"]]])]),_:1}),t(pe,{"show-search":o(P),"onUpdate:showSearch":e[3]||(e[3]=l=>A(P)?P.value=l:null),onQueryTable:v},null,8,["show-search"])]),_:1})]),default:n(()=>[f((y(),w(ke,{data:o(K),border:"",onSelectionChange:se},{default:n(()=>[t(h,{type:"selection",width:"55",align:"center"}),Fe("",!0),t(h,{label:"套餐名称",align:"center",prop:"packageName"}),t(h,{label:"备注",align:"center",prop:"remark"}),t(h,{label:"状态",align:"center",prop:"status"},{default:n(l=>[t(fe,{modelValue:l.row.status,"onUpdate:modelValue":I=>l.row.status=I,"active-value":"0","inactive-value":"1",onClick:I=>le(l.row)},null,8,["modelValue","onUpdate:modelValue","onClick"])]),_:1}),t(h,{align:"center","class-name":"small-padding fixed-width",label:"操作"},{default:n(l=>[t(J,{content:"修改",placement:"top"},{default:n(()=>[f(t(c,{link:"",type:"primary",icon:"Edit",onClick:I=>H(l.row)},null,8,["onClick"]),[[_,["system:tenantPackage:edit"]]])]),_:2},1024),t(J,{content:"删除",placement:"top"},{default:n(()=>[f(t(c,{link:"",type:"primary",icon:"Delete",onClick:I=>Q(l.row)},null,8,["onClick"]),[[_,["system:tenantPackage:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[be,o(b)]]),f(t(ve,{page:o(p).pageNum,"onUpdate:page":e[4]||(e[4]=l=>o(p).pageNum=l),limit:o(p).pageSize,"onUpdate:limit":e[5]||(e[5]=l=>o(p).pageSize=l),total:o(U),onPagination:v},null,8,["page","limit","total"]),[[Y,o(U)>0]])]),_:1}),t(ye,{modelValue:o(g).visible,"onUpdate:modelValue":e[14]||(e[14]=l=>o(g).visible=l),title:o(g).title,width:"500px","append-to-body":""},{footer:n(()=>[X("div",la,[t(c,{loading:o(V),type:"primary",onClick:ce},{default:n(()=>e[24]||(e[24]=[m("确 定")])),_:1},8,["loading"]),t(c,{onClick:ne},{default:n(()=>e[25]||(e[25]=[m("取 消")])),_:1})])]),default:n(()=>[t(C,{ref_key:"tenantPackageFormRef",ref:$,model:o(d),rules:o(ae),"label-width":"80px"},{default:n(()=>[t(u,{label:"套餐名称",prop:"packageName"},{default:n(()=>[t(i,{modelValue:o(d).packageName,"onUpdate:modelValue":e[6]||(e[6]=l=>o(d).packageName=l),placeholder:"请输入套餐名称"},null,8,["modelValue"])]),_:1}),t(u,{label:"关联菜单"},{default:n(()=>[t(D,{modelValue:o(E),"onUpdate:modelValue":e[7]||(e[7]=l=>A(E)?E.value=l:null),onChange:e[8]||(e[8]=l=>ie(l,"menu"))},{default:n(()=>e[21]||(e[21]=[m("展开/折叠")])),_:1},8,["modelValue"]),t(D,{modelValue:o(N),"onUpdate:modelValue":e[9]||(e[9]=l=>A(N)?N.value=l:null),onChange:e[10]||(e[10]=l=>de(l,"menu"))},{default:n(()=>e[22]||(e[22]=[m("全选/全不选 ")])),_:1},8,["modelValue"]),t(D,{modelValue:o(d).menuCheckStrictly,"onUpdate:modelValue":e[11]||(e[11]=l=>o(d).menuCheckStrictly=l),onChange:e[12]||(e[12]=l=>ue(l,"menu"))},{default:n(()=>e[23]||(e[23]=[m("父子联动 ")])),_:1},8,["modelValue"]),t(_e,{ref_key:"menuTreeRef",ref:k,class:"tree-border",data:o(T),"show-checkbox":"","node-key":"id","check-strictly":!o(d).menuCheckStrictly,"empty-text":"加载中，请稍候",props:{label:"label",children:"children"}},null,8,["data","check-strictly"])]),_:1}),t(u,{label:"备注",prop:"remark"},{default:n(()=>[t(i,{modelValue:o(d).remark,"onUpdate:modelValue":e[13]||(e[13]=l=>o(d).remark=l),placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}});export{pa as default};
