<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="年份" prop="year">
              <el-date-picker v-model="year" format="YYYY" placeholder="选择日期" type="year" value-format="YYYY" />
            </el-form-item>
            <el-form-item label-width="90" label="集中站名称" prop="disposeId">
              <el-select v-model="queryParams.disposeId" clearable filterable placeholder="请选择集中站" style="width: 240px">
                <el-option v-for="item in allList" :key="item.id" :label="item.label" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table
        ref="reportTable"
        v-loading="loading"
        :data="tableData"
        :cell-class-name="handleCellClass"
        :span-method="handleSpanMethod"
        border
        stripe
      >
        <el-table-column align="center" label="集中站处理" prop="stationName" width="150">
          <template #default="scope">
            <el-link @click="handleShowSystemSelect(scope.row)">
              {{ scope.row.stationName }} <el-icon class="el-icon--right"><View /></el-icon>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" label="时间" prop="month" width="100" />
        <el-table-column label="泥浆处理量（方）" prop="slurryDisposeAmount" align="center" />
        <el-table-column label="泥浆暂存量（方）" prop="slurryStagingAmount" align="center" />
        <el-table-column label="泥饼拉运量（方）" prop="mudPullingAmount" align="center" />
        <el-table-column label="泥饼暂存量（方）" prop="mudStagingAmount" align="center" />
        <el-table-column label="滤水拉运量（方）" prop="waterPullingAmount" align="center" />
        <el-table-column label="滤水暂存量（方）" prop="waterStagingAmount" align="center" />
      </el-table>
    </el-card>

    <el-dialog v-model="showSystemSelect" title="系统查询" width="1600px">
      <DrillingDaily :dispose-name="showSystemSelectStationName" />
    </el-dialog>
  </div>
</template>

<script lang="ts" name="MonthlyStatisticsJiZhong" setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { jiZhongMonthStat } from '@/api/epms/epnj/mudDaily';
import type { MudDailyQuery } from '@/api/epms/epnj/mudDaily/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import * as XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style-vite';
import { saveAs } from 'file-saver';
import DrillingDaily from '@/views/epms/epnj/drillingDaily/index.vue';
import { View } from '@element-plus/icons-vue';

// 定义表格行数据类型
interface TableRow {
  stationName: string; // 集中站名称（已转换为显示名称）
  stationKey: string; // 集中站原始ID（用于合并行）
  month: string; // 月份（如"1月份"）
  slurryDisposeAmount: string; // 泥浆处理量
  slurryStagingAmount: string; // 泥浆暂存量
  mudPullingAmount: string; // 泥饼拉运量
  mudStagingAmount: string; // 泥饼暂存量
  waterPullingAmount: string; // 滤水拉运量
  waterStagingAmount: string; // 滤水暂存量
  isLoss: boolean; // 是否为损耗行
}

// 定义后端返回的月度数据类型
interface MonthlyDataItem {
  mudStagingAmount: string;
  waterStagingAmount: string;
  waterPullingAmount: string;
  mudPullingAmount: string;
  slurryDisposeAmount: string;
  slurryStagingAmount: string;
}

// 定义后端返回的集中站数据类型
interface StationData {
  stationName: string; // 集中站ID
  monthlyData: Record<string, MonthlyDataItem>; // 月度数据（key为月份1-12）
  lossStr: string; // 损耗计算公式
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const showSystemSelect = ref(false);
const showSystemSelectStationName = ref('');
// 响应式数据
const tableData = ref<TableRow[]>([]); // 表格渲染数据
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const reportTable = ref<InstanceType<any>>(null);
const areaList = ref<OperationAreaVO[]>([]);
const allList = ref<{ id: string; label: string }[]>([]); // 集中站ID与名称映射
const year = ref(new Date().getFullYear().toString());
const queryFormRef = ref<ElFormInstance>();

// 查询参数
const queryParams = reactive<MudDailyQuery & { params?: { date?: string } }>({
  pageNum: 1,
  pageSize: 10,
  disposeType: 1,
  disposeId: undefined,
  slurryDisposeAmount: undefined,
  mudPullingAmount: undefined,
  mudStagingAmount: undefined,
  waterPullingAmount: undefined,
  waterStagingAmount: undefined,
  loss: undefined,
  params: {
    date: undefined
  }
});

// 用于记录集中站合并行数的映射表
const spanMap = new Map<string, number>();

/** 获取集中站列表（ID与名称映射） */
const queryAreaList = async () => {
  const areaQuery: OperationAreaQuery = { operationAreaType: 2 };
  try {
    const res = await listOperationArea(areaQuery);
    areaList.value = res.rows || [];
    // 构建ID与名称的映射关系
    allList.value = areaList.value.map((item) => ({
      id: item.operationAreaId as string,
      label: item.operationAreaName || `未知集中站(${item.operationAreaId})`
    }));
  } catch (error) {
    console.error('获取集中站列表失败:', error);
    allList.value = [];
  }
};

const isLossExceeded = (lossText: string) => {
  try {
    const currentLossMatch = lossText.match(/当前损耗[:：]\s*([+-]?\d+(\.\d+)?)/);
    const maxLossMatch = lossText.match(/损耗最大值[:：]\s*(\d+(\.\d+)?)/);
    if (currentLossMatch && maxLossMatch) {
      const currentLoss = parseFloat(currentLossMatch[1]);
      const maxLoss = parseFloat(maxLossMatch[1]);

      return currentLoss > maxLoss;
    } else {
    }
  } catch (error) {
    console.error('解析损耗数值失败:', error);
  }
  return false;
};

const handleCellClass = ({ row, columnIndex }: { row: TableRow; columnIndex: number }) => {
  if (row.isLoss && columnIndex === 2) {
    if (isLossExceeded(row.slurryDisposeAmount)) {
      return 'text-red';
    }
  }
  return '';
};

/** 将后端数据转换为表格渲染数据 */
const transformBackendData = (backendData: StationData[]) => {
  const result: TableRow[] = [];
  spanMap.clear();

  backendData.forEach((station) => {
    const { stationName: stationId, monthlyData, lossStr } = station;
    // 获取集中站显示名称（ID转名称）
    const stationLabel = allList.value.find((item) => item.id === stationId)?.label || stationId;

    // 处理1-12月数据（确保月份顺序）
    const months = Array.from({ length: 12 }, (_, i) => (i + 1).toString());
    months.forEach((month) => {
      const monthData = monthlyData[month] || {
        mudStagingAmount: '0',
        waterStagingAmount: '0',
        waterPullingAmount: '0',
        mudPullingAmount: '0',
        slurryDisposeAmount: '0',
        slurryStagingAmount: '0'
      };

      result.push({
        stationName: stationLabel,
        stationKey: stationId,
        month: `${month}月份`,
        slurryDisposeAmount: monthData.slurryDisposeAmount,
        slurryStagingAmount: monthData.slurryStagingAmount,
        mudPullingAmount: monthData.mudPullingAmount,
        mudStagingAmount: monthData.mudStagingAmount,
        waterPullingAmount: monthData.waterPullingAmount,
        waterStagingAmount: monthData.waterStagingAmount,
        isLoss: false
      });
    });

    // 添加损耗行
    result.push({
      stationName: stationLabel,
      stationKey: stationId,
      month: '损耗≤10%',
      slurryDisposeAmount: lossStr,
      slurryStagingAmount: '',
      mudPullingAmount: '',
      mudStagingAmount: '',
      waterPullingAmount: '',
      waterStagingAmount: '',
      isLoss: true
    });

    // 记录当前集中站的总行数（12个月 + 1行损耗）
    spanMap.set(stationId, 13);
  });
  return result;
};

/** 表格合并行逻辑 */
const handleSpanMethod = ({ row, rowIndex, columnIndex }: { row: TableRow; rowIndex: number; columnIndex: number }) => {
  // 集中站名称列（第0列）合并
  if (columnIndex === 0) {
    // 只在每个集中站的第一行设置合并行数
    if (rowIndex === 0 || tableData.value[rowIndex - 1].stationKey !== row.stationKey) {
      return {
        rowspan: spanMap.get(row.stationKey) || 1,
        colspan: 1
      };
    } else {
      // 同一集中站的其他行不显示名称
      return {
        rowspan: 0,
        colspan: 0
      };
    }
  }

  // 损耗行的数据列合并（从泥浆处理量列开始合并6列）
  if (row.isLoss && columnIndex >= 2) {
    return {
      rowspan: 1,
      colspan: 6
    };
  }

  // 其他列不合并
  return {
    rowspan: 1,
    colspan: 1
  };
};

/** 导出表格数据 */
const handleExport = () => {
  try {
    const tableEl = reportTable.value?.$el;
    if (!tableEl) return;

    // 获取表格DOM（处理固定列）
    let tableDom = tableEl.querySelector('.el-table__fixed') as HTMLElement;
    if (!tableDom) {
      tableDom = tableEl.querySelector('.el-table__body-wrapper table') as HTMLElement;
    }

    // 转换表格为工作簿
    const wb = XLSX.utils.table_to_book(tableDom, { raw: true });
    const wsName = wb.SheetNames[0];
    const ws = wb.Sheets[wsName];

    // 设置列宽自适应
    const range = XLSX.utils.decode_range(ws['!ref'] || 'A1:H1');
    const colWidths = [];
    for (let c = range.s.c; c <= range.e.c; c++) {
      let maxWidth = 0;
      for (let r = range.s.r; r <= range.e.r; r++) {
        const cellAddr = XLSX.utils.encode_cell({ r, c });
        const cell = ws[cellAddr];
        if (cell && cell.v) {
          const text = String(cell.v);
          // 计算宽度（中文字符占2单位，英文字符占1单位）
          const width = text.split('').reduce((acc, char) => acc + (char.charCodeAt(0) > 255 ? 2 : 1), 0);
          maxWidth = Math.max(maxWidth, width);
        }
      }
      colWidths.push({ wch: Math.min(maxWidth + 2, 60) }); // 限制最大宽度
    }
    ws['!cols'] = colWidths;

    // 设置单元格样式（居中+边框）
    const baseStyle = {
      alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      },
      font: { sz: 11, name: '宋体', color: { rgb: '000000' } }
    };

    // 应用样式到所有单元格
    Object.keys(ws).forEach((cellAddr) => {
      if (!cellAddr.startsWith('!')) {
        const cell = ws[cellAddr];
        cell.s = { ...baseStyle };
        // 数字格式化
        if (typeof cell.v === 'number') {
          cell.z = '0.00';
        }
      }
    });

    // 处理合并单元格样式
    if (ws['!merges']) {
      ws['!merges'].forEach((merge) => {
        for (let r = merge.s.r; r <= merge.e.r; r++) {
          for (let c = merge.s.c; c <= merge.e.c; c++) {
            const addr = XLSX.utils.encode_cell({ r, c });
            if (!ws[addr]) ws[addr] = { t: 's', v: '' };
            ws[addr].s = { ...baseStyle };
          }
        }
      });
    }

    // 导出为Excel
    const wbout = XLSXStyle.write(wb, {
      bookType: 'xlsx',
      type: 'binary',
      cellStyles: true
    });
    saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), `集中站月度统计_${new Date().getTime()}.xlsx`);
  } catch (e) {
    console.error('导出失败:', e);
    proxy?.$message.error('导出失败，请重试');
  }
};

// 字符串转ArrayBuffer
const s2ab = (s: string) => {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xff;
  return buf;
};

/** 查询数据 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await jiZhongMonthStat(year.value, queryParams);
    // 转换数据并设置表格
    tableData.value = transformBackendData(res || []);
    total.value = tableData.value.length;
  } catch (error) {
    console.error('获取数据失败:', error);
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 重置查询条件 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

const handleShowSystemSelect = async (row?: TableRow) => {
  // 获取当前选中的集中站名称
  const selectedStationName = row?.stationName;
  if (selectedStationName) {
    // 显示系统选择弹窗
    showSystemSelect.value = true;
    // 设置当前选中的集中站名称
    showSystemSelectStationName.value = selectedStationName;
  }
};

// 页面加载时初始化
onMounted(() => {
  // 先获取集中站列表，再加载数据
  queryAreaList().then(() => {
    getList();
  });
});
</script>

<style scoped>
.allowClick {
  color: #1e9fff;
  background-color: #fdfdfd;
  cursor: pointer;
}
:deep(.el-table__cell.el-table__cell--merge) {
  /* 超限红色 */
  &.text-red {
    color: #ff4d4f !important;
    font-weight: 500;
  }
}

/* 确保单元格内的文本元素继承样式 */
:deep(.el-table__cell.el-table__cell--merge .cell) {
  color: inherit !important;
}
</style>
