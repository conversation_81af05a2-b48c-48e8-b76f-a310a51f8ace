import{d as re,a as He,r as b,h as $e,ak as te,bj as Le,ai as A,b as je,aH as xe,c as oe,o as u,p as l,t,w,q as U,a7 as Fe,M as Me,e as a,A as We,G as qe,H as ne,B as Te,F as Je,C as Oe,x as f,D as ze,a8 as ie,K as Be,J as s,am as de,aI as Ye,ay as Ke,ax as Qe,aJ as Ge,aL as Ze,y as j,v as Xe,az as he,aA as el,bc as ll,cA as al,aD as tl,Q as ol}from"./index-D07cMzhp.js";import{_ as nl}from"./index-DVHplxfU.js";import{E as il}from"./el-row-CikYE3zA.js";import{_ as dl}from"./index-BWMgqvQ9.js";import{E as sl}from"./el-date-picker-HyhB9X9n.js";import{l as rl,g as J,u as se,s as pl,a as ul}from"./index-DjjjlEwF.js";import{f as O}from"./types-DSjC7ANO.js";import{l as ml}from"./index-BhIIZXqy.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./dayjs.min-Brw96_N0.js";import"./el-tree-DW6MoFaI.js";import"./index-VIEDZI2D.js";const vl={class:"p-2"},fl={class:"mb-[10px]"},cl={class:"dialog-footer"},bl={class:"dialog-footer"},gl={class:"dialog-footer"},wl={class:"dialog-footer"},yl={style:{height:"68vh"}},kl=re({name:"constructionPrepare"}),Vl=re({...kl,setup(Nl){He();const z=b({}),{proxy:m}=$e(),{epfj_approval_status:pe}=te(m==null?void 0:m.useDict("epfj_approval_status")),K=b([]),Q=b([]),g=b(!1),B=b(!0),x=b(!0),ue=b([]),me=b(!0),ve=b(!0),Y=b(0),E=b(["",""]),G=b(),P=b(),Z=Le(),fe=b({}),S=A({visible:!1,title:""}),_=A({visible:!1,title:""}),C=A({visible:!1,title:""}),I=A({visible:!1,title:""}),X=A({visible:!1,url:""}),h={sealId:void 0,wellId:void 0,wellName:void 0,applicationId:void 0,applicationName:void 0,scrapId:void 0,scrapName:void 0,qukuaiId:void 0,qukuaiName:void 0,workAreaId:void 0,workAreaName:void 0,environmentalLicense:void 0,hsePlan:void 0,wellHandoverReport:void 0,emergencyPlan:void 0,shiftMeetingRecords:void 0,siteAcceptanceReport:void 0,dailyReport:void 0,supervisionRecord:void 0,workSummary:void 0,wellSealDescription:void 0,geologicalDesign:void 0,engineeringDesign:void 0,constructionPlanDesign:void 0,approvalStatus:void 0,approvalOpinions:void 0},ce=A({form:{...h},queryParams:{pageNum:1,pageSize:10,workAreaId:void 0,applicationName:void 0,wellName:void 0,params:{}},rules:{wellId:[{required:!0,message:"井不能为空",trigger:"blur"}],workAreaId:[{required:!0,message:"所属地不能为空",trigger:"blur"}]}}),{queryParams:v,form:n,rules:F}=te(ce),R=async()=>{B.value=!0,v.value.params={},m==null||m.addDateRange(v.value,E.value,"ApplicationDate");const i=await rl(v.value);Q.value=i.rows,Y.value=i.total,B.value=!1},M=()=>{H(),S.visible=!1,_.visible=!1,C.visible=!1,I.visible=!1},be=i=>(v.value.pageNum-1)*v.value.pageSize+i+1,ge=async i=>{H();const e=i==null?void 0:i.sealId,p=await J(e);Object.assign(n.value,p.data),S.visible=!0,S.title="许可证和作业计划上传"},we=async i=>{H();const e=i==null?void 0:i.sealId,p=await J(e);Object.assign(n.value,p.data),_.visible=!0,_.title="油水井交接"},ye=async i=>{H();const e=i==null?void 0:i.sealId,p=await J(e);Object.assign(n.value,p.data),C.visible=!0,C.title="风险交底"},ke=async i=>{H();const e=i==null?void 0:i.sealId,p=await J(e);Object.assign(n.value,p.data),I.visible=!0,I.title="开工验收"},W=()=>{var i;(i=P.value)==null||i.validate(async e=>{e&&(g.value=!0,n.value.sealId?(await se(n.value).finally(()=>g.value=!1),await pl(n.value.sealId,n.value.approvalStatus,0)):m==null||m.$modal.msgError("数据异常"),m==null||m.$modal.msgSuccess("操作成功"),S.visible=!1,_.visible=!1,C.visible=!1,I.visible=!1,await R())})},c=async(i,e)=>{i&&m.showAttachPreview({attachSourceId:i,attachSourceType:"wasteWellSealProcess",attachCategory:e})},Ve=async()=>{z.value.operationAreaParentId=0,z.value.operationAreaType=0;const i=await ml(z.value);K.value=i.rows},H=()=>{var i;n.value={...h},(i=P.value)==null||i.resetFields()},q=()=>{v.value.pageNum=1,R()},Ne=()=>{var i;E.value=["",""],(i=G.value)==null||i.resetFields(),q()},Se=i=>{ue.value=i.map(e=>e.wellId),me.value=i.length!=1,ve.value=!i.length},T=()=>{var i;(i=P.value)==null||i.validate(async e=>{e&&(g.value=!0,n.value.sealId?await se(n.value).finally(()=>g.value=!1):await ul(n.value).finally(()=>g.value=!1),m==null||m.$modal.msgSuccess("操作成功"),S.visible=!1,_.visible=!1,C.visible=!1,I.visible=!1,await R())})};return je(()=>{Ve(),R()}),(i,e)=>{var le,ae;const p=qe,r=We,_e=ze,Ce=Te,Ie=sl,d=Be,$=Me,ee=Fe,Pe=dl,Re=il,V=Qe,De=Ge,y=Ze,Ae=Ke,Ue=he,D=nl,L=el,k=xe("hasPermi"),Ee=Xe;return u(),oe("div",vl,[l(Ye,{"enter-active-class":(le=a(m))==null?void 0:le.animate.searchAnimate.enter,"leave-active-class":(ae=a(m))==null?void 0:ae.animate.searchAnimate.leave},{default:t(()=>[w(U("div",fl,[l(ee,{shadow:"hover"},{default:t(()=>[l($,{ref_key:"queryFormRef",ref:G,model:a(v),inline:!0,"label-width":"100px"},{default:t(()=>[l(r,{label:"封井申请名称",prop:"applicationName"},{default:t(()=>[l(p,{modelValue:a(v).applicationName,"onUpdate:modelValue":e[0]||(e[0]=o=>a(v).applicationName=o),placeholder:"请输入封井申请名称",clearable:"",onKeyup:ne(q,["enter"])},null,8,["modelValue"])]),_:1}),l(r,{label:"井名称",prop:"wellName"},{default:t(()=>[l(p,{modelValue:a(v).wellName,"onUpdate:modelValue":e[1]||(e[1]=o=>a(v).wellName=o),placeholder:"请输入井名称",clearable:"",onKeyup:ne(q,["enter"])},null,8,["modelValue"])]),_:1}),l(r,{label:"所属地",prop:"workAreaId"},{default:t(()=>[l(Ce,{modelValue:a(v).workAreaId,"onUpdate:modelValue":e[2]||(e[2]=o=>a(v).workAreaId=o),class:"searchDate",clearable:"",filterable:"",placeholder:"选择所属地"},{default:t(()=>[(u(!0),oe(Je,null,Oe(a(K),o=>(u(),f(_e,{key:o.operationAreaId,label:o.operationAreaName,value:o.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"封井申请时间",style:{width:"400px"}},{default:t(()=>[l(Ie,{modelValue:a(E),"onUpdate:modelValue":e[3]||(e[3]=o=>ie(E)?E.value=o:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]},null,8,["modelValue","default-time"])]),_:1}),l(r,null,{default:t(()=>[l(d,{type:"primary",icon:"Search",onClick:q},{default:t(()=>e[40]||(e[40]=[s("搜索")])),_:1}),l(d,{icon:"Refresh",onClick:Ne},{default:t(()=>e[41]||(e[41]=[s("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[de,a(x)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(ee,{shadow:"never"},{header:t(()=>[l(Re,{gutter:10,class:"mb8"},{default:t(()=>[l(Pe,{showSearch:a(x),"onUpdate:showSearch":e[4]||(e[4]=o=>ie(x)?x.value=o:null),onQueryTable:R},null,8,["showSearch"])]),_:1})]),default:t(()=>[w((u(),f(Ae,{data:a(Q),stripe:"",onSelectionChange:Se},{default:t(()=>[l(V,{type:"selection",width:"55",align:"center"}),l(V,{label:"序号",align:"center",type:"index",width:"55",index:be}),l(V,{align:"center",label:"井名称",prop:"wellName"}),l(V,{align:"center",label:"所属地",prop:"workAreaName"}),l(V,{align:"center",label:"资产报废名称",prop:"scrapName"}),l(V,{align:"center",label:"封井申请名称",prop:"applicationName"}),l(V,{label:"审批状态",align:"center",prop:"approvalStatus"},{default:t(o=>[l(De,{options:a(pe),value:o.row.approvalStatus},null,8,["options","value"])]),_:1}),l(V,{label:"文件查看",align:"center","class-name":"small-padding fixed-width",fixed:"right","min-width":"220"},{default:t(o=>[l(y,{content:"查看施工现场环保许可证",placement:"top"},{default:t(()=>[w((u(),f(d,{disabled:!o.row.environmentalLicense,icon:"Document",link:"",type:"primary",onClick:N=>c(o.row.sealId,"environmentalLicense")},{default:t(()=>e[42]||(e[42]=[s("施工现场环保许可证")])),_:2},1032,["disabled","onClick"])),[[k,["epfj:wellSeal:preview"]]])]),_:2},1024),l(y,{content:"查看HSE作业计划书",placement:"top"},{default:t(()=>[w((u(),f(d,{disabled:!o.row.hsePlan,icon:"Document",link:"",type:"primary",onClick:N=>c(o.row.sealId,"hsePlan")},{default:t(()=>e[43]||(e[43]=[s("HSE作业计划书")])),_:2},1032,["disabled","onClick"])),[[k,["epfj:wellSeal:preview"]]])]),_:2},1024),l(y,{content:"查看作业现场油水井交接书",placement:"top"},{default:t(()=>[w((u(),f(d,{disabled:!o.row.wellHandoverReport,icon:"Document",link:"",type:"primary",onClick:N=>c(o.row.sealId,"wellHandoverReport")},{default:t(()=>e[44]||(e[44]=[s("作业现场油水井交接书")])),_:2},1032,["disabled","onClick"])),[[k,["epfj:wellSeal:preview"]]])]),_:2},1024),l(y,{content:"查看单井应急预案",placement:"top"},{default:t(()=>[w((u(),f(d,{disabled:!o.row.emergencyPlan,icon:"Document",link:"",type:"primary",onClick:N=>c(o.row.sealId,"emergencyPlan")},{default:t(()=>e[45]||(e[45]=[s("单井应急预案")])),_:2},1032,["disabled","onClick"])),[[k,["epfj:wellSeal:preview"]]])]),_:2},1024),l(y,{content:"查看班前、班后会记录",placement:"top"},{default:t(()=>[w((u(),f(d,{disabled:!o.row.shiftMeetingRecords,icon:"Document",link:"",type:"primary",onClick:N=>c(o.row.sealId,"shiftMeetingRecords")},{default:t(()=>e[46]||(e[46]=[s("班前、班后会记录")])),_:2},1032,["disabled","onClick"])),[[k,["epfj:wellSeal:preview"]]])]),_:2},1024),l(y,{content:"查看现场开工验收单",placement:"top"},{default:t(()=>[w((u(),f(d,{disabled:!o.row.siteAcceptanceReport,icon:"Document",link:"",type:"primary",onClick:N=>c(o.row.sealId,"siteAcceptanceReport")},{default:t(()=>e[47]||(e[47]=[s("现场开工验收单")])),_:2},1032,["disabled","onClick"])),[[k,["epfj:wellSeal:preview"]]])]),_:2},1024)]),_:1}),l(V,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作","min-width":"110"},{default:t(o=>[o.row.approvalStatus==a(O).xuKeZhengHeZuoYeJiHuaShangChuan.value?(u(),f(y,{key:0,content:"许可证和作业计划上传",placement:"top"},{default:t(()=>[w((u(),f(d,{icon:"Edit",link:"",type:"primary",onClick:N=>ge(o.row)},{default:t(()=>e[48]||(e[48]=[s("许可证和作业计划上传")])),_:2},1032,["onClick"])),[[k,["epfj:constructionPrepare:permit"]]])]),_:2},1024)):j("",!0),o.row.approvalStatus==a(O).youShuiJingJiaoJie.value?(u(),f(y,{key:1,content:"油水井交接",placement:"top"},{default:t(()=>[w((u(),f(d,{icon:"Edit",link:"",type:"primary",onClick:N=>we(o.row)},{default:t(()=>e[49]||(e[49]=[s("油水井交接")])),_:2},1032,["onClick"])),[[k,["epfj:constructionPrepare:handover"]]])]),_:2},1024)):j("",!0),o.row.approvalStatus==a(O).fengXianJiaoDi.value?(u(),f(y,{key:2,content:"风险交底",placement:"top"},{default:t(()=>[w((u(),f(d,{icon:"Edit",link:"",type:"primary",onClick:N=>ye(o.row)},{default:t(()=>e[50]||(e[50]=[s("风险交底")])),_:2},1032,["onClick"])),[[k,["epfj:constructionPrepare:risk"]]])]),_:2},1024)):j("",!0),o.row.approvalStatus==a(O).kaiGongYanShou.value?(u(),f(y,{key:3,content:"开工验收",placement:"top"},{default:t(()=>[w((u(),f(d,{icon:"Edit",link:"",type:"primary",onClick:N=>ke(o.row)},{default:t(()=>e[51]||(e[51]=[s("开工验收")])),_:2},1032,["onClick"])),[[k,["epfj:constructionPrepare:start"]]])]),_:2},1024)):j("",!0)]),_:1})]),_:1},8,["data"])),[[Ee,a(B)]]),w(l(Ue,{total:a(Y),page:a(v).pageNum,"onUpdate:page":e[5]||(e[5]=o=>a(v).pageNum=o),limit:a(v).pageSize,"onUpdate:limit":e[6]||(e[6]=o=>a(v).pageSize=o),onPagination:R},null,8,["total","page","limit"]),[[de,a(Y)>0]])]),_:1}),l(L,{title:a(S).title,modelValue:a(S).visible,"onUpdate:modelValue":e[12]||(e[12]=o=>a(S).visible=o),width:"550px","append-to-body":""},{footer:t(()=>[U("div",cl,[l(d,{loading:a(g),type:"warning",onClick:W},{default:t(()=>e[52]||(e[52]=[s("提 交")])),_:1},8,["loading"]),l(d,{loading:a(g),type:"primary",onClick:T},{default:t(()=>e[53]||(e[53]=[s("保 存")])),_:1},8,["loading"]),l(d,{onClick:M},{default:t(()=>e[54]||(e[54]=[s("取 消")])),_:1})])]),default:t(()=>[l($,{ref_key:"wellSealFormRef",ref:P,model:a(n),rules:a(F),"label-width":"115px"},{default:t(()=>[l(r,{label:"封井名称",prop:"wellName"},{default:t(()=>[l(p,{modelValue:a(n).wellName,"onUpdate:modelValue":e[7]||(e[7]=o=>a(n).wellName=o),placeholder:"请输入封井名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"资产报废名称",prop:"scrapName"},{default:t(()=>[l(p,{modelValue:a(n).scrapName,"onUpdate:modelValue":e[8]||(e[8]=o=>a(n).scrapName=o),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"封井申请名称",prop:"applicationName"},{default:t(()=>[l(p,{modelValue:a(n).applicationName,"onUpdate:modelValue":e[9]||(e[9]=o=>a(n).applicationName=o),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"环保许可证",prop:"environmentalLicense"},{default:t(()=>[l(D,{modelValue:a(n).environmentalLicense,"onUpdate:modelValue":e[10]||(e[10]=o=>a(n).environmentalLicense=o),"attach-source-id":a(n).sealId,disabled:!1,"attach-category":"environmentalLicense","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1}),l(r,{label:"HSE作业计划书",prop:"hsePlan"},{default:t(()=>[l(D,{modelValue:a(n).hsePlan,"onUpdate:modelValue":e[11]||(e[11]=o=>a(n).hsePlan=o),"attach-source-id":a(n).sealId,disabled:!1,"attach-category":"hsePlan","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(L,{title:a(_).title,modelValue:a(_).visible,"onUpdate:modelValue":e[19]||(e[19]=o=>a(_).visible=o),width:"550px","append-to-body":""},{footer:t(()=>[U("div",bl,[l(d,{loading:a(g),type:"warning",onClick:W},{default:t(()=>e[57]||(e[57]=[s("提 交")])),_:1},8,["loading"]),l(d,{loading:a(g),type:"primary",onClick:T},{default:t(()=>e[58]||(e[58]=[s("保 存")])),_:1},8,["loading"]),l(d,{onClick:M},{default:t(()=>e[59]||(e[59]=[s("取 消")])),_:1})])]),default:t(()=>[l($,{ref_key:"wellSealFormRef",ref:P,model:a(n),rules:a(F),"label-width":"115px"},{default:t(()=>[l(r,{label:"封井名称",prop:"wellName"},{default:t(()=>[l(p,{modelValue:a(n).wellName,"onUpdate:modelValue":e[13]||(e[13]=o=>a(n).wellName=o),placeholder:"请输入封井名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"资产报废名称",prop:"scrapName"},{default:t(()=>[l(p,{modelValue:a(n).scrapName,"onUpdate:modelValue":e[14]||(e[14]=o=>a(n).scrapName=o),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"封井申请名称",prop:"applicationName"},{default:t(()=>[l(p,{modelValue:a(n).applicationName,"onUpdate:modelValue":e[15]||(e[15]=o=>a(n).applicationName=o),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"环保许可证",prop:"environmentalLicense"},{default:t(()=>[l(d,{type:"primary",onClick:e[16]||(e[16]=o=>c(a(n).sealId,"environmentalLicense"))},{default:t(()=>e[55]||(e[55]=[s("预览")])),_:1})]),_:1}),l(r,{label:"HSE作业计划书",prop:"hsePlan"},{default:t(()=>[l(d,{type:"primary",onClick:e[17]||(e[17]=o=>c(a(n).sealId,"hsePlan"))},{default:t(()=>e[56]||(e[56]=[s("预览")])),_:1})]),_:1}),l(r,{label:"作业现场油水井交接书",prop:"wellHandoverReport"},{default:t(()=>[l(D,{modelValue:a(n).wellHandoverReport,"onUpdate:modelValue":e[18]||(e[18]=o=>a(n).wellHandoverReport=o),"attach-source-id":a(n).sealId,disabled:!1,"attach-category":"wellHandoverReport","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(L,{title:a(C).title,modelValue:a(C).visible,"onUpdate:modelValue":e[28]||(e[28]=o=>a(C).visible=o),width:"550px","append-to-body":""},{footer:t(()=>[U("div",gl,[l(d,{loading:a(g),type:"warning",onClick:W},{default:t(()=>e[63]||(e[63]=[s("提 交")])),_:1},8,["loading"]),l(d,{loading:a(g),type:"primary",onClick:T},{default:t(()=>e[64]||(e[64]=[s("保 存")])),_:1},8,["loading"]),l(d,{onClick:M},{default:t(()=>e[65]||(e[65]=[s("取 消")])),_:1})])]),default:t(()=>[l($,{ref_key:"wellSealFormRef",ref:P,model:a(n),rules:a(F),"label-width":"115px"},{default:t(()=>[l(r,{label:"封井名称",prop:"wellName"},{default:t(()=>[l(p,{modelValue:a(n).wellName,"onUpdate:modelValue":e[20]||(e[20]=o=>a(n).wellName=o),placeholder:"请输入封井名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"资产报废名称",prop:"scrapName"},{default:t(()=>[l(p,{modelValue:a(n).scrapName,"onUpdate:modelValue":e[21]||(e[21]=o=>a(n).scrapName=o),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"封井申请名称",prop:"applicationName"},{default:t(()=>[l(p,{modelValue:a(n).applicationName,"onUpdate:modelValue":e[22]||(e[22]=o=>a(n).applicationName=o),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"环保许可证",prop:"environmentalLicense"},{default:t(()=>[l(d,{type:"primary",onClick:e[23]||(e[23]=o=>c(a(n).sealId,"environmentalLicense"))},{default:t(()=>e[60]||(e[60]=[s("预览")])),_:1})]),_:1}),l(r,{label:"HSE作业计划书",prop:"hsePlan"},{default:t(()=>[l(d,{type:"primary",onClick:e[24]||(e[24]=o=>c(a(n).sealId,"hsePlan"))},{default:t(()=>e[61]||(e[61]=[s("预览")])),_:1})]),_:1}),l(r,{label:"作业现场油水井交接书",prop:"wellHandoverReport"},{default:t(()=>[l(d,{type:"primary",onClick:e[25]||(e[25]=o=>c(a(n).sealId,"wellHandoverReport"))},{default:t(()=>e[62]||(e[62]=[s("预览")])),_:1})]),_:1}),l(r,{label:"单井应急预案",prop:"emergencyPlan"},{default:t(()=>[l(D,{modelValue:a(n).emergencyPlan,"onUpdate:modelValue":e[26]||(e[26]=o=>a(n).emergencyPlan=o),"attach-source-id":a(n).sealId,disabled:!1,"attach-category":"emergencyPlan","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1}),l(r,{label:"班前、班后会记录",prop:"shiftMeetingRecords"},{default:t(()=>[l(D,{modelValue:a(n).shiftMeetingRecords,"onUpdate:modelValue":e[27]||(e[27]=o=>a(n).shiftMeetingRecords=o),"attach-source-id":a(n).sealId,disabled:!1,"attach-category":"shiftMeetingRecords","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(L,{title:a(I).title,modelValue:a(I).visible,"onUpdate:modelValue":e[38]||(e[38]=o=>a(I).visible=o),width:"550px","append-to-body":""},{footer:t(()=>[U("div",wl,[l(d,{loading:a(g),type:"warning",onClick:W},{default:t(()=>e[71]||(e[71]=[s("提 交")])),_:1},8,["loading"]),l(d,{loading:a(g),type:"primary",onClick:T},{default:t(()=>e[72]||(e[72]=[s("保 存")])),_:1},8,["loading"]),l(d,{onClick:M},{default:t(()=>e[73]||(e[73]=[s("取 消")])),_:1})])]),default:t(()=>[l($,{ref_key:"wellSealFormRef",ref:P,model:a(n),rules:a(F),"label-width":"115px"},{default:t(()=>[l(r,{label:"封井名称",prop:"wellName"},{default:t(()=>[l(p,{modelValue:a(n).wellName,"onUpdate:modelValue":e[29]||(e[29]=o=>a(n).wellName=o),placeholder:"请输入封井名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"资产报废名称",prop:"scrapName"},{default:t(()=>[l(p,{modelValue:a(n).scrapName,"onUpdate:modelValue":e[30]||(e[30]=o=>a(n).scrapName=o),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"封井申请名称",prop:"applicationName"},{default:t(()=>[l(p,{modelValue:a(n).applicationName,"onUpdate:modelValue":e[31]||(e[31]=o=>a(n).applicationName=o),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),l(r,{label:"环保许可证",prop:"environmentalLicense"},{default:t(()=>[l(d,{type:"primary",onClick:e[32]||(e[32]=o=>c(a(n).sealId,"environmentalLicense"))},{default:t(()=>e[66]||(e[66]=[s("预览")])),_:1})]),_:1}),l(r,{label:"HSE作业计划书",prop:"hsePlan"},{default:t(()=>[l(d,{type:"primary",onClick:e[33]||(e[33]=o=>c(a(n).sealId,"hsePlan"))},{default:t(()=>e[67]||(e[67]=[s("预览")])),_:1})]),_:1}),l(r,{label:"作业现场油水井交接书",prop:"wellHandoverReport"},{default:t(()=>[l(d,{type:"primary",onClick:e[34]||(e[34]=o=>c(a(n).sealId,"wellHandoverReport"))},{default:t(()=>e[68]||(e[68]=[s("预览")])),_:1})]),_:1}),l(r,{label:"单井应急预案",prop:"emergencyPlan"},{default:t(()=>[l(d,{type:"primary",onClick:e[35]||(e[35]=o=>c(a(n).sealId,"emergencyPlan"))},{default:t(()=>e[69]||(e[69]=[s("预览")])),_:1})]),_:1}),l(r,{label:"班前、班后会记录",prop:"shiftMeetingRecords"},{default:t(()=>[l(d,{type:"primary",onClick:e[36]||(e[36]=o=>c(a(n).sealId,"shiftMeetingRecords"))},{default:t(()=>e[70]||(e[70]=[s("预览")])),_:1})]),_:1}),l(r,{label:"现场开工验收单",prop:"siteAcceptanceReport"},{default:t(()=>[l(D,{modelValue:a(n).siteAcceptanceReport,"onUpdate:modelValue":e[37]||(e[37]=o=>a(n).siteAcceptanceReport=o),"attach-source-id":a(n).sealId,disabled:!1,"attach-category":"siteAcceptanceReport","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(L,{title:"附件预览",modelValue:a(X).visible,"onUpdate:modelValue":e[39]||(e[39]=o=>a(X).visible=o),width:"80%",he:"","append-to-body":""},{default:t(()=>[U("div",yl,[Z.value?(u(),f(ll(Z.value),al(tl({key:0},a(fe))),null,16)):j("",!0)])]),_:1},8,["modelValue"])])}}}),xl=ol(Vl,[["__scopeId","data-v-0d652551"]]);export{xl as default};
