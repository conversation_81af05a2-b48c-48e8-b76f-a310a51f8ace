import{d as i,bz as b,h as w,ak as v,X as T,c as k,o as E,p as t,ay as C,e as s,t as l,ax as y,aJ as B,q as D,z as $,aL as z,K as O,W as q}from"./index-D07cMzhp.js";import{d as I}from"./index-DhTJxyQH.js";const L=i({name:"Online"}),V=i({...L,props:{devices:b.any.isRequired},setup(r){const{proxy:e}=w(),{sys_device_type:p}=v(e==null?void 0:e.useDict("sys_device_type")),d=r,_=T(()=>d.devices),u=c=>{q.confirm("删除设备后，在该设备登录需要重新进行验证").then(()=>I(c.tokenId)).then(a=>{a.code===200?(e==null||e.$modal.msgSuccess("删除成功"),e==null||e.$tab.refreshPage()):e==null||e.$modal.msgError(a.msg)}).catch(()=>{})};return(c,a)=>{const m=B,o=y,f=O,g=z,h=C;return E(),k("div",null,[t(h,{data:s(_),border:"",style:{width:"100%",height:"100%","font-size":"14px"}},{default:l(()=>[t(o,{label:"设备类型",align:"center"},{default:l(n=>[t(m,{options:s(p),value:n.row.deviceType},null,8,["options","value"])]),_:1}),t(o,{label:"主机",align:"center",prop:"ipaddr","show-overflow-tooltip":!0}),t(o,{label:"登录地点",align:"center",prop:"loginLocation","show-overflow-tooltip":!0}),t(o,{label:"操作系统",align:"center",prop:"os","show-overflow-tooltip":!0}),t(o,{label:"浏览器",align:"center",prop:"browser","show-overflow-tooltip":!0}),t(o,{label:"登录时间",align:"center",prop:"loginTime",width:"180"},{default:l(n=>[D("span",null,$(s(e).parseTime(n.row.loginTime)),1)]),_:1}),t(o,{align:"center","class-name":"small-padding fixed-width",label:"操作"},{default:l(n=>[t(g,{content:"删除",placement:"top"},{default:l(()=>[t(f,{link:"",type:"primary",icon:"Delete",onClick:N=>u(n.row)},null,8,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])])}}});export{V as _};
