<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="土地复垦名称" prop="recordName">
              <el-input v-model="queryParams.recordName" placeholder="请输入土地复垦名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="井名称" prop="wellName">
              <el-input v-model="queryParams.wellName" placeholder="请输入井名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属地" prop="workAreaId">
              <el-select v-model="queryParams.operationAreaId" class="searchDate" clearable filterable placeholder="选择所属地">
                <el-option
                  v-for="operationArea in operationAreaList"
                  :key="operationArea.operationAreaId"
                  :label="operationArea.operationAreaName"
                  :value="operationArea.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="土地复垦时间" style="width: 400px">
              <el-date-picker
                v-model="dateRangeRecordDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epfj:landReclamation:add']">新增</el-button>
          </el-col>
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epfj:landReclamation:export']">导出</el-button>-->
          <!--          </el-col>-->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="landReclamationList" stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" type="index" width="55" :index="indexMethod" />
        <el-table-column align="center" label="土地复垦名称" prop="recordName" />
        <el-table-column label="土地复垦时间" align="center" prop="recordDate" width="105">
          <template #default="scope">
            <span>{{ parseTime(scope.row.recordDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="所属地" prop="operationAreaId">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="井名称" prop="sealNames" />
        <el-table-column label="审批状态" align="center" prop="approvalStatus">
          <template #default="scope">
            <dict-tag :options="epfj_approval_status" :value="scope.row.approvalStatus" />
          </template>
        </el-table-column>
        <el-table-column label="文件查看" align="center" class-name="small-padding fixed-width" fixed="right" min-width="120">
          <template #default="scope">
            <el-tooltip content="查看复垦验收报告" placement="top">
              <el-button
                v-hasPermi="['epfj:landReclamation:preview']"
                :disabled="!scope.row.reclamationAcceptanceReport"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.recordId, 'reclamationAcceptanceReport')"
                >复垦验收报告</el-button
              >
            </el-tooltip>
            <el-tooltip content="查看复垦前照片" placement="top">
              <el-button
                v-hasPermi="['epfj:landReclamation:preview']"
                :disabled="!scope.row.preReclamationPhotos"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.recordId, 'preReclamationPhotos')"
                >复垦前照片</el-button
              >
            </el-tooltip>
            <el-tooltip content="查看复垦中照片" placement="top">
              <el-button
                v-hasPermi="['epfj:landReclamation:preview']"
                :disabled="!scope.row.underReclamationPhotos"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.recordId, 'underReclamationPhotos')"
                >复垦中照片</el-button
              >
            </el-tooltip>
            <el-tooltip content="查看复垦后照片" placement="top">
              <el-button
                v-hasPermi="['epfj:landReclamation:preview']"
                :disabled="!scope.row.afterReclamationPhotos"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.recordId, 'afterReclamationPhotos')"
                >复垦后照片</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" min-width="130">
          <template #default="scope">
            <el-tooltip v-if="scope.row.approvalStatus == fjStatusEnum.tuDiFuKen.value" content="复垦验收" placement="top">
              <el-button v-hasPermi="['epfj:landReclamation:fuKen']" icon="Edit" link type="primary" @click="handleFuKen(scope.row)"
                >复垦验收</el-button
              >
            </el-tooltip>
            <el-tooltip v-if="scope.row.approvalStatus == fjStatusEnum.tuDiFuKen.value" content="删除" placement="top">
              <el-button v-hasPermi="['epfj:landReclamation:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 复垦验收报告对话框 -->
    <el-dialog :title="fuKenDialog.title" v-model="fuKenDialog.visible" width="550px" append-to-body>
      <el-form ref="landReclamationFormRef" :model="form" :rules="rules" label-width="115px">
        <el-form-item label="土地复垦名称" prop="recordName">
          <el-input v-model="form.recordName" placeholder="请输入土地复垦名称" />
        </el-form-item>
        <el-form-item label="土地复垦时间" prop="recordDate">
          <el-date-picker v-model="form.recordDate" clearable placeholder="请选择土地复垦时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" clearable placeholder="请选择所属地" @change="getWellSealList">
            <el-option
              v-for="operationArea in operationAreaList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="封井选井" prop="sealIdList">
          <el-select v-model="form.sealIdList" clearable filterable multiple placeholder="请选择封井列表">
            <el-option v-for="item in wellList" :key="item.sealId" :label="item.wellName" :value="item.sealId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="复垦验收报告" prop="reclamationAcceptanceReport">
          <attachFileUpload
            v-model="form.reclamationAcceptanceReport"
            :attach-source-id="form.recordId"
            :disabled="false"
            attach-category="reclamationAcceptanceReport"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
        <el-form-item label="复垦前照片" prop="preReclamationPhotos">
          <attachImageUpload
            v-model="form.preReclamationPhotos"
            :attach-source-id="form.recordId"
            :disabled="false"
            attach-category="preReclamationPhotos"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
        <el-form-item label="复垦中照片" prop="underReclamationPhotos">
          <attachImageUpload
            v-model="form.underReclamationPhotos"
            :attach-source-id="form.recordId"
            :disabled="false"
            attach-category="underReclamationPhotos"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
        <el-form-item label="复垦后照片" prop="afterReclamationPhotos">
          <attachImageUpload
            v-model="form.afterReclamationPhotos"
            :attach-source-id="form.recordId"
            :disabled="false"
            attach-category="afterReclamationPhotos"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="warning" @click="submitStatus">提 交</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 附件预览 -->
    <el-dialog title="附件预览" v-model="previewDialog.visible" width="80%" he append-to-body>
      <div style="height: 68vh">
        <component :is="previewComponent" v-if="previewComponent" v-bind="previewProps" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="landReclamation" lang="ts">
import { listWellSeal } from '@/api/epms/epfj/wellSeal';
import {
  addLandReclamation,
  delLandReclamation,
  getLandReclamation,
  listLandReclamation,
  submitApprovalStatus,
  updateLandReclamation
} from '@/api/epms/epfj/landReclamation';
import { WellSealQuery, WellSealVO } from '@/api/epms/epfj/wellSeal/types';
import { LandReclamationForm, LandReclamationQuery, LandReclamationVO } from '@/api/epms/epfj/landReclamation/types';

import { fjStatusEnum } from '@/api/epms/epfj/epfjEnum/types';
import { shallowRef } from 'vue';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import dayjs from 'dayjs';

const router = useRouter();
const operationAreaQuery = ref<OperationAreaQuery>({});
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epfj_approval_status } = toRefs<any>(proxy?.useDict('epfj_approval_status'));
const operationAreaList = ref<OperationAreaVO[]>([]); // 所属地列表
const landReclamationList = ref<LandReclamationVO[]>([]);
const fuKenDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeRecordDate = ref<[DateModelType, DateModelType]>(['', '']);
const queryFormRef = ref<ElFormInstance>();
const landReclamationFormRef = ref<ElFormInstance>();
const previewComponent = shallowRef();
const previewProps = ref({} as Record<string, any>);
const previewDialog = reactive({
  visible: false,
  url: ''
});
const initFormData: LandReclamationForm = {
  recordId: undefined,
  recordName: undefined,
  recordDate: undefined,
  operationAreaId: undefined,
  sealIds: undefined,
  sealIdList: undefined,
  reclamationAcceptanceReport: undefined,
  preReclamationPhotos: undefined,
  underReclamationPhotos: undefined,
  afterReclamationPhotos: undefined,
  approvalStatus: fjStatusEnum.tuDiFuKen.value,
  approvalOpinions: undefined
};
const data = reactive<PageData<LandReclamationForm, LandReclamationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    operationAreaId: undefined,
    recordName: undefined,
    wellName: undefined,
    params: {}
  },
  rules: {
    recordId: [{ required: true, message: '记录id不能为空', trigger: 'blur' }],
    recordName: [{ required: true, message: '土地复垦名称不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
const wellList = ref<WellSealVO[]>([]);
const WellSealQuery = reactive<WellSealQuery>(<WellSealQuery>{
  pageSize: -1,
  pageNum: 1,
  workAreaId: undefined,
  approvalStatus: fjStatusEnum.yiFengJing.value
});
/** 查询封井列表 */
const getWellSealList = async (operationAreaId: number) => {
  if (operationAreaId) {
    WellSealQuery.workAreaId = operationAreaId;
  } else {
    WellSealQuery.workAreaId = undefined;
  }
  const res = await listWellSeal(WellSealQuery);
  wellList.value = res.rows;
};
/** 查询土地复垦列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeRecordDate.value, 'RecordDate');
  const res = await listLandReclamation(queryParams.value);
  landReclamationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  fuKenDialog.visible = false;
};
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};

/** 提交 */
const submitStatus = () => {
  landReclamationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.recordId) {
        await updateLandReclamation(form.value).finally(() => (buttonLoading.value = false));
        await submitApprovalStatus(form.value.recordId, form.value.approvalStatus, 0);
      } else {
        proxy?.$modal.msgError('数据异常');
      }
      proxy?.$modal.msgSuccess('操作成功');
      fuKenDialog.visible = false;
      await getList();
    }
  });
};
/** 删除按钮操作 */
const handleDelete = async (row?: LandReclamationVO) => {
  const _recordIds = row?.recordId || ids.value;
  await proxy?.$modal.confirm('是否确认删除土地复垦编号为"' + _recordIds + '"的数据项？').finally(() => (loading.value = false));
  await delLandReclamation(_recordIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};
/**
 * 处理土地复垦按钮
 * @param row
 */
const handleFuKen = async (row?: LandReclamationVO) => {
  reset();
  const _recordId = row?.recordId;
  const res = await getLandReclamation(_recordId);
  await getWellSealList(null);
  Object.assign(form.value, res.data);
  fuKenDialog.visible = true;
  fuKenDialog.title = '复垦验收报告';
};

/** 审批弹窗附件预览 */
const previewFile = async (recordId?: string | number, category?: string) => {
  if (!recordId) return;

  proxy.showAttachPreview({
    attachSourceId: recordId,
    attachSourceType: 'wasteWellSealProcess',
    attachCategory: category
  });
  // const props = {
  //   attachSourceId: recordId,
  //   attachSourceType: "wasteWellSealProcess",
  //   attachCategory: category
  // };
  //
  // // 异步加载 preview.vue 组件
  // previewComponent.value = defineAsyncComponent(() =>
  //   import('@/views/comm/attach/preview.vue')
  // );
  //
  // // 传递参数给 preview.vue
  // previewProps.value = props;
  // previewDialog.visible = true;
};

/** 查询所属地、单井、片区列表 */
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaParentId = 0;
  operationAreaQuery.value.operationAreaType = 0;
  const res = await listOperationArea(operationAreaQuery.value);
  operationAreaList.value = res.rows;
};
const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationAreaItem = operationAreaList.value.find((item) => item.operationAreaId === operationAreaId);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  landReclamationFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: LandReclamationVO[]) => {
  ids.value = selection.map((item) => item.recordId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  getWellSealList(null);
  form.value.recordDate = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
  fuKenDialog.visible = true;
  fuKenDialog.title = '添加土地复垦';
};

/** 保存提交按钮 */
const submitForm = () => {
  landReclamationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.recordId) {
        await updateLandReclamation(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addLandReclamation(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      fuKenDialog.visible = false;
      await getList();
    }
  });
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epfj/landReclamation/export',
    {
      ...queryParams.value
    },
    `土地复垦_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getoperationAreaList();
  getWellSealList(null);
  getList();
});
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  line-height: 18px;
  align-items: center;
}
</style>
