<template>
  <div class="floatWin" ref="floatWinEl" v-if="total > 0">
    <!-- 告警图标 -->
    <div class="alert-icon" @click.stop="toggleCollapse">
      <el-badge :max="99" :value="total">
        <el-button type="danger" circle>
          <el-icon size="20px">
            <Warning />
          </el-icon>
        </el-button>
      </el-badge>
    </div>

    <!-- 展开状态 -->
    <el-card v-show="!isCollapsed">
      <!-- 折叠按钮 -->
      <!--<el-button
          icon="ArrowDown"
          circle
          size="small"
          style="position: absolute; top: 10px; right: 10px; z-index: 10000;"
          @click.stop="toggleCollapse"
      />
-->

      <!-- 列表内容 -->
      <ul class="warn-list">
        <li v-for="(item, index) in warnList" :key="index" class="warn-item">
          <div class="warn-header">
            <div class="warn-time">{{ item.occurTime }}</div>
            <div class="warn-level">
              <dict-tag :options="warn_level" :value="item.warnLevel" />
            </div>
          </div>
          <el-tooltip :content="item.warnName" placement="top">
            <div class="warn-content" @click="toWarnPage(item)">
              {{ item.warnName }}
            </div>
          </el-tooltip>
        </li>
      </ul>
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[5, 10, 15]"
        :pager-count="3"
        :total="total"
        background
        class="mt-4 page"
        layout="total, sizes, prev, pager, next"
        size="small"
        @size-change="getList"
        @current-change="getList"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { listWarnRecord } from '@/api/comm/warn/warnRecord/warnRecord';
import { Warning } from '@element-plus/icons-vue';
import { getRoutePathWarnConfig, WarnSystemTypeConfig } from '@/layout/components/WarnInfo/warnSystemType';
import router from '@/router';

// ========================
// 数据定义
// ========================
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { warn_level } = toRefs<any>(proxy?.useDict('warn_level'));

const route = useRoute();
const floatWinEl = ref<HTMLElement | null>(null);
const lastRouteType = ref<number | null>(null); // 记录上一个路由类型（如 'epnj'）

const isCollapsed = ref(true);
const allowCollapse = ref(true);

const title = ref('告警列表');
const loading = ref(false);
const total = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 5,
  systemType: undefined,
  warnLevel: undefined,
  warnStatus: 1
});
const warnList = ref([]);
const flag = ref(0);
const warnCard = ref({
  width: 400,
  height: 300
});
const warnConfig = ref<WarnSystemTypeConfig>(null);
// ========================
// 路由监听
// ========================
// 路由配置和对应的告警系统类型

const updateState = (flagValue: number, titleValue: string, systemType: number, routeType: number) => {
  flag.value = flagValue;
  queryParams.value.systemType = systemType;
  title.value = titleValue;

  allowCollapse.value = lastRouteType.value === routeType;
  lastRouteType.value = routeType;
  getList();
};

// ========================
// 初始化 & 获取数据
// ========================

onMounted(() => {});

const getList = async () => {
  loading.value = true;
  const response = await listWarnRecord(queryParams.value);
  warnList.value = response.rows;
  total.value = response.total;

  // 自动折叠：无数据时隐藏面板
  if (total.value === 0) {
    isCollapsed.value = true;
    setWindowSize(0, 0);
  }

  loading.value = false;
};

// ========================
// 折叠/展开逻辑
// ========================

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  setWindowSize(isCollapsed.value ? 30 : warnCard.value.width, isCollapsed.value ? 30 : warnCard.value.height);
};

const setWindowSize = (width: number, height: number) => {
  if (floatWinEl.value) {
    floatWinEl.value.style.width = `${width}px`;
    floatWinEl.value.style.height = `${height}px`;
  }
};
/**
 * 跳转告警页面或处理页面
 * @param row
 */
const toWarnPage = (row: any) => {
  if (row.jumpUrl) {
    router.push({
      path: row.jumpUrl
    });
  } else {
    warnConfig.value.toWarnRecordPage();
  }
};

watch(
  () => route.path,
  (path) => {
    warnConfig.value = getRoutePathWarnConfig(path);
    if (warnConfig.value) {
      updateState(warnConfig.value.systemType, warnConfig.value.title, warnConfig.value.systemType, warnConfig.value.systemType);
    }
  },
  { immediate: true, deep: true }
);
</script>

<style scoped lang="scss">
.floatWin {
  width: 0px;
  height: 0px;
  position: fixed;
  bottom: 40px;
  right: 20px;
  z-index: 1000; // 需要低于 el-tooltip 的2002
  transition: all 0.3s ease-in-out;
  line-height: 20px;
  :deep(.el-card__body) {
    padding: 15px 10px 15px 15px !important;
  }
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 10000;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.1);
  }
}

.warn-list {
  list-style: none;
  padding: 0 5px 0 0;
  margin: 0;
  overflow: auto;
  height: 240px;
  line-height: 18px;

  .warn-item {
    border-bottom: 1px solid #ebeef5;
    padding: 5px 0;

    &:last-child {
      border-bottom: none;
    }
    .warn-header {
      display: flex;
      justify-content: space-between;
      height: 18px;
      line-height: 18px;
    }

    .warn-level {
      font-size: 12px;
    }

    .warn-time {
      font-size: 12px;
      color: #999;
    }

    .warn-content {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 280px; /* 根据实际需求调整最大宽度 */
      font-size: 14px;
    }
  }
}
</style>
