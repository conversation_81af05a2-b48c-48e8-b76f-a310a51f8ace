import{aC as z,d as Q,r as o,a6 as K,k as S,b as X,c as q,o as A,p as a,t as l,a7 as G,a8 as Y,e as t,q as s,x as W,w as Z,v as j,Q as J}from"./index-D07cMzhp.js";import{E as $}from"./el-row-CikYE3zA.js";import"./el-tree-DW6MoFaI.js";import{E as ee}from"./el-tree-select-mjERCflS.js";import{E as ae}from"./el-date-picker-HyhB9X9n.js";import{E as te}from"./el-col-BaG5Rg5z.js";import{_ as le}from"./HmiPreview-CGaT42aW.js";import oe from"./echarts-C6-fmX5b.js";import se from"./warn-Bso79wuI.js";import ne from"./guifan-B4521Vic.js";import{a as re,g as de,l as ie}from"./index-BhIIZXqy.js";import{m as H}from"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";import"./index-C0_-menq.js";import"./el-tab-pane-B0KEvacl.js";import"./index-BYLYBMy7.js";import"./index-DIX2TuGt.js";const ue=k=>z({url:"/epms/PlanningStatistics/getOperationAreaIndicator",method:"get",params:k}),me={class:"waterline p-2"},pe={class:"card-header"},ce={class:"query"},ve={class:"card-header"},fe={class:"query"},he={class:"card-body"},_e={class:"card-body"},ye={class:"card-body"},ge=Q({__name:"zonglan",setup(k,{expose:O}){const w=o("huanbao-qushui"),u=o("qushui-1906906389068820489"),v={value:"id",label:"name",children:"children"},D=o(!1),f=o([]),T=o([]),C=o({id:83,label:"二连分公司",name:"二连分公司",children:{}}),U=o(1),L=o([]),p=o("1906906389068820489"),E=o({}),n=o({danwei:void 0,operationAreaType:2,dateTime:H().format("YYYY-12-DD")}),h=o(null),N=o(),c=o(),P=o(1),b=K({storageKey:"useDarkKey",valueDark:"dark",valueLight:"light"}),R=()=>{re("0,1").then(e=>{const m=[];C.value.children=e.data.filter(r=>!m.includes(r.id)),T.value=[C.value],n.value.danwei=83,n.value.operationAreaType=2,e.data[0].name?v.label="name":e.data[0].label&&(v.label="label"),V()})},x=()=>{b.value?u.value="lanse-qushui-"+p.value:u.value="qushui-"+p.value};S(b,()=>{x()});const V=async _=>{n.value.danwei!="83"?await de(n.value.danwei).then(e=>{n.value.operationAreaType=e.data.operationAreaType}):n.value.operationAreaType=2,D.value=!0,n.value.dateTime=H(n.value.dateTime).format("YYYY-12-DD"),await ue(n.value).then(e=>{f.value=e.data;const m=function(r){const i=new Date(r),y=i.getFullYear(),g=i.getMonth()+1;return y+"年"+g+"月"};f.value.XAxis.forEach((r,i)=>{f.value.XAxis[i]=m(r)})}),h.value.initChart(),D.value=!1},B=()=>{E.value.operationAreaType=0,ie(E.value).then(_=>{L.value=_.rows})},F=()=>{h.value&&h.value.resizeChart()};return X(()=>{R(),B(),x()}),O({resizeChart:F}),(_,e)=>{const m=le,r=G,i=te,y=ae,g=ee,M=$,I=j;return A(),q("div",me,[a(M,{gutter:14},{default:l(()=>[a(i,{span:12},{default:l(()=>[a(r,{class:"has-el-card-title",shadow:"never"},{header:l(()=>e[6]||(e[6]=[s("div",{class:"card-header"},[s("span",null,"取水业务流程图")],-1)])),default:l(()=>[a(m,{modelValue:t(w),"onUpdate:modelValue":e[0]||(e[0]=d=>Y(w)?w.value=d:null),class:"hmi-div"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:12},{default:l(()=>[a(r,{class:"has-el-card-title",shadow:"never"},{header:l(()=>[s("div",pe,[e[7]||(e[7]=s("span",null,"取水流向图",-1)),s("div",ce,[a(y,{modelValue:t(c),"onUpdate:modelValue":e[1]||(e[1]=d=>Y(c)?c.value=d:null),"date-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD",placeholder:"选择时间",style:{margin:"0px 10px",width:"200px"},type:"date","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"]),a(g,{modelValue:t(p),"onUpdate:modelValue":e[2]||(e[2]=d=>Y(p)?p.value=d:null),data:t(T),props:v,onChange:x,placeholder:"请选择取水区块","render-after-expand":!1,style:{width:"200px"}},null,8,["modelValue","data"])])])]),default:l(()=>[(A(),W(m,{key:t(u)+t(c),ref:"HmiQuRef",modelValue:t(u),"onUpdate:modelValue":e[3]||(e[3]=d=>Y(u)?u.value=d:null),hmiDataTime:t(c),class:"hmi-div"},null,8,["modelValue","hmiDataTime"]))]),_:1})]),_:1})]),_:1}),a(M,{gutter:14,style:{"margin-top":"10px"}},{default:l(()=>[a(i,{span:12},{default:l(()=>[a(r,{class:"has-el-card-title",shadow:"never"},{header:l(()=>[s("div",ve,[e[8]||(e[8]=s("span",null,"月度取水对比图",-1)),s("div",fe,[a(y,{modelValue:t(n).dateTime,"onUpdate:modelValue":e[4]||(e[4]=d=>t(n).dateTime=d),type:"year",placeholder:"选择年度",format:"YYYY","date-format":"YYYY","value-format":"YYYY-MM-DD",onChange:V,style:{margin:"0px 10px",width:"200px"}},null,8,["modelValue"]),a(g,{modelValue:t(n).danwei,"onUpdate:modelValue":e[5]||(e[5]=d=>t(n).danwei=d),data:t(T),props:v,onChange:V,"check-strictly":"",placeholder:"请选择取水区块","render-after-expand":!1,style:{width:"200px"}},null,8,["modelValue","data"])])])]),default:l(()=>[Z((A(),q("div",he,[a(oe,{ref_key:"chartRef",ref:h,chartData:t(f),style:{width:"100%",height:"100%"}},null,8,["chartData"])])),[[I,t(D)]])]),_:1})]),_:1}),a(i,{span:6},{default:l(()=>[a(r,{class:"has-el-card-title",shadow:"never"},{header:l(()=>e[9]||(e[9]=[s("div",{class:"card-header"},[s("span",null,"取水标准规范")],-1)])),default:l(()=>[s("div",_e,[a(ne,{documentCategory:t(P),documentType:t(N)},null,8,["documentCategory","documentType"])])]),_:1})]),_:1}),a(i,{span:6},{default:l(()=>[a(r,{class:"has-el-card-title",shadow:"never"},{header:l(()=>e[10]||(e[10]=[s("div",{class:"card-header"},[s("span",null,"取水告警列表")],-1)])),default:l(()=>[s("div",ye,[a(se,{systemType:t(U)},null,8,["systemType"])])]),_:1})]),_:1})]),_:1})])}}}),Ne=J(ge,[["__scopeId","data-v-6d5e5aca"]]);export{Ne as default};
