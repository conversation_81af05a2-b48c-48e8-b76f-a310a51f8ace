import{d as U,h as Z,ak as H,r as n,b as P,c as x,o as h,p as e,t as a,a7 as X,q as l,ay as G,e as o,ax as W,z as L,aJ as K,w as $,v as ee,B as ae,D as te,F as le,C as oe,x as ne,bi as se,Q as re}from"./index-D07cMzhp.js";import{E as ie}from"./el-row-CikYE3zA.js";import{E as de}from"./el-col-BaG5Rg5z.js";import{E as pe}from"./el-date-picker-HyhB9X9n.js";import ue from"./echarts-CRStbJFZ.js";import ce from"./warn-Ck5g5Dg4.js";import me from"./guifan-B4521Vic.js";import{l as _e}from"./index-BhIIZXqy.js";import{m as y}from"./dayjs.min-Brw96_N0.js";import{f as fe,h as he}from"./index-DAD2mDUi.js";import{l as ye}from"./index-fayfDNIG.js";import"./index-VIEDZI2D.js";import"./index-C0_-menq.js";import"./el-tab-pane-B0KEvacl.js";import"./index-BYLYBMy7.js";import"./index-DIX2TuGt.js";const ge={class:"waterline p-2"},ve={class:"card-header"},we={class:"query"},Ye={class:"hmi-div"},De={class:"hmi-div"},be={class:"card-header"},xe={class:"query"},Te={class:"card-body"},Ce={class:"card-body"},Ee={class:"card-body"},Me=U({__name:"zonglan",setup(Ve,{expose:q}){const{proxy:g}=Z(),{epnj_handling_type:Ae,drilling_well_type:B}=H(g==null?void 0:g.useDict("epnj_handling_type","drilling_well_type"));n("huanbao-nijiang"),n("qushui-1906906389068820489");const v=n(!1),m=n([]),T=n([]),C=n([]),w=n({operationAreaId:"83"}),E=n([]),M=n({}),d=n({danwei:"83",flowType:2,dateTime:y().format("YYYY-12-DD")}),Y=n({date:y().format("YYYY-MM-DD")}),V=n({}),_=n(null),I=n(),R=n(3),j=n(3),D=async()=>{v.value=!0,d.value.dateTime=y(d.value.dateTime).format("YYYY"),await fe(d.value).then(i=>{m.value=i.data;const t=function(p){const r=new Date(p),b=r.getFullYear(),f=r.getMonth()+1;return b+"年"+f+"月"};m.value.XAxis.forEach((p,r)=>{m.value.XAxis[r]=t(p)})}),_.value.initChart(),v.value=!1},z=()=>{M.value.operationAreaType=0,_e(M.value).then(i=>{E.value=i.rows})},F=()=>{_.value&&_.value.resizeChart()},Q=()=>{w.value.flowType=2,w.value.transportTime=y().format("YYYY-MM-DD HH:mm:ss"),he(w.value).then(i=>{T.value=i.rows})},S=i=>{se.push({path:"/epnj/drillingDaily",query:{date:i.date,wellNumber:i.wellNumber}})},A=()=>{ye(Y.value).then(i=>{C.value=i.rows})};return P(()=>{Q(),z(),D(),A()}),q({resizeChart:F}),(i,t)=>{const p=pe,r=W,b=K,f=G,u=X,c=de,k=ie,N=te,J=ae,O=ee;return h(),x("div",ge,[e(k,{gutter:14},{default:a(()=>[e(c,{span:12},{default:a(()=>[e(u,{class:"has-el-card-title",shadow:"never"},{header:a(()=>[l("div",ve,[t[3]||(t[3]=l("span",null,"钻井日报",-1)),l("div",we,[e(p,{modelValue:o(Y).date,"onUpdate:modelValue":t[0]||(t[0]=s=>o(Y).date=s),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","date-format":"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:A,style:{margin:"0px 10px",width:"200px"}},null,8,["modelValue"])])])]),default:a(()=>[l("div",Ye,[e(f,{stripe:"",ref:"table",data:o(C),"row-style":o(V),height:"100%",onRowClick:S},{default:a(()=>[e(r,{align:"center",label:"日期",prop:"date","min-width":"110px",fixed:"left"},{default:a(s=>[l("span",null,L(i.parseTime(s.row.date,"{y}-{m}-{d}")),1)]),_:1}),e(r,{align:"center",label:"钻井公司",prop:"drillingCompany",fixed:"left"}),e(r,{align:"center",label:"钻井井号",prop:"wellNumber","min-width":"130px"}),e(r,{align:"center",label:"井别",prop:"wellType"},{default:a(s=>[e(b,{options:o(B),value:s.row.wellType},null,8,["options","value"])]),_:1}),e(r,{align:"center",label:"开钻日期",prop:"spudDate","min-width":"110px"},{default:a(s=>[l("span",null,L(i.parseTime(s.row.spudDate,"{y}-{m}-{d}")),1)]),_:1})]),_:1},8,["data","row-style"])])]),_:1})]),_:1}),e(c,{span:12},{default:a(()=>[e(u,{class:"has-el-card-title",shadow:"never"},{header:a(()=>t[4]||(t[4]=[l("div",{class:"card-header"},[l("span",null,"泥浆拉运统计")],-1)])),default:a(()=>[l("div",De,[e(f,{stripe:"",ref:"table",data:o(T),"row-style":o(V),height:"100%"},{default:a(()=>[e(r,{align:"center",label:"",prop:"rowName",fixed:"left"}),e(r,{align:"center",label:"今日",prop:"dayData",fixed:"left"}),e(r,{align:"center",label:"本月",prop:"monthData",fixed:"left"}),e(r,{align:"center",label:"本年",prop:"yearData"})]),_:1},8,["data","row-style"])])]),_:1})]),_:1})]),_:1}),e(k,{gutter:14,style:{"margin-top":"10px"}},{default:a(()=>[e(c,{span:12},{default:a(()=>[e(u,{class:"has-el-card-title",shadow:"never"},{header:a(()=>[l("div",be,[t[5]||(t[5]=l("span",null,"随钻处理、集中处理对比图",-1)),l("div",xe,[e(p,{modelValue:o(d).dateTime,"onUpdate:modelValue":t[1]||(t[1]=s=>o(d).dateTime=s),type:"year",placeholder:"选择年度",format:"YYYY","date-format":"YYYY","value-format":"YYYY",onChange:D,style:{margin:"0px 10px",width:"200px"}},null,8,["modelValue"]),e(J,{modelValue:o(d).danwei,"onUpdate:modelValue":t[2]||(t[2]=s=>o(d).danwei=s),placeholder:"请选择取水区块",style:{width:"200px"},onChange:D},{default:a(()=>[e(N,{value:"83",label:"全部"}),(h(!0),x(le,null,oe(o(E),s=>(h(),ne(N,{key:s.operationAreaId,label:s.operationAreaName,value:s.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])]),default:a(()=>[$((h(),x("div",Te,[e(ue,{ref_key:"chartRef",ref:_,chartData:o(m),style:{width:"100%",height:"100%"}},null,8,["chartData"])])),[[O,o(v)]])]),_:1})]),_:1}),e(c,{span:6},{default:a(()=>[e(u,{class:"has-el-card-title",shadow:"never"},{header:a(()=>t[6]||(t[6]=[l("div",{class:"card-header"},[l("span",null,"泥浆标准规范")],-1)])),default:a(()=>[l("div",Ce,[e(me,{documentCategory:o(R),documentType:o(I),"show-activity":!1},null,8,["documentCategory","documentType"])])]),_:1})]),_:1}),e(c,{span:6},{default:a(()=>[e(u,{class:"has-el-card-title",shadow:"never"},{header:a(()=>t[7]||(t[7]=[l("div",{class:"card-header"},[l("span",null,"泥浆告警列表")],-1)])),default:a(()=>[l("div",Ee,[e(ce,{systemType:o(j)},null,8,["systemType"])])]),_:1})]),_:1})]),_:1})])}}}),He=re(Me,[["__scopeId","data-v-2afa693b"]]);export{He as default};
