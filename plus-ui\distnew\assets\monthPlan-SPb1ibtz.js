import{Q as I,m as q,w as b,v as _,c as x,o as U,q as N,p as l,t as m,E as f}from"./index-D07cMzhp.js";import{i as v,b as y,c as P}from"./plan-Aci2wsnf.js";function h(...n){let e=0;for(const i of n)e=Number(g(e,i));return e}function g(n,e){let i=0,r=0,t,s;try{i=n.toString().split(".")[1].length}catch{i=0}try{r=e.toString().split(".")[1].length}catch{r=0}if(s=Math.abs(i-r),t=Math.pow(10,Math.max(i,r)),s>0){const d=Math.pow(10,s);i>r?(n=Number(n.toString().replace(".","")),e=Number(e.toString().replace(".",""))*d):(n=Number(n.toString().replace(".",""))*d,e=Number(e.toString().replace(".","")))}else n=Number(n.toString().replace(".","")),e=Number(e.toString().replace(".",""));return(n+e)/t}const S={name:"monthPlan",data(){return{loading:!1,planId:this.$route.query.planId,planType:this.$route.query.planType,indicatorList:[],tableData:[],planList:[],planList1:[1],planList2:[2,3],monthId2PlanId:{},editConfig:{trigger:"click",mode:"cell",showStatus:!0}}},watch:{$route:{handler:function(n){n.query.planId&&(this.planId=n.query.planId,this.planType=n.query.planType,this.init())},immediate:!0}},methods:{async init(){this.planType==1?this.planList=this.planList1:this.planType==2&&(this.planList=this.planList2),await this.initMonthPlan(),await this.queryMonthvalues()},async initMonthPlan(){await P({planId:this.planId}).then(n=>{n.code===200&&console.log("初始化月计划成功")})},async queryMonthvalues(){await y({planId:this.planId}).then(n=>{if(n.code===200){let e=n.data.indicator,i=n.data.indicatorValue;this.monthId2PlanId=n.data.monthId2PlanId;let r=[];Object.keys(e).forEach(t=>{r.push({indicatorId:t,indicatorName:e[t].indicatorName+"("+e[t].indicatorUnitName+")",indicatorValue:i[t].indicatorValue,indicatorUnitName:e[t].indicatorUnitName,month1:i[t].monthValues[0]||0,month2:i[t].monthValues[1]||0,month3:i[t].monthValues[2]||0,month4:i[t].monthValues[3]||0,month5:i[t].monthValues[4]||0,month6:i[t].monthValues[5]||0,month7:i[t].monthValues[6]||0,month8:i[t].monthValues[7]||0,month9:i[t].monthValues[8]||0,month10:i[t].monthValues[9]||0,month11:i[t].monthValues[10]||0,month12:i[t].monthValues[11]||0})}),r.forEach(t=>{t.quarter1=h(t.month1,t.month2,t.month3),t.quarter2=h(t.month4,t.month5,t.month6),t.quarter3=h(t.month7,t.month8,t.month9),t.quarter4=h(t.month10,t.month11,t.month12),t.indicatorUnitName==="%"?(t.quarter1=Number(Number(t.quarter1/3).toFixed(2)),t.quarter2=Number(Number(t.quarter2/3).toFixed(2)),t.quarter3=Number(Number(t.quarter3/3).toFixed(2)),t.quarter4=Number(Number(t.quarter4/3).toFixed(2)),t.indicatorValueUsed=Number(h(t.quarter1,t.quarter2,t.quarter3,t.quarter4)/4).toFixed(2),t.indicatorValueRemain="-"):(t.indicatorValueUsed=h(t.quarter1,t.quarter2,t.quarter3,t.quarter4),t.indicatorValueRemain=h(t.indicatorValue,-t.indicatorValueUsed))}),this.tableData=r.filter(t=>this.planList.includes(Number(t.indicatorId)))}})},async editClosedEvent(n){let e=n.row,i=n.column;const r=i.property,t=e[r],s=this.$refs.xTable;if(s.isUpdateByRow(e,r)){let d=this.calcSum(e),u=!1;if(e.indicatorUnitName==="%")u=!0;else if(Number(d)>Number(e.indicatorValue)){f.error("超出指标总值"),await s.revertData(e,r);return}let p=this.getPlanId(r);v({planId:p,indicatorId:e.indicatorId,indicatorValue:t}).then(V=>{if(V.code===200){let c=0,a="";i.params.quarter==="quarter1"?(c=h(e.month1,e.month2,e.month3),a="quarter1"):i.params.quarter==="quarter2"?(c=h(e.month4,e.month5,e.month6),a="quarter2"):i.params.quarter==="quarter3"?(c=h(e.month7,e.month8,e.month9),a="quarter3"):i.params.quarter==="quarter4"&&(c=h(e.month10,e.month11,e.month12),a="quarter4"),u?(e[a]=Number(Number(c)/3).toFixed(2),e.indicatorValueRemain="-",e.indicatorValueUsed=Number(h(e.quarter1,e.quarter2,e.quarter3,e.quarter4)/4).toFixed(2)):(e[a]=c,e.indicatorValueUsed=h(e.quarter1,e.quarter2,e.quarter3,e.quarter4),e.indicatorValueRemain=h(e.indicatorValue,-Number(e.indicatorValueUsed))),s.reloadRow(e,null,r),f.success("设置成功")}}).catch(()=>{s.revertData(e,r)})}},calcSum(n){return h(n.month1,n.month2,n.month3,n.month4,n.month5,n.month6,n.month7,n.month8,n.month9,n.month10,n.month11,n.month12)},getPlanId(n){let e="";switch(n){case"month1":e=this.monthId2PlanId[1];break;case"month2":e=this.monthId2PlanId[2];break;case"month3":e=this.monthId2PlanId[3];break;case"month4":e=this.monthId2PlanId[4];break;case"month5":e=this.monthId2PlanId[5];break;case"month6":e=this.monthId2PlanId[6];break;case"month7":e=this.monthId2PlanId[7];break;case"month8":e=this.monthId2PlanId[8];break;case"month9":e=this.monthId2PlanId[9];break;case"month10":e=this.monthId2PlanId[10];break;case"month11":e=this.monthId2PlanId[11];break;case"month12":e=this.monthId2PlanId[12];break}return e}}},M={class:"qyui-cell row qyui-container pd20"},L={class:"qyui-cell row"};function E(n,e,i,r,t,s){const d=q("vxe-column"),u=q("vxe-input"),p=q("vxe-colgroup"),V=q("vxe-table"),c=_;return b((U(),x("div",M,[N("div",L,[l(V,{ref:"xTable",data:t.tableData,"edit-config":t.editConfig,border:"","keep-source":"",resizable:"","show-overflow":"",onEditClosed:s.editClosedEvent},{default:m(()=>[l(d,{type:"seq",width:"60",fixed:"left"}),l(d,{title:"指标",width:"200",field:"indicatorName",fixed:"left"}),l(d,{title:"总计",width:"100",field:"indicatorValue",align:"center",fixed:"left"}),l(d,{title:"分配总计",width:"100",field:"indicatorValueUsed",align:"center",fixed:"left"}),l(d,{title:"剩余未分配",width:"100",field:"indicatorValueRemain",align:"center",fixed:"left"}),l(p,{title:"第一季度",align:"center"},{default:m(()=>[l(d,{"edit-render":{},params:{quarter:"quarter1"},field:"month1",title:"1月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month1,"onUpdate:modelValue":o=>a.month1=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"edit-render":{},params:{quarter:"quarter1"},field:"month2",title:"2月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month2,"onUpdate:modelValue":o=>a.month2=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"edit-render":{},params:{quarter:"quarter1"},field:"month3",title:"3月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month3,"onUpdate:modelValue":o=>a.month3=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"class-name":"quarter",field:"quarter1",title:"季度合计",width:"100"})]),_:1}),l(p,{title:"第二季度",align:"center"},{default:m(()=>[l(d,{"edit-render":{},params:{quarter:"quarter2"},field:"month4",title:"4月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month4,"onUpdate:modelValue":o=>a.month4=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"edit-render":{},params:{quarter:"quarter2"},field:"month5",title:"5月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month5,"onUpdate:modelValue":o=>a.month5=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"edit-render":{},params:{quarter:"quarter2"},field:"month6",title:"6月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month6,"onUpdate:modelValue":o=>a.month6=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"class-name":"quarter",field:"quarter2",title:"季度合计",width:"100"})]),_:1}),l(p,{title:"第三季度",align:"center"},{default:m(()=>[l(d,{"edit-render":{},params:{quarter:"quarter3"},field:"month7",title:"7月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month7,"onUpdate:modelValue":o=>a.month7=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"edit-render":{},params:{quarter:"quarter3"},field:"month8",title:"8月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month8,"onUpdate:modelValue":o=>a.month8=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"edit-render":{},params:{quarter:"quarter3"},field:"month9",title:"9月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month9,"onUpdate:modelValue":o=>a.month9=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"class-name":"quarter",field:"quarter3",title:"季度合计",width:"100"})]),_:1}),l(p,{title:"第四季度",align:"center"},{default:m(()=>[l(d,{"edit-render":{},params:{quarter:"quarter4"},field:"month10",title:"10月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month10,"onUpdate:modelValue":o=>a.month10=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"edit-render":{},params:{quarter:"quarter4"},field:"month11",title:"11月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month11,"onUpdate:modelValue":o=>a.month11=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"edit-render":{},params:{quarter:"quarter4"},field:"month12",title:"12月",width:"100"},{edit:m(({row:a})=>[l(u,{modelValue:a.month12,"onUpdate:modelValue":o=>a.month12=o,type:"text"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(d,{"class-name":"quarter",field:"quarter4",title:"季度合计",width:"100"})]),_:1})]),_:1},8,["data","edit-config","onEditClosed"])])])),[[c,t.loading]])}const T=I(S,[["render",E]]);export{T as default};
