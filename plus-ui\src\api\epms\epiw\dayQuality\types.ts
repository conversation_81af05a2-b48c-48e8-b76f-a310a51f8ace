export interface DayQualityVO {
  /**
   * 监测报告id
   */
  qualityReportId: string | number;

  /**
   * 检测时间
   */
  detectionTime: string;

  /**
   * 附件
   */
  file: string;
}

export interface DayQualityForm extends BaseEntity {
  /**
   * 监测报告id
   */
  qualityReportId?: string | number;

  /**
   * 检测时间
   */
  detectionTime?: string;

  /**
   * 附件
   */
  file?: string;
}

export interface DayQualityQuery extends PageQuery {
  /**
   * 检测时间
   */
  detectionTime?: string;

  /**
   * 附件
   */
  file?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
