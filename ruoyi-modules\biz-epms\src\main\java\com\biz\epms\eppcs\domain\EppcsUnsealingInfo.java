package com.biz.epms.eppcs.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.comm.attach.annotation.AttachField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 拆封信息对象 eppcs_unsealing_info
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("eppcs_unsealing_info")
public class EppcsUnsealingInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 拆封id
     */
    @TableId(value = "unsealing_id")
    private Long unsealingId;

    /**
     * 关联申请ID
     */
    private Long applicationId;


    /**
     * 实验室封条照片附件
     */
    @AttachField
    private String unsealPhoto;

    /**
     * 拆封时间
     */
    private Date unsealTime;

    /**
     * 拆封人
     */
    private String unsealer;

    /**
     * 拆封状态 (0草稿,1驳回,2待确认,3已确认)
     */
    private Integer unsealingStatus;



    /**
     * 审批时间
     */
    private Date approvalTime;

    /**
     * 审批人
     */
    private String approver;

    /**
     * 审批建议
     */
    private String approvalRemark;

    /**
     * 备注
     */
    private String remark;


}
