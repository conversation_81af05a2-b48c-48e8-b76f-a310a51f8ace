import{aC as t}from"./index-D07cMzhp.js";const n=e=>t({url:"/system/menu/list",method:"get",params:e}),r=e=>t({url:"/system/menu/"+e,method:"get"}),u=()=>t({url:"/system/menu/treeselect",method:"get"}),m=e=>t({url:"/system/menu/roleMenuTreeselect/"+e,method:"get"}),a=e=>t({url:"/system/menu/tenantPackageMenuTreeselect/"+e,method:"get"}),l=e=>t({url:"/system/menu",method:"post",data:e}),o=e=>t({url:"/system/menu",method:"put",data:e}),c=e=>t({url:"/system/menu/"+e,method:"delete"}),d=e=>t({url:"/system/menu/cascade/"+e,method:"delete"});export{l as a,a as b,d as c,c as d,r as g,n as l,m as r,u as t,o as u};
