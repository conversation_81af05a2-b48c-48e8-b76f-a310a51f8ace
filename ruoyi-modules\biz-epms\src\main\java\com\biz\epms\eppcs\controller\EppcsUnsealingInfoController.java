package com.biz.epms.eppcs.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.biz.comm.attach.annotation.AttachDelete;
import com.biz.comm.attach.annotation.AttachUpload;
import com.biz.epms.eppcs.domain.bo.EppcsUnsealingInfoBo;
import com.biz.epms.eppcs.domain.vo.EppcsUnsealingInfoVo;
import com.biz.epms.eppcs.service.IEppcsUnsealingInfoService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 拆封信息
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/epms/eppcs/unsealingInfo")
public class EppcsUnsealingInfoController extends BaseController {

    private final IEppcsUnsealingInfoService eppcsUnsealingInfoService;

    /**
     * 查询拆封信息列表
     */
    @SaCheckPermission("eppcs:unsealingInfo:list")
    @GetMapping("/list")
    public TableDataInfo<EppcsUnsealingInfoVo> list(EppcsUnsealingInfoBo bo, PageQuery pageQuery) {
        return eppcsUnsealingInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出拆封信息列表
     */
    @SaCheckPermission("eppcs:unsealingInfo:export")
    @Log(title = "拆封信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EppcsUnsealingInfoBo bo, HttpServletResponse response) {
        List<EppcsUnsealingInfoVo> list = eppcsUnsealingInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "拆封信息", EppcsUnsealingInfoVo.class, response);
    }

    /**
     * 获取拆封信息详细信息
     *
     * @param unsealingId 主键
     */
    @SaCheckPermission("eppcs:unsealingInfo:query")
    @GetMapping("/{unsealingId}")
    public R<EppcsUnsealingInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long unsealingId) {
        return R.ok(eppcsUnsealingInfoService.queryById(unsealingId));
    }

    /**
     * 新增拆封信息
     */
    @SaCheckPermission("eppcs:unsealingInfo:add")
    @Log(title = "拆封信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    @AttachUpload(
        sourceIdExpression = "#bo.unsealingId",
        attachIdExpression = {"#bo.unsealPhoto"}
    )
    public R<Long> add(@Validated(AddGroup.class) @RequestBody EppcsUnsealingInfoBo bo) {
        boolean result = eppcsUnsealingInfoService.insertByBo(bo);
        if (result) {
            return R.ok("新增成功", bo.getUnsealingId());
        } else {
            return R.fail("新增失败");
        }
    }

    /**
     * 修改拆封信息
     */
    @SaCheckPermission("eppcs:unsealingInfo:edit")
    @Log(title = "拆封信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EppcsUnsealingInfoBo bo) {
        return toAjax(eppcsUnsealingInfoService.updateByBo(bo));
    }

    /**
     * 删除拆封信息
     *
     * @param unsealingIds 主键串
     */
    @SaCheckPermission("eppcs:unsealingInfo:remove")
    @Log(title = "拆封信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{unsealingIds}")
    @AttachDelete(
        sourceIdExpression = "#unsealingIds"
    )
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] unsealingIds) {
        return toAjax(eppcsUnsealingInfoService.deleteWithValidByIds(List.of(unsealingIds), true));
    }

    /**
     * 提交拆封信息
     *
     * @param unsealingId 拆封信息ID
     * @param userType 用户类型
     */
    @SaCheckPermission("eppcs:unsealingInfo:submit")
    @Log(title = "提交拆封信息", businessType = BusinessType.UPDATE)
    @PostMapping("/submit/{unsealingId}")
    public R<Void> submitUnsealingInfo(@NotNull(message = "拆封信息ID不能为空") @PathVariable Long unsealingId,
                                      @RequestParam(defaultValue = "1") Integer userType) {
        return toAjax(eppcsUnsealingInfoService.submitUnsealingInfo(unsealingId, userType));
    }

    /**
     * 确认拆封信息
     *
     * @param unsealingId 拆封信息ID
     * @param userType 用户类型
     * @param action 动作 (1通过, 2驳回)
     * @param approvalRemark 审批意见
     */
    @SaCheckPermission("eppcs:unsealingInfo:confirm")
    @Log(title = "确认拆封信息", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{unsealingId}")
    public R<Void> confirmUnsealingInfo(@NotNull(message = "拆封信息ID不能为空") @PathVariable Long unsealingId,
                                       @RequestParam Integer userType,
                                       @RequestParam Integer action,
                                       @RequestParam(required = false) String approvalRemark) {
        return toAjax(eppcsUnsealingInfoService.confirmUnsealingInfo(
            unsealingId, userType, action, approvalRemark, LoginHelper.getLoginUser().getNickname()));
    }
}
