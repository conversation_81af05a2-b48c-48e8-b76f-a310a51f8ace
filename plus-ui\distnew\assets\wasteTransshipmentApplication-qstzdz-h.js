import{d as $,h as le,ak as O,r as l,ai as x,b as se,c as R,o as F,p as t,t as s,w as D,q as de,a7 as ue,M as ce,e as b,A as pe,B as ve,F as me,C as ge,x as M,D as be,K as fe,J as B,am as P,aI as _e,ay as ye,ax as he,v as we,az as Ce}from"./index-D07cMzhp.js";import{E as Te}from"./el-row-CikYE3zA.js";import{_ as Se}from"./index-BWMgqvQ9.js";import{E as Ne}from"./el-col-BaG5Rg5z.js";import{E as Fe}from"./el-date-picker-HyhB9X9n.js";import{l as Ae}from"./index-Dt50K0j_.js";import{o as V}from"./index-ChPmfMlc.js";import{u as A,X as Ee}from"./xlsx-BexUIDLF.js";import"./el-tree-DW6MoFaI.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";const Ie={class:"p-2"},qe={class:"mb-[10px]"},xe=$({name:"WasteOutboundRecord"}),Je=$({...xe,setup(De){const{proxy:y}=le(),{epwf_transport_condition:Be,epwf_container_type:Le,epwf_approval_status:Ue}=O(y==null?void 0:y.useDict("epwf_transport_condition","epwf_container_type","epwf_approval_status")),L=l([]);l(!1);const E=l(!0),I=l(!0),Y=l([]),j=l(!0),X=l(!0),h=l(),q=l(0),G=l(["",""]),J=l(["",""]),K=l(["",""]),U=l([]),H=l([]),k=l();l();const Q=l(null);x({visible:!1,title:""}),x({visible:!1,title:""});const Z=x({form:{...{recordId:void 0,generateBatchCode:void 0,incomeBatchCode:void 0,outboundBatchCode:void 0,disposalBatchCode:void 0,generateTime:void 0,incomeTime:void 0,outboundTime:void 0,containerCode:void 0,containerType:void 0,containerNumber:void 0,wasteCommonName:void 0,wasteNationalName:void 0,wasteCategory:void 0,wasteCode:void 0,generateQuantity:void 0,incomeQuantity:void 0,outboundQuantity:void 0,disposalQuantity:void 0,unit:void 0,facilityCode:void 0,generateFacilityCode:void 0,facilityType:void 0,generateAgent:void 0,transportAgent:void 0,storageAgent:void 0,outboundAgent:void 0,generateDestination:void 0,outboundDestination:void 0,generateWasteUnitId:void 0,centralizedUnitId:void 0,approvalStatus:1,approvalOpinions:void 0,territorialUnitId:void 0,generatePlace:void 0,generateFile:void 0,weighFile:void 0,storeFile:void 0,labelFile:void 0,transportCondition:void 0,transportApplicationFile:void 0,transportWasteFile:void 0,wasteLabel:void 0,transportMultFile:void 0,poundList:void 0,processPhotos:void 0,disposalWay:void 0,receiveType:void 0,businessLicenseName:void 0,businessLicenseEncode:void 0,wasteComponentName:void 0,wasteShape:void 0,dangerCharacteristics:void 0,address:void 0}},queryParams:{pageNum:1,pageSize:10,generateBatchCode:void 0,incomeBatchCode:void 0,outboundBatchCode:void 0,disposalBatchCode:void 0,containerCode:void 0,containerType:void 0,containerNumber:void 0,wasteCommonName:void 0,wasteNationalName:void 0,wasteCategory:void 0,wasteCode:void 0,generateQuantity:void 0,incomeQuantity:void 0,outboundQuantity:void 0,disposalQuantity:void 0,unit:void 0,facilityCode:void 0,generateFacilityCode:void 0,facilityType:void 0,generateAgent:void 0,transportAgent:void 0,storageAgent:void 0,outboundAgent:void 0,generateDestination:void 0,outboundDestination:void 0,generateWasteUnitId:void 0,centralizedUnitId:void 0,approvalStatus:void 0,approvalOpinions:void 0,territorialUnitId:void 0,generatePlace:void 0,generateFile:void 0,weighFile:void 0,storeFile:void 0,labelFile:void 0,transportCondition:void 0,transportApplicationFile:void 0,transportWasteFile:void 0,wasteLabel:void 0,transportMultFile:void 0,poundList:void 0,processPhotos:void 0,disposalWay:void 0,receiveType:void 0,businessLicenseName:void 0,businessLicenseEncode:void 0,wasteComponentName:void 0,wasteShape:void 0,dangerCharacteristics:void 0,address:void 0,includeSelf:!0,params:{generateTime:void 0,incomeTime:void 0,outboundTime:void 0}},rules:{recordId:[{required:!0,message:"记录ID不能为空",trigger:"blur"}],generateWasteUnitId:[{required:!0,message:"产废单位不能为空",trigger:"blur"}],centralizedUnitId:[{required:!0,message:"归口部门不能为空",trigger:"blur"}],territorialUnitId:[{required:!0,message:"属地单位不能为空",trigger:"blur"}],generateBatchCode:[{required:!0,message:"产生批次不能为空",trigger:"blur"}],generateFacilityCode:[{required:!0,message:"产生危险废物设施编码不能为空",trigger:"blur"}],generateQuantity:[{required:!0,message:"产生量不能为空",trigger:"blur"}],unit:[{required:!0,message:"计量单位不能为空",trigger:"blur"}],generateTime:[{required:!0,message:"产生时间不能为空",trigger:"blur"}],generatePlace:[{required:!0,message:"危废产生地点不能为空",trigger:"blur"}],containerCode:[{required:!0,message:"容器/包装编码不能为空",trigger:"blur"}],containerType:[{required:!0,message:"容器/包装类型不能为空",trigger:"blur"}],containerNumber:[{required:!0,message:"容器/包装数量不能为空",trigger:"blur"}],generateAgent:[{required:!0,message:"产生部门经办人不能为空",trigger:"blur"}],generateDestination:[{required:!0,message:"产生去向不能为空",trigger:"blur"}]}}),{queryParams:r,form:Qe,rules:We}=O(Z),C=async()=>{if(E.value=!0,r.value.params={},h.value){r.value.params=typeof r.value.params=="object"&&!Array.isArray(r.value.params)?r.value.params:{};const e=h.value;r.value.params.beginOutboundTime=`${h.value}-01 00:00:00`;const[d,i]=e.split("-");if(!d||!i)throw new Error("时间格式错误");const n=new Date(d,parseInt(i,10),0).getDate(),v=`${d}-${i}-${n.toString().padStart(2,"0")} 23:59:59`;r.value.params.endOutboundTime=v}const o=await Ae(r.value);L.value=o.rows.map(e=>{var d,i;return{...e,createRateOutCode:[(d=e.generateBatchCode)==null?void 0:d.trim(),(i=e.outboundBatchCode)==null?void 0:i.trim()].filter(n=>n).join("/")||"--"}}),q.value=o.total,E.value=!1},ee=o=>{const{columns:e,data:d}=o,i=[];return e.forEach((n,v)=>{if(v===0){i[v]="合计";return}if(n.property==="disposalQuantity"){const _=d.map(f=>Number(f[n.property])||0).reduce((f,c)=>f+c,0);i[v]=`${_.toFixed(2)} `}else i[v]=""}),i},W=()=>{r.value.pageNum=1,C()},te=()=>{var o;G.value=["",""],J.value=["",""],K.value=["",""],(o=k.value)==null||o.resetFields(),W()},ae=o=>(r.value.pageNum-1)*r.value.pageSize+o+1,oe=async()=>{const o=await V(null,2);H.value=o.data;const e=await V(null,3);U.value=e.data},ie=o=>{Y.value=o.map(e=>e.recordId),j.value=o.length!=1,X.value=!o.length},ne=()=>{var o;try{const e=(o=Q.value)==null?void 0:o.$el;let d=e.querySelector(".el-table__fixed");d||(d=e);const i=A.table_to_book(d,{raw:!0}),n=i.Sheets[i.SheetNames[0]],v=[],m=A.decode_range(n["!ref"]);for(let c=m.s.c;c<=m.e.c;c++){let u=0;for(let g=m.s.r;g<=m.e.r;g++){const a=A.encode_cell({r:g,c}),w=n[a];if(w&&w.v){const T=String(w.v).split("").reduce((S,N)=>S+(N.charCodeAt(0)>255?2:1),0);T>u&&(u=T)}}v.push({wch:Math.min(u+2,60)})}n["!cols"]=v;const _={alignment:{horizontal:"center",vertical:"center",wrapText:!0},border:{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},font:{sz:11,name:"宋体",color:{rgb:"000000"}}};Object.keys(n).forEach(c=>{if(!c.startsWith("!")){const u=n[c];u.s=u.s?{...u.s,..._}:{..._},typeof u.v=="number"&&(u.z=u.z||"0.00")}}),n["!merges"]&&n["!merges"].forEach(c=>{for(let u=c.s.r;u<=c.e.r;u++)for(let g=c.s.c;g<=c.e.c;g++){const a=A.encode_cell({r:u,c:g});n[a]||(n[a]={t:"s",v:""}),n[a].s={..._}}});const f=Ee.write(i,{bookType:"xlsx",bookSST:!1,type:"binary",cellStyles:!0});saveAs(new Blob([re(f)],{type:"application/octet-stream"}),`危废委外利用处置_${new Date().getTime()}.xlsx`)}catch(e){typeof console<"u"&&console.error(e)}},re=o=>{const e=new ArrayBuffer(o.length),d=new Uint8Array(e);for(let i=0;i<o.length;i++)d[i]=o.charCodeAt(i)&255;return e};return se(()=>{oe(),C()}),(o,e)=>{var S,N;const d=be,i=ve,n=pe,v=Fe,m=fe,_=ce,f=ue,c=Ne,u=Se,g=Te,a=he,w=ye,z=Ce,T=we;return F(),R("div",Ie,[t(_e,{"enter-active-class":(S=b(y))==null?void 0:S.animate.searchAnimate.enter,"leave-active-class":(N=b(y))==null?void 0:N.animate.searchAnimate.leave},{default:s(()=>[D(de("div",qe,[t(f,{shadow:"hover"},{default:s(()=>[t(_,{ref_key:"queryFormRef",ref:k,model:b(r),inline:!0,"label-width":"100px"},{default:s(()=>[t(n,{label:"属地单位",style:{width:"308px"}},{default:s(()=>[t(i,{modelValue:b(r).territorialUnitId,"onUpdate:modelValue":e[0]||(e[0]=p=>b(r).territorialUnitId=p),placeholder:"请选择属地单位"},{default:s(()=>[(F(!0),R(me,null,ge(U.value,p=>(F(),M(d,{key:p.deptId,label:p.deptName,value:p.deptId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(n,{label:"委外利用/处理月份","label-width":"130px",style:{width:"338px"}},{default:s(()=>[t(v,{modelValue:h.value,"onUpdate:modelValue":e[1]||(e[1]=p=>h.value=p),"default-value":new Date,placeholder:"选择月份",type:"month","value-format":"YYYY-MM"},null,8,["modelValue","default-value"])]),_:1}),t(n,null,{default:s(()=>[t(m,{type:"primary",icon:"Search",onClick:W},{default:s(()=>e[5]||(e[5]=[B("搜索")])),_:1}),t(m,{icon:"Refresh",onClick:te},{default:s(()=>e[6]||(e[6]=[B("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[P,I.value]])]),_:1},8,["enter-active-class","leave-active-class"]),t(f,{shadow:"never"},{header:s(()=>[t(g,{gutter:10,class:"mb8"},{default:s(()=>[t(c,{span:1.5},{default:s(()=>[t(m,{type:"warning",plain:"",icon:"Download",onClick:ne},{default:s(()=>e[7]||(e[7]=[B("导出")])),_:1})]),_:1}),t(u,{showSearch:I.value,"onUpdate:showSearch":e[2]||(e[2]=p=>I.value=p),onQueryTable:C},null,8,["showSearch"])]),_:1})]),default:s(()=>[D((F(),M(w,{ref_key:"reportTable",ref:Q,data:L.value,"summary-method":ee,"show-summary":"",stripe:"",onSelectionChange:ie},{default:s(()=>[t(a,{index:ae,label:"序号",type:"index",width:"55"}),t(a,{label:"委外利用/处置批次编码",align:"center",prop:"disposalBatchCode"}),t(a,{label:"出厂时间",align:"center",prop:"outboundTime"}),t(a,{label:"容器/包装编码",align:"center",prop:"containerCode"}),t(a,{label:"容器/包装类型",align:"center",prop:"containerType"}),t(a,{label:"容器/包装数量",align:"center",prop:"containerNumber"}),t(a,{label:"危险废物名称",align:"center"},{default:s(()=>[t(a,{label:"行业俗称",align:"center",prop:"wasteCommonName"}),t(a,{label:"国家危废名录名称",align:"center",prop:"wasteNationalName"})]),_:1}),t(a,{label:"危险废物类别",align:"center",prop:"wasteCategory"}),t(a,{label:"危险废物代码",align:"center",prop:"wasteCode"}),t(a,{label:"委外利用/处置量",align:"center",prop:"disposalQuantity"}),t(a,{label:"计量单位",align:"center",prop:"unit"}),t(a,{label:"利用/处置方式",align:"center",prop:"disposalWay"}),t(a,{label:"接收类型",align:"center",prop:"receiveType"}),t(a,{label:"危险废物经营许可证持有单位",align:"center"},{default:s(()=>[t(a,{label:"单位名称",align:"center",prop:"businessLicenseName"}),t(a,{label:"许可证编码",align:"center",prop:"businessLicenseEncode"})]),_:1}),t(a,{label:"产生批次编码/出库批次编码",align:"center",prop:"createRateOutCode"})]),_:1},8,["data"])),[[T,E.value]]),D(t(z,{total:q.value,page:b(r).pageNum,"onUpdate:page":e[3]||(e[3]=p=>b(r).pageNum=p),limit:b(r).pageSize,"onUpdate:limit":e[4]||(e[4]=p=>b(r).pageSize=p),onPagination:C},null,8,["total","page","limit"]),[[P,q.value>0]])]),_:1})])}}});export{Je as default};
