package com.biz.epms.epcom.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.epms.common.enums.EpmsBjlx;
import com.biz.epms.epcom.domain.EpcomMeteringDevice;
import com.biz.epms.epcom.domain.bo.EpcomMeteringDeviceBo;
import com.biz.epms.epcom.domain.vo.EpcomMeteringDeviceVo;
import com.biz.epms.epcom.mapper.EpcomMeteringDeviceMapper;
import com.biz.epms.epcom.service.IEpcomMeteringDeviceService;
import com.biz.epms.eptw.service.ICommdevService;
import com.biz.warn.domain.Commdev;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 计量器具Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class EpcomMeteringDeviceServiceImpl implements IEpcomMeteringDeviceService {

    private final EpcomMeteringDeviceMapper baseMapper;
    private final ICommdevService commdevService;


    /**
     * 查询计量器具
     *
     * @param meteringDeviceId 主键
     * @return 计量器具
     */
    @Override
    public EpcomMeteringDeviceVo queryById(Long meteringDeviceId) {
        return baseMapper.selectVoById(meteringDeviceId);
    }

    /**
     * 分页查询计量器具列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 计量器具分页列表
     */
    @Override
    public TableDataInfo<EpcomMeteringDeviceVo> queryPageList(EpcomMeteringDeviceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EpcomMeteringDevice> lqw = new LambdaQueryWrapper<>();
        Page<EpcomMeteringDeviceVo> result = baseMapper.selectVoPageList(pageQuery.build(), lqw, bo);

        Calendar calendar = Calendar.getInstance();
        // 报告5年后过期
        long millisecondsPerDay = 1000 * 60 * 60 * 24;
        result.getRecords().forEach(record -> {

            if (record.getCalibrationTime() != null) {
                Date currentDate = new Date();
                Date endDate = record.getCalibrationTime();
                calendar.setTime(endDate);
                calendar.add(Calendar.YEAR, 1);
                Long effectDay = (calendar.getTime().getTime() - currentDate.getTime()) / millisecondsPerDay;
                record.setEffectiveDay(effectDay >= 0 ? effectDay.toString() : "已过期");
            }
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的计量器具列表
     *
     * @param bo 查询条件
     * @return 计量器具列表
     */
    @Override
    public List<EpcomMeteringDeviceVo> queryList(EpcomMeteringDeviceBo bo) {
        PageQuery pageQuery = new PageQuery(-1, 1);
        LambdaQueryWrapper<EpcomMeteringDevice> lqw = new LambdaQueryWrapper<>();
        return baseMapper.selectVoPageList(pageQuery.build(), lqw, bo).getRecords();
    }

    private LambdaQueryWrapper<EpcomMeteringDevice> buildQueryWrapper(EpcomMeteringDeviceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<EpcomMeteringDevice> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(EpcomMeteringDevice::getMeteringDeviceId);
        lqw.le(bo.getCalibrationTimeEnd() != null, EpcomMeteringDevice::getCalibrationTime, bo.getCalibrationTimeEnd());
        lqw.ge(bo.getCalibrationTimeStart() != null, EpcomMeteringDevice::getCalibrationTime, bo.getCalibrationTimeStart());
        lqw.like(StringUtils.isNotBlank(bo.getMeteringDeviceName()), EpcomMeteringDevice::getMeteringDeviceName, bo.getMeteringDeviceName());
        lqw.eq(bo.getMeteringDeviceType() != null, EpcomMeteringDevice::getMeteringDeviceType, bo.getMeteringDeviceType());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceModel()), EpcomMeteringDevice::getDeviceModel, bo.getDeviceModel());
        lqw.eq(StringUtils.isNotBlank(bo.getManufacturer()), EpcomMeteringDevice::getManufacturer, bo.getManufacturer());
        lqw.eq(bo.getInstallDate() != null, EpcomMeteringDevice::getInstallDate, bo.getInstallDate());
        lqw.eq(bo.getCalibrationTime() != null, EpcomMeteringDevice::getCalibrationTime, bo.getCalibrationTime());
        lqw.eq(bo.getStatus() != null, EpcomMeteringDevice::getStatus, bo.getStatus());
        lqw.eq(bo.getCurrentUserId() != null, EpcomMeteringDevice::getCurrentUserId, bo.getCurrentUserId());
        lqw.eq(bo.getCurrentUserType() != null, EpcomMeteringDevice::getCurrentUserType, bo.getCurrentUserType());
        lqw.eq(StringUtils.isNotBlank(bo.getLocation()), EpcomMeteringDevice::getLocation, bo.getLocation());
        lqw.eq(bo.getIotId() != null, EpcomMeteringDevice::getIotId, bo.getIotId());
        return lqw;
    }

    /**
     * 新增计量器具
     *
     * @param bo 计量器具
     * @return 是否新增成功
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public Boolean insertByBo(EpcomMeteringDeviceBo bo) {
        Commdev commdev = new Commdev();
        commdev.setBjlxid(EpmsBjlx.jiliangqiju.getValue());
        commdev.setMingzi(bo.getMeteringDeviceName());
        commdev.setChangzhanid(1);

        int commdevInsertResult = commdevService.insertCommdev(commdev); // 调用 mapper 插入方法
        if (commdevInsertResult <= 0) {
            throw new RuntimeException("计量器具新增设备表失败");
        }
        // 获取自动生成的 ID
        bo.setIotId(commdev.getId());
        EpcomMeteringDevice add = MapstructUtils.convert(bo, EpcomMeteringDevice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new RuntimeException("新增计量器具失败");
        }
        bo.setMeteringDeviceId(add.getMeteringDeviceId());
        return true;
    }

    /**
     * 修改计量器具
     *
     * @param bo 计量器具
     * @return 是否修改成功
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public Boolean updateByBo(EpcomMeteringDeviceBo bo) {
        if (bo.getIotId() != null) {
            Commdev commdev = new Commdev();
            commdev.setBjlxid(EpmsBjlx.jiliangqiju.getValue());
            commdev.setId(bo.getIotId());
            commdev.setMingzi(bo.getMeteringDeviceName());
            int commdevUpdateResult = commdevService.updateCommdev(commdev);
            if (commdevUpdateResult <= 0) {
                throw new RuntimeException("计量器具修改设备表失败");
            }
        }
        EpcomMeteringDevice update = MapstructUtils.convert(bo, EpcomMeteringDevice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(EpcomMeteringDevice entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除计量器具信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Integer[] oitIds, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        if (oitIds != null && oitIds.length > 0) {
            commdevService.deleteCommdevByIds(oitIds);
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


}
