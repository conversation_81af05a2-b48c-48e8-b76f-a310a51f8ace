package com.biz.epms.epnj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.epms.epfy.common.enums.EpfyTransportApplicationFlowTypeEnum;
import com.biz.epms.epfy.domain.bo.EpfyTransportApplicationBo;
import com.biz.epms.epfy.domain.bo.EpfyTransportRecordBo;
import com.biz.epms.epfy.domain.vo.EpfyTransportApplicationVo;
import com.biz.epms.epfy.domain.vo.EpfyTransportRecordVo;
import com.biz.epms.epfy.mapper.EpfyTransportRecordMapper;
import com.biz.epms.epfy.service.IEpfyTransportApplicationService;
import com.biz.epms.epfy.service.IEpfyTransportRecordService;
import com.biz.epms.epnj.domain.EpnjMudDaily;
import com.biz.epms.epnj.domain.bo.EpnjMudDailyBo;
import com.biz.epms.epnj.domain.vo.EpnjMudDailyVo;
import com.biz.epms.epnj.domain.vo.EpnjWellPreparationVo;
import com.biz.epms.epnj.mapper.EpnjMudDailyMapper;
import com.biz.epms.epnj.service.IEpnjMudDailyService;
import com.biz.epms.epnj.service.IEpnjWellPreparationService;
import com.biz.epms.eptw.domain.bo.EptwOperationAreaBo;
import com.biz.epms.eptw.domain.vo.EptwOperationAreaVo;
import com.biz.epms.eptw.service.IEptwOperationAreaService;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 泥浆日报Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RequiredArgsConstructor
@Service
public class EpnjMudDailyServiceImpl implements IEpnjMudDailyService {

    private final static Integer NIJIANG = 2;
    private final EpnjMudDailyMapper baseMapper;
    private final IEpfyTransportApplicationService epfyTransportApplicationService;
    private final IEpfyTransportRecordService epfyTransportRecordService;
    private final EpfyTransportRecordMapper epfyTransportRecordMapper;
    private final IEptwOperationAreaService eptwOperationAreaService;
    private final IEpnjWellPreparationService epnjWellPreparationService;

    /**
     * 查询泥浆日报
     *
     * @param id 主键
     * @return 泥浆日报
     */
    @Override
    public EpnjMudDailyVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询泥浆日报列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 泥浆日报分页列表
     */
    @Override
    public TableDataInfo<EpnjMudDailyVo> queryPageList(EpnjMudDailyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EpnjMudDaily> lqw = buildQueryWrapper(bo);
        Page<EpnjMudDailyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的泥浆日报列表
     *
     * @param bo 查询条件
     * @return 泥浆日报列表
     */
    @Override
    public List<EpnjMudDailyVo> queryList(EpnjMudDailyBo bo) {
        LambdaQueryWrapper<EpnjMudDaily> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<EpnjMudDaily> buildQueryWrapper(EpnjMudDailyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<EpnjMudDaily> lqw = Wrappers.lambdaQuery();
//        lqw.orderByAsc(EpnjMudDaily::getId);
        lqw.between(params.get("beginDate") != null && params.get("endDate") != null,
                EpnjMudDaily::getDate, params.get("beginDate"), params.get("endDate"));
        lqw.eq(bo.getDate() != null, EpnjMudDaily::getDate, bo.getDate());
        lqw.eq(bo.getDisposeType() != null, EpnjMudDaily::getDisposeType, bo.getDisposeType());
        lqw.eq(bo.getDisposeId() != null, EpnjMudDaily::getDisposeId, bo.getDisposeId());
        lqw.eq(bo.getSlurryDisposeAmount() != null, EpnjMudDaily::getSlurryDisposeAmount, bo.getSlurryDisposeAmount());
        lqw.eq(bo.getMudPullingAmount() != null, EpnjMudDaily::getMudPullingAmount, bo.getMudPullingAmount());
        lqw.eq(bo.getMudStagingAmount() != null, EpnjMudDaily::getMudStagingAmount, bo.getMudStagingAmount());
        lqw.eq(bo.getWaterPullingAmount() != null, EpnjMudDaily::getWaterPullingAmount, bo.getWaterPullingAmount());
        lqw.eq(bo.getWaterStagingAmount() != null, EpnjMudDaily::getWaterStagingAmount, bo.getWaterStagingAmount());
        lqw.eq(bo.getLoss() != null, EpnjMudDaily::getLoss, bo.getLoss());
        lqw.like(bo.getWellNames() != null, EpnjMudDaily::getWellNames, bo.getWellNames());
        lqw.orderByDesc(EpnjMudDaily::getDate);
        return lqw;
    }

    /**
     * 新增泥浆日报
     *
     * @param bo 泥浆日报
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(EpnjMudDailyBo bo) {
        EpnjMudDaily add = MapstructUtils.convert(bo, EpnjMudDaily.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改泥浆日报
     *
     * @param bo 泥浆日报
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(EpnjMudDailyBo bo) {
        EpnjMudDaily update = MapstructUtils.convert(bo, EpnjMudDaily.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(EpnjMudDaily entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除泥浆日报信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


    /**
     * 定时任务，计算拉运量
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void calcTransportQuantityTask() {
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        this.calcTransportQuantity(date);
    }

    @Override
    public EpnjMudDailyBo getLoss(EpnjMudDailyBo bo) throws ParseException {
        BigDecimal slurryDisposeAmount = bo.getSlurryDisposeAmount();
        BigDecimal slurryStagingAmount = bo.getSlurryStagingAmount();
        if (slurryDisposeAmount == null) {
            slurryDisposeAmount = BigDecimal.ZERO;
        }
        if (slurryStagingAmount == null) {
            slurryStagingAmount = BigDecimal.ZERO;
        }
        if (bo.getDisposeType() == 1) {
            BigDecimal slurryTotal = BigDecimal.ZERO;
            EptwOperationAreaVo eptwOperationArea = eptwOperationAreaService.queryById(bo.getDisposeId());

            if (eptwOperationArea == null) {
                return bo;
            }
            // 获取对应拉运记录的总量
            EpfyTransportApplicationBo appBo = new EpfyTransportApplicationBo();
            appBo.setUnloadLocationType(2);// 集中站，卸车地点类型
            appBo.setMediumCategory(3); // 混合相
            appBo.setUnloadLocation(eptwOperationArea.getOperationAreaName());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            List<EpfyTransportApplicationVo> applications = epfyTransportApplicationService.queryList(appBo);
            List<String> wellNames = new ArrayList<>();
            for (EpfyTransportApplicationVo application : applications) {
                EpfyTransportRecordBo recordBo = new EpfyTransportRecordBo();
                String dateStr = bo.getDate();
                Date date = formatter.parse(dateStr);
                recordBo.setTransportTime(date);
                recordBo.setApplicationId(application.getAppId());
                List<EpfyTransportRecordVo> records = epfyTransportRecordService.queryList(recordBo);

                for (EpfyTransportRecordVo record : records) {
                    if (!wellNames.contains(record.getDeparturePoint())) {
                        wellNames.add(record.getDeparturePoint());
                    }
                    if (record.getMediumCategory() == 3) {
                        slurryTotal = slurryTotal.add(record.getNumber());
                    }
                }
            }
            bo.setWellNames(String.join(",", wellNames));
            slurryDisposeAmount = slurryTotal;
            bo.setSlurryDisposeAmount(slurryTotal);
        } else if (bo.getDisposeType() == 2) {
            EpnjWellPreparationVo preparation = epnjWellPreparationService.queryById(bo.getDisposeId());
            if (preparation != null) {
                bo.setWellNames(preparation.getWellName());
            }
        }
        if (slurryDisposeAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal mudTotal = BigDecimal.ZERO;
            BigDecimal waterTotal = BigDecimal.ZERO;
            // 固相总和：固相拉运量 + 固相暂存量
            if (bo.getMudPullingAmount() != null) {
                mudTotal = mudTotal.add(bo.getMudPullingAmount());
            }
            if (bo.getMudStagingAmount() != null) {
                mudTotal = mudTotal.add(bo.getMudStagingAmount());
            }
            // 液相总和：液相拉运量 + 液相暂存量
            if (bo.getWaterPullingAmount() != null) {
                waterTotal = waterTotal.add(bo.getWaterPullingAmount());
            }
            if (bo.getWaterStagingAmount() != null) {
                waterTotal = waterTotal.add(bo.getWaterStagingAmount());
            }
            // 总消耗 = 处理量 - 固相总和 - 液相总和
            BigDecimal loss = slurryDisposeAmount
                    .subtract(mudTotal)
                    .subtract(waterTotal)
                    .subtract(slurryStagingAmount);
            bo.setLoss(loss);
        }
        // 总拉运 = 泥饼拉运 + 滤水拉运
        bo.setTotalPullingAmount(bo.getWaterPullingAmount().add(bo.getMudPullingAmount()));
        return bo;
    }

    /**
     * 计算拉运量
     * @param date
     */
    @Override
    public void calcTransportQuantity(String date) {
        EptwOperationAreaBo areaBo = new EptwOperationAreaBo();
        areaBo.setOperationAreaType(2);
        List<EptwOperationAreaVo> operationAreas = eptwOperationAreaService.queryList(areaBo);

        EpfyTransportApplicationBo bo = new EpfyTransportApplicationBo();
        bo.setFlowType(NIJIANG);
        List<EpfyTransportApplicationVo> applications = epfyTransportApplicationService.queryList(bo);
//        根据关联ID分组
        Map<Long, List<EpfyTransportApplicationVo>> appMap = applications.stream().collect(Collectors.groupingBy(EpfyTransportApplicationVo::getRelatedId));

        EpfyTransportRecordBo recordBo = new EpfyTransportRecordBo();
        recordBo.setFlowType(NIJIANG);
        if (date != null) {
            Date dateNow = DateUtils.parseDate(date);
            recordBo.setTransportTime(dateNow);
        }
        List<EpfyTransportRecordVo> transportRecordVoList = epfyTransportRecordService.queryList(recordBo);
        Map<Long, List<EpfyTransportRecordVo>> transportRecordVoListByApplicationId = StreamUtils.groupByKey(transportRecordVoList, EpfyTransportRecordVo::getApplicationId);
        appMap.keySet().parallelStream().forEach(key -> {
            List<EpfyTransportRecordVo> vos = new ArrayList<>();
            List<EpfyTransportApplicationVo> list = appMap.get(key);
            Integer type = list.getFirst().getUnloadLocationType();
            for (EpfyTransportApplicationVo application : list) {
                List<EpfyTransportRecordVo> records = transportRecordVoListByApplicationId.get(application.getAppId());
                if (CollectionUtils.isEmpty(records)) {
                    // records = epfyTransportRecordService.queryList(recordBo);
                    continue;
                }

                for (EpfyTransportRecordVo record : records) {
                    if (record.getMediumCategory() == 1 || record.getMediumCategory() == 2 || record.getMediumCategory() == 3) {
                        vos.add(record);
                    }
                }
            }
            Map<LocalDate, List<EpfyTransportRecordVo>> recordMap = vos.stream()
                    .collect(Collectors.groupingBy(record ->
                            record.getTransportTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                    ));
            for (LocalDate date1 : recordMap.keySet()) {
                EpnjMudDailyBo mudDailyBo = new EpnjMudDailyBo();
                BigDecimal nb = BigDecimal.ZERO;
                BigDecimal ls = BigDecimal.ZERO;
                List<EpfyTransportRecordVo> records = recordMap.get(date1);
                for (EpfyTransportRecordVo record : records) {
                    if (record.getMediumCategory() == 1) {
                        nb = nb.add(record.getNumber());
                    } else if (record.getMediumCategory() == 2) {
                        ls = ls.add(record.getNumber());
                    }
                }

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDate = date1.format(formatter);
                mudDailyBo.setDate(formattedDate);
                mudDailyBo.setDisposeId(key);
                // 根据卸车地点类型设置处理类型，两个字典是相反的
                if (type == 1) {
                    mudDailyBo.setDisposeType(2);
                } else if (type == 2) {
                    mudDailyBo.setDisposeType(1);
                }
                boolean exists = operationAreas.stream()
                        .anyMatch(area -> area.getOperationAreaId().equals(mudDailyBo.getDisposeId()));

                if (mudDailyBo.getDisposeType() == 1 && !exists) {
                    String arrivalPoint = records.getFirst().getArrivalPoint().trim();
                    Optional<EptwOperationAreaVo> first = operationAreas.stream().filter(area -> area.getOperationAreaName().equals(arrivalPoint)).findFirst();
                    if (first.isEmpty()) {
                        continue;
                    }
                    mudDailyBo.setDisposeId(first.get().getOperationAreaId());
                }
                List<EpnjMudDailyVo> list1 = this.queryList(mudDailyBo);
                mudDailyBo.setMudPullingAmount(nb);
                mudDailyBo.setWaterPullingAmount(ls);
                try {
                    if (list1.isEmpty()) {
                        this.getLoss(mudDailyBo);
                        this.insertByBo(mudDailyBo);
                    } else {
                        EpnjMudDailyVo first = list1.getFirst();
                        mudDailyBo.setId(first.getId());
                        this.getLoss(mudDailyBo);
                        this.updateByBo(mudDailyBo);
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }


    /**
     * 处理运输记录生成泥浆日报逻辑
     *
     * 该方法通过关联取水作业区域、废液运输申请和运输记录，生成对应的泥浆处理日报数据。
     * 主要流程包括：查询操作区域数据、获取运输申请、按关联ID分组处理、遍历运输记录、
     * 计算运输量、验证区域有效性、查询现有日报数据并执行新增或更新操作。
     *
     * @param date 日期字符串，格式为"yyyy-MM-dd"，用于指定运输时间
     * @throws ParseException 当日期解析失败时抛出异常
     */
    public void processTransportRecords(String date) {

        // 1. 查询所有泥浆模块拉运记录
        EpfyTransportRecordBo transportRecordBoQuery = new EpfyTransportRecordBo();
        transportRecordBoQuery.setFlowType(EpfyTransportApplicationFlowTypeEnum.NI_JIANG.getValue());
        transportRecordBoQuery.setTransportTime(DateUtils.parseDate(date));
        List<EpfyTransportRecordVo> transportRecordList = epfyTransportRecordMapper.selectVoPageWithFlowType(new PageQuery(null, 1).build(), transportRecordBoQuery)
                .getRecords();

        if (CollectionUtils.isEmpty(transportRecordList)) {
            return;
        }

        // 2. 查出已存在的日报数据
        List<EpnjMudDaily> epnjMudDailyList = baseMapper.selectList(new LambdaQueryWrapper<EpnjMudDaily>()
                .eq(date != null, EpnjMudDaily::getDate, date));

        // 3. 按处理类型将拉运记录分组处理取出随钻和集中站处理， key1: 处理类型 key2: 拉运时间(yyyy-MM-dd) value: 拉运记录
        Map<Integer, Map<String, List<EpfyTransportRecordVo>>> typeTimeTransportRecordMapList = StreamUtils.groupBy2Key(
                transportRecordList,
                EpfyTransportRecordVo::getUnloadLocationType,
                v -> DateUtils.formatDate(v.getTransportTime()));

        // 4. 按处理类型将泥浆日报分组处理取出随钻和集中站处理， key1: 处理类型 key2: 拉运时间(yyyy-MM-dd) value: 拉运记录
        Map<Integer, Map<String, List<EpnjMudDaily>>> typeTimeEpnjMudDailyMapList = StreamUtils.groupBy2Key(
                epnjMudDailyList,
                EpnjMudDaily::getDisposeType,
                EpnjMudDaily::getDate);

        // 5. 开始合并计算拉运量
        // 遍历处理，第一层：随钻、集中站处理
        typeTimeTransportRecordMapList.forEach((type, map) -> {
            // 泥浆日报的类型和拉运记录的是相反的,需要转换一下
            int disposeType = type == 1 ? 2 : 1;

            Map<String, List<EpnjMudDaily>> epnjMudDailyMapList = typeTimeEpnjMudDailyMapList.getOrDefault(disposeType, new HashMap<>());
            // 遍历处理，第二层：拉运时间
            map.forEach((transportTime, list) -> {
                epnjMudDailyMapList.putIfAbsent(transportTime, new ArrayList<>());
                EpnjMudDailyBo mudDailyBo = new EpnjMudDailyBo();
            });
        });

    }

}


