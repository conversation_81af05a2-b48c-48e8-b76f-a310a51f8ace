import{d as h,h as Q,ak as j,r as p,ai as G,x as S,o as V,t,p as o,aA as H,a8 as M,e as l,M as O,A as W,G as X,H as C,K as Y,J as c,y as Z,ay as ee,ax as oe,aJ as te,q as I,z as le,az as ae}from"./index-Bm6k27Yz.js";import{E as ne}from"./el-row-DPMJBKHh.js";import{u as se,d as re}from"./index-DoOXrQ_s.js";const ue={class:"dialog-footer"},ie=h({name:"SelectUser"}),ce=h({...ie,props:{roleId:{type:[Number,String],required:!0}},emits:["ok"],setup(R,{expose:E,emit:U}){const T=R,{proxy:r}=Q(),{sys_normal_disable:q}=j(r==null?void 0:r.useDict("sys_normal_disable")),b=p([]),i=p(!1),_=p(0),v=p([]),n=G({pageNum:1,pageSize:10,roleId:void 0,userName:void 0,phonenumber:void 0}),w=p(),y=p(),z=()=>{n.roleId=T.roleId,m(),i.value=!0},F=s=>{var e;(e=w.value)==null||e.toggleRowSelection(s,!1)},x=s=>{v.value=s.map(e=>e.userId)},m=async()=>{const s=await se(n);b.value=s.rows,_.value=s.total},f=()=>{n.pageNum=1,m()},B=()=>{var s;(s=y.value)==null||s.resetFields(),m()},K=U,$=async()=>{const s=n.roleId,e=v.value.join(",");if(e==""){r==null||r.$modal.msgError("请选择要分配的用户");return}await re({roleId:s,userIds:e}),r==null||r.$modal.msgSuccess("分配成功"),K("ok"),i.value=!1};return E({show:z}),(s,e)=>{const k=X,g=W,d=Y,A=O,u=oe,D=te,L=ee,J=ae,N=ne,P=H;return V(),S(N,null,{default:t(()=>[o(P,{modelValue:l(i),"onUpdate:modelValue":e[5]||(e[5]=a=>M(i)?i.value=a:null),title:"选择用户",width:"800px",top:"5vh","append-to-body":""},{footer:t(()=>[I("div",ue,[o(d,{type:"primary",onClick:$},{default:t(()=>e[8]||(e[8]=[c("确 定")])),_:1}),o(d,{onClick:e[4]||(e[4]=a=>i.value=!1)},{default:t(()=>e[9]||(e[9]=[c("取 消")])),_:1})])]),default:t(()=>[o(A,{ref_key:"queryFormRef",ref:y,model:l(n),inline:!0},{default:t(()=>[o(g,{label:"用户名称",prop:"userName"},{default:t(()=>[o(k,{modelValue:l(n).userName,"onUpdate:modelValue":e[0]||(e[0]=a=>l(n).userName=a),placeholder:"请输入用户名称",clearable:"",onKeyup:C(f,["enter"])},null,8,["modelValue"])]),_:1}),o(g,{label:"手机号码",prop:"phonenumber"},{default:t(()=>[o(k,{modelValue:l(n).phonenumber,"onUpdate:modelValue":e[1]||(e[1]=a=>l(n).phonenumber=a),placeholder:"请输入手机号码",clearable:"",onKeyup:C(f,["enter"])},null,8,["modelValue"])]),_:1}),o(g,null,{default:t(()=>[o(d,{type:"primary",icon:"Search",onClick:f},{default:t(()=>e[6]||(e[6]=[c("搜索")])),_:1}),o(d,{icon:"Refresh",onClick:B},{default:t(()=>e[7]||(e[7]=[c("重置")])),_:1})]),_:1})]),_:1},8,["model"]),o(N,null,{default:t(()=>[o(L,{ref_key:"tableRef",ref:w,data:l(b),border:"",height:"260px",onRowClick:F,onSelectionChange:x},{default:t(()=>[o(u,{type:"selection",width:"55"}),o(u,{label:"用户名称",prop:"userName","show-overflow-tooltip":!0}),o(u,{label:"用户昵称",prop:"nickName","show-overflow-tooltip":!0}),o(u,{label:"邮箱",prop:"email","show-overflow-tooltip":!0}),o(u,{label:"手机",prop:"phonenumber","show-overflow-tooltip":!0}),o(u,{label:"状态",align:"center",prop:"status"},{default:t(a=>[o(D,{options:l(q),value:a.row.status},null,8,["options","value"])]),_:1}),o(u,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:t(a=>[I("span",null,le(l(r).parseTime(a.row.createTime)),1)]),_:1})]),_:1},8,["data"]),l(_)>0?(V(),S(J,{key:0,page:l(n).pageNum,"onUpdate:page":e[2]||(e[2]=a=>l(n).pageNum=a),limit:l(n).pageSize,"onUpdate:limit":e[3]||(e[3]=a=>l(n).pageSize=a),total:l(_),onPagination:m},null,8,["page","limit","total"])):Z("",!0)]),_:1})]),_:1},8,["modelValue"])]),_:1})}}});export{ce as _};
