<template>
  <div class="app-container">
    <div ref="qbnhtjChart" v-loading="loading" style="width: 100%; height: 260px"></div>
  </div>
</template>

<script>
import { EnergyConsumptionStatistics } from '@/api/epms/eptw/jhtj/jhtj.js';
import * as echarts from 'echarts';
// 是否暗黑模式
const isDarkRef = useDark({
  storageKey: 'useDarkKey',
  valueDark: 'dark',
  valueLight: 'light'
});

export default {
  name: 'qbnhtj',
  props: {
    fatherQueryForm: {
      type: Object,
      required: true
    },
    currentNengYuanName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      isDark: isDarkRef,
      loading: false,
      // 能源总耗echart实例
      echartItem: null,
      resizeHandler: null
    };
  },
  watch: {
    isDark: {
      handler(newValue, oldValue) {
        this.showChart();
      },
      deep: true
    }
  },
  mounted() {
    // this.showChart();
    // 监听尺寸变化，刷新图表
    let that = this;
    this.initChart();
    this.resizeHandler = () => {
      if (this.echartItem) {
        this.echartItem.resize();
      }
    };
    window.addEventListener('resize', this.resizeHandler);
  },
  beforeDestroy() {
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
    }
    this.disposeChart();
  },
  methods: {
    disposeChart() {
      if (this.echartItem) {
        this.echartItem.dispose();
        this.echartItem = null;
      }
    },
    initChart() {
      if (!this.$refs.qbnhtjChart) return;
      this.disposeChart();
      this.echartItem = echarts.init(this.$refs.qbnhtjChart, this.isDark ? 'dark' : 'light');
    },
    /** 加载图表 */
    async showChart() {
      this.loading = true;
      this.$emit('loadingFinish', { type: 'qbLoading', loading: this.loading });
      let rs = null;
      try {
        rs = await EnergyConsumptionStatistics({
          danwei: this.fatherQueryForm['org'],
          indicatorId: this.fatherQueryForm['nengYuanId'],
          dateTime: this.fatherQueryForm['date']
        });
      } catch (e) {
        ElMessage.error(`获取${this.currentNengYuanName}统计情况的数据失败`);
        return;
      } finally {
        this.loading = false;
        this.$emit('loadingFinish', { type: 'qbLoading', loading: this.loading });
      }
      this.initChart();

      let option = {
        backgroundColor: 'rgba(255,255,255,0)',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        title: {
          left: 'center',
          text: this.currentNengYuanName + '统计情况'
        },
        legend: {
          top: 'bottom',
          data: ['实际', '计划', '同期', '环比']
        },
        xAxis: [
          {
            type: 'category',
            data: rs['data']['XAxis'],
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '',
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '实际',
            type: 'bar',
            data: rs['data']['shiJi']
          },
          {
            name: '计划',
            type: 'bar',
            data: rs['data']['plan']
          },
          {
            name: '同期',
            type: 'bar',
            data: rs['data']['lastYear']
          },
          {
            name: '环比',
            type: 'line',
            yAxisIndex: 1,
            data: rs['data']['huanBi']
          }
        ]
      };

      echarts.init(this.$refs.qbnhtjChart, this.isDark ? 'dark' : 'light').setOption(option, true);
    }
  }
};
</script>

<style></style>
