import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MonthQualityForm, MonthQualityQuery, MonthQualityVO } from '@/api/epms/epiw/monthQuality/types';

/**
 * 查询月度水质监测列表
 * @param query
 * @returns {*}
 */

export const listMonthQuality = (query?: MonthQualityQuery): AxiosPromise<MonthQualityVO[]> => {
  return request({
    url: '/epms/monthQuality/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询月度水质监测详细
 * @param qualityReportId
 */
export const getMonthQuality = (qualityReportId: string | number): AxiosPromise<MonthQualityVO> => {
  return request({
    url: '/epms/monthQuality/' + qualityReportId,
    method: 'get'
  });
};

/**
 * 新增月度水质监测
 * @param data
 */
export const addMonthQuality = (data: MonthQualityForm) => {
  return request({
    url: '/epms/monthQuality',
    method: 'post',
    data: data
  });
};

/**
 * 修改月度水质监测
 * @param data
 */
export const updateMonthQuality = (data: MonthQualityForm) => {
  return request({
    url: '/epms/monthQuality',
    method: 'put',
    data: data
  });
};

/**
 * 删除月度水质监测
 * @param qualityReportId
 */
export const delMonthQuality = (qualityReportId: string | number | Array<string | number>) => {
  return request({
    url: '/epms/monthQuality/' + qualityReportId,
    method: 'delete'
  });
};
