<template>
  <div class="common-layout p-2">
    <div class="mb-[10px]">
      <el-card shadow="hover">
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="所属地" prop="operationAreaParentId">
            <el-select v-model="queryParams.operationAreaParentId" placeholder="请选择所属地" @change="getAreaList">
              <el-option v-for="dict in suoShuDiList" :key="dict.operationAreaId" :label="dict.operationAreaName" :value="dict.operationAreaId" />
            </el-select>
          </el-form-item>
          <el-form-item label="片区" prop="iotId">
            <el-select filterable v-model="queryParams.iotId" class="searchDate" placeholder="选择片区" :popper-append-to-body="false">
              <el-option
                v-for="operationArea in pianQuList"
                :key="operationArea.iotId"
                :label="operationArea.operationAreaName"
                :value="parseInt(operationArea.iotId)"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="时间">
            <el-date-picker
              v-model="queryParams.start"
              type="date"
              value-format="YYYY-MM-DD"
              :clearable="false"
              class="searchDate"
              placeholder="开始日期"
            >
            </el-date-picker>
            至
            <el-date-picker
              v-model="queryParams.end"
              type="date"
              value-format="YYYY-MM-DD"
              :clearable="false"
              class="searchDate"
              placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item style="padding-left: 20px">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button type="warning" icon="Download" @click="handleExport">导出</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    <el-card shadow="never" class="grid-container">
      <vxe-grid ref="xTable" :data="tableData" :export-config="{ type: 'xlsx' }" height="auto" v-bind="gridOptions" @edit-closed="editClosedEvent">
        <template #val_default="obj">
          {{ showValue(obj) }}
        </template>
        <template #val_edit="obj">
          <el-input v-model="obj.row[obj.column.property]"></el-input>
        </template>
        <template #action_default="{ row }">
          <el-tooltip content="记录附件预览" placement="top">
            <el-button link type="primary" icon="Document" @click="handleUpload(row)">上传现场照片</el-button>
          </el-tooltip>
          <el-tooltip content="记录附件预览" placement="top">
            <el-button link type="primary" icon="Document" @click="handlePreview(row)">现场照片预览</el-button>
          </el-tooltip>
        </template>
      </vxe-grid>
    </el-card>

    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="wasteStorageRecordFormRef" model="form" label-width="120px">
        <el-form-item label="现场照片" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.pianQuId + form.Date"
            :disabled="false"
            attach-category="treatmentPhoto"
            attach-source-type="riCuoShiJianCe"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="submitForm">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="javascript" name="rcssjjc">
import moment from 'dayjs';
import { getDataList, updateData } from '@/api/epms/epiw/cuoShiJingFeiYeChuLi/index.js';
import { listOperationAreaByDataScope } from '@/api/epms/epcom/operationArea/index';
import { checkPermi } from '@/utils/permission';
import Preview from '@/views/comm/attach/preview.vue';

export default {
  name: 'dailyReport',
  components: { Preview },
  props: {
    monitorPoints: {
      type: Number,
      default: 5 // 默认监测点数量
    }
  },
  data() {
    return {
      loading: false,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      suoShuDiList: [],
      dialog: {
        visible: false,
        title: ''
      },
      previewDialog: {
        visible: false,
        attachSourceId: null
      },
      pianQuList: [], // 所属地列表
      form: {
        pianQuId: undefined,
        Date: undefined,
        file: undefined
      },
      queryParams: {
        start: moment().subtract(1, 'months').format('YYYY-MM-DD'),
        end: moment().format('YYYY-MM-DD'),
        operationAreaParentId: null,
        iotId: null
      },
      gridOptions: {
        border: true,
        stripe: true,
        resizable: true,
        showOverflow: true,
        keepSource: true,
        height: 600,
        editConfig: {
          trigger: 'dblclick',
          mode: 'cell',
          showStatus: true,
          beforeEditMethod({ column }) {
            if (column.params?.permission) {
              return checkPermi(column.params.permission);
            }
            return true;
          }
        },
        columns: []
      },
      tableData: []
    };
  },
  async mounted() {
    if (useRoute().query?.iotId) {
      this.queryParams.iotId = parseInt(useRoute().query.iotId);
    }
    if (useRoute().query?.operationAreaParentId) {
      this.queryParams.operationAreaParentId = useRoute().query.operationAreaParentId;
    }
    await this.getSuoShuDiList();
    await this.getAreaList();
    await this.getList();
  },
  methods: {
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    async getSuoShuDiList() {
      const query = {
        operationAreaType: 0
      };
      const res = await listOperationAreaByDataScope(query);

      //排除矿管站和输油处
      const excludeIds = ['1906889578986254337', '1906889534388219906'];
      this.suoShuDiList = res.rows.filter((item) => !excludeIds.includes(item.operationAreaId));
      this.queryParams.operationAreaParentId = this.suoShuDiList[0].operationAreaId;
    },
    async getAreaList() {
      const query = {
        operationAreaType: 3,
        operationAreaParentId: this.queryParams.operationAreaParentId
      };
      const res = await listOperationAreaByDataScope(query);
      //排除矿管站和输油处片区
      const excludeIds = ['1906906389068820508', '1906906389068820509'];
      this.pianQuList = res.rows.filter((item) => !excludeIds.includes(item.operationAreaId));
      if (!this.queryParams.iotId) {
        this.queryParams.iotId = res.rows[0].iotId;
      }
    },
    handleQuery() {
      this.getList();
    },

    submitForm() {
      this.form.file = undefined;
      this.dialog.visible = false;
    },
    handleUpload(row) {
      this.form.pianQuId = row.bengId;
      this.form.Date = row.date;
      this.dialog.visible = true;
      this.dialog.title = '上传现场照片';
    },
    async getList() {
      this.loading = true;
      const response = await getDataList(this.queryParams);
      this.tableData = response.rows;
      const operationArea = this.pianQuList.find((area) => area.iotId === this.queryParams.iotId);
      this.tableData.forEach((item) => {
        item.unit = operationArea.operationAreaName;
      });
      this.initColumn(this.tableData);
      this.loading = false;
    },
    showValue(obj) {
      return obj.row[obj.column.property];
    },
    handlePreview(row) {
      this.showAttachPreview({
        attachSourceId: row.bengId + row.date,
        attachCategory: 'treatmentPhoto',
        attachSourceType: 'riCuoShiJianCe'
      });
    },

    initColumn(tab) {
      const cols = [
        { title: '片区', field: 'unit', width: 150, align: 'center', fixed: 'left' },
        { title: '日期', field: 'date', width: 150, align: 'center', fixed: 'left' }
      ];

      // 获取所有监测点名称
      const row = tab[0] || {};
      const pointEntries = Object.keys(row)
        .filter((key) => key.startsWith('CuoShiYeChuLiJianCeDian') && key.endsWith('MingCheng'))
        .map((key) => {
          const numMatch = key.match(/^CuoShiYeChuLiJianCeDian(\d+)MingCheng$/);
          if (!numMatch) return null;
          return {
            key,
            num: parseInt(numMatch[1], 10),
            name: row[key]
          };
        })
        .filter((entry) => entry !== null && entry.name);
      pointEntries.sort((a, b) => a.num - b.num);
      // 动态生成监测点列
      pointEntries.forEach((entry) => {
        const pointNum = entry.num;
        cols.push({
          title: entry.name,
          align: 'center',
          children: [
            {
              field: `CuoShiYeChuLiJianCeDian${pointNum}HanYouLiang`,
              title: '含油量mg/L',
              width: 150,
              align: 'center',
              editRender: {},
              slots: { default: 'val_default', edit: 'val_edit' }
            },
            {
              field: `CuoShiYeChuLiJianCeDian${pointNum}XuanFuWu`,
              title: '悬浮物含量mg/L',
              width: 150,
              align: 'center',
              editRender: {},
              slots: { default: 'val_default', edit: 'val_edit' }
            },
            {
              field: `CuoShiYeChuLiJianCeDian${pointNum}ZongHanTieLiang`,
              title: '总铁含量mg/L',
              width: 150,
              align: 'center',
              editRender: {},
              slots: { default: 'val_default', edit: 'val_edit' }
            },
            {
              field: `CuoShiYeChuLiJianCeDian${pointNum}JuHeWu`,
              title: '聚合物mg/L',
              width: 150,
              align: 'center',
              editRender: {},
              slots: { default: 'val_default', edit: 'val_edit' }
            },
            {
              field: `CuoShiYeChuLiJianCeDian${pointNum}PHZhi`,
              title: 'PH值',
              width: 150,
              align: 'center',
              editRender: {},
              slots: { default: 'val_default', edit: 'val_edit' }
            }
          ]
        });
      });

      cols.push({
        field: 'RiCuoShiYeChuLiLiang',
        title: '流量计读数m³',
        width: 150,
        align: 'center',
        editRender: {},
        slots: { default: 'val_default', edit: 'val_edit' }
      });
      cols.push({
        field: 'action',
        title: '现场照片',
        width: 270,
        align: 'center',
        slots: { default: 'action_default', edit: 'val_edit' }
      });
      cols.push({
        field: 'RiCueShiYeJianCeFenXiRen',
        title: '分析人',
        width: 150,
        align: 'center',
        editRender: {},
        slots: { default: 'val_default', edit: 'val_edit' }
      });
      cols.push({
        field: 'RiCueShiYeJianCeLuRuRen',
        title: '录入人',
        width: 150,
        align: 'center',
        editRender: {},
        slots: { default: 'val_default', edit: 'val_edit' }
      });
      // cols.push({
      //   field: "RiCueShiYeJianCeShenHeRen",
      //   title: "审核人",
      //   width: 150,
      //   align: "center",
      //   editRender: {},
      //   slots: { default: "val_default", edit: "val_edit" },
      //   params: {
      //     permission: ['epms:rcssjjc:approve'],
      //   },
      // });
      cols.push({
        field: 'RiCueShiYeJianCeBeiZhu',
        title: '备注',
        width: 150,
        align: 'center',
        editRender: {},
        slots: { default: 'val_default', edit: 'val_edit' }
      });

      this.gridOptions.columns = [{ title: '措施井废液处理日水质监测、日处理量记录', align: 'center', children: cols }];
    },
    editClosedEvent(obj) {
      const row = obj.row;
      const column = obj.column;
      const field = column.property;
      const cellValue = row[field];
      const bujiancanshuId = row[field + 'ParamId'];

      const $table = this.$refs.xTable;

      if (cellValue) {
        updateData({
          dayDate: row.date,
          bujianId: row.bengId,
          bujiancanshu: bujiancanshuId,
          value: cellValue
        }).then((response) => {
          if (response.code === 200) {
            $table.reloadRow(row, null, field);
            this.$modal.msgSuccess('修改成功');
          }
        });
      }
    },
    handleExport() {
      const $grid = this.$refs.xTable;
      if ($grid) {
        $grid.exportData({
          type: 'xlsx',
          filename: '措施井废液处理日水质监测、日处理量记录_' + this.queryParams.start + '-' + this.queryParams.end,
          sheetMethod(params) {
            const { worksheet } = params;
            worksheet.eachRow((excelRow) => {
              excelRow.eachCell((excelCell) => {
                // 设置单元格边框
                excelCell.border = {
                  top: {
                    style: 'thin',
                    color: {
                      argb: 'black'
                    }
                  },
                  left: {
                    style: 'thin',
                    color: {
                      argb: 'black'
                    }
                  },
                  bottom: {
                    style: 'thin',
                    color: {
                      argb: 'black'
                    }
                  },
                  right: {
                    style: 'thin',
                    color: {
                      argb: 'black'
                    }
                  }
                };
              });
            });
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100%; // 父容器高度
}
.grid-container {
  flex: 1; // 表格容器占满剩余空间
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  height: 100%;
}
vxe-grid {
  height: 100%;
}
.el-date-editor {
  --el-date-editor-width: 100%;
}
</style>
