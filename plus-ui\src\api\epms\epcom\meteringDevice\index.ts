import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MeteringDeviceForm, MeteringDeviceQuery, MeteringDeviceVO } from '@/api/epms/epcom/meteringDevice/types';

/**
 * 查询计量器具列表
 * @param query
 * @returns {*}
 */

export const listMeteringDevice = (query?: MeteringDeviceQuery): AxiosPromise<MeteringDeviceVO[]> => {
  return request({
    url: '/epms/meteringDevice/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询计量器具详细
 * @param meteringDeviceId
 */
export const getMeteringDevice = (meteringDeviceId: string | number): AxiosPromise<MeteringDeviceVO> => {
  return request({
    url: '/epms/meteringDevice/' + meteringDeviceId,
    method: 'get'
  });
};

/**
 * 新增计量器具
 * @param data
 */
export const addMeteringDevice = (data: MeteringDeviceForm) => {
  return request({
    url: '/epms/meteringDevice',
    method: 'post',
    data: data
  });
};

/**
 * 修改计量器具
 * @param data
 */
export const updateMeteringDevice = (data: MeteringDeviceForm) => {
  return request({
    url: '/epms/meteringDevice',
    method: 'put',
    data: data
  });
};

/**
 * 删除计量器具
 * @param meteringDeviceId
 * @param oitId
 */
export const delMeteringDevice = (meteringDeviceId: string | number | Array<string | number>, oitId: string | number | Array<string | number>) => {
  return request({
    url: '/epms/meteringDevice',
    method: 'delete',
    params: {
      meteringDeviceIds: Array.isArray(meteringDeviceId) ? meteringDeviceId.join(',') : meteringDeviceId,
      oitIds: Array.isArray(oitId) ? oitId.join(',') : oitId
    }
  });
};

/**
 * 获取计量器具使用者列表
 */
export const getCurrentUserMap = () => {
  return request({
    url: '/epms/meteringDevice/currentUser',
    method: 'get'
  });
};
