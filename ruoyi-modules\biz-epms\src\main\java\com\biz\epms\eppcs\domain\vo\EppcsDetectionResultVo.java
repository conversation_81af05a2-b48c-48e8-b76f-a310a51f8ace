package com.biz.epms.eppcs.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.biz.epms.eppcs.domain.EppcsDetectionResult;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 检测结果视图对象 eppcs_detection_result
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = EppcsDetectionResult.class)
public class EppcsDetectionResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 检测结果id
     */
    @ExcelProperty(value = "检测结果id")
    private Long resultId;

    /**
     * 申请id
     */
    @ExcelProperty(value = "申请id")
    private Long applicationId;

    /**
     * 样品编号
     */
    @ExcelProperty(value = "样品编号")
    private String sampleNumber;

    /**
     * 检测报告附件
     */
    @ExcelProperty(value = "检测报告附件")
    private Long reportFile;

    /**
     * 检测结果数据 JSON格式
     */
    @ExcelProperty(value = "检测结果数据 JSON格式")
    private String resultData;

    /**
     * 检测结果 (0达标,1不达标)
     */
    @ExcelProperty(value = "检测结果")
    private Integer resultStatus;

    /**
     * 检测结果描述
     */
    @ExcelProperty(value = "检测结果描述")
    private String resultStatusDesc;

    /**
     * 检测状态 (0草稿,1驳回,2待属地单位确认,3待煤层气管理中心确认,4检测完成)
     */
    @ExcelProperty(value = "检测状态")
    private Integer detectionStatus;

    /**
     * 检测状态描述
     */
    @ExcelProperty(value = "检测状态描述")
    private String detectionStatusDesc;

    /**
     * 检测日期
     */
    @ExcelProperty(value = "检测日期")
    private Date detectionDate;

    /**
     * 报送时间
     */
    @ExcelProperty(value = "报送时间")
    private Date submitTime;

    /**
     * 审批时间
     */
    @ExcelProperty(value = "审批时间")
    private Date approvalTime;

    /**
     * 审批建议
     */
    @ExcelProperty(value = "审批建议")
    private String approvalRemark;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 预警标记 (0无预警,1有预警)
     */
    @ExcelProperty(value = "预警标记")
    private Integer warningFlag;

    /**
     * 预警类型
     */
    @ExcelProperty(value = "预警类型")
    private String warningType;

    /**
     * 预警时间
     */
    @ExcelProperty(value = "预警时间")
    private Date warningTime;

    /**
     * 检测项目名称
     */
    @ExcelProperty(value = "检测项目名称")
    private String projectName;

    /**
     * 检测标准
     */
    @ExcelProperty(value = "检测标准")
    private Integer detectionStandard;

    /**
     * 审批人
     */
    @ExcelProperty(value = "审批人")
    private String approver;

}
