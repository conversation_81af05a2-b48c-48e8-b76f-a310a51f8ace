import{d as Me,h as Re,r as d,ai as B,b as Le,x as p,o as i,t as l,w as te,p as t,e as _,M as oe,y,A as je,an as De,I as Oe,J as v,c as j,K as Fe,F as K,C as H,aw as Pe,z as ne,q as D,G as ze,v as We,aA as Ae,B as Ge,D as qe,ay as Je,ax as Ke}from"./index-Bm6k27Yz.js";import{_ as He}from"./index-CgIrpc6R.js";/* empty css                          */import{g as Qe,a as Xe,c as Ye,b as Ze,d as xe,t as O,e as he,f as ea}from"./index-D3G1-70w.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-CKyedCMh.js";const aa={class:"dialog-footer"},la={class:"dialog-footer",style:{float:"right","padding-bottom":"20px"}},da=Me({__name:"submitVerify",props:{taskVariables:{type:Object,default:()=>{}}},emits:["submitCallback","cancelCallback"],setup(ue,{expose:de,emit:ie}){const{proxy:a}=Re(),Q=d(),X=d(),Y=d(),Z=d(),x=d(),h=ue,r=d(!0),u=d(!0),w=d(""),V=d([]),M=d(void 0),N=d([]),ee=d([]),R=d(!1),$=d(!0),S=d(!0),F=d([]),L=d({}),P=d(""),I=d({pop:!1,trust:!1,transfer:!1,addSign:!1,subSign:!1,termination:!1,back:!1}),U=d([]),f=d({id:void 0,createTime:void 0,updateTime:void 0,tenantId:void 0,definitionId:void 0,instanceId:void 0,flowName:void 0,businessId:void 0,nodeCode:void 0,nodeName:void 0,flowCode:void 0,flowStatus:void 0,formCustom:void 0,formPath:void 0,nodeType:void 0,nodeRatio:void 0,applyNode:!1,buttonList:[]}),k=B({visible:!1,title:"提示"}),z=d(!1),n=d({taskId:void 0,message:void 0,assigneeMap:{},variables:{},messageType:["1"],flowCopyList:[]}),c=d({taskId:void 0,nodeCode:void 0,message:void 0,variables:{},messageType:["1"]}),ve=async s=>{M.value=void 0,V.value=[],n.value.fileId=void 0,w.value=s,n.value.message=void 0,k.visible=!0,r.value=!0,u.value=!0;const e=await Qe(w.value);f.value=e.data,I.value={},f.value.buttonList.forEach(C=>{I.value[C.code]=C.show}),u.value=!1;const m={taskId:w.value,variables:h.taskVariables},g=await Xe(m);U.value=g.data,r.value=!1};Le(()=>{});const T=ie,re=async()=>{n.value.taskId=w.value,n.value.variables=h.taskVariables;let s=!1;if(I.value.pop&&U.value&&U.value.length>0){if(U.value.forEach(e=>{(Object.keys(n.value.assigneeMap).length===0||n.value.assigneeMap[e.nodeCode]===""||n.value.assigneeMap[e.nodeCode]===null||n.value.assigneeMap[e.nodeCode]===void 0)&&(s=!0)}),s)return a==null||a.$modal.msgWarning("请选择审批人！"),!1}else n.value.assigneeMap={};if(V.value&&V.value.length>0){const e=[];V.value.forEach(m=>{const g={userId:m.userId,userName:m.nickName};e.push(g)}),n.value.flowCopyList=e}await(a==null?void 0:a.$modal.confirm("是否确认提交？")),r.value=!0,u.value=!0;try{await Ye(n.value),k.visible=!1,T("submitCallback"),a==null||a.$modal.msgSuccess("操作成功")}finally{r.value=!1,u.value=!1}},me=async()=>{c.value={},c.value.messageType=["1"],R.value=!0,$.value=!0,S.value=!0;const s=await Ze(f.value.definitionId,f.value.nodeCode);F.value=s.data,$.value=!1,S.value=!1,c.value.nodeCode=F.value[0].nodeCode},fe=async()=>{c.value.taskId=w.value,await(a==null?void 0:a.$modal.confirm("是否确认驳回到申请人？")),r.value=!0,$.value=!0,S.value=!0,await xe(c.value).finally(()=>{r.value=!1,u.value=!1}),k.visible=!1,$.value=!1,S.value=!1,T("submitCallback"),a==null||a.$modal.msgSuccess("操作成功")},ae=async()=>{k.visible=!1,u.value=!1,L.value={},n.value.assigneeMap={},T("cancelCallback")},pe=()=>{Q.value.open()},ce=s=>{s&&s.length>0&&(V.value=s,M.value=V.value.map(e=>e.userId).join(","))},ge=s=>{const e=s.userId,m=V.value.findIndex(g=>g.userId===e);V.value.splice(m,1),M.value=V.value.map(g=>g.userId).join(",")},be=async()=>{Z.value.open()},ke=async s=>{if(s&&s.length>0){const e=B({userIds:s.map(m=>m.userId),taskId:w.value,message:n.value.message});await(a==null?void 0:a.$modal.confirm("是否确认提交？")),r.value=!0,u.value=!0,await O(e,"addSignature").finally(()=>{r.value=!1,u.value=!1}),k.visible=!1,T("submitCallback"),a==null||a.$modal.msgSuccess("操作成功")}else a==null||a.$modal.msgWarning("请选择用户！")},Ce=async s=>{await(a==null?void 0:a.$modal.confirm("是否确认提交？")),r.value=!0,u.value=!0;const e=B({userIds:[s.userId],taskId:w.value,message:n.value.message});await O(e,"reductionSignature").finally(()=>{r.value=!1,u.value=!1}),k.visible=!1,T("submitCallback"),a==null||a.$modal.msgSuccess("操作成功")},ye=()=>{X.value.open()},we=async s=>{if(s&&s.length>0){const e=B({userId:s[0].userId,taskId:w.value,message:n.value.message});await(a==null?void 0:a.$modal.confirm("是否确认提交？")),r.value=!0,u.value=!0,await O(e,"transferTask").finally(()=>{r.value=!1,u.value=!1}),k.visible=!1,T("submitCallback"),a==null||a.$modal.msgSuccess("操作成功")}else a==null||a.$modal.msgWarning("请选择用户！")},Ie=()=>{Y.value.open()},Ve=async s=>{if(s&&s.length>0){const e=B({userId:s[0].userId,taskId:w.value,message:n.value.message});await(a==null?void 0:a.$modal.confirm("是否确认提交？")),r.value=!0,u.value=!0,await O(e,"delegateTask").finally(()=>{r.value=!1,u.value=!1}),k.visible=!1,T("submitCallback"),a==null||a.$modal.msgSuccess("操作成功")}else a==null||a.$modal.msgWarning("请选择用户！")},_e=async()=>{const s={taskId:w.value,comment:n.value.message};await(a==null?void 0:a.$modal.confirm("是否确认终止？")),r.value=!0,u.value=!0,await he(s).finally(()=>{r.value=!1,u.value=!1}),k.visible=!1,T("submitCallback"),a==null||a.$modal.msgSuccess("操作成功")},Te=async()=>{const s=await ea(w.value);N.value=s.data,N.value&&N.value.length>0&&N.value.forEach(e=>{e.nodeName=f.value.nodeName}),z.value=!0},Ue=async s=>{s.permissionFlag||a==null||a.$modal.msgError("没有可选择的人员，请联系管理员！"),ee.value=s.permissionFlag,P.value=s.nodeCode,x.value.open()},Se=async s=>{const e=s.map(g=>g.userId),m=s.map(g=>g.nickName);n.value.assigneeMap[P.value]=e.join(","),L.value[P.value]=m.join(",")};return de({openDialog:ve}),(s,e)=>{const m=Oe,g=De,C=je,le=He,b=Fe,Ne=Pe,W=ze,$e=qe,Be=Ge,A=Ae,G=Ke,Ee=Je,se=We;return i(),p(A,{modelValue:_(k).visible,"onUpdate:modelValue":e[10]||(e[10]=o=>_(k).visible=o),title:_(k).title,width:"50%",draggable:"","before-close":ae,center:"","close-on-click-modal":!1},{footer:l(()=>[D("span",aa,[t(b,{disabled:u.value,type:"primary",onClick:re},{default:l(()=>e[15]||(e[15]=[v(" 提交 ")])),_:1},8,["disabled"]),f.value.flowStatus==="waiting"&&I.value.trust?(i(),p(b,{key:0,disabled:u.value,type:"primary",onClick:Ie},{default:l(()=>e[16]||(e[16]=[v(" 委托 ")])),_:1},8,["disabled"])):y("",!0),f.value.flowStatus==="waiting"&&I.value.transfer?(i(),p(b,{key:1,disabled:u.value,type:"primary",onClick:ye},{default:l(()=>e[17]||(e[17]=[v(" 转办 ")])),_:1},8,["disabled"])):y("",!0),f.value.flowStatus==="waiting"&&Number(f.value.nodeRatio)>0&&I.value.addSign?(i(),p(b,{key:2,disabled:u.value,type:"primary",onClick:be},{default:l(()=>e[18]||(e[18]=[v(" 加签 ")])),_:1},8,["disabled"])):y("",!0),f.value.flowStatus==="waiting"&&Number(f.value.nodeRatio)>0&&I.value.subSign?(i(),p(b,{key:3,disabled:u.value,type:"primary",onClick:Te},{default:l(()=>e[19]||(e[19]=[v(" 减签 ")])),_:1},8,["disabled"])):y("",!0),f.value.flowStatus==="waiting"&&I.value.termination?(i(),p(b,{key:4,disabled:u.value,type:"danger",onClick:_e},{default:l(()=>e[20]||(e[20]=[v(" 终止 ")])),_:1},8,["disabled"])):y("",!0),f.value.flowStatus==="waiting"&&I.value.back?(i(),p(b,{key:5,disabled:u.value,type:"danger",onClick:me},{default:l(()=>e[21]||(e[21]=[v(" 退回 ")])),_:1},8,["disabled"])):y("",!0),t(b,{disabled:u.value,onClick:ae},{default:l(()=>e[22]||(e[22]=[v("取消")])),_:1},8,["disabled"])])]),default:l(()=>[te((i(),p(_(oe),{model:n.value,"label-width":"120px"},{default:l(()=>[t(C,{label:"消息提醒"},{default:l(()=>[t(g,{modelValue:n.value.messageType,"onUpdate:modelValue":e[0]||(e[0]=o=>n.value.messageType=o)},{default:l(()=>[t(m,{value:"1",name:"type",disabled:""},{default:l(()=>e[11]||(e[11]=[v("站内信")])),_:1}),t(m,{value:"2",name:"type"},{default:l(()=>e[12]||(e[12]=[v("邮件")])),_:1}),t(m,{value:"3",name:"type"},{default:l(()=>e[13]||(e[13]=[v("短信")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(C,{label:"附件"},{default:l(()=>[t(le,{modelValue:n.value.fileId,"onUpdate:modelValue":e[1]||(e[1]=o=>n.value.fileId=o),"file-type":["png","jpg","jpeg","doc","docx","xlsx","xls","ppt","txt","pdf"],"file-size":20},null,8,["modelValue"])]),_:1}),I.value.copy?(i(),p(C,{key:0,label:"抄送"},{default:l(()=>[t(b,{type:"primary",icon:"Plus",circle:"",onClick:pe}),(i(!0),j(K,null,H(V.value,o=>(i(),p(Ne,{key:o.userId,closable:"",style:{margin:"2px"},onClose:q=>ge(o)},{default:l(()=>[v(ne(o.nickName),1)]),_:2},1032,["onClose"]))),128))]),_:1})):y("",!0),I.value.pop&&U.value&&U.value.length>0?(i(),p(C,{key:1,label:"下一步审批人",prop:"assigneeMap"},{default:l(()=>[(i(!0),j(K,null,H(U.value,(o,q)=>(i(),j("div",{key:q,style:{"margin-bottom":"5px",width:"500px"}},[D("span",null,"【"+ne(o.nodeName)+"】：",1),y("",!0),t(W,{placeholder:"请选择审批人",readonly:"",modelValue:L.value[o.nodeCode],"onUpdate:modelValue":J=>L.value[o.nodeCode]=J},{append:l(()=>[t(b,{onClick:J=>Ue(o),icon:"search"},{default:l(()=>e[14]||(e[14]=[v("选择")])),_:2},1032,["onClick"])]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))]),_:1})):y("",!0),f.value.flowStatus==="waiting"?(i(),p(C,{key:2,label:"审批意见"},{default:l(()=>[t(W,{modelValue:n.value.message,"onUpdate:modelValue":e[2]||(e[2]=o=>n.value.message=o),type:"textarea",resize:"none"},null,8,["modelValue"])]),_:1})):y("",!0)]),_:1},8,["model"])),[[se,r.value]]),t(_(E),{ref_key:"userSelectCopyRef",ref:Q,multiple:!0,data:M.value,onConfirmCallBack:ce},null,8,["data"]),t(_(E),{ref_key:"transferTaskRef",ref:X,multiple:!1,onConfirmCallBack:we},null,512),t(_(E),{ref_key:"delegateTaskRef",ref:Y,multiple:!1,onConfirmCallBack:Ve},null,512),t(_(E),{ref_key:"multiInstanceUserRef",ref:Z,multiple:!0,onConfirmCallBack:ke},null,512),t(_(E),{ref_key:"porUserRef",ref:x,multiple:!0,userIds:ee.value,onConfirmCallBack:Se},null,8,["userIds"]),t(A,{modelValue:R.value,"onUpdate:modelValue":e[8]||(e[8]=o=>R.value=o),draggable:"",title:"驳回",width:"40%","close-on-click-modal":!1},{footer:l(()=>[D("div",la,[t(b,{disabled:S.value,type:"primary",onClick:fe},{default:l(()=>e[26]||(e[26]=[v("确认")])),_:1},8,["disabled"]),t(b,{disabled:S.value,onClick:e[7]||(e[7]=o=>R.value=!1)},{default:l(()=>e[27]||(e[27]=[v("取消")])),_:1},8,["disabled"])])]),default:l(()=>[f.value.flowStatus==="waiting"?te((i(),p(_(oe),{key:0,model:c.value,"label-width":"120px"},{default:l(()=>[t(C,{label:"驳回节点"},{default:l(()=>[t(Be,{modelValue:c.value.nodeCode,"onUpdate:modelValue":e[3]||(e[3]=o=>c.value.nodeCode=o),clearable:"",placeholder:"请选择",style:{width:"300px"}},{default:l(()=>[(i(!0),j(K,null,H(F.value,o=>(i(),p($e,{key:o.nodeCode,label:o.nodeName,value:o.nodeCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(C,{label:"消息提醒"},{default:l(()=>[t(g,{modelValue:c.value.messageType,"onUpdate:modelValue":e[4]||(e[4]=o=>c.value.messageType=o)},{default:l(()=>[t(m,{label:"1",name:"type",disabled:""},{default:l(()=>e[23]||(e[23]=[v("站内信")])),_:1}),t(m,{label:"2",name:"type"},{default:l(()=>e[24]||(e[24]=[v("邮件")])),_:1}),t(m,{label:"3",name:"type"},{default:l(()=>e[25]||(e[25]=[v("短信")])),_:1})]),_:1},8,["modelValue"])]),_:1}),f.value.flowStatus==="waiting"?(i(),p(C,{key:0,label:"附件"},{default:l(()=>[t(le,{modelValue:c.value.fileId,"onUpdate:modelValue":e[5]||(e[5]=o=>c.value.fileId=o),"file-type":["png","jpg","jpeg","doc","docx","xlsx","xls","ppt","txt","pdf"],"file-size":20},null,8,["modelValue"])]),_:1})):y("",!0),t(C,{label:"审批意见"},{default:l(()=>[t(W,{modelValue:c.value.message,"onUpdate:modelValue":e[6]||(e[6]=o=>c.value.message=o),type:"textarea",resize:"none"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[se,$.value]]):y("",!0)]),_:1},8,["modelValue"]),t(A,{modelValue:z.value,"onUpdate:modelValue":e[9]||(e[9]=o=>z.value=o),draggable:"",title:"减签人员",width:"700px",height:"400px","append-to-body":"","close-on-click-modal":!1},{default:l(()=>[D("div",null,[t(Ee,{data:N.value,border:""},{default:l(()=>[t(G,{prop:"nodeName",label:"任务名称"}),t(G,{prop:"nickName",label:"办理人"}),t(G,{label:"操作",align:"center",width:"160"},{default:l(o=>[t(b,{type:"danger",size:"small",icon:"Delete",onClick:q=>Ce(o.row)},{default:l(()=>e[28]||(e[28]=[v("删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"])}}});export{da as _};
