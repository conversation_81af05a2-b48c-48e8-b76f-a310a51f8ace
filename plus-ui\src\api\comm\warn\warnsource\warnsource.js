import request from '@/utils/request.js';

// 查询告警配置列表
export function listWarnsource(query) {
  return request({
    url: '/warnsource/list',
    method: 'get',
    params: query
  });
}

// 查询告警配置详细
export function getWarnsource(reqData) {
  return request({
    url: '/warnsource/single',
    method: 'get',
    params: reqData
  });
}

// 新增告警配置
export function addWarnsource(data) {
  return request({
    url: '/warnsource',
    method: 'post',
    data: data
  });
}

// 修改告警配置
export function updateWarnsource(data) {
  return request({
    url: '/warnsource',
    method: 'put',
    data: data
  });
}

// 删除告警配置
export function delWarnsource(warnSetIds) {
  return request({
    url: '/warnsource/' + warnSetIds,
    method: 'delete'
  });
}

// 根据bjlxid查询bujiancanshu列表
export function bujiancanshuListByBjlxid(bjlxid) {
  return request({
    url: '/warnsource/bujiancanshu/' + bjlxid,
    method: 'get'
  });
}
// 根据bjlxid查询bujianid列表
export function bujianidListByBjlxid(bjlxid) {
  return request({
    url: '/warnsource/bujianid/' + bjlxid,
    method: 'get'
  });
}

// 查询bujianleixing列表
export function allListBujianleixing(query) {
  return request({
    url: '/bujianleixing/allList',
    method: 'get',
    params: query
  });
}
