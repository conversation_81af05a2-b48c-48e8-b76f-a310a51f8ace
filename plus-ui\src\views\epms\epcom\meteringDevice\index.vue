<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="器具名称" prop="meteringDeviceName">
              <el-input v-model="queryParams.meteringDeviceName" clearable placeholder="请输入器具名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备型号" prop="deviceModel">
              <el-input v-model="queryParams.deviceModel" clearable placeholder="请输入设备型号" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="厂家" prop="manufacturer">
              <el-input v-model="queryParams.manufacturer" clearable placeholder="请输入厂家" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="安装日期" prop="installDate">
              <el-date-picker
                v-model="queryParams.installDate"
                clearable
                end-placeholder="结束日期"
                range-separator="至"
                start-placeholder="开始日期"
                type="daterange"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                <el-option v-for="dict in epcom_metering_device_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="当前使用者" prop="currentUserId">
              <el-select v-model="queryParams.currentUserId" placeholder="请选择当前使用者" clearable>
                <el-option v-for="dict in currentUserList" :key="dict.id" :label="dict.mingzi" :value="parseInt(dict.id)" />
              </el-select>
            </el-form-item>
            <el-form-item label="所在位置" prop="location">
              <el-input v-model="queryParams.location" clearable placeholder="请输入所在位置" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="标定时间" prop="calibrationTime">
              <el-date-picker
                v-model="queryParams.calibrationTime"
                clearable
                end-placeholder="结束日期"
                range-separator="至"
                start-placeholder="开始日期"
                type="datetimerange"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="属地单位" prop="operationAreaId">
              <el-select v-model="queryParams.operationAreaId" placeholder="请选择属地单位" clearable>
                <el-option
                  v-for="dict in operationAreaList"
                  :key="dict.operationAreaId"
                  :label="dict.operationAreaName"
                  :value="dict.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:meteringDevice:add']" icon="Plus" plain type="primary" @click="handleAdd"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:meteringDevice:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:meteringDevice:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:meteringDevice:export']" icon="Download" plain type="warning" @click="handleExport">导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="meteringDeviceList" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="器具名称" min-width="120" prop="meteringDeviceName" />
        <el-table-column label="器具类型" align="center" prop="meteringDeviceType" min-width="100">
          <template #default="scope">
            {{ getMeterinigTypeName(scope.row.meteringDeviceType) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="属地单位" min-width="100" prop="operationAreaName" />
        <el-table-column align="center" label="设备型号" min-width="120" prop="deviceModel" />
        <el-table-column align="center" label="厂家" prop="manufacturer" />
        <el-table-column label="安装日期" align="center" prop="installDate">
          <template #default="scope">
            <span>{{ parseTime(scope.row.installDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="标定时间" align="center" prop="calibrationTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.calibrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="剩余时间(天)" prop="effectiveDay" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="epcom_metering_device_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="当前使用者" align="center" prop="currentUserId">
          <template #default="scope">
            {{ getUserName(scope.row.currentUserId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="所在位置" prop="location" />
        <!--        <el-table-column label="物联网id" align="center" prop="iotId" />-->
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="附件预览" placement="top">
              <el-button v-hasPermi="['epms:meteringDevice:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >附件预览
              </el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:meteringDevice:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情
              </el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epms:meteringDevice:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)">修改 </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:meteringDevice:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改计量器具对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="meteringDeviceFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="器具名称" prop="meteringDeviceName">
          <el-input v-model="form.meteringDeviceName" placeholder="请输入器具名称" />
        </el-form-item>
        <el-form-item label="器具类型" prop="meteringDeviceType">
          <el-tree-select
            v-model="form.meteringDeviceType"
            :data="meteringDeviceTypeOptions"
            :props="{ value: 'deviceTypeId', label: 'deviceTypeName', children: 'children' }"
            value-key="deviceTypeId"
            placeholder="请选择父id"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="属地单位" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择属地单位">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备型号" prop="deviceModel">
          <el-input v-model="form.deviceModel" placeholder="请输入设备型号" />
        </el-form-item>
        <el-form-item label="厂家" prop="manufacturer">
          <el-input v-model="form.manufacturer" placeholder="请输入厂家" />
        </el-form-item>
        <el-form-item label="安装日期" prop="installDate">
          <el-date-picker v-model="form.installDate" clearable placeholder="请选择安装日期" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="标定时间" prop="calibrationTime">
          <el-date-picker v-model="form.calibrationTime" clearable placeholder="请选择标定时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option v-for="dict in epcom_metering_device_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="使用者类型" prop="currentUserType">
          <el-select v-model="form.currentUserType" placeholder="请选择当前使用者类型" @change="changeUserType" filterable>
            <el-option v-for="dict in eptw_device_user_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前使用者" prop="currentUserId">
          <el-select v-model="form.currentUserId" placeholder="请选择当前使用者" filterable>
            <el-option v-for="dict in currentUserListOption" :key="dict.id" :label="dict.mingzi" :value="parseInt(dict.id)"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入所在位置" />
        </el-form-item>
        <el-form-item label="合格证" prop="certificateFile">
          <attachFileUpload
            v-model="form.certificateFile"
            :attach-source-id="form.meteringDeviceId"
            :disabled="false"
            attach-category="deviceCertificate"
            attach-source-type="meteringDevice"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="校准附件" prop="calibrationFile">
          <attachFileUpload
            v-model="form.calibrationFile"
            :attach-source-id="form.meteringDeviceId"
            :disabled="false"
            attach-category="deviceCalibration"
            attach-source-type="meteringDevice"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>

        <!--        <el-form-item label="物联网id" prop="iotId">-->
        <!--          <el-input v-model="form.iotId" placeholder="请输入物联网id" />-->
        <!--        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="meteringDeviceFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <el-form-item label="器具名称" prop="meteringDeviceName">
          <el-input v-model="form.meteringDeviceName" placeholder="请输入器具名称" />
        </el-form-item>
        <el-form-item label="器具类型" prop="meteringDeviceType">
          <el-tree-select
            v-model="form.meteringDeviceType"
            :data="meteringDeviceTypeOptions"
            :props="{ value: 'deviceTypeId', label: 'deviceTypeName', children: 'children' }"
            value-key="deviceTypeId"
            placeholder="请选择父id"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="属地单位" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择属地单位">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="设备型号" prop="deviceModel">
          <el-input v-model="form.deviceModel" placeholder="请输入设备型号" />
        </el-form-item>
        <el-form-item label="厂家" prop="manufacturer">
          <el-input v-model="form.manufacturer" placeholder="请输入厂家" />
        </el-form-item>
        <el-form-item label="安装日期" prop="installDate">
          <el-date-picker v-model="form.installDate" clearable placeholder="请选择安装日期" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="标定时间" prop="calibrationTime">
          <el-date-picker v-model="form.calibrationTime" clearable placeholder="请选择标定时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option v-for="dict in epcom_metering_device_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="使用者类型" prop="currentUserType">
          <el-select v-model="form.currentUserType" placeholder="请选择当前使用者类型" @change="changeUserType">
            <el-option v-for="dict in eptw_device_user_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前使用者" prop="currentUserId">
          <el-select v-model="form.currentUserId" placeholder="请选择当前使用者">
            <el-option v-for="dict in currentUserListOption" :key="dict.id" :label="dict.mingzi" :value="parseInt(dict.id)"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所在位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入所在位置" />
        </el-form-item>
        <!--        <el-form-item label="物联网id" prop="iotId">-->
        <!--          <el-input v-model="form.iotId" placeholder="请输入物联网id" />-->
        <!--        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MeteringDevice" lang="ts">
import {
  addMeteringDevice,
  delMeteringDevice,
  getCurrentUserMap,
  getMeteringDevice,
  listMeteringDevice,
  updateMeteringDevice
} from '@/api/epms/epcom/meteringDevice';
import { MeteringDeviceForm, MeteringDeviceQuery, MeteringDeviceVO } from '@/api/epms/epcom/meteringDevice/types';
import { listMeteringDeviceType } from '@/api/epms/epcom/meteringDeviceType';
import { CommdevVO } from '@/api/comm/commdev/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { MeteringDeviceTypeVO } from '@/api/epms/epcom/meteringDeviceType/types';

type MeteringDeviceTypeOption = {
  deviceTypeId: number;
  deviceTypeName: string;
  children?: MeteringDeviceTypeOption[];
};
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epcom_metering_device_status, eptw_device_user_type } = toRefs<any>(proxy?.useDict('epcom_metering_device_status', 'eptw_device_user_type'));

const meteringDeviceList = ref<MeteringDeviceVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const oitIds = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const router = useRouter();
const operationAreaQuery = reactive<OperationAreaQuery>({});
const operationAreaList = ref<OperationAreaVO[]>([]);
const meteringTypes = ref<MeteringDeviceTypeVO[]>([]);
const queryFormRef = ref<ElFormInstance>();
const meteringDeviceFormRef = ref<ElFormInstance>();
const meteringDeviceTypeOptions = ref<MeteringDeviceTypeOption[]>([]); //计量器具类型
const currentUserMap = ref<Map<string, CommdevVO[]>>(new Map()); // 计量器具类型
const currentUserList = ref<CommdevVO[]>([]); // 计量器具类型
const currentUserListOption = ref<CommdevVO[]>([]); // 计量器具类型
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MeteringDeviceForm = {
  meteringDeviceId: undefined,
  meteringDeviceName: undefined,
  meteringDeviceType: undefined,
  deviceModel: undefined,
  manufacturer: undefined,
  installDate: undefined,
  calibrationTime: undefined,
  status: undefined,
  currentUserId: undefined,
  currentUserType: undefined,
  location: undefined,
  iotId: undefined
};
const data = reactive<PageData<MeteringDeviceForm, MeteringDeviceQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    meteringDeviceName: undefined,
    meteringDeviceType: undefined,
    deviceModel: undefined,
    manufacturer: undefined,
    installDate: [],
    calibrationTime: [],
    status: undefined,
    currentUserId: undefined,
    currentUserType: undefined,
    location: undefined,
    iotId: undefined,
    uploadTime: undefined,
    file: undefined,
    params: {}
  },
  rules: {
    meteringDeviceId: [{ required: true, message: '计量器具id不能为空', trigger: 'blur' }],
    meteringDeviceType: [{ required: true, message: '计量器具类型不能为空', trigger: 'change' }],
    meteringDeviceName: [{ required: true, message: '计量器具名称不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '属地单位不能为空', trigger: 'blur' }],
    deviceModel: [{ required: true, message: '设备型号不能为空', trigger: 'blur' }],
    manufacturer: [{ required: true, message: '厂家不能为空', trigger: 'blur' }],
    installDate: [{ required: true, message: '安装日期不能为空', trigger: 'blur' }],
    calibrationTime: [{ required: true, message: '标定时间不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
    currentUserId: [{ required: true, message: '当前使用者不能为空', trigger: 'blur' }],
    currentUserType: [{ required: true, message: '当前使用者类型不能为空', trigger: 'blur' }],
    location: [{ required: true, message: '所在位置不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询计量器具列表 */
const getList = async () => {
  loading.value = true;
  if (queryParams.value.installDate && queryParams.value.installDate.length === 2) {
    queryParams.value.params['begininstallDate'] = queryParams.value.installDate[0];
    queryParams.value.params['endinstallDate'] = queryParams.value.installDate[1];
  } else {
    queryParams.value.params['begininstallDate'] = undefined;
    queryParams.value.params['endinstallDate'] = undefined;
  }
  queryParams.value.installDate = undefined;
  if (queryParams.value.calibrationTime && queryParams.value.calibrationTime.length === 2) {
    queryParams.value.params['beginCalibrationTime'] = queryParams.value.calibrationTime[0];
    queryParams.value.params['endCalibrationTime'] = queryParams.value.calibrationTime[1];
  } else {
    queryParams.value.params['beginCalibrationTime'] = undefined;
    queryParams.value.params['endCalibrationTime'] = undefined;
  }
  queryParams.value.calibrationTime = undefined;
  const res = await listMeteringDevice(queryParams.value);
  meteringDeviceList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};
const getTreeselect = async () => {
  const res = await listMeteringDeviceType();
  meteringDeviceTypeOptions.value = [];
  const data: MeteringDeviceTypeOption = { deviceTypeId: 0, deviceTypeName: '顶级节点', children: [] };
  data.children = proxy?.handleTree<MeteringDeviceTypeOption>(res.data, 'deviceTypeId', 'parentId');
  meteringDeviceTypeOptions.value.push(data);
};
//获取当前使用者
const getCurrentUser = async () => {
  const res = await getCurrentUserMap();
  if (res) {
    currentUserMap.value = new Map(Object.entries(res));
    currentUserList.value = Array.from(currentUserMap.value.values()).flat();
  } else {
    console.error('getCurrentUserMap response is invalid:', res); // 添加日志
  }
};
//获取当前使用者名称
const getUserName = (currentUserId: number) => {
  if (currentUserId) {
    const user = currentUserList.value.find((user) => user.id === currentUserId);
    return user ? user.mingzi : currentUserId;
  } else {
    return '未知';
  }
};
const getMeterinigType = () => {
  listMeteringDeviceType().then((res) => {
    meteringTypes.value = res.data;
  });
};

const getMeterinigTypeName = (deviceTypeId: number) => {
  if (!deviceTypeId) {
    return '未知';
  }

  const deviceType = meteringTypes.value.find((item) => item.deviceTypeId === deviceTypeId);
  return deviceType ? deviceType.deviceTypeName : '未知';
};

const getAreaList = () => {
  operationAreaQuery.operationAreaType = 0;
  listOperationArea(operationAreaQuery).then((res) => {
    operationAreaList.value = res.rows;
  });
};

//当前使用者类型切换
const changeUserType = (currentUserType: number) => {
  if (currentUserType && currentUserMap.value.has(currentUserType.toString())) {
    currentUserListOption.value = currentUserMap.value.get(currentUserType.toString()) || [];
  } else {
    currentUserListOption.value = currentUserList.value;
  }
};
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  meteringDeviceFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: MeteringDeviceVO[]) => {
  ids.value = selection.map((item) => item.meteringDeviceId);
  oitIds.value = selection.filter((item) => item.iotId != null).map((item) => item.iotId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  getTreeselect();
  changeUserType(null);
  dialog.visible = true;
  dialog.title = '添加计量器具';
};

const handlePreview = async (row?: MeteringDeviceVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.meteringDeviceId,
    attachSourceType: 'meteringDevice'
    // attachCategory: "deviceCalibration"
  });
};
/** 详情按钮操作 */
const handleDetail = async (row?: MeteringDeviceVO) => {
  reset();
  await getTreeselect();
  const _meteringDeviceId = row?.meteringDeviceId || ids.value[0];
  const res = await getMeteringDevice(_meteringDeviceId);
  Object.assign(form.value, res.data);
  if (form.value.currentUserType) {
    changeUserType(form.value.currentUserType);
  }
  detaildialog.visible = true;
  detaildialog.title = '计量器具详情';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: MeteringDeviceVO) => {
  reset();
  await getTreeselect();
  const _meteringDeviceId = row?.meteringDeviceId || ids.value[0];
  const res = await getMeteringDevice(_meteringDeviceId);
  Object.assign(form.value, res.data);
  if (form.value.currentUserType) {
    changeUserType(form.value.currentUserType);
  }
  dialog.visible = true;
  dialog.title = '修改计量器具';
};

/** 提交按钮 */
const submitForm = () => {
  meteringDeviceFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.meteringDeviceId) {
        await updateMeteringDevice(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addMeteringDevice(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: MeteringDeviceVO) => {
  const _meteringDeviceIds = row?.meteringDeviceId || ids.value;
  const _oitId = row?.iotId || oitIds.value;
  await proxy?.$modal.confirm('是否确认删除计量器具编号为"' + _meteringDeviceIds + '"的数据项？').finally(() => (loading.value = false));
  await delMeteringDevice(_meteringDeviceIds, _oitId);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/meteringDevice/export',
    {
      ...queryParams.value
    },
    `计量器具_${new Date().getTime()}.xlsx`
  );
};

const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getMeterinigType();
  getAreaList();
  getCurrentUser();
  getList();
});
</script>
<style lang="scss" scoped>
:deep(.el-date-editor) {
  --el-date-editor-width: 100%;
}
</style>
