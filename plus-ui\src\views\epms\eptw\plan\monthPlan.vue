<template>
  <div class="qyui-cell row qyui-container pd20" v-loading="loading">
    <div class="qyui-cell row">
      <vxe-table ref="xTable" :data="tableData" :edit-config="editConfig" border keep-source resizable show-overflow @edit-closed="editClosedEvent">
        <vxe-column type="seq" width="60" fixed="left"></vxe-column>
        <vxe-column title="指标" width="200" field="indicatorName" fixed="left"></vxe-column>
        <vxe-column title="总计" width="100" field="indicatorValue" align="center" fixed="left"></vxe-column>
        <vxe-column title="分配总计" width="100" field="indicatorValueUsed" align="center" fixed="left"></vxe-column>
        <vxe-column title="剩余未分配" width="100" field="indicatorValueRemain" align="center" fixed="left"></vxe-column>

        <vxe-colgroup title="第一季度" align="center">
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter1' }" field="month1" title="1月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month1" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter1' }" field="month2" title="2月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month2" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter1' }" field="month3" title="3月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month3" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column class-name="quarter" field="quarter1" title="季度合计" width="100"> </vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="第二季度" align="center">
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter2' }" field="month4" title="4月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month4" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter2' }" field="month5" title="5月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month5" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter2' }" field="month6" title="6月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month6" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column class-name="quarter" field="quarter2" title="季度合计" width="100"> </vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="第三季度" align="center">
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter3' }" field="month7" title="7月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month7" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter3' }" field="month8" title="8月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month8" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter3' }" field="month9" title="9月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month9" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column class-name="quarter" field="quarter3" title="季度合计" width="100"> </vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="第四季度" align="center">
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter4' }" field="month10" title="10月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month10" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter4' }" field="month11" title="11月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month11" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column :edit-render="{}" :params="{ quarter: 'quarter4' }" field="month12" title="12月" width="100">
            <template #edit="{ row }">
              <vxe-input v-model="row.month12" type="text"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column class-name="quarter" field="quarter4" title="季度合计" width="100"> </vxe-column>
        </vxe-colgroup>
      </vxe-table>
    </div>
  </div>
</template>

<script>
import { initMonthPlan, insertOrUpdateDetail, queryMonthvalues } from '@/api/epms/eptw/plan/plan.js';
import { FloatAdd } from '@/utils/index.ts';
import { ElMessage } from 'element-plus';

export default {
  name: 'monthPlan',

  data() {
    return {
      loading: false,
      planId: this.$route.query.planId,
      planType: this.$route.query.planType,
      indicatorList: [],
      tableData: [],
      planList: [],
      planList1: [1],
      planList2: [2, 3],
      monthId2PlanId: {},
      editConfig: { trigger: 'click', mode: 'cell', showStatus: true }
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        if (route.query.planId) {
          this.planId = route.query.planId;
          this.planType = route.query.planType;
          this.init();
        }
      },
      immediate: true
    }
  },

  methods: {
    async init() {
      if (this.planType == 1) {
        this.planList = this.planList1;
      } else if (this.planType == 2) {
        this.planList = this.planList2;
      }
      await this.initMonthPlan();
      await this.queryMonthvalues();
    },
    async initMonthPlan() {
      await initMonthPlan({ planId: this.planId }).then((res) => {
        if (res.code === 200) {
          console.log('初始化月计划成功');
        }
      });
    },
    async queryMonthvalues() {
      await queryMonthvalues({ planId: this.planId }).then((res) => {
        if (res.code === 200) {
          let indicator = res.data.indicator;
          let indicatorValue = res.data.indicatorValue;
          this.monthId2PlanId = res.data.monthId2PlanId;
          let list = [];
          Object.keys(indicator).forEach((key) => {
            list.push({
              indicatorId: key,
              indicatorName: indicator[key].indicatorName + '(' + indicator[key].indicatorUnitName + ')',
              indicatorValue: indicatorValue[key].indicatorValue,
              indicatorUnitName: indicator[key].indicatorUnitName,
              month1: indicatorValue[key]['monthValues'][0] || 0,
              month2: indicatorValue[key]['monthValues'][1] || 0,
              month3: indicatorValue[key]['monthValues'][2] || 0,
              month4: indicatorValue[key]['monthValues'][3] || 0,
              month5: indicatorValue[key]['monthValues'][4] || 0,
              month6: indicatorValue[key]['monthValues'][5] || 0,
              month7: indicatorValue[key]['monthValues'][6] || 0,
              month8: indicatorValue[key]['monthValues'][7] || 0,
              month9: indicatorValue[key]['monthValues'][8] || 0,
              month10: indicatorValue[key]['monthValues'][9] || 0,
              month11: indicatorValue[key]['monthValues'][10] || 0,
              month12: indicatorValue[key]['monthValues'][11] || 0
            });
          });
          list.forEach((row) => {
            row['quarter1'] = FloatAdd(row['month1'], row['month2'], row['month3']);
            row['quarter2'] = FloatAdd(row['month4'], row['month5'], row['month6']);
            row['quarter3'] = FloatAdd(row['month7'], row['month8'], row['month9']);
            row['quarter4'] = FloatAdd(row['month10'], row['month11'], row['month12']);

            if (row['indicatorUnitName'] === '%') {
              row['quarter1'] = Number(Number(row['quarter1'] / 3).toFixed(2));
              row['quarter2'] = Number(Number(row['quarter2'] / 3).toFixed(2));
              row['quarter3'] = Number(Number(row['quarter3'] / 3).toFixed(2));
              row['quarter4'] = Number(Number(row['quarter4'] / 3).toFixed(2));
              row['indicatorValueUsed'] = Number(FloatAdd(row['quarter1'], row['quarter2'], row['quarter3'], row['quarter4']) / 4).toFixed(2);

              row['indicatorValueRemain'] = '-';
            } else {
              row['indicatorValueUsed'] = FloatAdd(row['quarter1'], row['quarter2'], row['quarter3'], row['quarter4']);

              row['indicatorValueRemain'] = FloatAdd(row['indicatorValue'], -row['indicatorValueUsed']);
            }
          });
          this.tableData = list.filter((item) => this.planList.includes(Number(item.indicatorId)));
        }
      });
    },
    async editClosedEvent(obj) {
      let row = obj.row;
      let column = obj.column;
      const field = column.property;
      const cellValue = row[field];
      const $table = this.$refs.xTable;
      // 判断单元格值是否被修改
      if ($table.isUpdateByRow(row, field)) {
        let sum = this.calcSum(row);
        let perSent = false;
        if (row.indicatorUnitName === '%') {
          perSent = true;
          // sum = Number(Number(sum) / 12).toFixed(2)
        } else {
          if (Number(sum) > Number(row.indicatorValue)) {
            ElMessage.error('超出指标总值');
            // this.$message({
            //   message: '超出指标总值',
            //   type: 'error'
            // });
            await $table.revertData(row, field);
            return;
          }
        }
        let planId = this.getPlanId(field);
        insertOrUpdateDetail({
          planId: planId,
          indicatorId: row.indicatorId,
          indicatorValue: cellValue
        })
          .then((res) => {
            if (res.code === 200) {
              //更新季度值
              let sum = 0;
              let fieldname = '';

              if (column.params.quarter === 'quarter1') {
                sum = FloatAdd(row['month1'], row['month2'], row['month3']);
                fieldname = 'quarter1';
              } else if (column.params.quarter === 'quarter2') {
                sum = FloatAdd(row['month4'], row['month5'], row['month6']);
                fieldname = 'quarter2';
              } else if (column.params.quarter === 'quarter3') {
                sum = FloatAdd(row['month7'], row['month8'], row['month9']);
                fieldname = 'quarter3';
              } else if (column.params.quarter === 'quarter4') {
                sum = FloatAdd(row['month10'], row['month11'], row['month12']);
                fieldname = 'quarter4';
              }

              if (!perSent) {
                row[fieldname] = sum;
                row['indicatorValueUsed'] = FloatAdd(row['quarter1'], row['quarter2'], row['quarter3'], row['quarter4']);
                row['indicatorValueRemain'] = FloatAdd(row['indicatorValue'], -Number(row['indicatorValueUsed']));
              } else {
                row[fieldname] = Number(Number(sum) / 3).toFixed(2);
                row['indicatorValueRemain'] = '-';
                row['indicatorValueUsed'] = Number(FloatAdd(row['quarter1'], row['quarter2'], row['quarter3'], row['quarter4']) / 4).toFixed(2);
              }
              $table.reloadRow(row, null, field);
              ElMessage.success('设置成功'); // 简洁写法
              // this.$message({
              //   message: '设置成功',
              //   type: 'success'
              // });
            }
          })
          .catch(() => {
            $table.revertData(row, field);
          });
      }
    },
    calcSum(row) {
      return FloatAdd(
        row.month1,
        row.month2,
        row.month3,
        row.month4,
        row.month5,
        row.month6,
        row.month7,
        row.month8,
        row.month9,
        row.month10,
        row.month11,
        row.month12
      );
    },
    getPlanId(field) {
      let planId = '';
      switch (field) {
        case 'month1':
          planId = this.monthId2PlanId[1];
          break;
        case 'month2':
          planId = this.monthId2PlanId[2];
          break;
        case 'month3':
          planId = this.monthId2PlanId[3];
          break;
        case 'month4':
          planId = this.monthId2PlanId[4];
          break;
        case 'month5':
          planId = this.monthId2PlanId[5];
          break;
        case 'month6':
          planId = this.monthId2PlanId[6];
          break;
        case 'month7':
          planId = this.monthId2PlanId[7];
          break;
        case 'month8':
          planId = this.monthId2PlanId[8];
          break;
        case 'month9':
          planId = this.monthId2PlanId[9];
          break;
        case 'month10':
          planId = this.monthId2PlanId[10];
          break;
        case 'month11':
          planId = this.monthId2PlanId[11];
          break;
        case 'month12':
          planId = this.monthId2PlanId[12];
          break;
      }
      return planId;
    }
  }
};
</script>
<style>
h2 {
  font-size: 18px;
  margin: 0;
  margin-bottom: 30px;
}
.quarter {
}
</style>
