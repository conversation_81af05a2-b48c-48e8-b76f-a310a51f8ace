package com.biz.epms.eppcs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.epms.eppcs.common.enums.EppcsCurrentPhaseEnums;
import com.biz.epms.eppcs.domain.EppcsSamplingApplication;
import com.biz.epms.eppcs.domain.bo.EppcsSamplingApplicationBo;
import com.biz.epms.eppcs.domain.vo.EppcsSamplingApplicationVo;
import com.biz.epms.eppcs.mapper.EppcsSamplingApplicationMapper;
import com.biz.epms.eppcs.mapper.EppcsSamplingInfoMapper;
import com.biz.epms.eppcs.service.IEppcsSamplingApplicationService;
import com.biz.epms.eppcs.state.EppcsStateMachineManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 取样检测申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class EppcsSamplingApplicationServiceImpl implements IEppcsSamplingApplicationService {

    private final EppcsSamplingApplicationMapper baseMapper;
    private final EppcsStateMachineManager stateMachineManager;
    private final EppcsSamplingInfoMapper samplingInfoMapper;

    /**
     * 查询取样检测申请
     *
     * @param applicationId 主键
     * @return 取样检测申请
     */
    @Override
    public EppcsSamplingApplicationVo queryById(Long applicationId){
        return baseMapper.selectVoById(applicationId);
    }

    /**
     * 分页查询取样检测申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 取样检测申请分页列表
     */
    @Override
    public TableDataInfo<EppcsSamplingApplicationVo> queryPageList(EppcsSamplingApplicationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EppcsSamplingApplication> lqw = buildQueryWrapper(bo);
        Page<EppcsSamplingApplicationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的取样检测申请列表
     *
     * @param bo 查询条件
     * @return 取样检测申请列表
     */
    @Override
    public List<EppcsSamplingApplicationVo> queryList(EppcsSamplingApplicationBo bo) {
        LambdaQueryWrapper<EppcsSamplingApplication> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<EppcsSamplingApplication> buildQueryWrapper(EppcsSamplingApplicationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<EppcsSamplingApplication> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(EppcsSamplingApplication::getApplyTime);
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), EppcsSamplingApplication::getProjectName, bo.getProjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getSamplingPoint()), EppcsSamplingApplication::getSamplingPoint, bo.getSamplingPoint());
        lqw.eq(bo.getDetectionStandard() != null, EppcsSamplingApplication::getDetectionStandard, bo.getDetectionStandard());
        lqw.eq(bo.getApplicationUnitId() != null, EppcsSamplingApplication::getApplicationUnitId, bo.getApplicationUnitId());
        lqw.eq(bo.getLocalUnitId() != null, EppcsSamplingApplication::getLocalUnitId, bo.getLocalUnitId());
        lqw.eq(bo.getApplyTime() != null, EppcsSamplingApplication::getApplyTime, bo.getApplyTime());
        lqw.eq(bo.getApplicationStatus() != null, EppcsSamplingApplication::getApplicationStatus, bo.getApplicationStatus());
        lqw.eq(bo.getCurrentPhase() != null, EppcsSamplingApplication::getCurrentPhase, bo.getCurrentPhase());
        lqw.eq(StringUtils.isNotBlank(bo.getFile()), EppcsSamplingApplication::getFile, bo.getFile());
        lqw.eq(bo.getApprovalTime() != null, EppcsSamplingApplication::getApprovalTime, bo.getApprovalTime());
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalRemark()), EppcsSamplingApplication::getApprovalRemark, bo.getApprovalRemark());

        // 处理applicationIds批量查询
        if (StringUtils.isNotBlank(bo.getApplicationIds())) {
            String[] ids = bo.getApplicationIds().split(",");
            List<Long> applicationIdList = new ArrayList<>();
            for (String id : ids) {
                try {
                    applicationIdList.add(Long.parseLong(id.trim()));
                } catch (NumberFormatException e) {
                    // 忽略无效的ID
                }
            }
            if (!applicationIdList.isEmpty()) {
                lqw.in(EppcsSamplingApplication::getApplicationId, applicationIdList);
            }
        }

        return lqw;
    }

    /**
     * 新增取样检测申请
     *
     * @param bo 取样检测申请
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(EppcsSamplingApplicationBo bo) {

        EppcsSamplingApplication add = MapstructUtils.convert(bo, EppcsSamplingApplication.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setApplicationId(add.getApplicationId());
        }
        return flag;
    }

    /**
     * 修改取样检测申请
     *
     * @param bo 取样检测申请
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(EppcsSamplingApplicationBo bo) {

        EppcsSamplingApplication update = MapstructUtils.convert(bo, EppcsSamplingApplication.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(EppcsSamplingApplication entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除取样检测申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 提交申请
     *
     * @param applicationId 申请ID
     * @param userType 用户类型
     * @return 是否成功
     */
    @Override
    public Boolean submitApplication(Long applicationId, Integer userType) {
        EppcsSamplingApplication application = baseMapper.selectById(applicationId);
        if (application == null) {
            throw new ServiceException("申请不存在");
        }

        try {
            // 使用状态机获取下一状态
            int nextStatus = stateMachineManager.getNextApplicationStatus(userType, application.getApplicationStatus(), EppcsStateMachineManager.ACTION_SUBMIT);
            // 更新状态和阶段
            application.setApplicationStatus(nextStatus);

            return baseMapper.updateById(application) > 0;
        } catch (Exception e) {
            log.error("提交申请失败: {}", e.getMessage());
            throw new ServiceException("提交申请失败: " + e.getMessage());
        }
    }

    /**
     * 审批申请
     *
     * @param applicationId 申请ID
     * @param userType 用户类型
     * @param action 动作 (1通过, 2驳回)
     * @param approvalRemark 审批意见
     * @param approver 审批人
     * @return 是否成功
     */
    @Override
    public Boolean approveApplication(Long applicationId, Integer userType, Integer action,
                                    String approvalRemark, String approver) {
        EppcsSamplingApplication application = baseMapper.selectById(applicationId);
        if (application == null) {
            throw new ServiceException("申请不存在");
        }

        try {
            // 使用状态机获取下一状态
            int nextStatus = stateMachineManager.getNextApplicationStatus(
                userType,
                application.getApplicationStatus(),
                action
            );

            // 更新状态、阶段和审批信息
            application.setApplicationStatus(nextStatus);
            application.setApprovalRemark(approvalRemark);
            application.setApprover(StringUtils.isNotBlank(application.getApprover()) ? application.getApprover() + "," + approver : approver);

            application.setApprovalTime(new Date());

            if (nextStatus == EppcsStateMachineManager.APPLICATION_APPROVED){
                application.setCurrentPhase(EppcsCurrentPhaseEnums.SAMPLING_APPLICATION_COMPLETED.getValue());
            }

            return baseMapper.updateById(application) > 0;
        } catch (Exception e) {
            log.error("审批申请失败: {}", e.getMessage());
            throw new ServiceException("审批申请失败: " + e.getMessage());
        }
    }


}
