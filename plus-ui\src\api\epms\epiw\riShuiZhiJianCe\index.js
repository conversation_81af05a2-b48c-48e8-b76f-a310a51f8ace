// 线路
import request from '@/utils/request';

//数据列表
export function getDataList(params) {
  return request({
    url: '/epms/waterQualityMontioringReport/getDataList',
    method: 'get',
    params: params
  });
}

//修改数据
export function updateData(params) {
  return request({
    url: '/epms/waterQualityMontioringReport/updateData',
    method: 'get',
    params: params
  });
}

//删除数据
export function deleteData(params) {
  return request({
    url: '/epms/waterQualityMontioringReport/deleteData',
    method: 'get',
    params: params
  });
}
