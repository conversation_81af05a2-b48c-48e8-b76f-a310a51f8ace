<template>
  <div class="p-3">
    <el-form :inline="true" :model="queryParams">
      <el-form-item label="年份">
        <el-date-picker
          v-model="queryParams.yearId"
          :picker-options="pickerOptions"
          format="YYYY年"
          placeholder="全部"
          type="year"
          value-format="YYYY"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button v-hasPermi="['epms:plan:listWithIndicator']" icon="Search" type="primary" @click="queryJiHua">查询</el-button>
        <el-button type="primary" plain icon="Plus" v-hasPermi="['epms:plan:add']" @click="handleAdd">新增</el-button>
        <!--        <el-button type="warning" plain icon="Download" v-hasPermi="['epms:plan:export']" @click="handleExport">导出</el-button>-->
      </el-form-item>
    </el-form>

    <el-table ref="table" :data="scjhList" border height="100%" stripe>
      <el-table-column type="index" label="序号" width="80px" align="center" fixed="left"></el-table-column>
      <el-table-column align="center" fixed="left" label="取水区域" prop="areaName"></el-table-column>
      <el-table-column align="center" label="年份" prop="yearId" fixed="left"></el-table-column>
      <el-table-column v-for="item in indicatorList" :key="item.id" :label="item.indicatorName + '(' + item.indicatorUnitName + ')'" align="center">
        <template v-slot:default="scope">
          <span>{{ showIndicator(scope.row, item) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" prop="remark"></el-table-column>

      <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
        <template #default="scope">
          <el-button v-hasPermi="['emcs:plan:query']" type="primary" @click="handleFj(scope.row)">年度计划分解</el-button>
          <el-button v-hasPermi="['epms:plan:edit']" icon="Edit" type="text" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['epms:plan:remove']" icon="Delete" type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!--    <pagination-->
    <!--      :total="total"-->
    <!--      :page.sync="queryParams.pageNum"-->
    <!--      :limit.sync="queryParams.pageSize"-->
    <!--      @pagination="getList" />-->

    <!-- 添加或修改用户配置对话框 -->
    <!-- 添加或修改用户配置对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="640px">
      <el-form ref="form" :model="form" label-width="140px" :rules="addFormRules">
        <el-form-item label="区域" prop="areaId">
          <el-select v-model="form.areaId" placeholder="选择区域" style="width: 100%" @change="getWaterPlan">
            <el-option
              v-for="deptment in getParkData"
              :key="deptment.operationAreaId"
              :label="deptment.operationAreaName"
              :value="deptment.operationAreaId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="年度" prop="yearId">
          <el-date-picker v-model="form.yearId" format="YYYY年" style="width: 100%" type="year" value-format="YYYY"> </el-date-picker>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea"></el-input>
        </el-form-item>

        <template v-for="item in indicatorList" :key="item.id">
          <el-form-item :label="item.indicatorName">
            <el-input v-model="form.indicatorDetail[item.indicatorId]" type="number">
              <template #append>{{ item.indicatorUnitName }}</template>
            </el-input>
          </el-form-item>
        </template>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { addPlan, delPlan, listPlanWithIndicator, queryTempIndicatorMap, selectQuYuList, updatePlan } from '@/api/epms/eptw/plan/plan.js';
import moment from 'dayjs';
import { listLicense } from '@/api/epms/eptw/license/index';

export default {
  name: 'areaPlan',

  data() {
    let that = this;
    return {
      PlanType: 1,
      PeriodType: 1,
      indicatorList: [],
      scjhList: [],
      areaType: 96,
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        yearId: moment().format('YYYY'),
        areaId: null,
        indicatorPlanType: 1, //取水计划
        periodType: 1, //年度计划
        areaType: 96 //部件类型-作业区级
      },
      // 弹出层标题
      title: '',
      isEdit: false,
      // 是否显示弹出层
      open: false,
      form: {
        // planSourceType: 1,
        indicatorPlanId: null,
        yearId: null,
        areaId: null,
        remark: null,
        indicatorPlanType: 1, //取水计划
        periodType: 1, //年度计划
        indicatorDetail: {},
        areaType: 96 //部件类型-取水区块
      },
      planList: [1],
      addFormRules: {
        areaId: [{ required: true, message: '请选择取水区域', trigger: 'blur' }],
        yearId: [{ required: true, message: '请选择年度', trigger: 'blur' }]
      }
    };
  },

  created() {
    // this.loadDicts()
    this.getParkList();
    this.getList();
    this.getindicatorList();
  },
  methods: {
    // loadDicts() {
    //   this.dictData = proxy?.useDict('area_plan_id');
    //   this.dictData.plan.forEach(item => {
    //     this.planList.push(Number(item.value))
    //   });
    // },

    /** 读取生产计划+年计划的指标库列表 */
    async getindicatorList() {
      queryTempIndicatorMap({ templatePlanType: this.PlanType, periodType: this.PeriodType }).then((res) => {
        const list = Object.values(res.data.data);
        list.forEach((item) => {
          if (this.planList.includes(item.indicatorId)) {
            this.indicatorList.push(item);
          }
        });
      });

      if (this.indicatorList.length < 1) {
        // this.$message(`未能找到对应的指标库模版，请先进行指标库模版的配置`, '失败', {
        //   type: 'error'
        // }).then(() => {
        //   this.$router.push('/jhdd/indicatortemplate')
        // })
        return;
      }
      this.indicatorList.forEach((item) => {
        this.form.indicatorDetail[item.indicatorId] = 0;
      });
      await this.$nextTick(() => {
        this.$refs['table'].doLayout();
      });
    },
    /** 查询计划列表 */
    queryJiHua() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    async getList() {
      listPlanWithIndicator(this.queryParams).then((res) => {
        this.total = res.total;
        this.scjhList = res.rows;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true;
      this.isEdit = true;
      // this.form.planSourceType = 1
      this.title = '取水计划';
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.isEdit = true;
      this.getParkList();
      // 深拷贝行数据
      this.form = JSON.parse(JSON.stringify(row));
      this.form.yearId = this.form.yearId + '';
      this.open = true;
      this.title = '修改计划';
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.indicatorPlanId != null) {
            updatePlan(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.reset();
              this.getList();
            });
          } else {
            addPlan(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.reset();
              this.getList();
            });
          }
        }
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        indicatorPlanId: null,
        yearId: null,
        areaId: null,
        remark: null,
        indicatorPlanType: this.PlanType,
        periodType: this.PeriodType,
        areaType: this.areaType,
        indicatorDetail: {}
      };

      // 直接赋值替代 this.$set
      // Object.keys(this.indicatorList).forEach(item => {
      //   this.form.indicatorDetail[item.indicatorId] = 0;
      // });

      this.form.resetFields;
    },
    handleFj(row) {
      this.$router.push({ path: '/intake/plan/monthPlan', query: { planId: row.indicatorPlanId, planType: 1 } });
    },
    showIndicator(row, indicator) {
      //显示
      return row.indicatorDetail[indicator.indicatorId];
    },
    handleDelete(row) {
      ElMessageBox.confirm('确认删除吗？', '删除确认', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then((action) => {
        if (action === 'confirm') {
          let delParam = {
            indicatorPlanType: 1,
            indicatorPlanId: row.indicatorPlanId,
            periodType: 1,
            yearId: row.yearId,
            areaType: 96
          };
          delPlan(delParam).then((res) => {
            ElMessage({
              message: '删除成功',
              type: 'success'
            });
            this.getList();
          });
        }
      });
    },
    handleExport() {
      this.download(
        'epms/plan/export',
        {
          ...this.queryParams
        },
        `plan_${new Date().getTime()}.xlsx`
      );
    },
    /** 计划调度页面添加计划单位下拉框管理列表 */
    async getParkList() {
      await selectQuYuList({ operationAreaType: 1 }).then((res) => {
        if (res.code === 200) {
          this.getParkData = res.rows;
        } else {
          this.$modal.msgError('获取数据失败');
        }
      });
    },
    getWaterPlan() {
      let query = {};
      query.operationAreaId = this.form.areaId;
      listLicense(query).then((res) => {
        if (res.rows && res.rows.length > 0) {
          const latestItem = res.rows.reduce((latest, current) => {
            return new Date(current.startTime) > new Date(latest.startTime) ? current : latest;
          });
          console.log(latestItem);
          this.form.indicatorDetail = { 1: latestItem.waterDrawingAmount / 10000 };
          // this.form.waterDrawingLicenseId = latestItem.waterDrawingLicenseId;
        } else {
          console.log('没有找到数据');
        }
      });
    }
  }
};
</script>
<style scoped>
input[type='number'] {
  -moz-appearance: textfield;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
