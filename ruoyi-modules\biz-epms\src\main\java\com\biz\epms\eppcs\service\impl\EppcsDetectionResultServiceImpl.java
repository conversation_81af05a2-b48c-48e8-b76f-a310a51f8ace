package com.biz.epms.eppcs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.epms.eppcs.domain.EppcsDetectionResult;
import com.biz.epms.eppcs.domain.EppcsSamplingApplication;
import com.biz.epms.eppcs.domain.bo.EppcsDetectionResultBo;
import com.biz.epms.eppcs.domain.vo.EppcsDetectionResultVo;
import com.biz.epms.eppcs.mapper.EppcsDetectionResultMapper;
import com.biz.epms.eppcs.mapper.EppcsSamplingApplicationMapper;
import com.biz.epms.eppcs.service.IEppcsDetectionResultService;
import com.biz.epms.eppcs.state.EppcsStateMachineManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 检测结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class EppcsDetectionResultServiceImpl implements IEppcsDetectionResultService {

    private final EppcsDetectionResultMapper baseMapper;
    private final EppcsStateMachineManager stateMachineManager;
    private final EppcsSamplingApplicationMapper samplingApplicationMapper;

    /**
     * 查询检测结果
     *
     * @param resultId 主键
     * @return 检测结果
     */
    @Override
    public EppcsDetectionResultVo queryById(Long resultId){
        return baseMapper.selectVoById(resultId);
    }

    /**
     * 分页查询检测结果列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 检测结果分页列表
     */
    @Override
    public TableDataInfo<EppcsDetectionResultVo> queryPageList(EppcsDetectionResultBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EppcsDetectionResult> lqw = buildQueryWrapper(bo);
        Page<EppcsDetectionResultVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的检测结果列表
     *
     * @param bo 查询条件
     * @return 检测结果列表
     */
    @Override
    public List<EppcsDetectionResultVo> queryList(EppcsDetectionResultBo bo) {
        LambdaQueryWrapper<EppcsDetectionResult> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<EppcsDetectionResult> buildQueryWrapper(EppcsDetectionResultBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<EppcsDetectionResult> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(EppcsDetectionResult::getResultId);
        lqw.eq(bo.getApplicationId() != null, EppcsDetectionResult::getApplicationId, bo.getApplicationId());
        lqw.eq(StringUtils.isNotBlank(bo.getResultData()), EppcsDetectionResult::getResultData, bo.getResultData());
        lqw.eq(bo.getResultStatus() != null, EppcsDetectionResult::getResultStatus, bo.getResultStatus());
        lqw.eq(bo.getApprovalTime() != null, EppcsDetectionResult::getApprovalTime, bo.getApprovalTime());
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalRemark()), EppcsDetectionResult::getApprovalRemark, bo.getApprovalRemark());
        return lqw;
    }

    /**
     * 新增检测结果
     *
     * @param bo 检测结果
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(EppcsDetectionResultBo bo) {
        EppcsDetectionResult add = MapstructUtils.convert(bo, EppcsDetectionResult.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setResultId(add.getResultId());

            // 更新取样申请的当前阶段为3（上传检测结果）
            if (add.getApplicationId() != null) {
                EppcsSamplingApplication application = samplingApplicationMapper.selectById(add.getApplicationId());
                if (application != null && application.getCurrentPhase() != 3) {
                    application.setCurrentPhase(3);
                    samplingApplicationMapper.updateById(application);
                }
            }
        }
        return flag;
    }

    /**
     * 修改检测结果
     *
     * @param bo 检测结果
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(EppcsDetectionResultBo bo) {
        EppcsDetectionResult update = MapstructUtils.convert(bo, EppcsDetectionResult.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(EppcsDetectionResult entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除检测结果信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 提交检测结果
     *
     * @param resultId 检测结果ID
     * @param userType 用户类型
     * @return 是否成功
     */
    @Override
    public Boolean submitDetectionResult(Long resultId, Integer userType) {
        EppcsDetectionResult detectionResult = baseMapper.selectById(resultId);
        if (detectionResult == null) {
            throw new ServiceException("检测结果不存在");
        }

        try {
            // 使用状态机获取下一状态
            int nextStatus = stateMachineManager.getNextDetectionStatus(userType, detectionResult.getDetectionStatus(), EppcsStateMachineManager.ACTION_SUBMIT);
            // 更新状态
            detectionResult.setDetectionStatus(nextStatus);
            detectionResult.setSubmitTime(new java.util.Date());

            return baseMapper.updateById(detectionResult) > 0;
        } catch (Exception e) {
            log.error("提交检测结果失败: {}", e.getMessage());
            throw new ServiceException("提交检测结果失败: " + e.getMessage());
        }
    }

    /**
     * 属地确认检测结果
     *
     * @param resultId 检测结果ID
     * @param userType 用户类型
     * @param action 动作 (1达标确认, 2不达标报告)
     * @param approvalRemark 审批意见
     * @param approver 审批人
     * @return 是否成功
     */
    @Override
    public Boolean localConfirmDetectionResult(Long resultId, Integer userType, Integer action,
                                              String approvalRemark, String approver) {
        EppcsDetectionResult detectionResult = baseMapper.selectById(resultId);
        if (detectionResult == null) {
            throw new ServiceException("检测结果不存在");
        }

        try {
            // 使用状态机获取下一状态
            int nextStatus = stateMachineManager.getNextDetectionStatus(userType, detectionResult.getDetectionStatus(), action);

            // 更新状态和审批信息
            detectionResult.setDetectionStatus(nextStatus);
            detectionResult.setApprovalRemark(approvalRemark);
            detectionResult.setApprover(approver);
            detectionResult.setApprovalTime(new java.util.Date());

            // 根据检测结果设置结果状态
            if (action == 1) {
                detectionResult.setResultStatus(0); // 达标
            } else if (action == 2) {
                detectionResult.setResultStatus(1); // 不达标
            }

            boolean result = baseMapper.updateById(detectionResult) > 0;

            // 如果是达标确认，检查是否需要更新申请阶段为"检测完成"
            if (result && action == 1 && nextStatus == 3) { // 待管理中心确认状态
                updateApplicationPhaseIfCompleted(detectionResult.getApplicationId());
            }

            return result;
        } catch (Exception e) {
            log.error("属地确认检测结果失败: {}", e.getMessage());
            throw new ServiceException("属地确认检测结果失败: " + e.getMessage());
        }
    }

    /**
     * 管理中心确认检测结果
     *
     * @param resultId 检测结果ID
     * @param userType 用户类型
     * @param action 动作 (1结束, 2驳回)
     * @param approvalRemark 审批意见
     * @param approver 审批人
     * @return 是否成功
     */
    @Override
    public Boolean centerConfirmDetectionResult(Long resultId, Integer userType, Integer action,
                                               String approvalRemark, String approver) {
        EppcsDetectionResult detectionResult = baseMapper.selectById(resultId);
        if (detectionResult == null) {
            throw new ServiceException("检测结果不存在");
        }

        try {
            // 使用状态机获取下一状态
            int nextStatus = stateMachineManager.getNextDetectionStatus(userType, detectionResult.getDetectionStatus(), action);

            // 更新状态和审批信息
            detectionResult.setDetectionStatus(nextStatus);
            detectionResult.setApprovalRemark(approvalRemark);
            detectionResult.setApprover(approver);
            detectionResult.setApprovalTime(new java.util.Date());

            boolean result = baseMapper.updateById(detectionResult) > 0;

            // 如果是结束确认，更新申请阶段为"检测完成"
            if (result && action == 1 && nextStatus == 4) { // 检测完成状态
                updateApplicationPhaseToCompleted(detectionResult.getApplicationId());
            }

            return result;
        } catch (Exception e) {
            log.error("管理中心确认检测结果失败: {}", e.getMessage());
            throw new ServiceException("管理中心确认检测结果失败: " + e.getMessage());
        }
    }

    /**
     * 检查申请下的所有检测结果是否都已确认，如果是则更新申请阶段
     */
    private void updateApplicationPhaseIfCompleted(Long applicationId) {
        if (applicationId == null) return;

        // 查询该申请下的所有检测结果
        LambdaQueryWrapper<EppcsDetectionResult> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EppcsDetectionResult::getApplicationId, applicationId);
        List<EppcsDetectionResult> detectionResultList = baseMapper.selectList(wrapper);

        // 检查是否所有检测结果都已完成
        boolean allCompleted = detectionResultList.stream()
            .allMatch(result -> result.getDetectionStatus() >= 3); // 3及以上表示已确认或完成

        if (allCompleted && !detectionResultList.isEmpty()) {
            // 更新申请阶段为4（检测完成）
            EppcsSamplingApplication application = samplingApplicationMapper.selectById(applicationId);
            if (application != null && application.getCurrentPhase() != 4) {
                application.setCurrentPhase(4);
                samplingApplicationMapper.updateById(application);
            }
        }
    }

    /**
     * 更新申请阶段为检测完成
     */
    private void updateApplicationPhaseToCompleted(Long applicationId) {
        if (applicationId == null) return;

        EppcsSamplingApplication application = samplingApplicationMapper.selectById(applicationId);
        if (application != null && application.getCurrentPhase() != 4) {
            application.setCurrentPhase(4);
            samplingApplicationMapper.updateById(application);
        }
    }
}
