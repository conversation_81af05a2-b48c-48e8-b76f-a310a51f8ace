import{_ as a}from"./HmiPreview-CGaT42aW.js";import{Q as r,c as s,o as i,p as l}from"./index-D07cMzhp.js";const m={name:"HmiRun",data(){return{fileName:""}},created(){this.fileName=this.$route.query.filename}},c={class:"app-container"};function p(_,e,u,f,o,d){const n=a;return i(),s("div",c,[l(n,{modelValue:o.fileName,"onUpdate:modelValue":e[0]||(e[0]=t=>o.fileName=t)},null,8,["modelValue"])])}const v=r(m,[["render",p]]);export{v as default};
