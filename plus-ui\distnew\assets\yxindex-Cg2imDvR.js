import{d as Ye,h as na,ak as Ue,r as f,ai as x,X as Le,b as ia,aH as pa,c,o as p,p as t,t as o,w as _,q as N,a7 as ua,M as da,e as l,A as sa,G as ma,H as K,B as va,F as y,C as A,x as d,D as fa,K as ga,J as m,am as Ne,aI as ba,ay as ca,ax as wa,z as D,aJ as ya,y as h,aL as Aa,v as Va,az as _a,a8 as ka,aA as Ia}from"./index-D07cMzhp.js";/* empty css                       */import{E as Sa,a as Ta}from"./el-radio-BSZOo4bv.js";import{_ as Ca}from"./index-DVHplxfU.js";import{E as Da}from"./el-row-CikYE3zA.js";import{_ as Ua}from"./index-BWMgqvQ9.js";import{E as La}from"./el-col-BaG5Rg5z.js";import{E as Na}from"./el-date-picker-HyhB9X9n.js";import{l as Ra,g as re,u as Re,a as qa,b as Ya,d as Ea}from"./index-M7LxucW_.js";import{l as qe}from"./index-BhIIZXqy.js";import{l as xa}from"./index-DmNU79vT.js";import{m as $a}from"./dayjs.min-Brw96_N0.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./el-tree-DW6MoFaI.js";import"./index-VIEDZI2D.js";const ja={class:"p-2"},Fa={class:"mb-[10px]"},Ma={class:"dialog-footer"},Pa={class:"dialog-footer"},Ha={class:"dialog-footer"},za=Ye({name:"TransportApplication"}),pl=Ye({...za,setup(Ka){const{proxy:s}=na(),{epfy_medium_category:ne,epfy_application_status:$,transport_application_status:Ee,transport_app_regulatory_authorities_approver:ie,transport_app_operationarea_examine:pe}=Ue(s==null?void 0:s.useDict("epfy_medium_category","epfy_application_status","transport_application_status","transport_app_regulatory_authorities_approver","transport_app_operationarea_examine")),ue=f([]),R=f({}),j=f([]),O=f([]),ee=f([]),q=f([]),ae=f([]),k=f(!1),Q=f(!0),B=f(!0),F=f([]),de=f(!0),se=f(!0),le=f(0),M=f(1),G=f(!1),me=f(),Y=f(),I=x({visible:!1,title:""}),T=x({visible:!1,title:""}),ve={appId:void 0,relatedId:void 0,appName:void 0,workAreaId:void 0,wellName:void 0,applicationDate:$a().format("YYYY-MM-DD HH:mm:ss"),loadingLocation:void 0,unloadLocationType:1,unloadLocation:void 0,file:void 0,applicationStatus:1,rejectSuggestion:void 0,flowType:2,mediumCategory:2,remark:void 0,superviseApprover:void 0,operationAreaReviewer:void 0,examineApproveTime:void 0,operationAreaReviewedTime:void 0,operationAreaApproveStatus:1,mediumType:8},xe=x({form:{...ve},queryParams:{pageNum:1,pageSize:10,relatedId:void 0,appName:void 0,workAreaId:void 0,wellName:void 0,loadingLocation:void 0,unloadLocationType:1,unloadLocation:void 0,file:void 0,applicationStatus:void 0,rejectSuggestion:void 0,flowType:2,mediumCategory:2,mediumType:void 0,params:{}},rules:{appId:[{required:!0,message:"申请ID，自动递增不能为空",trigger:"blur"}],appName:[{required:!0,message:"申请名称不能为空",trigger:"blur"}],mediumCategory:[{required:!0,message:"申请分类不能为空",trigger:"change"}],workAreaId:[{required:!0,message:"所属作业区不能为空",trigger:"blur"}],relatedId:[{required:!0,message:"单井名称不能为空",trigger:"change"}],applicationDate:[{required:!0,message:"申请日期不能为空",trigger:"blur"}],loadingLocation:[{required:!0,message:"装车地点不能为空",trigger:"blur"}],unloadLocationType:[{required:!0,message:"卸车地点类型不能为空",trigger:"change"}],unloadLocation:[{required:!0,message:"卸车地点不能为空",trigger:"blur"}]}}),{queryParams:v,form:r,rules:$e}=Ue(xe),je=x({appId:[{required:!0,message:"申请ID，自动递增不能为空",trigger:"blur"}],appName:[{required:!0,message:"申请名称不能为空",trigger:"blur"}],workAreaId:[{required:!0,message:"所属作业区不能为空",trigger:"blur"}],relatedId:[{required:!0,message:"单井名称不能为空",trigger:"change"}],wellName:[{required:!0,message:"单井名称不能为空",trigger:"blur"}],applicationDate:[{required:!0,message:"申请日期不能为空",trigger:"blur"}],loadingLocation:[{required:!0,message:"装车地点不能为空",trigger:"blur"}],unloadLocationType:[{required:!0,message:"卸车地点类型不能为空",trigger:"change"}],unloadLocation:[{required:!0,message:"卸车地点不能为空",trigger:"blur"}],applicationStatus:[{required:!0,message:"状态不能为空",trigger:"change"}],operationAreaApproveStatus:[{required:!0,message:"状态不能为空",trigger:"change"}],rejectSuggestion:[{required:!0,message:"审核意见不能为空",trigger:"blur"}]}),Fe=x({pageSize:-1,handlingType:2,workArea:void 0}),Me=n=>{if(n.transportRecordCount!=0)return!1;if(n.examineApproveTime!=null&&n.operationAreaReviewedTime!=null){const a=new Date(Date.now()-864e5),u=new Date(n.examineApproveTime),i=new Date(n.operationAreaReviewedTime);if(u<a&&i<a)return!0}return!1},fe=f({mediumType:"nj"}),J=f({operationAreaId:void 0}),ge=(n,a)=>{var u;return(u=n==null?void 0:n.find(i=>i.value==a))==null?void 0:u.label},be=Le(()=>{var a;return J.value.operationAreaId=r.value.workAreaId,(a=pe.value)==null?void 0:a.filter(u=>J.value.operationAreaId!==void 0&&J.value.operationAreaId!==null?u.value.includes(J.value.operationAreaId):!0)}),ce=Le(()=>{var a;return(a=ie.value)==null?void 0:a.filter(u=>fe.value.mediumType?u.value.includes(fe.value.mediumType):!0)}),C=async()=>{Q.value=!0;const n=await Ra(v.value);ue.value=n.rows,le.value=n.total,Q.value=!1},we=()=>{P(),I.visible=!1,T.visible=!1},P=()=>{var n;r.value={...ve},(n=Y.value)==null||n.resetFields(),W(null)},U=()=>{v.value.pageNum=1,C()},Pe=()=>{var n;(n=me.value)==null||n.resetFields(),U()},He=n=>{F.value=n.map(a=>a.appId),de.value=n.length!=1,se.value=!n.length},ze=()=>{P(),G.value=!1,I.visible=!0,I.title="添加拉运申请"},ye=async n=>{P();const a=(n==null?void 0:n.appId)||F.value[0],u=await re(a);Object.assign(r.value,u.data),await W(u.data.workAreaId),G.value=!0,I.visible=!0,I.title="修改拉运申请"},Ae=n=>{var a;(a=Y.value)==null||a.validate(async u=>{u&&(k.value=!0,r.value.appId?(r.value.reApprove=n,await Re(r.value).finally(()=>k.value=!1)):await qa(r.value).finally(()=>k.value=!1),s==null||s.$modal.msgSuccess("操作成功"),I.visible=!1,T.visible=!1,await C())})},Ke=()=>{var n;(n=Y.value)==null||n.validate(async a=>{if(a){if(k.value=!0,r.value.appId){const u=r.value.appId,i=r.value.rejectSuggestion,w=M.value==1?r.value.applicationStatus:r.value.operationAreaApproveStatus;await Ya(u,w,i,r.value.warnTime,M.value).finally(()=>k.value=!1)}s==null||s.$modal.msgSuccess("操作成功"),I.visible=!1,T.visible=!1,await C()}})},Ve=async(n,a)=>{P();const u=(n==null?void 0:n.appId)||F.value[0],i=await re(u);Object.assign(r.value,i.data),await W(i.data.workAreaId),M.value=a,G.value=!0,T.visible=!0,T.title="审核拉运申请"},_e=async n=>{const a=(n==null?void 0:n.appId)||F.value;await(s==null?void 0:s.$modal.confirm('是否确认删除拉运申请编号为"'+a+'"的数据项？').finally(()=>Q.value=!1)),await Ea(a),s==null||s.$modal.msgSuccess("删除成功"),await C()},Oe=async n=>{s==null||s.download("epfy/transportApplication/downloadMeasurementVoucher",{appId:n==null?void 0:n.appId},"二连分公司废液收集计量凭证.xls")},Qe=()=>{s==null||s.download("epfy/transportApplication/export",{...v.value},`transportApplication_${new Date().getTime()}.xlsx`)},Be=n=>{r.value.uploadTime=n.uploadTime},ke=n=>{const a=q.value.find(u=>u.prepId===r.value.relatedId);a&&(r.value.wellName=a.wellName),r.value.loadingLocation=r.value.wellName},Ge=async()=>{R.value.operationAreaParentId=0,R.value.operationAreaType=0;const n=await qe(R.value);j.value=n.rows,R.value.operationAreaParentId=null,R.value.operationAreaType=5;const a=await qe(R.value);ee.value=a.rows,O.value=a.rows;const u=await xa(Fe);ae.value=u.rows,q.value=u.rows},Je=n=>{if(!n)return"未知";const a=j.value.find(u=>u.operationAreaId===n);return a?a.operationAreaName:"未知"},W=async n=>{n!=null?(O.value=ee.value.filter(a=>a.operationAreaParentId===n),q.value=ae.value.filter(a=>a.workArea===n)):(O.value=ee.value,q.value=ae.value)},We=n=>(v.value.pageNum-1)*v.value.pageSize+n+1,Ze=({row:n,rowIndex:a})=>Me(n)?"warn-row":"",L=x({visible:!1,title:""}),Xe=async n=>{P();const a=(n==null?void 0:n.appId)||F.value[0],u=await re(a);Object.assign(r.value,u.data),L.visible=!0,L.title="修改预警时间"},he=async()=>{k.value=!0,r.value.appId&&await Re(r.value).finally(()=>k.value=!1),s==null||s.$modal.msgSuccess("操作成功"),I.visible=!1,L.visible=!1,await C()};return ia(()=>{Ge(),C()}),(n,a)=>{var Ce,De;const u=ma,i=sa,w=fa,V=va,H=Na,g=ga,Z=da,Ie=ua,X=La,ea=Ua,aa=Da,b=wa,te=ya,E=Aa,la=ca,ta=_a,oa=Ca,oe=Ia,Se=Ta,Te=Sa,S=pa("hasPermi"),ra=Va;return p(),c("div",ja,[t(ba,{"enter-active-class":(Ce=l(s))==null?void 0:Ce.animate.searchAnimate.enter,"leave-active-class":(De=l(s))==null?void 0:De.animate.searchAnimate.leave},{default:o(()=>[_(N("div",Fa,[t(Ie,{shadow:"hover"},{default:o(()=>[t(Z,{ref_key:"queryFormRef",ref:me,model:l(v),inline:!0,"label-width":"96px"},{default:o(()=>[t(i,{label:"申请名称",prop:"appName"},{default:o(()=>[t(u,{modelValue:l(v).appName,"onUpdate:modelValue":a[0]||(a[0]=e=>l(v).appName=e),placeholder:"请输入申请名称",clearable:"",onKeyup:K(U,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"所属作业区",prop:"workAreaId"},{default:o(()=>[t(V,{modelValue:l(v).workAreaId,"onUpdate:modelValue":a[1]||(a[1]=e=>l(v).workAreaId=e),class:"searchDate",clearable:"",filterable:"",placeholder:"选择所属作业区"},{default:o(()=>[(p(!0),c(y,null,A(l(j),e=>(p(),d(w,{key:e.operationAreaId,label:e.operationAreaName,value:e.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"单井名称",prop:"wellName"},{default:o(()=>[t(u,{modelValue:l(v).wellName,"onUpdate:modelValue":a[2]||(a[2]=e=>l(v).wellName=e),placeholder:"请输入单井名称",clearable:"",onKeyup:K(U,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"申请日期",prop:"params"},{default:o(()=>[t(H,{clearable:"",modelValue:l(v).params,"onUpdate:modelValue":a[3]||(a[3]=e=>l(v).params=e),type:"datetimerange","start-placeholder":"开始时间","range-separator":"至","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss",onKeyup:K(U,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"装车地点",prop:"loadingLocation"},{default:o(()=>[t(u,{modelValue:l(v).loadingLocation,"onUpdate:modelValue":a[4]||(a[4]=e=>l(v).loadingLocation=e),placeholder:"请输入装车地点",clearable:"",onKeyup:K(U,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"卸车地点",prop:"unloadLocation"},{default:o(()=>[t(u,{modelValue:l(v).unloadLocation,"onUpdate:modelValue":a[5]||(a[5]=e=>l(v).unloadLocation=e),placeholder:"请输入卸车地点",clearable:"",onKeyup:K(U,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"状态",prop:"applicationStatus"},{default:o(()=>[t(V,{modelValue:l(v).applicationStatus,"onUpdate:modelValue":a[6]||(a[6]=e=>l(v).applicationStatus=e),clearable:"",placeholder:"请选择状态"},{default:o(()=>[(p(!0),c(y,null,A(l($),e=>(p(),d(w,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,null,{default:o(()=>[t(g,{type:"primary",icon:"Search",onClick:U},{default:o(()=>a[47]||(a[47]=[m("搜索")])),_:1}),t(g,{icon:"Refresh",onClick:Pe},{default:o(()=>a[48]||(a[48]=[m("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[Ne,l(B)]])]),_:1},8,["enter-active-class","leave-active-class"]),t(Ie,{shadow:"never"},{header:o(()=>[t(aa,{gutter:10,class:"mb8"},{default:o(()=>[t(X,{span:1.5},{default:o(()=>[_((p(),d(g,{type:"primary",plain:"",icon:"Plus",onClick:ze},{default:o(()=>a[49]||(a[49]=[m("新增")])),_:1})),[[S,["epfy:transportApplication:add"]]])]),_:1}),t(X,{span:1.5},{default:o(()=>[_((p(),d(g,{disabled:l(de),icon:"Edit",plain:"",type:"success",onClick:a[7]||(a[7]=e=>ye())},{default:o(()=>a[50]||(a[50]=[m("修改")])),_:1},8,["disabled"])),[[S,["epfy:transportApplication:edit"]]])]),_:1}),t(X,{span:1.5},{default:o(()=>[_((p(),d(g,{disabled:l(se),icon:"Delete",plain:"",type:"danger",onClick:a[8]||(a[8]=e=>_e())},{default:o(()=>a[51]||(a[51]=[m("删除")])),_:1},8,["disabled"])),[[S,["epfy:transportApplication:remove"]]])]),_:1}),t(X,{span:1.5},{default:o(()=>[_((p(),d(g,{type:"warning",plain:"",icon:"Download",onClick:Qe},{default:o(()=>a[52]||(a[52]=[m("导出")])),_:1})),[[S,["epfy:transportApplication:export"]]])]),_:1}),t(ea,{showSearch:l(B),"onUpdate:showSearch":a[9]||(a[9]=e=>ka(B)?B.value=e:null),onQueryTable:C},null,8,["showSearch"])]),_:1})]),default:o(()=>[_((p(),d(la,{data:l(ue),"row-class-name":Ze,stripe:"",onSelectionChange:He},{default:o(()=>[t(b,{type:"selection",width:"55",align:"center"}),t(b,{index:We,label:"序号",type:"index",width:"50"}),t(b,{align:"center",label:"申请名称",prop:"appName",width:"200"}),t(b,{align:"center",label:"所属作业区",prop:"workAreaId",width:"130"},{default:o(e=>[m(D(Je(e.row.workAreaId)),1)]),_:1}),t(b,{align:"center",label:"单井名称",prop:"wellName",width:"110"}),t(b,{align:"center",label:"申请日期",prop:"applicationDate",width:"110"},{default:o(e=>[N("span",null,D(n.parseTime(e.row.applicationDate,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(b,{align:"center",label:"装车地点",prop:"loadingLocation",width:"110"}),t(b,{align:"center",label:"卸车地点",prop:"unloadLocation",width:"135"}),t(b,{align:"center",label:"归口部门审核人",prop:"superviseApprover",width:"80"},{default:o(e=>[m(D(ge(l(ie),e.row.superviseApprover)),1)]),_:1}),t(b,{label:"归口部门审核",align:"center",prop:"applicationStatus"},{default:o(e=>[t(te,{options:l($),value:e.row.applicationStatus},null,8,["options","value"])]),_:1}),t(b,{label:"归口部门审核时间",align:"center",prop:"examineApproveTime",width:"110"},{default:o(e=>[N("span",null,D(n.parseTime(e.row.examineApproveTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(b,{label:"作业区审核人",align:"center",prop:"operationAreaReviewer"},{default:o(e=>[m(D(ge(l(pe),e.row.operationAreaReviewer)),1)]),_:1}),t(b,{label:"作业区审核",align:"center",prop:"operationAreaApproveStatus"},{default:o(e=>[t(te,{options:l($),value:e.row.operationAreaApproveStatus},null,8,["options","value"])]),_:1}),t(b,{label:"作业区审核时间",align:"center",prop:"operationAreaReviewedTime",width:"110"},{default:o(e=>[N("span",null,D(n.parseTime(e.row.operationAreaReviewedTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(b,{label:"审核意见",align:"center",prop:"rejectSuggestion"}),t(b,{align:"center",label:"拉运状态",prop:"transportStatus",width:"120"},{default:o(e=>[t(te,{options:l(Ee),value:e.row.transportStatus},null,8,["options","value"])]),_:1}),t(b,{label:"备注信息",align:"center",prop:"remark"}),t(b,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作",width:"160"},{default:o(e=>[[1].includes(e.row.applicationStatus)&&[2].includes(e.row.operationAreaApproveStatus)?(p(),d(E,{key:0,content:"归口部门审核",placement:"top"},{default:o(()=>[_((p(),d(g,{icon:"Coordinate",link:"",type:"primary",onClick:z=>Ve(e.row,1)},{default:o(()=>a[53]||(a[53]=[m("归口部门审核")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:guikou"]]])]),_:2},1024)):h("",!0),[1].includes(e.row.operationAreaApproveStatus)?(p(),d(E,{key:1,content:"作业区审核",placement:"top"},{default:o(()=>[_((p(),d(g,{icon:"Coordinate",link:"",type:"primary",onClick:z=>Ve(e.row,2)},{default:o(()=>a[54]||(a[54]=[m("作业区审核")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:zuoyequ"]]])]),_:2},1024)):h("",!0),t(E,{content:"修改预警时间",placement:"top"},{default:o(()=>[_((p(),d(g,{icon:"Edit",link:"",type:"primary",onClick:z=>Xe(e.row)},{default:o(()=>a[55]||(a[55]=[m("修改预警时间")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:coordinate"]]])]),_:2},1024),e.row.operationAreaApproveStatus===2&&e.row.applicationStatus===2?(p(),d(E,{key:2,content:"计量凭证下载",placement:"top"},{default:o(()=>[t(g,{icon:"Download",link:"",type:"primary",onClick:z=>Oe(e.row)},{default:o(()=>a[56]||(a[56]=[m("计量凭证下载")])),_:2},1032,["onClick"])]),_:2},1024)):h("",!0),t(E,{content:"修改",placement:"top"},{default:o(()=>[_((p(),d(g,{icon:"Edit",link:"",type:"primary",onClick:z=>ye(e.row)},{default:o(()=>a[57]||(a[57]=[m("修改")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:edit"]]])]),_:2},1024),t(E,{content:"删除",placement:"top"},{default:o(()=>[_((p(),d(g,{icon:"Delete",link:"",type:"primary",onClick:z=>_e(e.row)},{default:o(()=>a[58]||(a[58]=[m("删除")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[ra,l(Q)]]),_(t(ta,{total:l(le),page:l(v).pageNum,"onUpdate:page":a[10]||(a[10]=e=>l(v).pageNum=e),limit:l(v).pageSize,"onUpdate:limit":a[11]||(a[11]=e=>l(v).pageSize=e),onPagination:C},null,8,["total","page","limit"]),[[Ne,l(le)>0]])]),_:1}),t(oe,{title:l(I).title,modelValue:l(I).visible,"onUpdate:modelValue":a[26]||(a[26]=e=>l(I).visible=e),width:"600px","append-to-body":""},{footer:o(()=>[N("div",Ma,[t(g,{loading:l(k),type:"primary",onClick:a[24]||(a[24]=e=>Ae(!1))},{default:o(()=>a[59]||(a[59]=[m("确 定")])),_:1},8,["loading"]),t(g,{loading:l(k),type:"primary",onClick:a[25]||(a[25]=e=>Ae(!0))},{default:o(()=>a[60]||(a[60]=[m("重新提交审核")])),_:1},8,["loading"]),t(g,{onClick:we},{default:o(()=>a[61]||(a[61]=[m("取 消")])),_:1})])]),default:o(()=>[t(Z,{ref_key:"transportApplicationFormRef",ref:Y,model:l(r),rules:l($e),"label-width":"120px"},{default:o(()=>[t(i,{label:"申请名称",prop:"appName"},{default:o(()=>[t(u,{modelValue:l(r).appName,"onUpdate:modelValue":a[12]||(a[12]=e=>l(r).appName=e),placeholder:"请输入申请名称"},null,8,["modelValue"])]),_:1}),t(i,{label:"申请分类",prop:"mediumCategory"},{default:o(()=>[t(V,{modelValue:l(r).mediumCategory,"onUpdate:modelValue":a[13]||(a[13]=e=>l(r).mediumCategory=e),placeholder:"请选择申请分类",disabled:""},{default:o(()=>[(p(!0),c(y,null,A(l(ne),e=>(p(),d(w,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"所属作业区",prop:"workAreaId"},{default:o(()=>[t(V,{filterable:"",modelValue:l(r).workAreaId,"onUpdate:modelValue":a[14]||(a[14]=e=>l(r).workAreaId=e),placeholder:"选择所属作业区",clearable:"",onChange:W},{default:o(()=>[(p(!0),c(y,null,A(l(j),e=>(p(),d(w,{key:e.operationAreaId,label:e.operationAreaName,value:e.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"单井名称",prop:"relatedId"},{default:o(()=>[t(V,{modelValue:l(r).relatedId,"onUpdate:modelValue":a[15]||(a[15]=e=>l(r).relatedId=e),placeholder:"请选择单井",onChange:ke},{default:o(()=>[(p(!0),c(y,null,A(l(q),e=>(p(),d(w,{key:e.prepId,label:e.wellName,value:e.prepId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"申请日期",prop:"applicationDate"},{default:o(()=>[t(H,{modelValue:l(r).applicationDate,"onUpdate:modelValue":a[16]||(a[16]=e=>l(r).applicationDate=e),clearable:"",placeholder:"请选择申请日期",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(i,{label:"装车地点",prop:"loadingLocation"},{default:o(()=>[t(u,{modelValue:l(r).loadingLocation,"onUpdate:modelValue":a[17]||(a[17]=e=>l(r).loadingLocation=e),disabled:"",placeholder:"请输入装车地点"},null,8,["modelValue"])]),_:1}),t(i,{label:"卸车地点",prop:"unloadLocation"},{default:o(()=>[t(V,{filterable:"",modelValue:l(r).unloadLocation,"onUpdate:modelValue":a[18]||(a[18]=e=>l(r).unloadLocation=e),placeholder:"选择卸车地点",clearable:"","allow-create":""},{default:o(()=>[(p(!0),c(y,null,A(l(O),e=>(p(),d(w,{key:e.operationAreaName,label:e.operationAreaName,value:e.operationAreaName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"归口部门审批人",prop:"superviseApprover"},{default:o(()=>[t(V,{modelValue:l(r).superviseApprover,"onUpdate:modelValue":a[19]||(a[19]=e=>l(r).superviseApprover=e),placeholder:"请选择归口部门批准人"},{default:o(()=>[(p(!0),c(y,null,A(l(ce),e=>(p(),d(w,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"作业区审核人",prop:"operationAreaReviewer"},{default:o(()=>[t(V,{modelValue:l(r).operationAreaReviewer,"onUpdate:modelValue":a[20]||(a[20]=e=>l(r).operationAreaReviewer=e),placeholder:"请选择作业区审核人"},{default:o(()=>[(p(!0),c(y,null,A(l(be),e=>(p(),d(w,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"检测报告",prop:"file"},{default:o(()=>[t(oa,{modelValue:l(r).file,"onUpdate:modelValue":a[21]||(a[21]=e=>l(r).file=e),"attach-source-id":l(r).appId,disabled:!1,"attach-category":"transportApplication","attach-source-type":"njTransportApplication",onUploadSuccess:Be},null,8,["modelValue","attach-source-id"])]),_:1}),l(G)?(p(),d(i,{key:0,label:"审核意见",prop:"rejectSuggestion"},{default:o(()=>[t(u,{modelValue:l(r).rejectSuggestion,"onUpdate:modelValue":a[22]||(a[22]=e=>l(r).rejectSuggestion=e),disabled:"",placeholder:"请输入内容",type:"textarea"},null,8,["modelValue"])]),_:1})):h("",!0),t(i,{label:"备注信息",prop:"remark"},{default:o(()=>[t(u,{modelValue:l(r).remark,"onUpdate:modelValue":a[23]||(a[23]=e=>l(r).remark=e),placeholder:"请输入内容",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(oe,{title:l(T).title,modelValue:l(T).visible,"onUpdate:modelValue":a[43]||(a[43]=e=>l(T).visible=e),width:"600px","append-to-body":""},{footer:o(()=>[N("div",Pa,[t(g,{loading:l(k),type:"primary",onClick:Ke},{default:o(()=>a[63]||(a[63]=[m("确 定")])),_:1},8,["loading"]),t(g,{onClick:we},{default:o(()=>a[64]||(a[64]=[m("取 消")])),_:1})])]),default:o(()=>[t(Z,{ref_key:"transportApplicationFormRef",ref:Y,model:l(r),rules:l(je),"label-width":"140px"},{default:o(()=>[t(i,{label:"申请名称",prop:"appName"},{default:o(()=>[t(u,{disabled:"",modelValue:l(r).appName,"onUpdate:modelValue":a[27]||(a[27]=e=>l(r).appName=e),placeholder:"请输入申请名称"},null,8,["modelValue"])]),_:1}),t(i,{label:"申请分类",prop:"mediumCategory"},{default:o(()=>[t(V,{modelValue:l(r).mediumCategory,"onUpdate:modelValue":a[28]||(a[28]=e=>l(r).mediumCategory=e),placeholder:"请选择申请分类",disabled:""},{default:o(()=>[(p(!0),c(y,null,A(l(ne),e=>(p(),d(w,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"所属作业区",prop:"workAreaId"},{default:o(()=>[t(V,{disabled:"",filterable:"",modelValue:l(r).workAreaId,"onUpdate:modelValue":a[29]||(a[29]=e=>l(r).workAreaId=e),class:"searchDate",placeholder:"选择所属作业区",clearable:""},{default:o(()=>[(p(!0),c(y,null,A(l(j),e=>(p(),d(w,{key:e.operationAreaId,label:e.operationAreaName,value:e.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"单井名称",prop:"relatedId"},{default:o(()=>[t(V,{disabled:"",modelValue:l(r).relatedId,"onUpdate:modelValue":a[30]||(a[30]=e=>l(r).relatedId=e),placeholder:"请选择单井",onChange:ke},{default:o(()=>[(p(!0),c(y,null,A(l(q),e=>(p(),d(w,{key:e.prepId,label:e.wellName,value:e.prepId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"申请日期",prop:"applicationDate"},{default:o(()=>[t(H,{modelValue:l(r).applicationDate,"onUpdate:modelValue":a[31]||(a[31]=e=>l(r).applicationDate=e),clearable:"",disabled:"",placeholder:"请选择申请日期",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(i,{label:"装车地点",prop:"loadingLocation"},{default:o(()=>[t(u,{disabled:"",modelValue:l(r).loadingLocation,"onUpdate:modelValue":a[32]||(a[32]=e=>l(r).loadingLocation=e),placeholder:"请输入装车地点"},null,8,["modelValue"])]),_:1}),t(i,{label:"卸车地点",prop:"unloadLocation"},{default:o(()=>[t(u,{modelValue:l(r).unloadLocation,"onUpdate:modelValue":a[33]||(a[33]=e=>l(r).unloadLocation=e),disabled:"",placeholder:"请输入卸车地点"},null,8,["modelValue"])]),_:1}),t(i,{label:"归口部门审核人",prop:"superviseApprover",disabled:""},{default:o(()=>[t(V,{modelValue:l(r).superviseApprover,"onUpdate:modelValue":a[34]||(a[34]=e=>l(r).superviseApprover=e),disabled:"",placeholder:"请选择批准人"},{default:o(()=>[(p(!0),c(y,null,A(l(ce),e=>(p(),d(w,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"归口部门审核状态",prop:"applicationStatus"},{default:o(()=>[t(Te,{modelValue:l(r).applicationStatus,"onUpdate:modelValue":a[35]||(a[35]=e=>l(r).applicationStatus=e),disabled:l(M)!=1},{default:o(()=>[(p(!0),c(y,null,A(l($),e=>(p(),d(Se,{key:e.value,value:parseInt(e.value)},{default:o(()=>[m(D(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),t(i,{label:"归口部门审核时间",prop:"examineApproveTime"},{default:o(()=>[t(H,{modelValue:l(r).examineApproveTime,"onUpdate:modelValue":a[36]||(a[36]=e=>l(r).examineApproveTime=e),clearable:"",disabled:"",placeholder:"",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(i,{label:"作业区审核人",prop:"operationAreaReviewer",disabled:""},{default:o(()=>[t(V,{modelValue:l(r).operationAreaReviewer,"onUpdate:modelValue":a[37]||(a[37]=e=>l(r).operationAreaReviewer=e),placeholder:"请选择审核人",disabled:""},{default:o(()=>[(p(!0),c(y,null,A(l(be),e=>(p(),d(w,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"作业区审核状态",prop:"operationAreaApproveStatus"},{default:o(()=>[t(Te,{modelValue:l(r).operationAreaApproveStatus,"onUpdate:modelValue":a[38]||(a[38]=e=>l(r).operationAreaApproveStatus=e),disabled:l(M)!=2},{default:o(()=>[(p(!0),c(y,null,A(l($),e=>(p(),d(Se,{key:e.value,value:parseInt(e.value)},{default:o(()=>[m(D(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),t(i,{label:"作业区审核时间",prop:"operationAreaReviewedTime"},{default:o(()=>[t(H,{modelValue:l(r).operationAreaReviewedTime,"onUpdate:modelValue":a[39]||(a[39]=e=>l(r).operationAreaReviewedTime=e),clearable:"",disabled:"",placeholder:"",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),_((p(),d(i,{label:"预警时间",prop:"warnTime"},{default:o(()=>[t(u,{modelValue:l(r).warnTime,"onUpdate:modelValue":a[40]||(a[40]=e=>l(r).warnTime=e)},{append:o(()=>a[62]||(a[62]=[m("小时")])),_:1},8,["modelValue"])]),_:1})),[[S,["epfy:transportApplication:coordinate"]]]),t(i,{label:"审核意见",prop:"rejectSuggestion"},{default:o(()=>[t(u,{modelValue:l(r).rejectSuggestion,"onUpdate:modelValue":a[41]||(a[41]=e=>l(r).rejectSuggestion=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1}),t(i,{label:"备注信息",prop:"remark"},{default:o(()=>[t(u,{modelValue:l(r).remark,"onUpdate:modelValue":a[42]||(a[42]=e=>l(r).remark=e),type:"textarea",disabled:"",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(oe,{modelValue:l(L).visible,"onUpdate:modelValue":a[46]||(a[46]=e=>l(L).visible=e),title:l(L).title,"append-to-body":"",width:"500px"},{footer:o(()=>[N("div",Ha,[t(g,{loading:l(k),type:"primary",onClick:he},{default:o(()=>a[66]||(a[66]=[m("确 定")])),_:1},8,["loading"]),t(g,{onClick:a[45]||(a[45]=e=>l(L).visible=!1)},{default:o(()=>a[67]||(a[67]=[m("取 消")])),_:1})])]),default:o(()=>[t(Z,{ref_key:"transportApplicationFormRef",ref:Y,model:l(r),"label-width":"110px"},{default:o(()=>[t(i,{label:"预警时间",prop:"warnTime"},{default:o(()=>[t(u,{modelValue:l(r).warnTime,"onUpdate:modelValue":a[44]||(a[44]=e=>l(r).warnTime=e)},{append:o(()=>a[65]||(a[65]=[m("小时")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});export{pl as default};
