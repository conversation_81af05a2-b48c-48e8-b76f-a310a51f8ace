import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { QualityTrilateralForm, QualityTrilateralQuery, QualityTrilateralVO } from '@/api/epms/eptw/qualityTrilateral/types';

/**
 * 查询采出水第三方水质监测报告列表
 * @param query
 * @returns {*}
 */

export const listQualityTrilateral = (query?: QualityTrilateralQuery): AxiosPromise<QualityTrilateralVO[]> => {
  return request({
    url: '/epms/qualityTrilateral/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询采出水第三方水质监测报告详细
 * @param qualityReportId
 */
export const getQualityTrilateral = (qualityReportId: string | number): AxiosPromise<QualityTrilateralVO> => {
  return request({
    url: '/epms/qualityTrilateral/' + qualityReportId,
    method: 'get'
  });
};

/**
 * 新增采出水第三方水质监测报告
 * @param data
 */
export const addQualityTrilateral = (data: QualityTrilateralForm) => {
  return request({
    url: '/epms/qualityTrilateral',
    method: 'post',
    data: data
  });
};

/**
 * 修改采出水第三方水质监测报告
 * @param data
 */
export const updateQualityTrilateral = (data: QualityTrilateralForm) => {
  return request({
    url: '/epms/qualityTrilateral',
    method: 'put',
    data: data
  });
};

/**
 * 删除采出水第三方水质监测报告
 * @param qualityReportId
 */
export const delQualityTrilateral = (qualityReportId: string | number | Array<string | number>) => {
  return request({
    url: '/epms/qualityTrilateral/' + qualityReportId,
    method: 'delete'
  });
};
