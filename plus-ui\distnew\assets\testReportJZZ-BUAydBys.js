import{d as J,h as fe,ak as L,r as p,ai as K,b as ge,aH as ve,c as _e,o as f,p as t,t as a,w as m,q as k,a7 as he,M as be,e as n,A as ye,G as Ae,H as we,K as Ee,J as u,am as Q,aI as Se,x as g,ay as ke,ax as Ce,z as G,aL as De,v as Ve,az as Ne,a8 as Te,aA as Ie}from"./index-D07cMzhp.js";import{_ as Ue}from"./index-DVHplxfU.js";import{E as je}from"./el-date-picker-HyhB9X9n.js";import{E as Fe}from"./el-row-CikYE3zA.js";import{_ as qe}from"./index-BWMgqvQ9.js";import{E as Re}from"./el-col-BaG5Rg5z.js";import{l as xe,g as Pe,u as $e,a as He,d as Ye}from"./index-UPLDtrdk.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";import"./el-tree-DW6MoFaI.js";const Me={class:"p-2"},ze={class:"mb-[10px]"},Be={class:"dialog-footer"},Le=J({name:"DrugAttcah"}),st=J({...Le,setup(Ke){const{proxy:i}=fe(),{epnj_drug_attach_type:Qe}=L(i==null?void 0:i.useDict("epnj_drug_attach_type")),U=p([]),y=p(!1),A=p(!0),w=p(!0),C=p([]),j=p(!0),F=p(!0),D=p(0),q=p(),V=p(),c=K({visible:!1,title:""}),R={drugAttachId:void 0,attachName:void 0,lifespanStart:void 0,lifespanEnd:void 0,file:void 0,uploadTime:void 0,drugAttachType:0,handlingType:1},O=K({form:{...R},queryParams:{pageNum:1,pageSize:10,attachName:void 0,lifespanStart:void 0,lifespanEnd:void 0,file:void 0,uploadTime:void 0,drugAttachType:0,handlingType:1,params:{}},rules:{drugAttachId:[{required:!0,message:"药剂附件id不能为空",trigger:"blur"}],lifespanStart:[{required:!0,message:"有效期开始时间不能为空",trigger:"blur"}],attachName:[{required:!0,message:"检测报告名称不能为空",trigger:"blur"}],lifespanEnd:[{required:!0,message:"有效期结束时间不能为空",trigger:"blur"}],file:[{required:!0,message:"请上传药剂检测报告",trigger:"blur"}]}}),{queryParams:d,form:s,rules:W}=L(O),_=async()=>{A.value=!0;const l=await xe(d.value);U.value=l.rows,D.value=l.total,A.value=!1},X=()=>{N(),c.visible=!1},N=()=>{var l;s.value={...R},(l=V.value)==null||l.resetFields()},T=()=>{d.value.pageNum=1,_()},Z=l=>{s.value.uploadTime=l.uploadTime},ee=()=>{var l;(l=q.value)==null||l.resetFields(),T()},te=l=>{C.value=l.map(e=>e.drugAttachId),j.value=l.length!=1,F.value=!l.length},ae=()=>{N(),c.visible=!0,c.title="添加药剂检测报告"},x=async l=>{N();const e=(l==null?void 0:l.drugAttachId)||C.value[0],E=await Pe(e);Object.assign(s.value,E.data),c.visible=!0,c.title="修改药剂检测报告"},le=async l=>{i.showAttachPreview({attachSourceId:l.drugAttachId,attachSourceType:"mudHandling",attachCategory:"chemicalReport"})},ne=()=>{var l;(l=V.value)==null||l.validate(async e=>{e&&(y.value=!0,s.value.drugAttachId?await $e(s.value).finally(()=>y.value=!1):await He(s.value).finally(()=>y.value=!1),i==null||i.$modal.msgSuccess("操作成功"),c.visible=!1,await _())})},P=async l=>{const e=(l==null?void 0:l.drugAttachId)||C.value;await(i==null?void 0:i.$modal.confirm('是否确认删除药剂检测报告编号为"'+e+'"的数据项？').finally(()=>A.value=!1)),await Ye(e),i==null||i.$modal.msgSuccess("删除成功"),await _()},oe=()=>{i==null||i.download("epnj/drugAttcah/export",{...d.value},`drugAttcah_${new Date().getTime()}.xlsx`)},ie=l=>(d.value.pageNum-1)*d.value.pageSize+l+1;return ge(()=>{_()}),(l,e)=>{var M,z;const E=Ae,h=ye,r=Ee,$=be,H=he,S=Re,se=qe,de=Fe,b=Ce,I=De,re=ke,ue=Ne,Y=je,pe=Ue,me=Ie,v=ve("hasPermi"),ce=Ve;return f(),_e("div",Me,[t(Se,{"enter-active-class":(M=n(i))==null?void 0:M.animate.searchAnimate.enter,"leave-active-class":(z=n(i))==null?void 0:z.animate.searchAnimate.leave},{default:a(()=>[m(k("div",ze,[t(H,{shadow:"hover"},{default:a(()=>[t($,{ref_key:"queryFormRef",ref:q,model:n(d),inline:!0,"label-width":"120px"},{default:a(()=>[t(h,{label:"检测报告名称",prop:"attachName"},{default:a(()=>[t(E,{modelValue:n(d).attachName,"onUpdate:modelValue":e[0]||(e[0]=o=>n(d).attachName=o),placeholder:"请输入检测报告名称",clearable:"",onKeyup:we(T,["enter"])},null,8,["modelValue"])]),_:1}),t(h,null,{default:a(()=>[t(r,{type:"primary",icon:"Search",onClick:T},{default:a(()=>e[11]||(e[11]=[u("搜索")])),_:1}),t(r,{icon:"Refresh",onClick:ee},{default:a(()=>e[12]||(e[12]=[u("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[Q,n(w)]])]),_:1},8,["enter-active-class","leave-active-class"]),t(H,{shadow:"never"},{header:a(()=>[t(de,{gutter:10,class:"mb8"},{default:a(()=>[t(S,{span:1.5},{default:a(()=>[m((f(),g(r,{type:"primary",plain:"",icon:"Plus",onClick:ae},{default:a(()=>e[13]||(e[13]=[u("新增")])),_:1})),[[v,["epnj:drugAttcah:add"]]])]),_:1}),t(S,{span:1.5},{default:a(()=>[m((f(),g(r,{disabled:n(j),icon:"Edit",plain:"",type:"success",onClick:e[1]||(e[1]=o=>x())},{default:a(()=>e[14]||(e[14]=[u("修改")])),_:1},8,["disabled"])),[[v,["epnj:drugAttcah:edit"]]])]),_:1}),t(S,{span:1.5},{default:a(()=>[m((f(),g(r,{disabled:n(F),icon:"Delete",plain:"",type:"danger",onClick:e[2]||(e[2]=o=>P())},{default:a(()=>e[15]||(e[15]=[u("删除")])),_:1},8,["disabled"])),[[v,["epnj:drugAttcah:remove"]]])]),_:1}),t(S,{span:1.5},{default:a(()=>[m((f(),g(r,{type:"warning",plain:"",icon:"Download",onClick:oe},{default:a(()=>e[16]||(e[16]=[u("导出")])),_:1})),[[v,["epnj:drugAttcah:export"]]])]),_:1}),t(se,{showSearch:n(w),"onUpdate:showSearch":e[3]||(e[3]=o=>Te(w)?w.value=o:null),onQueryTable:_},null,8,["showSearch"])]),_:1})]),default:a(()=>[m((f(),g(re,{stripe:"",data:n(U),onSelectionChange:te},{default:a(()=>[t(b,{type:"selection",width:"55",align:"center"}),t(b,{index:ie,label:"序号",type:"index",width:"50"}),t(b,{label:"检测报告名称",align:"center",prop:"attachName"}),t(b,{label:"有效期开始时间",align:"center",prop:"lifespanStart",width:"180"},{default:a(o=>[k("span",null,G(l.parseTime(o.row.lifespanStart,"{y}-{m}-{d}")),1)]),_:1}),t(b,{label:"有效期结束时间",align:"center",prop:"lifespanEnd",width:"180"},{default:a(o=>[k("span",null,G(l.parseTime(o.row.lifespanEnd,"{y}-{m}-{d}")),1)]),_:1}),t(b,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作"},{default:a(o=>[t(I,{content:"检测报告预览",placement:"top"},{default:a(()=>[m((f(),g(r,{icon:"Document",link:"",type:"primary",onClick:B=>le(o.row)},{default:a(()=>e[17]||(e[17]=[u("检测报告预览")])),_:2},1032,["onClick"])),[[v,["epnj:drugAttcah:preview"]]])]),_:2},1024),t(I,{content:"修改",placement:"top"},{default:a(()=>[m((f(),g(r,{link:"",type:"primary",icon:"Edit",onClick:B=>x(o.row)},{default:a(()=>e[18]||(e[18]=[u("修改")])),_:2},1032,["onClick"])),[[v,["epnj:drugAttcah:edit"]]])]),_:2},1024),t(I,{content:"删除",placement:"top"},{default:a(()=>[m((f(),g(r,{link:"",type:"primary",icon:"Delete",onClick:B=>P(o.row)},{default:a(()=>e[19]||(e[19]=[u("删除")])),_:2},1032,["onClick"])),[[v,["epnj:drugAttcah:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[ce,n(A)]]),m(t(ue,{total:n(D),page:n(d).pageNum,"onUpdate:page":e[4]||(e[4]=o=>n(d).pageNum=o),limit:n(d).pageSize,"onUpdate:limit":e[5]||(e[5]=o=>n(d).pageSize=o),onPagination:_},null,8,["total","page","limit"]),[[Q,n(D)>0]])]),_:1}),t(me,{title:n(c).title,modelValue:n(c).visible,"onUpdate:modelValue":e[10]||(e[10]=o=>n(c).visible=o),width:"500px","append-to-body":""},{footer:a(()=>[k("div",Be,[t(r,{loading:n(y),type:"primary",onClick:ne},{default:a(()=>e[20]||(e[20]=[u("确 定")])),_:1},8,["loading"]),t(r,{onClick:X},{default:a(()=>e[21]||(e[21]=[u("取 消")])),_:1})])]),default:a(()=>[t($,{ref_key:"drugAttcahFormRef",ref:V,model:n(s),rules:n(W),"label-width":"120px"},{default:a(()=>[t(h,{label:"检测报告名称",prop:"attachName"},{default:a(()=>[t(E,{modelValue:n(s).attachName,"onUpdate:modelValue":e[6]||(e[6]=o=>n(s).attachName=o),placeholder:"请输入检测报告名称"},null,8,["modelValue"])]),_:1}),t(h,{label:"有效期开始时间",prop:"lifespanStart"},{default:a(()=>[t(Y,{clearable:"",modelValue:n(s).lifespanStart,"onUpdate:modelValue":e[7]||(e[7]=o=>n(s).lifespanStart=o),type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择有效期开始时间"},null,8,["modelValue"])]),_:1}),t(h,{label:"有效期结束时间",prop:"lifespanEnd"},{default:a(()=>[t(Y,{modelValue:n(s).lifespanEnd,"onUpdate:modelValue":e[8]||(e[8]=o=>n(s).lifespanEnd=o),clearable:"",placeholder:"请选择有效期结束时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(h,{label:"检测报告",prop:"file"},{default:a(()=>[t(pe,{modelValue:n(s).file,"onUpdate:modelValue":e[9]||(e[9]=o=>n(s).file=o),"attach-source-id":n(s).drugAttachId,disabled:!1,"attach-category":"chemicalReport","attach-source-type":"mudHandling",onUploadSuccess:Z},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{st as default};
