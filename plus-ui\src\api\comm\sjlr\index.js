// 线路
import request from '@/utils/request.js';

//数据列表
export function getDataList(params) {
  return request({
    url: '/epms/dataReport/getDataList',
    method: 'get',
    params: params
  });
}

//部件类型列表
export function getShebeileixingList() {
  return request({
    url: '/epms/dataReport/getShebeileixingList',
    method: 'get'
  });
}

//设备列表
export function getshebeiList() {
  return request({
    url: '/epms/dataReport/getshebeiList',
    method: 'get'
  });
}

//参数列表
export function getCanshuList() {
  return request({
    url: '/epms/dataReport/getCanshuList',
    method: 'get'
  });
}

//能源站列表
export function getnengyuanzhanList() {
  return request({
    url: '/epms/dataReport/getEnergyStationList',
    method: 'get'
  });
}

//新增数据
export function addData(params) {
  return request({
    url: '/epms/dataReport/addData',
    method: 'get',
    params: params
  });
}

//修改数据
export function updateData(params) {
  return request({
    url: '/epms/dataReport/updateData',
    method: 'get',
    params: params
  });
}

//导入模版下载
export function downloadTemplate(params) {
  return request({
    url: '/epms/dataReport/downloadTemplate',
    method: 'get',
    params: params
  });
}

//修改数据
export function getTopologyByTernary(params) {
  return request({
    url: '/epms/dataReport/getTopologyByTernary',
    method: 'get',
    params: params
  });
}

//删除数据
export function deleteData(params) {
  return request({
    url: '/epms/dataReport/deleteData',
    method: 'get',
    params: params
  });
}
