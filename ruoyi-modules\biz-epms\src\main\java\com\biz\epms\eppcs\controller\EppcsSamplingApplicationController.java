package com.biz.epms.eppcs.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.biz.comm.attach.annotation.AttachDelete;
import com.biz.comm.attach.annotation.AttachUpload;
import com.biz.epms.eppcs.common.enums.EppcsCurrentPhaseEnums;
import com.biz.epms.eppcs.domain.bo.EppcsSamplingApplicationBo;
import com.biz.epms.eppcs.domain.vo.EppcsSamplingApplicationVo;
import com.biz.epms.eppcs.service.IEppcsSamplingApplicationService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 取样检测申请
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/epms/eppcs/samplingApplication")
public class EppcsSamplingApplicationController extends BaseController {

    private final IEppcsSamplingApplicationService eppcsSamplingApplicationService;

    /**
     * 查询取样检测申请列表
     */
    @SaCheckPermission("eppcs:samplingApplication:list")
    @GetMapping("/list")
    public TableDataInfo<EppcsSamplingApplicationVo> list(EppcsSamplingApplicationBo bo, PageQuery pageQuery) {
        return eppcsSamplingApplicationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出取样检测申请列表
     */
    @SaCheckPermission("eppcs:samplingApplication:export")
    @Log(title = "取样检测申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EppcsSamplingApplicationBo bo, HttpServletResponse response) {
        List<EppcsSamplingApplicationVo> list = eppcsSamplingApplicationService.queryList(bo);
        ExcelUtil.exportExcel(list, "取样检测申请", EppcsSamplingApplicationVo.class, response);
    }

    /**
     * 获取取样检测申请详细信息
     *
     * @param applicationId 主键
     */
    @SaCheckPermission("eppcs:samplingApplication:query")
    @GetMapping("/{applicationId}")
    public R<EppcsSamplingApplicationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long applicationId) {
        return R.ok(eppcsSamplingApplicationService.queryById(applicationId));
    }

    /**
     * 新增取样检测申请
     */
    @SaCheckPermission("eppcs:samplingApplication:add")
    @Log(title = "取样检测申请", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    @AttachUpload(
        sourceIdExpression = "#bo.applicationId",
        attachIdExpression = {"#bo.file"}
    )
    public R<Long> add(@Validated(AddGroup.class) @RequestBody EppcsSamplingApplicationBo bo) {
        // 默认0 草稿状态
        bo.setApplicationStatus(0);
        bo.setCurrentPhase(EppcsCurrentPhaseEnums.SAMPLING_APPLICATION.getValue());
        if (eppcsSamplingApplicationService.insertByBo(bo)){
            return R.ok("操作成功", bo.getApplicationId());
        }
        return R.fail();
    }

    /**
     * 修改取样检测申请
     */
    @SaCheckPermission("eppcs:samplingApplication:edit")
    @Log(title = "取样检测申请", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EppcsSamplingApplicationBo bo) {
        // 禁止直接修改状态
        bo.setApplicationStatus(null);
        return toAjax(eppcsSamplingApplicationService.updateByBo(bo));
    }

    /**
     * 删除取样检测申请
     *
     * @param applicationIds 主键串
     */
    @SaCheckPermission("eppcs:samplingApplication:remove")
    @Log(title = "取样检测申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applicationIds}")
    @AttachDelete(
        sourceIdExpression = "#applicationIds"
    )
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] applicationIds) {
        return toAjax(eppcsSamplingApplicationService.deleteWithValidByIds(List.of(applicationIds), true));
    }

    /**
     * 提交申请
     *
     * @param applicationId 申请ID
     * @param userType 用户类型
     */
    @SaCheckPermission("eppcs:samplingApplication:submit")
    @Log(title = "提交取样检测申请", businessType = BusinessType.UPDATE)
    @PostMapping("/submit/{applicationId}")
    public R<Void> submitApplication(@NotNull(message = "申请ID不能为空") @PathVariable Long applicationId,
                                   @RequestParam(defaultValue = "1") Integer userType) {
        return toAjax(eppcsSamplingApplicationService.submitApplication(applicationId, userType));
    }

    /**
     * 审批申请
     *
     * @param applicationId 申请ID
     * @param userType 用户类型
     * @param action 动作 (1通过, 2驳回)
     * @param approvalRemark 审批意见
     * @param approver 审批人
     */
    @SaCheckPermission("eppcs:samplingApplication:approve")
    @Log(title = "审批取样检测申请", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{applicationId}")
    public R<Void> approveApplication(@NotNull(message = "申请ID不能为空") @PathVariable Long applicationId,
                                    @RequestParam Integer userType,
                                    @RequestParam Integer action,
                                    @RequestParam(required = false) String approvalRemark) {
        return toAjax(eppcsSamplingApplicationService.approveApplication(
            applicationId, userType, action, approvalRemark, LoginHelper.getLoginUser().getNickname()));
    }

}
