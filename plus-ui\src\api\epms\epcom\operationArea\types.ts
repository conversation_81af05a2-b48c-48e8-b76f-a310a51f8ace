export interface OperationAreaVO {
  /**
   * 作业区id
   */
  operationAreaId: string | number;

  /**
   * 作业区名称
   */
  operationAreaName: string;

  /**
   * 父级ID
   */
  operationAreaParentId: string | number;

  /**
   * 物联网id
   */
  iotId: string | number;

  /**
   * 行政区域ID
   */
  administrativeArea: string | number;

  operationAreaType?: string | number;
  parkId?: string | number;
}

export interface OperationAreaForm extends BaseEntity {
  /**
   * 作业区id
   */
  operationAreaId?: string | number;

  /**
   * 作业区名称
   */
  operationAreaName?: string;

  /**
   * 父级ID
   */
  operationAreaParentId?: string | number;

  /**
   * 物联网id
   */
  iotId?: string | number;

  /**
   * 行政区域ID
   */
  administrativeArea?: string | number;

  operationAreaType?: string | number;
}

export interface OperationAreaQuery extends PageQuery {
  /**
   * 作业区名称
   */
  operationAreaName?: string;

  /**
   * 父ID
   */
  operationAreaParentId?: string | number;

  operationAreaType?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;

  /**
   * 行政区域ID
   */
  administrativeArea?: string | number;
}
