<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="封井申请名称" prop="applicationName">
              <el-input v-model="queryParams.applicationName" placeholder="请输入封井申请名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="井名称" prop="wellName">
              <el-input v-model="queryParams.wellName" placeholder="请输入井名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属地" prop="workAreaId">
              <el-select v-model="queryParams.workAreaId" class="searchDate" clearable filterable placeholder="选择所属地">
                <el-option
                  v-for="operationArea in operationAreaList"
                  :key="operationArea.operationAreaId"
                  :label="operationArea.operationAreaName"
                  :value="operationArea.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="封井申请时间" style="width: 400px">
              <el-date-picker
                v-model="dateRangeApplicationDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!--          <el-col :span="1.5">-->
          <!--            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epfj:wellSeal:export']">导出</el-button>-->
          <!--          </el-col>-->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="wellSealList" stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" type="index" width="55" :index="indexMethod" />
        <el-table-column align="center" label="井名称" prop="wellName" />
        <el-table-column align="center" label="所属地" prop="workAreaName" />
        <el-table-column align="center" label="资产报废名称" prop="scrapName" />
        <el-table-column align="center" label="封井申请名称" prop="applicationName" />
        <el-table-column label="审批状态" align="center" prop="approvalStatus">
          <template #default="scope">
            <dict-tag :options="epfj_approval_status" :value="scope.row.approvalStatus" />
          </template>
        </el-table-column>
        <el-table-column label="文件查看" align="center" class-name="small-padding fixed-width" fixed="right" min-width="220">
          <template #default="scope">
            <el-tooltip content="查看施工现场环保许可证" placement="top">
              <el-button
                v-hasPermi="['epfj:wellSeal:preview']"
                :disabled="!scope.row.environmentalLicense"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.sealId, 'environmentalLicense')"
                >施工现场环保许可证</el-button
              >
            </el-tooltip>
            <el-tooltip content="查看HSE作业计划书" placement="top">
              <el-button
                v-hasPermi="['epfj:wellSeal:preview']"
                :disabled="!scope.row.hsePlan"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.sealId, 'hsePlan')"
                >HSE作业计划书</el-button
              >
            </el-tooltip>
            <el-tooltip content="查看作业现场油水井交接书" placement="top">
              <el-button
                v-hasPermi="['epfj:wellSeal:preview']"
                :disabled="!scope.row.wellHandoverReport"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.sealId, 'wellHandoverReport')"
                >作业现场油水井交接书</el-button
              >
            </el-tooltip>
            <el-tooltip content="查看单井应急预案" placement="top">
              <el-button
                v-hasPermi="['epfj:wellSeal:preview']"
                :disabled="!scope.row.emergencyPlan"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.sealId, 'emergencyPlan')"
                >单井应急预案</el-button
              >
            </el-tooltip>
            <el-tooltip content="查看班前、班后会记录" placement="top">
              <el-button
                v-hasPermi="['epfj:wellSeal:preview']"
                :disabled="!scope.row.shiftMeetingRecords"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.sealId, 'shiftMeetingRecords')"
                >班前、班后会记录</el-button
              >
            </el-tooltip>
            <el-tooltip content="查看现场开工验收单" placement="top">
              <el-button
                v-hasPermi="['epfj:wellSeal:preview']"
                :disabled="!scope.row.siteAcceptanceReport"
                icon="Document"
                link
                type="primary"
                @click="previewFile(scope.row.sealId, 'siteAcceptanceReport')"
                >现场开工验收单</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" min-width="110">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.approvalStatus == fjStatusEnum.xuKeZhengHeZuoYeJiHuaShangChuan.value"
              content="许可证和作业计划上传"
              placement="top"
            >
              <el-button v-hasPermi="['epfj:constructionPrepare:permit']" icon="Edit" link type="primary" @click="handlePermit(scope.row)"
                >许可证和作业计划上传</el-button
              >
            </el-tooltip>
            <el-tooltip v-if="scope.row.approvalStatus == fjStatusEnum.youShuiJingJiaoJie.value" content="油水井交接" placement="top">
              <el-button v-hasPermi="['epfj:constructionPrepare:handover']" icon="Edit" link type="primary" @click="handleHandover(scope.row)"
                >油水井交接</el-button
              >
            </el-tooltip>
            <el-tooltip v-if="scope.row.approvalStatus == fjStatusEnum.fengXianJiaoDi.value" content="风险交底" placement="top">
              <el-button v-hasPermi="['epfj:constructionPrepare:risk']" icon="Edit" link type="primary" @click="handleRisk(scope.row)"
                >风险交底</el-button
              >
            </el-tooltip>
            <el-tooltip v-if="scope.row.approvalStatus == fjStatusEnum.kaiGongYanShou.value" content="开工验收" placement="top">
              <el-button v-hasPermi="['epfj:constructionPrepare:start']" icon="Edit" link type="primary" @click="handleStart(scope.row)"
                >开工验收</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 许可证和作业计划上传对话框 -->
    <el-dialog :title="permitDialog.title" v-model="permitDialog.visible" width="550px" append-to-body>
      <el-form ref="wellSealFormRef" :model="form" :rules="rules" label-width="115px">
        <el-form-item label="封井名称" prop="wellName">
          <el-input v-model="form.wellName" placeholder="请输入封井名称" disabled />
        </el-form-item>
        <el-form-item label="资产报废名称" prop="scrapName">
          <el-input v-model="form.scrapName" placeholder="请输入资产报废名称" disabled />
        </el-form-item>
        <el-form-item label="封井申请名称" prop="applicationName">
          <el-input v-model="form.applicationName" placeholder="请输入资产报废名称" disabled />
        </el-form-item>
        <el-form-item label="环保许可证" prop="environmentalLicense">
          <attachFileUpload
            v-model="form.environmentalLicense"
            :attach-source-id="form.sealId"
            :disabled="false"
            attach-category="environmentalLicense"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
        <el-form-item label="HSE作业计划书" prop="hsePlan">
          <attachFileUpload
            v-model="form.hsePlan"
            :attach-source-id="form.sealId"
            :disabled="false"
            attach-category="hsePlan"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="warning" @click="submitStatus">提 交</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 油水井交接对话框 -->
    <el-dialog :title="handoverDialog.title" v-model="handoverDialog.visible" width="550px" append-to-body>
      <el-form ref="wellSealFormRef" :model="form" :rules="rules" label-width="115px">
        <el-form-item label="封井名称" prop="wellName">
          <el-input v-model="form.wellName" placeholder="请输入封井名称" disabled />
        </el-form-item>
        <el-form-item label="资产报废名称" prop="scrapName">
          <el-input v-model="form.scrapName" placeholder="请输入资产报废名称" disabled />
        </el-form-item>
        <el-form-item label="封井申请名称" prop="applicationName">
          <el-input v-model="form.applicationName" placeholder="请输入资产报废名称" disabled />
        </el-form-item>
        <el-form-item label="环保许可证" prop="environmentalLicense">
          <el-button type="primary" @click="previewFile(form.sealId, 'environmentalLicense')">预览</el-button>
        </el-form-item>
        <el-form-item label="HSE作业计划书" prop="hsePlan">
          <el-button type="primary" @click="previewFile(form.sealId, 'hsePlan')">预览</el-button>
        </el-form-item>
        <el-form-item label="作业现场油水井交接书" prop="wellHandoverReport">
          <attachFileUpload
            v-model="form.wellHandoverReport"
            :attach-source-id="form.sealId"
            :disabled="false"
            attach-category="wellHandoverReport"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="warning" @click="submitStatus">提 交</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 风险交底对话框 -->
    <el-dialog :title="riskDialog.title" v-model="riskDialog.visible" width="550px" append-to-body>
      <el-form ref="wellSealFormRef" :model="form" :rules="rules" label-width="115px">
        <el-form-item label="封井名称" prop="wellName">
          <el-input v-model="form.wellName" placeholder="请输入封井名称" disabled />
        </el-form-item>
        <el-form-item label="资产报废名称" prop="scrapName">
          <el-input v-model="form.scrapName" placeholder="请输入资产报废名称" disabled />
        </el-form-item>
        <el-form-item label="封井申请名称" prop="applicationName">
          <el-input v-model="form.applicationName" placeholder="请输入资产报废名称" disabled />
        </el-form-item>
        <el-form-item label="环保许可证" prop="environmentalLicense">
          <el-button type="primary" @click="previewFile(form.sealId, 'environmentalLicense')">预览</el-button>
        </el-form-item>
        <el-form-item label="HSE作业计划书" prop="hsePlan">
          <el-button type="primary" @click="previewFile(form.sealId, 'hsePlan')">预览</el-button>
        </el-form-item>
        <el-form-item label="作业现场油水井交接书" prop="wellHandoverReport">
          <el-button type="primary" @click="previewFile(form.sealId, 'wellHandoverReport')">预览</el-button>
        </el-form-item>
        <el-form-item label="单井应急预案" prop="emergencyPlan">
          <attachFileUpload
            v-model="form.emergencyPlan"
            :attach-source-id="form.sealId"
            :disabled="false"
            attach-category="emergencyPlan"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
        <el-form-item label="班前、班后会记录" prop="shiftMeetingRecords">
          <attachFileUpload
            v-model="form.shiftMeetingRecords"
            :attach-source-id="form.sealId"
            :disabled="false"
            attach-category="shiftMeetingRecords"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="warning" @click="submitStatus">提 交</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 开工验收对话框 -->
    <el-dialog :title="startDialog.title" v-model="startDialog.visible" width="550px" append-to-body>
      <el-form ref="wellSealFormRef" :model="form" :rules="rules" label-width="115px">
        <el-form-item label="封井名称" prop="wellName">
          <el-input v-model="form.wellName" placeholder="请输入封井名称" disabled />
        </el-form-item>
        <el-form-item label="资产报废名称" prop="scrapName">
          <el-input v-model="form.scrapName" placeholder="请输入资产报废名称" disabled />
        </el-form-item>
        <el-form-item label="封井申请名称" prop="applicationName">
          <el-input v-model="form.applicationName" placeholder="请输入资产报废名称" disabled />
        </el-form-item>
        <el-form-item label="环保许可证" prop="environmentalLicense">
          <el-button type="primary" @click="previewFile(form.sealId, 'environmentalLicense')">预览</el-button>
        </el-form-item>
        <el-form-item label="HSE作业计划书" prop="hsePlan">
          <el-button type="primary" @click="previewFile(form.sealId, 'hsePlan')">预览</el-button>
        </el-form-item>
        <el-form-item label="作业现场油水井交接书" prop="wellHandoverReport">
          <el-button type="primary" @click="previewFile(form.sealId, 'wellHandoverReport')">预览</el-button>
        </el-form-item>
        <el-form-item label="单井应急预案" prop="emergencyPlan">
          <el-button type="primary" @click="previewFile(form.sealId, 'emergencyPlan')">预览</el-button>
        </el-form-item>
        <el-form-item label="班前、班后会记录" prop="shiftMeetingRecords">
          <el-button type="primary" @click="previewFile(form.sealId, 'shiftMeetingRecords')">预览</el-button>
        </el-form-item>
        <el-form-item label="现场开工验收单" prop="siteAcceptanceReport">
          <attachFileUpload
            v-model="form.siteAcceptanceReport"
            :attach-source-id="form.sealId"
            :disabled="false"
            attach-category="siteAcceptanceReport"
            attach-source-type="wasteWellSealProcess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="warning" @click="submitStatus">提 交</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 附件预览 -->
    <el-dialog title="附件预览" v-model="previewDialog.visible" width="80%" he append-to-body>
      <div style="height: 68vh">
        <component :is="previewComponent" v-if="previewComponent" v-bind="previewProps" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="constructionPrepare" lang="ts">
import { addWellSeal, getWellSeal, listWellSeal, submitApprovalStatus, updateWellSeal } from '@/api/epms/epfj/wellSeal';
import { WellSealForm, WellSealQuery, WellSealVO } from '@/api/epms/epfj/wellSeal/types';
import { fjStatusEnum } from '@/api/epms/epfj/epfjEnum/types';
import { shallowRef } from 'vue';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';

const router = useRouter();
const operationAreaQuery = ref<OperationAreaQuery>({});
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epfj_approval_status } = toRefs<any>(proxy?.useDict('epfj_approval_status'));
const operationAreaList = ref<OperationAreaVO[]>([]); // 所属地列表
const wellSealList = ref<WellSealVO[]>([]);

const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeApplicationDate = ref<[DateModelType, DateModelType]>(['', '']);
const queryFormRef = ref<ElFormInstance>();
const wellSealFormRef = ref<ElFormInstance>();
const previewComponent = shallowRef();
const previewProps = ref({} as Record<string, any>);
const permitDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const handoverDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const riskDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const startDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const previewDialog = reactive({
  visible: false,
  url: ''
});
const initFormData: WellSealForm = {
  sealId: undefined,
  wellId: undefined,
  wellName: undefined,
  applicationId: undefined,
  applicationName: undefined,
  scrapId: undefined,
  scrapName: undefined,
  qukuaiId: undefined,
  qukuaiName: undefined,
  workAreaId: undefined,
  workAreaName: undefined,
  environmentalLicense: undefined,
  hsePlan: undefined,
  wellHandoverReport: undefined,
  emergencyPlan: undefined,
  shiftMeetingRecords: undefined,
  siteAcceptanceReport: undefined,
  dailyReport: undefined,
  supervisionRecord: undefined,
  workSummary: undefined,
  wellSealDescription: undefined,
  geologicalDesign: undefined,
  engineeringDesign: undefined,
  constructionPlanDesign: undefined,
  approvalStatus: undefined,
  approvalOpinions: undefined
};
const data = reactive<PageData<WellSealForm, WellSealQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    workAreaId: undefined,
    applicationName: undefined,
    wellName: undefined,
    params: {}
  },
  rules: {
    wellId: [{ required: true, message: '井不能为空', trigger: 'blur' }],
    workAreaId: [{ required: true, message: '所属地不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询封井列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeApplicationDate.value, 'ApplicationDate');
  const res = await listWellSeal(queryParams.value);
  wellSealList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  permitDialog.visible = false;
  handoverDialog.visible = false;
  riskDialog.visible = false;
  startDialog.visible = false;
};
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};

/** 许可证和作业计划上传 */
const handlePermit = async (row?: WellSealVO) => {
  reset();
  const _sealId = row?.sealId;
  const res = await getWellSeal(_sealId);
  Object.assign(form.value, res.data);
  permitDialog.visible = true;
  permitDialog.title = '许可证和作业计划上传';
};

/** 油水井交接 */
const handleHandover = async (row?: WellSealVO) => {
  reset();
  const _sealId = row?.sealId;
  const res = await getWellSeal(_sealId);
  Object.assign(form.value, res.data);
  handoverDialog.visible = true;
  handoverDialog.title = '油水井交接';
};

/** 风险交底 */
const handleRisk = async (row?: WellSealVO) => {
  reset();
  const _sealId = row?.sealId;
  const res = await getWellSeal(_sealId);
  Object.assign(form.value, res.data);
  riskDialog.visible = true;
  riskDialog.title = '风险交底';
};

/** 开工验收 */
const handleStart = async (row?: WellSealVO) => {
  reset();
  const _sealId = row?.sealId;
  const res = await getWellSeal(_sealId);
  Object.assign(form.value, res.data);
  startDialog.visible = true;
  startDialog.title = '开工验收';
};

const submitStatus = () => {
  wellSealFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.sealId) {
        await updateWellSeal(form.value).finally(() => (buttonLoading.value = false));
        // 提交状态
        await submitApprovalStatus(form.value.sealId, form.value.approvalStatus, 0);
      } else {
        proxy?.$modal.msgError('数据异常');
      }
      proxy?.$modal.msgSuccess('操作成功');
      permitDialog.visible = false;
      handoverDialog.visible = false;
      riskDialog.visible = false;
      startDialog.visible = false;
      await getList();
    }
  });
};

/** 审批弹窗附件预览 */
const previewFile = async (sealId?: string | number, category?: string) => {
  if (!sealId) return;
  proxy.showAttachPreview({
    attachSourceId: sealId,
    attachSourceType: 'wasteWellSealProcess',
    attachCategory: category
  });
  // const props = {
  //   attachSourceId: sealId,
  //   attachSourceType: "wasteWellSealProcess",
  //   attachCategory: category
  // };
  //
  // // 异步加载 preview.vue 组件
  // previewComponent.value = defineAsyncComponent(() =>
  //   import('@/views/comm/attach/preview.vue')
  // );
  //
  // // 传递参数给 preview.vue
  // previewProps.value = props;
  // previewDialog.visible = true;
};

/** 查询所属地、单井、片区列表 */
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaParentId = 0;
  operationAreaQuery.value.operationAreaType = 0;
  const res = await listOperationArea(operationAreaQuery.value);
  operationAreaList.value = res.rows;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  wellSealFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeApplicationDate.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WellSealVO[]) => {
  ids.value = selection.map((item) => item.wellId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 提交按钮 */
const submitForm = () => {
  wellSealFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.sealId) {
        await updateWellSeal(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWellSeal(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      permitDialog.visible = false;
      handoverDialog.visible = false;
      riskDialog.visible = false;
      startDialog.visible = false;

      await getList();
    }
  });
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epfj/wellSeal/export',
    {
      ...queryParams.value
    },
    `wellSeal_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getoperationAreaList();
  getList();
});
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  line-height: 18px;
  align-items: center;
}
</style>
