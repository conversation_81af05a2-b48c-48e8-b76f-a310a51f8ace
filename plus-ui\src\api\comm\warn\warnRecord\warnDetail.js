import request from '@/utils/request.js';

// 查询warnDetail列表
export function listWarnDetail(query) {
  return request({
    url: '/dbedit/warnDetail/list',
    method: 'get',
    params: query
  });
}

// 查询warnRecord详细
export function getWarnDetail(warnRecordId) {
  return request({
    url: '/dbedit/warnDetail/' + warnRecordId,
    method: 'get'
  });
}

// 新增warnRecord
export function addWarnDetail(data) {
  return request({
    url: '/dbedit/warnDetail',
    method: 'post',
    data: data
  });
}

// 修改warnRecord
export function updateWarnDetail(data) {
  return request({
    url: '/dbedit/warnDetail',
    method: 'put',
    data: data
  });
}

// 删除warnRecord
export function delWarnDetail(warnRecordId) {
  return request({
    url: '/dbedit/warnDetail/' + warnRecordId,
    method: 'delete'
  });
}
