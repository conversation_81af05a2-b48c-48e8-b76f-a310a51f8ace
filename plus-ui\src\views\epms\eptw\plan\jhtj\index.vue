<template>
  <div class="p-2">
    <div class="jhtjFull">
      <!-- <div slot="header" class="clearfix">
        <span>全部能耗统计情况</span>
      </div> -->
      <div>
        <el-form ref="form" :inline="true" :model="form" class="mt10 mb10">
          <!--          <el-form-item label="取水区域" prop="org">-->
          <!--            <el-select v-model="form.org" required placeholder="请选择取水区域">-->
          <!--              <el-option v-for="dict in getDeptOrganParkData"-->
          <!--                :key="dict.parkId" :label="dict.parkName" :value="dict.parkId" />-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <el-form-item label="区域" prop="org">
            <el-select v-model="form.org" placeholder="选择区域">
              <el-option
                v-for="deptment in getParkData"
                :key="deptment.operationAreaId"
                :label="deptment.operationAreaName"
                :value="deptment.operationAreaId"
              />
            </el-select>
          </el-form-item>
          <!--          <el-form-item label="区域">-->
          <!--            <el-select v-model="form.quKuaiId">-->
          <!--              <el-option :key="0" :value="0" label="全部"></el-option>-->
          <!--              <el-option v-for="item in quKuaiList" :key="item.value" :label="item.label" :value="item.value" />-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <el-form-item label="日期">
            <el-date-picker
              v-model="form.date"
              :model-value="form.date"
              format="YYYY-MM-DD"
              placeholder="选择日期"
              type="date"
              value-format="YYYY-MM-DD"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="指标类型">
            <el-select v-model="form.nengYuanId">
              <el-option v-for="item in nengYuanList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              v-hasPermi="['emcs:plan:quanBuNengHaoTongJi']"
              v-loading="dropDownLoading || nengYuanZongHaoLoading || leiJiLoading || qbLoading"
              :disabled="dropDownLoading || nengYuanZongHaoLoading || leiJiLoading || qbLoading"
              icon="Search"
              type="primary"
              @click="search"
              >查询</el-button
            >
          </el-form-item>
        </el-form>
        <el-row :gutter="10" style="height: 350px">
          <el-col v-loading="leiJiLoading" :span="24" style="height: 100%">
            <el-card v-if="leiJiData" class="leftDetailBox" style="height: 100%">
              <div>
                <el-row :gutter="20">
                  <el-col :span="9">
                    <div class="totalBox">
                      <div class="totalBoxHeader">
                        月累计
                        <span v-if="currentNengYuanName">({{ currentNengYuanName }})</span>
                      </div>
                      <div class="totalBoxCont">{{ leiJiData['yueLeiJi'] || 0 }}</div>
                    </div>
                  </el-col>
                  <el-col :span="15" class="totalDetail">
                    <div>
                      <span class="tit">月计划</span>:
                      <span class="num">{{ leiJiData['yueJiHua'] || 0 }}</span>
                      <span v-if="currentNengYuanName">({{ currentNengYuanName }})</span>
                    </div>
                    <div class="titProgress">
                      <span class="tit">月累计</span>:
                      <el-progress
                        :percentage="
                          leiJiData['yueJiHua'] && leiJiData['yueJiHua'] !== 0
                            ? Number(((leiJiData['yueLeiJi'] * 100) / leiJiData['yueJiHua']).toFixed(0))
                            : 0
                        "
                        :stroke-width="10"
                      ></el-progress>
                    </div>
                    <!--                    <div><span class="tit">环比</span>:-->
                    <!--                      <span class="num grow" :class="{'red':!isNaN(cpuYueHuanBi) && cpuYueHuanBi>0,'green':isNaN(cpuYueHuanBi) || cpuYueHuanBi<=0}">-->
                    <!--                        <i class="el-icon-top" v-if="!isNaN(cpuYueHuanBi) && cpuYueHuanBi>0"></i>-->
                    <!--                        <i class="el-icon-bottom" v-else></i>-->
                    <!--                        {{ cpuYueHuanBi }}%-->
                    <!--                      </span>-->
                    <!--                    </div>-->
                  </el-col>
                </el-row>
              </div>
              <div>
                <el-row :gutter="20">
                  <el-col :span="9">
                    <div class="totalBox">
                      <div class="totalBoxHeader" style="background: #e6a23c">
                        年累计
                        <span v-if="currentNengYuanName">({{ currentNengYuanName }})</span>
                      </div>
                      <div class="totalBoxCont">{{ leiJiData['nianLeiJi'] || 0 }}</div>
                    </div>
                  </el-col>
                  <el-col :span="15" class="totalDetail">
                    <div>
                      <span class="tit">年计划</span>:
                      <span class="num">{{ leiJiData['nianJiHua'] || 0 }}</span>
                      <span v-if="currentNengYuanName">({{ currentNengYuanName }})</span>
                    </div>
                    <div class="titProgress">
                      <span class="tit">年累计</span>:
                      <el-progress
                        :percentage="
                          leiJiData['nianJiHua'] && leiJiData['nianJiHua'] !== 0
                            ? Number(((leiJiData['nianLeiJi'] * 100) / leiJiData['nianJiHua']).toFixed(0))
                            : 0
                        "
                        :stroke-width="10"
                      ></el-progress>
                    </div>
                    <div>
                      <!--                      <span class="tit">环比</span>:-->
                      <!--                      <span class="num grow" :class="{'red':!isNaN(cpuNianHuanBi) && cpuNianHuanBi>0,'green':isNaN(cpuNianHuanBi) || cpuNianHuanBi<=0}">-->
                      <!--                        <i class="el-icon-top" v-if="!isNaN(cpuNianHuanBi) && cpuNianHuanBi>0"></i>-->
                      <!--                        <i class="el-icon-bottom" v-else></i>-->
                      <!--                        {{ cpuNianHuanBi }}%-->
                      <!--                      </span>-->
                      <!-- <span class="num grow green" v-else>
                        <i class="el-icon-bottom"></i>
                        {{ cpuNianHuanBi }}%
                      </span> -->
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
          <!--          <el-col :span="10" style="height:100%;">-->
          <!--            <el-card style="height:100%;" class="showEchartBox" v-loading="nengYuanZongHaoLoading">-->
          <!--              <div style="width:100%; height:100%; margin: 0 auto;" id="main" ref="main"></div>-->
          <!--            </el-card>-->
          <!--          </el-col>-->
        </el-row>
        <el-card class="mt10">
          <qbnhtj :fatherQueryForm="form" :currentNengYuanName="currentNengYuanName" ref="qbnhtj" @loadingFinish="handleLoadingFinish"></qbnhtj>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import qbnhtj from './pages/qbnhtj.vue';

import { queryNYJHIndicatorByDate, queryQuShuiPlanIndicator, totalEnergyConsumption } from '@/api/epms/eptw/jhtj/jhtj.js';
import { selectQuYuList } from '@/api/epms/eptw/plan/plan.js';
import moment from 'dayjs';

export default {
  name: 'jhtj',

  components: {
    qbnhtj
  },
  computed: {
    //年计划
    cpuNianHuanBi: function () {
      if (this.leiJiData['nianHuanBi'] && this.leiJiData['nianHuanBi'] === -1) {
        return '--';
      } else {
        return this.leiJiData['nianHuanBi'] || 0;
      }
    },
    //月计划
    cpuYueHuanBi: function () {
      if (this.leiJiData['yueJiHua'] && this.leiJiData['yueJiHua'] === -1) {
        return '--';
      } else {
        return this.leiJiData['yueJiHua'] || 0;
      }
    }
  },
  data() {
    let that = this;
    return {
      // 时间插件限制范围(只能选择2022之后的)
      // pickerOptions: {
      //   disabledDate(time) {
      //     var a = moment(time);
      //     var b = moment(that.dict.type.biz_startDate[0]['label']);
      //     return a.diff(b, "days") < 0 || time.getTime() >= Date.now()
      //   }
      // },

      // 累计用的能源显示名（不能用计算属性，由查询触发）
      currentNengYuanName: '取水指标',
      leiJiLoading: false,
      // 所有下拉菜单基础数据的loading状态开关
      dropDownLoading: false,
      // 能源总耗loading状态开关
      nengYuanZongHaoLoading: false,
      // 子组件
      qbLoading: false,

      // 搜索条件
      form: {
        org: 0,
        nengYuanId: 1, //取水指标
        indicatorTypeId: 96,
        indicatorPlanType: 1,
        date: moment().format('YYYY-MM-DD')
      },
      // 累计（左上角）所用的数据
      leiJiData: {},
      // 能源类型下拉的数据
      nengYuanList: [],
      // 能源总耗echart实例
      echartItem: null,
      // 能源总耗的数据
      nengYuanZongHaoData: [],
      // 取水区域下拉框列表
      getParkData: []
    };
  },
  mounted() {
    // 加载下拉
    this.initData();
    // 监听尺寸变化，刷新图表
    let that = this;
    window.addEventListener('resize', function () {
      if (that.echartItem) {
        that.echartItem.resize();
      }
    });
  },
  methods: {
    async initData() {
      await this.getParkList();
      await this.loadNengYuanType();
      await this.loadLeiJi();
      setTimeout(() => {
        // 加载数据
        // this.showEcharts();
        this.setCurrentNengYuanName();
        this.$refs.qbnhtj.showChart();
      }, 1000);
    },
    /** 监听子组件加载完毕 */
    handleLoadingFinish(obj) {
      if (obj) {
        this[obj['type']] = obj['loading'];
      }
    },
    setCurrentNengYuanName() {
      if (this.nengYuanList && this.nengYuanList.length > 0) {
        this.currentNengYuanName = this.nengYuanList.filter((item) => item['value'] === this.form.nengYuanId)[0]['label'];
      } else {
        this.currentNengYuanName = '';
      }
    },

    /** 取消所有饼图上的高亮 */
    downplayAll() {
      for (let i = 0; i < this.nengYuanZongHaoData.length; i++) {
        this.echartItem.dispatchAction({
          type: 'downplay', //取消突出高亮显示;
          seriesIndex: 0,
          dataIndex: i
        });
      }
      // 清理事件
      this.echartItem.off('globalout');
    },
    /** 返回能源类型在能源列表中的下标，用于高亮饼图 */
    getNengYuanDataIdx() {
      let rs = -1;
      if (this.form.nengYuanId > 0) {
        for (const key in this.nengYuanZongHaoData) {
          if (this.nengYuanZongHaoData[key]['indicatorId'] === this.form.nengYuanId) {
            rs = key;
            break;
          }
        }
      }
      return rs;
    },
    /** 查询 */
    search() {
      this.loadLeiJi();
      // this.showEcharts()
      this.setCurrentNengYuanName();
      setTimeout(() => {
        this.$refs.qbnhtj.showChart();
      }, 1000);
    },
    /** 设置能源总耗的高亮 */
    setNengYuanZongHaoHightLight(dataIdx) {
      //突出高亮显示;
      this.echartItem.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: dataIdx
      });
      this.echartItem.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex: dataIdx,
        position: ['38%', '45%']
      });
    },
    /** 显示能源总耗概况 */
    async showEcharts() {
      try {
        this.nengYuanZongHaoLoading = true;
        this.nengYuanZongHaoData = [];
        let rp = await queryNYJHIndicatorByDate({ dateTime: this.form.date, danwei: this.form.org, qukuai: this.form.quKuaiId });
        if (rp['data']) {
          for (const key in rp['data']) {
            this.nengYuanZongHaoData.push({
              value: rp['data'][key]['value'],
              name: rp['data'][key]['name'],
              indicatorId: Number(key)
            });
          }
        }

        let option = {
          title: {
            text: '能源总耗概况',
            left: 'center'
          },
          tooltip: {
            trigger: 'item'
          },
          // legend: {
          //   left: 'center',
          //   top: 'bottom'
          // },
          // label: {
          //   formatter: '{b}({c})'
          // },
          // 高亮样式
          emphasis: {
            label: {
              color: 'red'
            }
          },
          series: [
            {
              type: 'pie',
              radius: ['35%', '55%'],
              data: this.nengYuanZongHaoData,
              minAngle: 2,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };

        this.echartItem = this.$echarts.getInstanceByDom(this.$refs.main);
        if (!this.echartItem) {
          this.echartItem = this.$echarts.init(this.$refs.main);
        }
        this.echartItem.setOption(option, true);

        // 清理高亮
        this.downplayAll();
        if (this.form.nengYuanId > 0) {
          let dataIdx = this.getNengYuanDataIdx();
          if (dataIdx > -1) {
            this.setNengYuanZongHaoHightLight(dataIdx);
            let that = this;
            // 鼠标离开整个图标之后的保持高亮
            this.echartItem.on('globalout', function (e) {
              that.setNengYuanZongHaoHightLight(dataIdx);
            });
          }
        }
      } catch (e) {
        this.$modal.msgError(`获取能源总耗概况的数据失败，${e}`);

        if (this.echartItem) {
          this.echartItem.clear();
        }
        return;
      } finally {
        this.nengYuanZongHaoLoading = false;
      }
    },
    /** 获取取水区域下拉框列表 */
    async getParkList() {
      await selectQuYuList({ operationAreaType: 1 }).then((res) => {
        if (res.code === 200) {
          this.getParkData = res.rows;
          this.form.org = this.getParkData[0].operationAreaId;
        } else {
          this.$modal.msgError('获取数据失败');
        }
      });
    },
    /** 读取能源类型下拉数据 */
    async loadNengYuanType() {
      try {
        this.dropDownLoading = true;
        await queryQuShuiPlanIndicator().then((data) => {
          if (data) {
            this.nengYuanList = [];
            for (const item of data['data']) {
              this.nengYuanList.push({
                label: item['aliasName'],
                value: item['indicatorId']
              });
            }
          }
        });
        this.dropDownLoading = false;
      } catch (e) {
        ElMessage.error('获取能源类型的下拉信息失败');
        this.dropDownLoading = false;
      }
    },
    /** 左上角的累计 */
    async loadLeiJi() {
      try {
        this.leiJiLoading = true;
        await totalEnergyConsumption({
          danwei: this.form.org,
          indicatorId: this.form.nengYuanId,
          dateTime: this.form.date,
          indicatorTypeId: this.form.indicatorTypeId,
          indicatorPlanType: this.form.indicatorPlanType
        }).then((res) => {
          if (res['code'] === 200) {
            this.leiJiData = res['data'];
          }
        });
      } catch (e) {
        this.$modal.msgError(`左上角的累计数据获取异常,${e}`);
      } finally {
        this.leiJiLoading = false;
      }
    }
  }
};
</script>
<style scoped lang="scss">
.jhtjFull {
  height: 100%;
  & > .el-card__body {
    height: calc(100% - 50px);
    overflow-y: auto;
  }
  .totalBox {
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;
    .totalBoxHeader {
      padding: 20px 0;
      background: #67c23a;
      color: #fff;
      text-align: center;
    }
    .totalBoxCont {
      padding: 20px 0;
      text-align: center;
      font-size: 18px;
    }
  }
  .totalDetail {
    & > div {
      padding: 10px 5px;
    }
    .tit {
      width: 4em;
      //color: #606266;
      display: inline-block;
      text-align: justify;
      text-align-last: justify;
      font-size: 13px;
    }
    .titProgress {
      display: flex;
      .el-progress {
        width: calc(100% - 7em);
        margin-left: 5px;
      }
    }
    .num {
      padding: 0 10px;
      color: #409eff;
    }
  }
}
</style>
<style lang="scss">
.jhtjFull {
  height: 100%;
  & > .el-card__body {
    height: calc(100% - 50px);
    overflow-y: auto;
  }
}
.leftDetailBox {
  height: 100%;
  .el-card__body {
    display: flex;
    flex-direction: column;
    height: 100%;
    & > div {
      flex: auto;
      display: flex;
      align-items: center;
      & > div {
        flex: 1;
      }
    }
    & > div:first-child {
      border-bottom: 1px solid #eee;
    }
  }
}
.showEchartBox {
  .el-card__body {
    height: 100%;
  }
}
</style>
