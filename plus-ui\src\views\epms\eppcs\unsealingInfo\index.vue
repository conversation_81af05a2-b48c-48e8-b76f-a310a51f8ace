<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :inline="true" :model="queryParams" label-width="100px">
            <el-form-item label="检测项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" clearable placeholder="请输入检测项目名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="样品名称" prop="sampleName">
              <el-input v-model="queryParams.sampleName" clearable placeholder="请输入样品名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="样品编号" prop="sampleNumber">
              <el-input v-model="queryParams.sampleNumber" clearable placeholder="请输入样品编号" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="拆封状态" prop="unsealingStatus">
              <el-select v-model="queryParams.unsealingStatus" clearable placeholder="请选择拆封状态">
                <el-option v-for="dict in eppcs_unsealing_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:unsealingInfo:add']" icon="Plus" plain type="primary" @click="handleAdd">填报拆封</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:unsealingInfo:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:unsealingInfo:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:unsealingInfo:export']" icon="Download" plain type="warning" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="unsealingInfoList" border stripe @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column
          :index="(index) => (queryParams.pageNum - 1) * queryParams.pageSize + index + 1"
          align="center"
          label="序号"
          type="index"
          width="55"
        />
        <el-table-column align="center" label="检测项目名称" prop="projectName" show-overflow-tooltip />
        <el-table-column align="center" label="样品名称" prop="sampleName" show-overflow-tooltip />
        <el-table-column align="center" label="样品编号" prop="sampleNumber" show-overflow-tooltip />
        <el-table-column align="center" label="实验室内样品密封条照片" prop="unsealPhoto" width="120">
          <template #default="scope">
            <el-button v-if="scope.row.unsealPhoto" icon="Document" link type="info" @click="handlePreview(scope.row)">查看</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="拆封时间" prop="unsealTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.unsealTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="拆封人" prop="unsealer" show-overflow-tooltip />
        <el-table-column align="center" label="拆封状态" prop="unsealingStatus" width="100">
          <template #default="scope">
            <dict-tag :options="eppcs_unsealing_status" :value="scope.row.unsealingStatus" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="审批时间" prop="approvalTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.approvalTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="审批意见" prop="approvalRemark" show-overflow-tooltip />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button icon="View" link type="primary" @click="handleView(scope.row)">查看</el-button>
            <el-tooltip v-if="[0, 1].includes(scope.row.unsealingStatus)" content="修改" placement="top">
              <el-button v-hasPermi="['eppcs:unsealingInfo:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"> 修改 </el-button>
            </el-tooltip>
            <el-tooltip v-if="[0, 1].includes(scope.row.unsealingStatus)" content="提交" placement="top">
              <el-button v-hasPermi="['eppcs:unsealingInfo:submit']" icon="Upload" link type="success" @click="handleSubmit(scope.row)">
                提交
              </el-button>
            </el-tooltip>

            <el-tooltip v-if="canConfirm(scope.row)" content="确认" placement="top">
              <el-button v-hasPermi="['eppcs:unsealingInfo:confirm']" icon="Check" link type="warning" @click="handleConfirm(scope.row)">
                确认
              </el-button>
            </el-tooltip>

            <el-tooltip v-if="[0, 1].includes(scope.row.unsealingStatus)" content="删除" placement="top">
              <el-button v-hasPermi="['eppcs:unsealingInfo:remove']" icon="Delete" link type="danger" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改拆封信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" append-to-body width="800px">
      <el-form ref="unsealingInfoFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="取样申请" prop="applicationId">
              <el-select
                v-model="form.applicationId"
                :disabled="isViewMode"
                placeholder="请选择取样申请"
                style="width: 100%"
                @change="handleApplicationChange"
              >
                <el-option
                  v-for="app in applicationOptions"
                  :key="app.applicationId"
                  :label="`${app.projectName} - ${app.samplingPoint}`"
                  :value="app.applicationId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品名称" prop="sampleName">
              <el-input v-model="form.sampleName" :disabled="isViewMode" placeholder="请输入样品名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="样品编号" prop="sampleNumber">
              <el-input v-model="form.sampleNumber" :disabled="isViewMode" placeholder="请输入样品编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="拆封时间" prop="unsealTime">
              <el-date-picker
                v-model="form.unsealTime"
                :disabled="isViewMode"
                placeholder="请选择拆封时间"
                style="width: 100%"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="拆封人" prop="unsealer">
              <el-input v-model="form.unsealer" :disabled="isViewMode" placeholder="请输入拆封人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="实验室内样品密封条照片" prop="unsealPhoto">
          <attachFileUpload
            v-model="form.unsealPhoto"
            :attach-source-id="form.unsealingId"
            :disabled="isViewMode"
            :file-size="50"
            :limit="10"
            attach-category="unseal"
            attach-source-type="unsealingInfo"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" :disabled="isViewMode" :rows="3" placeholder="请输入备注信息" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!isViewMode" :loading="buttonLoading" type="primary" @click="submitForm('save')">保存</el-button>
          <el-button
            v-if="!isViewMode && (form.unsealingStatus === 0 || form.unsealingStatus === 1)"
            :loading="buttonLoading"
            type="success"
            @click="submitForm('submit')"
            >提交</el-button
          >
          <el-button @click="cancel">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 确认对话框 -->
    <el-dialog v-model="confirmDialog.visible" append-to-body title="确认拆封信息" width="700px">
      <el-form ref="confirmFormRef" :model="form" :rules="confirmRules" label-width="120px">
        <el-card shadow="never" style="margin-bottom: 20px">
          <template #header>
            <span style="font-weight: bold">拆封信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="样品名称">
                <span>{{ form.sampleName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="样品编号">
                <span>{{ form.sampleNumber }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="拆封时间">
                <span>{{ parseTime(form.unsealTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="拆封人">
                <span>{{ form.unsealer }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="当前状态">
                <dict-tag :options="eppcs_unsealing_status" :value="form.unsealingStatus" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item v-if="form.remark" label="备注信息">
            <span>{{ form.remark }}</span>
          </el-form-item>
          <el-form-item label="实验室内样品密封条照片" prop="unsealPhoto">
            <attachFileUpload
              v-model="form.unsealPhoto"
              :attach-source-id="form.unsealingId"
              :disabled="true"
              attach-category="unseal"
              attach-source-type="unsealingInfo"
            />
          </el-form-item>
        </el-card>

        <el-form-item label="审批意见" prop="approvalRemark">
          <el-input v-model="form.approvalRemark" :rows="4" placeholder="请输入审批意见（必填）" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="confirmLoading" type="success" @click="handleConfirmSubmit(1)">确认</el-button>
          <el-button :loading="confirmLoading" type="danger" @click="handleConfirmSubmit(2)">驳回</el-button>
          <el-button @click="confirmDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" name="UnsealingInfo" setup>
import {
  addUnsealingInfo,
  confirmUnsealingInfo,
  delUnsealingInfo,
  getUnsealingInfo,
  listUnsealingInfo,
  submitUnsealingInfo,
  updateUnsealingInfo
} from '@/api/epms/eppcs/unsealingInfo';
import { UnsealingInfoForm, UnsealingInfoQuery, UnsealingInfoVO } from '@/api/epms/eppcs/unsealingInfo/types';
import { listSamplingApplication } from '@/api/epms/eppcs/samplingApplication';
import dayjs from 'dayjs';
import { useUserStore } from '@/store/modules/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eppcs_unsealing_status } = toRefs<any>(proxy?.useDict('eppcs_unsealing_status'));

const unsealingInfoList = ref<UnsealingInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const unsealingInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 确认相关
const confirmDialog = reactive({
  visible: false
});
const confirmFormRef = ref<ElFormInstance>();
const confirmLoading = ref(false);
const confirmRules = reactive({
  approvalRemark: [{ required: true, message: '审批意见不能为空', trigger: 'blur' }]
});

// 取样申请选项
const applicationOptions = ref<any[]>([]);

// 表单状态
const isNewForm = computed(() => !form.value.unsealingId);
const isViewMode = ref(false);

const initFormData: UnsealingInfoForm = {
  unsealingId: undefined,
  applicationId: undefined,
  sampleName: undefined,
  sampleNumber: undefined,
  unsealPhoto: undefined,
  unsealTime: undefined,
  unsealer: undefined,
  unsealingStatus: 0, // 默认草稿状态
  remark: undefined,
  approvalTime: undefined,
  approvalRemark: undefined
};

const data = reactive<PageData<UnsealingInfoForm, UnsealingInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    sampleName: undefined,
    sampleNumber: undefined,
    unsealingStatus: undefined,
    params: {}
  },
  rules: {
    applicationId: [{ required: true, message: '取样申请不能为空', trigger: 'change' }],
    sampleName: [{ required: true, message: '样品名称不能为空', trigger: 'blur' }],
    sampleNumber: [{ required: true, message: '样品编号不能为空', trigger: 'blur' }],
    unsealTime: [{ required: true, message: '拆封时间不能为空', trigger: 'change' }],
    unsealer: [{ required: true, message: '拆封人不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询拆封信息列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listUnsealingInfo(queryParams.value);
    const unsealingList = res.rows || [];

    // 收集申请ID并批量查询申请信息
    if (unsealingList.length > 0) {
      const applicationIds = [...new Set(unsealingList.map((item) => item.applicationId).filter((id) => id))];

      if (applicationIds.length > 0) {
        try {
          // 批量查询取样申请信息
          const applicationRes = await listSamplingApplication({
            applicationIds: applicationIds.join(','),
            pageNum: 1,
            pageSize: applicationIds.length
          });

          const applicationMap = new Map();
          (applicationRes.rows || []).forEach((app) => {
            applicationMap.set(app.applicationId, app);
          });

          // 填充项目名称
          unsealingList.forEach((item) => {
            const application = applicationMap.get(item.applicationId);
            if (application) {
              item.projectName = application.projectName;
            }
          });
        } catch (error) {
          console.warn('查询申请信息失败:', error);
        }
      }
    }

    unsealingInfoList.value = unsealingList;
    total.value = res.total;
  } catch (error) {
    console.error('查询列表失败:', error);
    proxy?.$modal.msgError('查询列表失败');
  } finally {
    loading.value = false;
  }
};

/** 取消按钮 */
const cancel = () => {
  reset();
  isViewMode.value = false;
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  unsealingInfoFormRef.value?.resetFields();
  getApplicationList();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: UnsealingInfoVO[]) => {
  ids.value = selection.map((item) => item.unsealingId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = async () => {
  reset();
  isViewMode.value = false;

  // 设置默认值
  form.value.unsealTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  form.value.unsealingStatus = 0; // 草稿状态

  // 获取当前用户信息作为拆封人
  try {
    const userProfile = useUserStore();
    if (userProfile.nickname) {
      form.value.unsealer = userProfile.nickname;
    }
  } catch (error) {
    console.warn('获取用户信息失败:', error);
  }

  dialog.visible = true;
  dialog.title = '填报拆封信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: UnsealingInfoVO) => {
  reset();
  isViewMode.value = false;
  const _unsealingId = row?.unsealingId || ids.value[0];
  const res = await getUnsealingInfo(_unsealingId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改拆封信息';
};

/** 提交按钮 */
const submitForm = (action: 'save' | 'submit' = 'save') => {
  unsealingInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        let unsealingId = form.value.unsealingId;

        if (unsealingId) {
          // 修改
          await updateUnsealingInfo(form.value);
        } else {
          // 新增
          const res = await addUnsealingInfo(form.value);
          unsealingId = res.data; // 直接获取返回的ID
          form.value.unsealingId = unsealingId;
        }

        // 如果是提交操作，调用提交接口
        if (action === 'submit' && unsealingId) {
          await submitUnsealingInfo(unsealingId, 1);
        }
        proxy?.$modal.msgSuccess('操作成功');

        dialog.visible = false;
        await getList();
      } catch (error) {
        console.error('操作失败:', error);
        proxy?.$modal.msgError('操作失败');
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: UnsealingInfoVO) => {
  const _unsealingIds = row?.unsealingId || ids.value;
  await proxy?.$modal.confirm('是否确认删除拆封信息编号为"' + _unsealingIds + '"的数据项？').finally(() => (loading.value = false));
  await delUnsealingInfo(_unsealingIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/eppcs/unsealingInfo/export',
    {
      ...queryParams.value
    },
    `unsealingInfo_${new Date().getTime()}.xlsx`
  );
};

/** 判断是否可以确认 */
const canConfirm = (row: UnsealingInfoVO) => {
  // 待确认状态：2
  return row.unsealingStatus === 2;
};

/** 跳转预览 */
const handlePreview = async (row?: UnsealingInfoVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.unsealingId,
    attachSourceType: 'unsealingInfo',
    attachCategory: 'general'
  });
};

/** 提交拆封信息 */
const handleSubmit = async (row: UnsealingInfoVO) => {
  try {
    await proxy?.$modal.confirm('确认提交该拆封信息吗？提交后将进入确认流程，无法修改。');
    await submitUnsealingInfo(row.unsealingId, 1);
    proxy?.$modal.msgSuccess('提交成功');
    await getList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error);
      proxy?.$modal.msgError('提交失败');
    }
  }
};

/** 确认拆封信息 */
const handleConfirm = (row: UnsealingInfoVO) => {
  form.value = { ...row };
  confirmDialog.visible = true;
};

/** 提交确认 */
const handleConfirmSubmit = async (action: number) => {
  if (!confirmFormRef.value) return;

  const valid = await confirmFormRef.value.validate();
  if (!valid) return;

  try {
    confirmLoading.value = true;

    // 获取当前用户信息作为审批人
    const user = useUserStore();
    const approver = user?.nickname || user?.name || '当前用户';

    await confirmUnsealingInfo(
      form.value.unsealingId,
      1, // 用户类型，这里默认为1
      action,
      form.value.approvalRemark,
      approver
    );

    const actionText = action === 1 ? '确认' : '驳回';
    proxy?.$modal.msgSuccess(`${actionText}成功`);
    confirmDialog.visible = false;
    await getList();
  } catch (error) {
    console.error('确认失败:', error);
    proxy?.$modal.msgError('确认失败');
  } finally {
    confirmLoading.value = false;
  }
};

/** 获取取样申请列表 */
const getApplicationList = async () => {
  try {
    // 获取阶段为2（样品拆封）的申请
    const res = await listSamplingApplication({ currentPhase: 3, pageSize: -1 });
    applicationOptions.value = res.rows || [];
  } catch (error) {
    console.error('获取取样申请列表失败:', error);
  }
};

/** 查看详情 */
const handleView = (row: UnsealingInfoVO) => {
  getUnsealingInfo(row.unsealingId).then((response) => {
    form.value = response.data;
    isViewMode.value = true;
    dialog.visible = true;
    dialog.title = '查看拆封信息详情';
  });
};

/** 处理取样申请选择变化 */
const handleApplicationChange = (applicationId: string | number) => {
  const selectedApp = applicationOptions.value.find((app) => app.applicationId === applicationId);
  if (selectedApp) {
    // 自动填充样品名称等信息
    form.value.projectName = selectedApp.projectName;
  }
};

onMounted(async () => {
  await getApplicationList();
  await getList();
});
</script>
