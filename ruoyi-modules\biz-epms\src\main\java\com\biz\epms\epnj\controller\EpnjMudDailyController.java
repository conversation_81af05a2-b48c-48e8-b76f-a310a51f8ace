package com.biz.epms.epnj.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.biz.epms.epfy.domain.bo.EpfyTransportApplicationBo;
import com.biz.epms.epfy.domain.bo.EpfyTransportRecordBo;
import com.biz.epms.epfy.domain.vo.EpfyTransportApplicationVo;
import com.biz.epms.epfy.domain.vo.EpfyTransportRecordVo;
import com.biz.epms.epfy.service.IEpfyTransportApplicationService;
import com.biz.epms.epfy.service.IEpfyTransportRecordService;
import com.biz.epms.epnj.domain.bo.EpnjDrillingDailyBo;
import com.biz.epms.epnj.domain.bo.EpnjMudDailyBo;
import com.biz.epms.epnj.domain.vo.EpnjDrillingDailyVo;
import com.biz.epms.epnj.domain.vo.EpnjMudDailyVo;
import com.biz.epms.epnj.domain.vo.EpnjWellPreparationVo;
import com.biz.epms.epnj.service.IEpnjMudDailyService;
import com.biz.epms.epnj.service.IEpnjWellPreparationService;
import com.biz.epms.epnj.service.impl.EpnjDrillingDailyServiceImpl;
import com.biz.epms.eptw.domain.vo.EptwOperationAreaVo;
import com.biz.epms.eptw.service.IEptwOperationAreaService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 泥浆日报
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/epms/mudDaily")
public class EpnjMudDailyController extends BaseController {

    private final IEpnjMudDailyService epnjMudDailyService;

    @Autowired
    private IEpfyTransportRecordService epfyTransportRecordService;

    @Autowired
    private IEpfyTransportApplicationService epfyTransportApplicationService;
    @Autowired
    private EpnjDrillingDailyServiceImpl epnjDrillingDailyServiceImpl;
    @Autowired
    private IEpnjWellPreparationService epnjWellPreparationService;
    @Autowired
    private IEptwOperationAreaService eptwOperationAreaService;

    /**
     * 查询泥浆日报列表
     */
    @SaCheckPermission("epms:mudDaily:list")
    @GetMapping("/list")
    public TableDataInfo<EpnjMudDailyVo> list(EpnjMudDailyBo bo, PageQuery pageQuery) {
        return epnjMudDailyService.queryPageList(bo, pageQuery);
    }


    /**
     * 查询泥浆日报统计
     */
    @GetMapping("/getMudDailyStatistics")
    public R<List<EpnjMudDailyVo>> getMudDailyStatistics(EpnjMudDailyBo bo, PageQuery pageQuery) {
        TableDataInfo<EpnjMudDailyVo> mudDailys = epnjMudDailyService.queryPageList(bo, pageQuery);

        List<EpnjMudDailyVo> list = new ArrayList<>();
        for (EpnjMudDailyVo mudDaily : mudDailys.getRows()) {
            EpfyTransportApplicationBo appBo = new EpfyTransportApplicationBo();
            appBo.setFlowType(2);
            appBo.setRelatedId(mudDaily.getDisposeId());
            appBo.setUnloadLocationType(mudDaily.getDisposeType());
            List<EpfyTransportApplicationVo> applications = epfyTransportApplicationService.queryList(appBo);
            // 申请
            for (EpfyTransportApplicationVo application : applications) {
                EpfyTransportRecordBo recordBo = new EpfyTransportRecordBo();
                recordBo.setApplicationId(application.getAppId());
                List<EpfyTransportRecordVo> records = epfyTransportRecordService.queryList(recordBo);
                BigDecimal sum = BigDecimal.ZERO;
                for (EpfyTransportRecordVo record : records) {
                    sum = sum.add(record.getNumber());
                }
                EpnjMudDailyVo mudDaily1 = mudDaily;
                mudDaily1.setStartAddress(records.getFirst().getDeparturePoint());// 起运点
                mudDaily1.setMediumCategory(records.getFirst().getMediumCategory()); // 介质类别
                mudDaily1.setEndAddress(records.getFirst().getArrivalPoint()); // 卸液点
                mudDaily1.setNumber(sum);
                list.add(mudDaily1);
            }
        }
        return R.ok(list);
    }


    /**
     * 导出泥浆日报列表
     */
    @SaCheckPermission("epms:mudDaily:export")
    @Log(title = "泥浆日报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EpnjMudDailyBo bo, HttpServletResponse response) {
        List<EpnjMudDailyVo> list = epnjMudDailyService.queryList(bo);
        ExcelUtil.exportExcel(list, "泥浆日报", EpnjMudDailyVo.class, response);
    }

    /**
     * 获取泥浆日报详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("epms:mudDaily:query")
    @GetMapping("/{id}")
    public R<EpnjMudDailyVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(epnjMudDailyService.queryById(id));
    }

    /**
     * 新增泥浆日报
     */
    @SaCheckPermission("epms:mudDaily:add")
    @Log(title = "泥浆日报", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody EpnjMudDailyBo bo) throws ParseException {
        if (bo.getId() != null) {
            EpnjMudDailyBo bo1 = epnjMudDailyService.getLoss(bo);
            return toAjax(epnjMudDailyService.updateByBo(bo1));
        }
        EpnjMudDailyBo bo1 = epnjMudDailyService.getLoss(bo);
        return toAjax(epnjMudDailyService.insertByBo(bo1));
    }

    /**
     * 修改泥浆日报
     */
    @SaCheckPermission("epms:mudDaily:edit")
    @Log(title = "泥浆日报", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EpnjMudDailyBo bo) throws ParseException {
        EpnjMudDailyBo bo1 = epnjMudDailyService.getLoss(bo);
        return toAjax(epnjMudDailyService.updateByBo(bo1));
    }

    /**
     * 删除泥浆日报
     *
     * @param ids 主键串
     */
    @SaCheckPermission("epms:mudDaily:remove")
    @Log(title = "泥浆日报", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(epnjMudDailyService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 重新计算
     */
    @GetMapping("/reCompute")
    public void reCompute(@RequestParam(value = "date", required = false) String date) {
        epnjMudDailyService.calcTransportQuantity(date);
    }

    @GetMapping("/getDailyDate")
    public TableDataInfo<EpnjMudDailyVo> getDailyDate(EpnjMudDailyBo bo, PageQuery pageQuery) {

        TableDataInfo<EpnjMudDailyVo> result = epnjMudDailyService.queryPageList(bo, pageQuery);
        List<EpnjMudDailyVo> mudDailys = result.getRows();
        List<EpnjMudDailyVo> processedList = new ArrayList<>();
        for (EpnjMudDailyVo mudDaily : mudDailys) {
            //处理数据中总拉运量和损耗为空的情况，置为0
            if(mudDaily.getTotalPullingAmount() == null){
                mudDaily.setTotalPullingAmount(BigDecimal.ZERO.setScale(1, RoundingMode.HALF_UP));
            }
            if (mudDaily.getLoss() == null){
                mudDaily.setLoss(BigDecimal.ZERO.setScale(1, RoundingMode.HALF_UP));
            }
            if (mudDaily.getMudStagingAmount() == null){
                mudDaily.setMudStagingAmount(BigDecimal.ZERO.setScale(1, RoundingMode.HALF_UP));
            }
            if (mudDaily.getWaterStagingAmount() == null){
                mudDaily.setWaterStagingAmount(BigDecimal.ZERO.setScale(1, RoundingMode.HALF_UP));
            }
            if (mudDaily.getSlurryStagingAmount() == null){
                mudDaily.setSlurryStagingAmount(BigDecimal.ZERO.setScale(1, RoundingMode.HALF_UP));
            }
            if (mudDaily.getDisposeType() == 1) { // 集中站处理

                EptwOperationAreaVo operationArea = eptwOperationAreaService.queryById(mudDaily.getDisposeId());
                if (operationArea == null) {
                    processedList.add(mudDaily);
                    continue;
                }
                EpfyTransportApplicationBo applicationBo = new EpfyTransportApplicationBo();
                applicationBo.setUnloadLocation(operationArea.getOperationAreaName());
                applicationBo.setMediumCategory(3); // 混合相
                List<EpfyTransportApplicationVo> applications = epfyTransportApplicationService.queryList(applicationBo);
                List<Map<String, Object>> dataList = new ArrayList<>();
                for (EpfyTransportApplicationVo application : applications) {
                    EpfyTransportRecordBo recordBo = new EpfyTransportRecordBo();
                    recordBo.setApplicationId(application.getAppId());
                    LocalDate localDate = LocalDate.parse(mudDaily.getDate());
                    Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                    recordBo.setTransportTime(date);
                    List<EpfyTransportRecordVo> records = epfyTransportRecordService.queryList(recordBo);

                    // 按照名称进行分组，单个时间，单个名称只需要一条数据
                    Map<String, List<EpfyTransportRecordVo>> groupedByWell = records.stream()
                            .collect(Collectors.groupingBy(EpfyTransportRecordVo::getDeparturePoint));

                    for (String wellName : groupedByWell.keySet()) {
                        EpfyTransportRecordVo record = groupedByWell.get(wellName).getFirst();
                        Map<String, Object> map = new HashMap<>();
                        map.put("wellName", wellName);
                        EpnjDrillingDailyBo epnjDrillingDailyBo = new EpnjDrillingDailyBo();
                        epnjDrillingDailyBo.setDate(date);
                        epnjDrillingDailyBo.setWellNumber(record.getDeparturePoint());
                        List<EpnjDrillingDailyVo> epnjDrillingDailyVos = epnjDrillingDailyServiceImpl.queryList(epnjDrillingDailyBo);
                        if (!epnjDrillingDailyVos.isEmpty()) {
                            EpnjDrillingDailyVo epnjDrillingDailyVo = epnjDrillingDailyVos.getFirst();
                            map.put("drillingCompany", epnjDrillingDailyVo.getDrillingCompany());
                            map.put("wellType", epnjDrillingDailyVo.getWellType());
                            map.put("spudDate", epnjDrillingDailyVo.getSpudDate());
                        }
                        if (!dataList.contains(map)) {
                            dataList.add(map);
                        }
                    }
                }

                if(!dataList.isEmpty()){
                    EpnjMudDailyVo mudDailyVo = new EpnjMudDailyVo();
                    BeanUtils.copyProperties(mudDaily,mudDailyVo);
                    mudDailyVo.setMudPullingAmount(null);
                    mudDailyVo.setMudStagingAmount(null);
                    mudDailyVo.setWaterStagingAmount(null);
                    mudDailyVo.setWaterPullingAmount(null);
                    mudDailyVo.setTotalPullingAmount(null);
                    mudDailyVo.setLoss(null);
                    mudDailyVo.setSlurryStagingAmount(null);
                    processedList.add(mudDailyVo);
                    mudDailyVo.setWellList(dataList);
                }
                processedList.add(mudDaily);
            } else if (mudDaily.getDisposeType() == 2) { // 随钻处理
                if(mudDaily.getWaterStagingAmount() == null){
                    mudDaily.setWaterStagingAmount(BigDecimal.ZERO.setScale(1, RoundingMode.HALF_UP));
                }
                if (mudDaily.getMudStagingAmount() == null){
                    mudDaily.setMudStagingAmount(BigDecimal.ZERO.setScale(1, RoundingMode.HALF_UP));
                }
                EpnjDrillingDailyBo epnjDrillingDailyBo = new EpnjDrillingDailyBo();
                // 根据时间，井名查询钻井系统列表数据
                LocalDate localDate = LocalDate.parse(mudDaily.getDate());
                Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                epnjDrillingDailyBo.setDate(date);
                EpnjWellPreparationVo epnjWellPreparationVo = epnjWellPreparationService.queryById(mudDaily.getDisposeId());
                if (epnjWellPreparationVo == null) {
                    processedList.add(mudDaily);
                    continue;
                }
                epnjDrillingDailyBo.setWellNumber(epnjWellPreparationVo.getWellName());

                List<EpnjDrillingDailyVo> epnjDrillingDailyVos = epnjDrillingDailyServiceImpl.queryList(epnjDrillingDailyBo);
                if (!epnjDrillingDailyVos.isEmpty()) {
                    EpnjDrillingDailyVo epnjDrillingDaily = epnjDrillingDailyVos.getFirst();
                    Map<String, Object> map = new HashMap<>();
                    map.put("wellName", epnjDrillingDaily.getWellNumber());
                    map.put("drillingCompany", epnjDrillingDaily.getDrillingCompany());
                    map.put("wellType", epnjDrillingDaily.getWellType());
                    map.put("spudDate", epnjDrillingDaily.getSpudDate());
                    List<Map<String, Object>> list = new ArrayList<>();
                    list.add(map);
                    mudDaily.setWellList(list);
                }
                processedList.add(mudDaily);
            }
        }
        result.setRows(processedList);
        return result;
    }

    /**
     * 泥浆随钻月度统计
     */
    @GetMapping("/suiZuanMonthStat")
    public Map<String, Object> suiZuanMonthStat(EpnjMudDailyBo bo) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> totalMap = new HashMap<>();
        totalMap.put("mudTotal",BigDecimal.ZERO);
        totalMap.put("cakeTotal",BigDecimal.ZERO);
        totalMap.put("filtrateTotal",BigDecimal.ZERO);
        totalMap.put("wellNumber", "总量");
        List<EpnjMudDailyVo> voList = epnjMudDailyService.queryList(bo);
        Map<Long, EpnjMudDailyVo> mergedMap = new HashMap<>();
        for (EpnjMudDailyVo vo : voList) {
            Long disposeId = vo.getDisposeId();
            if (disposeId == null) {
                continue;
            }

            if (mergedMap.containsKey(disposeId)) {
                EpnjMudDailyVo existingVo = mergedMap.get(disposeId);
                existingVo.setSlurryDisposeAmount(addBigDecimal(existingVo.getSlurryDisposeAmount(), vo.getSlurryDisposeAmount()));
                existingVo.setSlurryStagingAmount(addBigDecimal(existingVo.getSlurryStagingAmount(), vo.getSlurryStagingAmount()));
                existingVo.setMudPullingAmount(addBigDecimal(existingVo.getMudPullingAmount(), vo.getMudPullingAmount()));
                existingVo.setMudStagingAmount(addBigDecimal(existingVo.getMudStagingAmount(), vo.getMudStagingAmount()));
                existingVo.setWaterPullingAmount(addBigDecimal(existingVo.getWaterPullingAmount(), vo.getWaterPullingAmount()));
                existingVo.setWaterStagingAmount(addBigDecimal(existingVo.getWaterStagingAmount(), vo.getWaterStagingAmount()));
            } else {
                EpnjMudDailyVo newVo = new EpnjMudDailyVo();
                BeanUtils.copyProperties(vo, newVo);
                mergedMap.put(disposeId, newVo);
            }
        }

        voList = new ArrayList<>(mergedMap.values());
        for (EpnjMudDailyVo vo : voList) {

            BigDecimal currentMud = (BigDecimal) totalMap.get("mudTotal");
            BigDecimal slurryDisposeAmount = vo.getSlurryDisposeAmount() != null ? vo.getSlurryDisposeAmount() : BigDecimal.ZERO;
            BigDecimal slurryStagingAmount = vo.getSlurryStagingAmount() != null ? vo.getSlurryStagingAmount() : BigDecimal.ZERO;
            BigDecimal addMud = slurryDisposeAmount.add(slurryStagingAmount);
            totalMap.put("mudTotal", currentMud.add(addMud));

            // 处理泥饼总量（泥饼拉运量 + 泥饼暂存量）
            BigDecimal currentCake = (BigDecimal) totalMap.get("cakeTotal");
            BigDecimal mudPullingAmount = vo.getMudPullingAmount() != null ? vo.getMudPullingAmount() : BigDecimal.ZERO;
            BigDecimal mudStagingAmount = vo.getMudStagingAmount() != null ? vo.getMudStagingAmount() : BigDecimal.ZERO;
            BigDecimal addCake = mudPullingAmount.add(mudStagingAmount);
            totalMap.put("cakeTotal", currentCake.add(addCake));

            // 处理滤水总量（滤水拉运量 + 滤水暂存量）
            BigDecimal currentFiltrate = (BigDecimal) totalMap.get("filtrateTotal");
            BigDecimal waterPullingAmount = vo.getWaterPullingAmount() != null ? vo.getWaterPullingAmount() : BigDecimal.ZERO;
            BigDecimal waterStagingAmount = vo.getWaterStagingAmount() != null ? vo.getWaterStagingAmount() : BigDecimal.ZERO;
            BigDecimal addFiltrate = waterPullingAmount.add(waterStagingAmount);
            totalMap.put("filtrateTotal", currentFiltrate.add(addFiltrate));
        }
        BigDecimal mudTotal = (BigDecimal) totalMap.get("mudTotal");
        BigDecimal cakeTotal = (BigDecimal) totalMap.get("cakeTotal");
        BigDecimal filtrateTotal = (BigDecimal) totalMap.get("filtrateTotal");
        BigDecimal actualLoss = mudTotal.subtract(cakeTotal.add(filtrateTotal));
        BigDecimal maxLoss = mudTotal.multiply(new BigDecimal("0.10"));

        String lossStr = "当前损耗: "+actualLoss+" = "+mudTotal + " - (" + cakeTotal + " + " + filtrateTotal + "), " +
            "损耗最大值: "+
            maxLoss + " = " + mudTotal+" * "+"10%";

        result.put("data", voList);
        result.put("total", totalMap);
        result.put("loss", lossStr);
        return result;
    }


    /**
     * 泥浆集中站月度统计
     */
    @GetMapping("/jiZhongMonthStat")
    public List<Map<String, Object>> jiZhongMonthStat(Integer year, EpnjMudDailyBo bo) {
        Map<String, Object> paramMap = new HashMap<>();
        if (year != null) {
            LocalDate firstDayOfYear = LocalDate.of(year, 1, 1);
            LocalDate lastDayOfYear = LocalDate.of(year, 12, 31);

            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String beginDateStr = firstDayOfYear.format(dateFormatter);
            String endDateStr = lastDayOfYear.format(dateFormatter);
            paramMap.put("beginDate", beginDateStr);
            paramMap.put("endDate", endDateStr);
        }else{
            throw new ServiceException("未选择年份");
        }
        bo.setParams(paramMap);
        List<EpnjMudDailyVo> voList = epnjMudDailyService.queryList(bo);
        // 按集中站分组
        Map<Long, List<EpnjMudDailyVo>> stationGroup = voList.stream()
            .collect(Collectors.groupingBy(EpnjMudDailyVo::getDisposeId));

        // 构建结果列表
        List<Map<String, Object>> result = new ArrayList<>();

        // 处理每个集中站的数据
        for (Map.Entry<Long, List<EpnjMudDailyVo>> entry : stationGroup.entrySet()) {
            Long stationName = entry.getKey();
            List<EpnjMudDailyVo> stationData = entry.getValue();

            // 初始化12个月的数据
            Map<Integer, Map<String, BigDecimal>> monthlyData = new HashMap<>();
            for (int month = 1; month <= 12; month++) {
                monthlyData.put(month, new HashMap<String, BigDecimal>() {{
                    put("slurryDisposeAmount", BigDecimal.ZERO);
                    put("slurryStagingAmount", BigDecimal.ZERO);
                    put("mudPullingAmount", BigDecimal.ZERO);
                    put("mudStagingAmount", BigDecimal.ZERO);
                    put("waterPullingAmount", BigDecimal.ZERO);
                    put("waterStagingAmount", BigDecimal.ZERO);
                }});
            }

            // 填充实际数据
            for (EpnjMudDailyVo vo : stationData) {
                try {
                    // 解析日期获取月份
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    LocalDate date = LocalDate.parse(vo.getDate(), formatter);
                    int month = date.getMonthValue();

                    // 累加到对应月份
                    Map<String, BigDecimal> monthData = monthlyData.get(month);
                    monthData.put("slurryDisposeAmount", monthData.get("slurryDisposeAmount").add(vo.getSlurryDisposeAmount()));
                    monthData.put("slurryStagingAmount", monthData.get("slurryStagingAmount").add(vo.getSlurryStagingAmount()));
                    monthData.put("mudPullingAmount", monthData.get("mudPullingAmount").add(vo.getMudPullingAmount()));
                    monthData.put("mudStagingAmount", monthData.get("mudStagingAmount").add(vo.getMudStagingAmount()));
                    monthData.put("waterPullingAmount", monthData.get("waterPullingAmount").add(vo.getWaterPullingAmount()));
                    monthData.put("waterStagingAmount", monthData.get("waterStagingAmount").add(vo.getWaterStagingAmount()));
                } catch (Exception e) {

                }
            }

            // 计算年度总计
            Map<String, BigDecimal> annualTotal = new HashMap<>();
            annualTotal.put("slurryDisposeAmount", BigDecimal.ZERO);
            annualTotal.put("slurryStagingAmount", BigDecimal.ZERO);
            annualTotal.put("mudPullingAmount", BigDecimal.ZERO);
            annualTotal.put("mudStagingAmount", BigDecimal.ZERO);
            annualTotal.put("waterPullingAmount", BigDecimal.ZERO);
            annualTotal.put("waterStagingAmount", BigDecimal.ZERO);

            for (Map<String, BigDecimal> monthData : monthlyData.values()) {
                for (Map.Entry<String, BigDecimal> dataEntry : monthData.entrySet()) {
                    annualTotal.put(dataEntry.getKey(), annualTotal.get(dataEntry.getKey()).add(dataEntry.getValue()));
                }
            }

            // 计算损耗
            BigDecimal mudTotal = annualTotal.get("slurryDisposeAmount").add(annualTotal.get("slurryStagingAmount"));
            BigDecimal cakeTotal = annualTotal.get("mudPullingAmount").add(annualTotal.get("mudStagingAmount"));
            BigDecimal filtrateTotal = annualTotal.get("waterPullingAmount").add(annualTotal.get("waterStagingAmount"));
            BigDecimal actualLoss = mudTotal.subtract(cakeTotal.add(filtrateTotal));
            BigDecimal maxLoss = mudTotal.multiply(new BigDecimal("0.10"));

            //当前损耗:  xxx = （xxx-(xxx)）  , 损耗最大值： xxx =  (xxxx)
            String lossStr = "当前损耗: "+actualLoss+" = "+mudTotal + " - (" + cakeTotal + " + " + filtrateTotal + "), " +
                "损耗最大值: "+
                maxLoss + " = " + mudTotal+" * "+"10%";

            Map<String, Object> stationResult = new LinkedHashMap<>();
            stationResult.put("stationName", stationName);
            stationResult.put("monthlyData", monthlyData);
            stationResult.put("lossStr", lossStr);

            result.add(stationResult);
        }
        return result;

    }

    private BigDecimal addBigDecimal(BigDecimal a, BigDecimal b) {
        a = a == null ? BigDecimal.ZERO : a;
        b = b == null ? BigDecimal.ZERO : b;
        return a.add(b);
    }
}
