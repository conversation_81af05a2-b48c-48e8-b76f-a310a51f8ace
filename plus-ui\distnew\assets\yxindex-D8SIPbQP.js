import{d as xe,h as ra,ak as Ne,r as f,ai as x,X as Re,b as na,aH as ia,c,o as u,p as t,t as o,w as _,q as R,a7 as ua,M as pa,e as l,A as da,G as sa,H as K,B as ma,F as A,C as w,x as d,D as va,K as fa,J as s,am as qe,aI as ga,ay as ba,ax as ca,z as U,aJ as ya,y as ee,aL as Aa,v as wa,az as Va,a8 as _a,aA as ka}from"./index-D07cMzhp.js";/* empty css                       */import{E as Ia,a as Sa}from"./el-radio-BSZOo4bv.js";import{_ as Ta}from"./index-DVHplxfU.js";import{E as Ca}from"./el-row-CikYE3zA.js";import{_ as Da}from"./index-BWMgqvQ9.js";import{E as Ua}from"./el-col-BaG5Rg5z.js";import{E as La}from"./el-date-picker-HyhB9X9n.js";import{l as Na,g as ne,u as Ye,a as Ra,b as qa,d as Ya}from"./index-M7LxucW_.js";import{l as ie}from"./index-BhIIZXqy.js";import{m as xa}from"./dayjs.min-Brw96_N0.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./el-tree-DW6MoFaI.js";import"./index-VIEDZI2D.js";const Ea={class:"p-2"},ja={class:"mb-[10px]"},za={class:"dialog-footer"},$a={class:"dialog-footer"},Fa={class:"dialog-footer"},Ma=xe({name:"TransportApplication"}),il=xe({...Ma,setup(Ha){const{proxy:m}=ra(),{epfy_medium_category:ue,epnj_suizuan_yexiang_medium_type:Pa,epfy_application_status:E,epfy_unload_location_type:Ka,transport_app_regulatory_authorities_approver:pe,transport_app_operationarea_examine:de,transport_application_status:Ee}=Ne(m==null?void 0:m.useDict("epfy_medium_category","epnj_suizuan_yexiang_medium_type","epfy_application_status","epfy_unload_location_type","transport_app_regulatory_authorities_approver","transport_app_operationarea_examine","transport_application_status")),se=f([]),T=f({}),j=f([]),O=f([]),ae=f([]),le=f([]),z=f([]),k=f(!1),B=f(!0),J=f(!0),$=f([]),me=f(!0),ve=f(!0),te=f(0),F=f(1),Q=f(!1),fe=f(),q=f(),I=x({visible:!1,title:""}),C=x({visible:!1,title:""}),ge={appId:void 0,relatedId:void 0,appName:void 0,workAreaId:void 0,wellName:void 0,applicationDate:xa().format("YYYY-MM-DD HH:mm:ss"),loadingLocation:void 0,unloadLocationType:2,unloadLocation:void 0,file:void 0,applicationStatus:1,rejectSuggestion:void 0,flowType:2,mediumCategory:2,remark:void 0,superviseApprover:void 0,operationAreaReviewer:void 0,examineApproveTime:void 0,operationAreaReviewedTime:void 0,operationAreaApproveStatus:1,mediumType:8},je=x({form:{...ge},queryParams:{pageNum:1,pageSize:10,relatedId:void 0,appName:void 0,workAreaId:void 0,wellName:void 0,loadingLocation:void 0,unloadLocationType:2,unloadLocation:void 0,file:void 0,applicationStatus:void 0,rejectSuggestion:void 0,flowType:2,mediumCategory:2,mediumType:void 0,params:{}},rules:{appId:[{required:!0,message:"申请ID不能为空",trigger:"blur"}],appName:[{required:!0,message:"申请名称不能为空",trigger:"blur"}],mediumCategory:[{required:!0,message:"申请分类不能为空",trigger:"change"}],workAreaId:[{required:!0,message:"所属作业区不能为空",trigger:"blur"}],relatedId:[{required:!0,message:"集中站名称不能为空",trigger:"change"}],applicationDate:[{required:!0,message:"申请日期不能为空",trigger:"blur"}],loadingLocation:[{required:!0,message:"装车地点不能为空",trigger:"blur"}],unloadLocationType:[{required:!0,message:"卸车地点类型不能为空",trigger:"change"}],unloadLocation:[{required:!0,message:"卸车地点不能为空",trigger:"blur"}],mediumType:[{required:!0,message:"介质类型不能为空",trigger:"blur"}]}}),{queryParams:v,form:r,rules:ze}=Ne(je),$e=x({appId:[{required:!0,message:"申请ID不能为空",trigger:"blur"}],appName:[{required:!0,message:"申请名称不能为空",trigger:"blur"}],workAreaId:[{required:!0,message:"所属作业区不能为空",trigger:"blur"}],relatedId:[{required:!0,message:"集中站名称不能为空",trigger:"change"}],wellName:[{required:!0,message:"集中站名称不能为空",trigger:"blur"}],applicationDate:[{required:!0,message:"申请日期不能为空",trigger:"blur"}],loadingLocation:[{required:!0,message:"装车地点不能为空",trigger:"blur"}],unloadLocationType:[{required:!0,message:"卸车地点类型不能为空",trigger:"change"}],unloadLocation:[{required:!0,message:"卸车地点不能为空",trigger:"blur"}],applicationStatus:[{required:!0,message:"状态不能为空",trigger:"change"}],operationAreaApproveStatus:[{required:!0,message:"状态不能为空",trigger:"change"}],rejectSuggestion:[{required:!0,message:"审核意见不能为空",trigger:"blur"}]});x({pageSize:-1,handlingType:1,workArea:void 0});const Fe=n=>{if(n.transportRecordCount!=0)return!1;if(n.examineApproveTime!=null&&n.operationAreaReviewedTime!=null){const e=new Date(Date.now()-864e5),p=new Date(n.examineApproveTime),i=new Date(n.operationAreaReviewedTime);if(p<e&&i<e)return!0}return!1},be=f({mediumType:"nj"}),G=f({operationAreaId:void 0}),ce=Re(()=>{var e;return G.value.operationAreaId=r.value.workAreaId,(e=de.value)==null?void 0:e.filter(p=>G.value.operationAreaId!==void 0&&G.value.operationAreaId!==null?p.value.includes(G.value.operationAreaId):!0)}),ye=Re(()=>{var e;return(e=pe.value)==null?void 0:e.filter(p=>be.value.mediumType?p.value.includes(be.value.mediumType):!0)}),Ae=(n,e)=>{var p;return console.log(n,e),(p=n==null?void 0:n.find(i=>i.value==e))==null?void 0:p.label},D=async()=>{B.value=!0;const n=await Na(v.value);se.value=n.rows,te.value=n.total,B.value=!1},we=()=>{M(),I.visible=!1,C.visible=!1},M=()=>{var n;r.value={...ge},(n=q.value)==null||n.resetFields(),W(null)},L=()=>{v.value.pageNum=1,D()},Me=()=>{var n;(n=fe.value)==null||n.resetFields(),L()},He=n=>{$.value=n.map(e=>e.appId),me.value=n.length!=1,ve.value=!n.length},Pe=()=>{Z(),M(),Q.value=!1,I.visible=!0,I.title="添加拉运申请"},Ve=async n=>{Z(),M();const e=(n==null?void 0:n.appId)||$.value[0],p=await ne(e);Object.assign(r.value,p.data),await W(p.data.workAreaId),Q.value=!0,I.visible=!0,I.title="修改拉运申请"},_e=n=>{var e;(e=q.value)==null||e.validate(async p=>{p&&(k.value=!0,r.value.appId?(r.value.reApprove=n,await Ye(r.value).finally(()=>k.value=!1)):await Ra(r.value).finally(()=>k.value=!1),m==null||m.$modal.msgSuccess("操作成功"),I.visible=!1,C.visible=!1,await D())})},Ke=()=>{var n;(n=q.value)==null||n.validate(async e=>{if(e){if(k.value=!0,r.value.appId){const p=r.value.appId,i=r.value.rejectSuggestion,y=F.value==1?r.value.applicationStatus:r.value.operationAreaApproveStatus;await qa(p,y,i,r.value.warnTime,F.value).finally(()=>k.value=!1)}m==null||m.$modal.msgSuccess("操作成功"),I.visible=!1,C.visible=!1,await D()}})},ke=async(n,e)=>{Z(),M();const p=(n==null?void 0:n.appId)||$.value[0],i=await ne(p);Object.assign(r.value,i.data),await W(i.data.workAreaId),F.value=e,Q.value=!0,C.visible=!0,C.title="审核拉运申请"},Ie=async n=>{const e=(n==null?void 0:n.appId)||$.value;await(m==null?void 0:m.$modal.confirm('是否确认删除拉运申请编号为"'+e+'"的数据项？').finally(()=>B.value=!1)),await Ya(e),m==null||m.$modal.msgSuccess("删除成功"),await D()},Oe=async n=>{m==null||m.download("epfy/transportApplication/downloadMeasurementVoucher",{appId:n==null?void 0:n.appId},"二连分公司废液收集计量凭证.xls")},Be=()=>{m==null||m.download("epfy/transportApplication/export",{...v.value},`transportApplication_${new Date().getTime()}.xlsx`)},Je=n=>{r.value.uploadTime=n.uploadTime},Se=n=>{const e=z.value.find(p=>p.operationAreaId===r.value.relatedId);e&&(r.value.wellName=e.operationAreaName),r.value.loadingLocation=e.operationAreaName},Qe=n=>{if(!n)return"未知";const e=j.value.find(p=>p.operationAreaId===n);return e?e.operationAreaName:"未知"},Z=async()=>{T.value.operationAreaParentId=0,T.value.operationAreaType=0;const n=await ie(T.value);j.value=n.rows,T.value.operationAreaParentId=null,T.value.operationAreaType=2;const e=await ie(T.value);le.value=e.rows,z.value=e.rows,T.value.operationAreaParentId=null,T.value.operationAreaType=5;const p=await ie(T.value);ae.value=p.rows,O.value=p.rows},W=async n=>{n!=null?(z.value=le.value.filter(e=>e.operationAreaParentId===n),O.value=ae.value.filter(e=>e.operationAreaParentId===n)):(z.value=le.value,O.value=ae.value)},Ge=n=>(v.value.pageNum-1)*v.value.pageSize+n+1,Ze=({row:n,rowIndex:e})=>Fe(n)?"warn-row":"",N=x({visible:!1,title:""}),We=async n=>{M();const e=(n==null?void 0:n.appId)||$.value[0],p=await ne(e);Object.assign(r.value,p.data),N.visible=!0,N.title="修改预警时间"},Xe=async()=>{k.value=!0,r.value.appId&&await Ye(r.value).finally(()=>k.value=!1),m==null||m.$modal.msgSuccess("操作成功"),I.visible=!1,N.visible=!1,await D()};return na(()=>{Z(),D()}),(n,e)=>{var Ue,Le;const p=sa,i=da,y=va,V=ma,H=La,g=fa,X=pa,Te=ua,h=Ua,he=Da,ea=Ca,b=ca,oe=ya,Y=Aa,aa=ba,la=Va,ta=Ta,re=ka,Ce=Sa,De=Ia,S=ia("hasPermi"),oa=wa;return u(),c("div",Ea,[t(ga,{"enter-active-class":(Ue=l(m))==null?void 0:Ue.animate.searchAnimate.enter,"leave-active-class":(Le=l(m))==null?void 0:Le.animate.searchAnimate.leave},{default:o(()=>[_(R("div",ja,[t(Te,{shadow:"hover"},{default:o(()=>[t(X,{ref_key:"queryFormRef",ref:fe,inline:!0,model:l(v),"label-width":"85px"},{default:o(()=>[t(i,{label:"申请名称",prop:"appName"},{default:o(()=>[t(p,{modelValue:l(v).appName,"onUpdate:modelValue":e[0]||(e[0]=a=>l(v).appName=a),placeholder:"请输入申请名称",clearable:"",onKeyup:K(L,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"所属作业区",prop:"workAreaId"},{default:o(()=>[t(V,{modelValue:l(v).workAreaId,"onUpdate:modelValue":e[1]||(e[1]=a=>l(v).workAreaId=a),class:"searchDate",clearable:"",filterable:"",placeholder:"选择所属作业区"},{default:o(()=>[(u(!0),c(A,null,w(l(j),a=>(u(),d(y,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"集中站名称",prop:"wellName"},{default:o(()=>[t(p,{modelValue:l(v).wellName,"onUpdate:modelValue":e[2]||(e[2]=a=>l(v).wellName=a),placeholder:"请输入集中站名称",clearable:"",onKeyup:K(L,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"申请日期",prop:"params"},{default:o(()=>[t(H,{modelValue:l(v).params,"onUpdate:modelValue":e[3]||(e[3]=a=>l(v).params=a),clearable:"","end-placeholder":"结束时间","range-separator":"至","start-placeholder":"开始时间",type:"datetimerange","value-format":"YYYY-MM-DD HH:mm:ss",onKeyup:K(L,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"装车地点",prop:"loadingLocation"},{default:o(()=>[t(p,{modelValue:l(v).loadingLocation,"onUpdate:modelValue":e[4]||(e[4]=a=>l(v).loadingLocation=a),placeholder:"请输入装车地点",clearable:"",onKeyup:K(L,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"卸车地点",prop:"unloadLocation"},{default:o(()=>[t(p,{modelValue:l(v).unloadLocation,"onUpdate:modelValue":e[5]||(e[5]=a=>l(v).unloadLocation=a),placeholder:"请输入卸车地点",clearable:"",onKeyup:K(L,["enter"])},null,8,["modelValue"])]),_:1}),t(i,{label:"状态",prop:"applicationStatus"},{default:o(()=>[t(V,{modelValue:l(v).applicationStatus,"onUpdate:modelValue":e[6]||(e[6]=a=>l(v).applicationStatus=a),clearable:"",placeholder:"请选择状态"},{default:o(()=>[(u(!0),c(A,null,w(l(E),a=>(u(),d(y,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,null,{default:o(()=>[t(g,{type:"primary",icon:"Search",onClick:L},{default:o(()=>e[47]||(e[47]=[s("搜索")])),_:1}),t(g,{icon:"Refresh",onClick:Me},{default:o(()=>e[48]||(e[48]=[s("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[qe,l(J)]])]),_:1},8,["enter-active-class","leave-active-class"]),t(Te,{shadow:"never"},{header:o(()=>[t(ea,{gutter:10,class:"mb8"},{default:o(()=>[t(h,{span:1.5},{default:o(()=>[_((u(),d(g,{type:"primary",plain:"",icon:"Plus",onClick:Pe},{default:o(()=>e[49]||(e[49]=[s("新增")])),_:1})),[[S,["epfy:transportApplication:add"]]])]),_:1}),t(h,{span:1.5},{default:o(()=>[_((u(),d(g,{disabled:l(me),icon:"Edit",plain:"",type:"success",onClick:e[7]||(e[7]=a=>Ve())},{default:o(()=>e[50]||(e[50]=[s("修改")])),_:1},8,["disabled"])),[[S,["epfy:transportApplication:edit"]]])]),_:1}),t(h,{span:1.5},{default:o(()=>[_((u(),d(g,{disabled:l(ve),icon:"Delete",plain:"",type:"danger",onClick:e[8]||(e[8]=a=>Ie())},{default:o(()=>e[51]||(e[51]=[s("删除")])),_:1},8,["disabled"])),[[S,["epfy:transportApplication:remove"]]])]),_:1}),t(h,{span:1.5},{default:o(()=>[_((u(),d(g,{type:"warning",plain:"",icon:"Download",onClick:Be},{default:o(()=>e[52]||(e[52]=[s("导出")])),_:1})),[[S,["epfy:transportApplication:export"]]])]),_:1}),t(he,{showSearch:l(J),"onUpdate:showSearch":e[9]||(e[9]=a=>_a(J)?J.value=a:null),onQueryTable:D},null,8,["showSearch"])]),_:1})]),default:o(()=>[_((u(),d(aa,{data:l(se),"row-class-name":Ze,stripe:"",onSelectionChange:He},{default:o(()=>[t(b,{align:"center",type:"selection",width:"55"}),t(b,{index:Ge,label:"序号",type:"index",width:"50"}),t(b,{label:"申请名称",align:"center",prop:"appName"}),t(b,{align:"center",label:"所属作业区",prop:"workAreaId",width:"135"},{default:o(a=>[s(U(Qe(a.row.workAreaId)),1)]),_:1}),t(b,{align:"center",label:"集中站名称",prop:"wellName",width:"130"}),t(b,{label:"申请日期",align:"center",prop:"applicationDate","min-width":"110"},{default:o(a=>[R("span",null,U(n.parseTime(a.row.applicationDate,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(b,{align:"center",label:"装车地点",prop:"loadingLocation",width:"130"}),e[59]||(e[59]=s(" < ")),t(b,{align:"center",label:"卸车地点",prop:"unloadLocation",width:"135"}),t(b,{label:"归口部门审核人",align:"center",prop:"superviseApprover"},{default:o(a=>[s(U(Ae(l(pe),a.row.superviseApprover)),1)]),_:1}),t(b,{label:"归口部门审核",align:"center",prop:"applicationStatus"},{default:o(a=>[t(oe,{options:l(E),value:a.row.applicationStatus},null,8,["options","value"])]),_:1}),t(b,{align:"center",label:"归口部门审核时间",prop:"examineApproveTime",width:"110"},{default:o(a=>[R("span",null,U(n.parseTime(a.row.examineApproveTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(b,{label:"作业区审核人",align:"center",prop:"operationAreaReviewer"},{default:o(a=>[s(U(Ae(l(de),a.row.operationAreaReviewer)),1)]),_:1}),t(b,{label:"作业区审核",align:"center",prop:"operationAreaApproveStatus"},{default:o(a=>[t(oe,{options:l(E),value:a.row.operationAreaApproveStatus},null,8,["options","value"])]),_:1}),t(b,{label:"作业区审核时间",align:"center",prop:"operationAreaReviewedTime",width:"110"},{default:o(a=>[R("span",null,U(n.parseTime(a.row.operationAreaReviewedTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(b,{label:"审核意见",align:"center",prop:"rejectSuggestion"}),t(b,{align:"center",label:"拉运状态",prop:"transportStatus",width:"120"},{default:o(a=>[t(oe,{options:l(Ee),value:a.row.transportStatus},null,8,["options","value"])]),_:1}),t(b,{label:"备注信息",align:"center",prop:"remark"}),t(b,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作","min-width":"210"},{default:o(a=>[[1].includes(a.row.applicationStatus)&&[2].includes(a.row.operationAreaApproveStatus)?(u(),d(Y,{key:0,content:"归口部门审核",placement:"top"},{default:o(()=>[_((u(),d(g,{icon:"Coordinate",link:"",type:"primary",onClick:P=>ke(a.row,1)},{default:o(()=>e[53]||(e[53]=[s("归口部门审核")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:guikou"]]])]),_:2},1024)):ee("",!0),[1].includes(a.row.operationAreaApproveStatus)?(u(),d(Y,{key:1,content:"作业区审核",placement:"top"},{default:o(()=>[_((u(),d(g,{icon:"Coordinate",link:"",type:"primary",onClick:P=>ke(a.row,2)},{default:o(()=>e[54]||(e[54]=[s("作业区审核")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:zuoyequ"]]])]),_:2},1024)):ee("",!0),t(Y,{content:"修改预警时间",placement:"top"},{default:o(()=>[_((u(),d(g,{icon:"Edit",link:"",type:"primary",onClick:P=>We(a.row)},{default:o(()=>e[55]||(e[55]=[s("修改预警时间")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:coordinate"]]])]),_:2},1024),a.row.operationAreaApproveStatus===2&&a.row.applicationStatus===2?(u(),d(Y,{key:2,content:"计量凭证下载",placement:"top"},{default:o(()=>[t(g,{icon:"Download",link:"",type:"primary",onClick:P=>Oe(a.row)},{default:o(()=>e[56]||(e[56]=[s("计量凭证下载")])),_:2},1032,["onClick"])]),_:2},1024)):ee("",!0),t(Y,{content:"修改",placement:"top"},{default:o(()=>[_((u(),d(g,{icon:"Edit",link:"",type:"primary",onClick:P=>Ve(a.row)},{default:o(()=>e[57]||(e[57]=[s("修改")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:edit"]]])]),_:2},1024),t(Y,{content:"删除",placement:"top"},{default:o(()=>[_((u(),d(g,{icon:"Delete",link:"",type:"primary",onClick:P=>Ie(a.row)},{default:o(()=>e[58]||(e[58]=[s("删除")])),_:2},1032,["onClick"])),[[S,["epfy:transportApplication:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[oa,l(B)]]),_(t(la,{total:l(te),page:l(v).pageNum,"onUpdate:page":e[10]||(e[10]=a=>l(v).pageNum=a),limit:l(v).pageSize,"onUpdate:limit":e[11]||(e[11]=a=>l(v).pageSize=a),onPagination:D},null,8,["total","page","limit"]),[[qe,l(te)>0]])]),_:1}),t(re,{title:l(I).title,modelValue:l(I).visible,"onUpdate:modelValue":e[26]||(e[26]=a=>l(I).visible=a),width:"600px","append-to-body":""},{footer:o(()=>[R("div",za,[t(g,{loading:l(k),type:"primary",onClick:e[24]||(e[24]=a=>_e(!1))},{default:o(()=>e[60]||(e[60]=[s("确 定")])),_:1},8,["loading"]),t(g,{loading:l(k),type:"primary",onClick:e[25]||(e[25]=a=>_e(!0))},{default:o(()=>e[61]||(e[61]=[s("重新提交审核")])),_:1},8,["loading"]),t(g,{onClick:we},{default:o(()=>e[62]||(e[62]=[s("取 消")])),_:1})])]),default:o(()=>[t(X,{ref_key:"transportApplicationFormRef",ref:q,model:l(r),rules:l(ze),"label-width":"120px"},{default:o(()=>[t(i,{label:"申请名称",prop:"appName"},{default:o(()=>[t(p,{modelValue:l(r).appName,"onUpdate:modelValue":e[12]||(e[12]=a=>l(r).appName=a),placeholder:"请输入申请名称"},null,8,["modelValue"])]),_:1}),t(i,{label:"申请分类",prop:"mediumCategory"},{default:o(()=>[t(V,{modelValue:l(r).mediumCategory,"onUpdate:modelValue":e[13]||(e[13]=a=>l(r).mediumCategory=a),placeholder:"请选择申请分类",disabled:""},{default:o(()=>[(u(!0),c(A,null,w(l(ue),a=>(u(),d(y,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"所属作业区",prop:"workAreaId"},{default:o(()=>[t(V,{filterable:"",modelValue:l(r).workAreaId,"onUpdate:modelValue":e[14]||(e[14]=a=>l(r).workAreaId=a),placeholder:"选择所属作业区",clearable:"",onChange:W},{default:o(()=>[(u(!0),c(A,null,w(l(j),a=>(u(),d(y,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"集中站名称",prop:"relatedId"},{default:o(()=>[t(V,{modelValue:l(r).relatedId,"onUpdate:modelValue":e[15]||(e[15]=a=>l(r).relatedId=a),placeholder:"请选择集中站",onChange:Se},{default:o(()=>[(u(!0),c(A,null,w(l(z),a=>(u(),d(y,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"申请日期",prop:"applicationDate"},{default:o(()=>[t(H,{modelValue:l(r).applicationDate,"onUpdate:modelValue":e[16]||(e[16]=a=>l(r).applicationDate=a),clearable:"",placeholder:"请选择申请日期",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(i,{label:"装车地点",prop:"loadingLocation"},{default:o(()=>[t(p,{modelValue:l(r).loadingLocation,"onUpdate:modelValue":e[17]||(e[17]=a=>l(r).loadingLocation=a),disabled:"",placeholder:"请输入装车地点"},null,8,["modelValue"])]),_:1}),t(i,{label:"卸车地点",prop:"unloadLocation"},{default:o(()=>[t(V,{filterable:"",modelValue:l(r).unloadLocation,"onUpdate:modelValue":e[18]||(e[18]=a=>l(r).unloadLocation=a),placeholder:"选择卸车地点",clearable:"","allow-create":""},{default:o(()=>[(u(!0),c(A,null,w(l(O),a=>(u(),d(y,{key:a.operationAreaName,label:a.operationAreaName,value:a.operationAreaName},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"归口部门审批人",prop:"superviseApprover"},{default:o(()=>[t(V,{modelValue:l(r).superviseApprover,"onUpdate:modelValue":e[19]||(e[19]=a=>l(r).superviseApprover=a),placeholder:"请选择归口部门批准人"},{default:o(()=>[(u(!0),c(A,null,w(l(ye),a=>(u(),d(y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"作业区审核人",prop:"operationAreaReviewer"},{default:o(()=>[t(V,{modelValue:l(r).operationAreaReviewer,"onUpdate:modelValue":e[20]||(e[20]=a=>l(r).operationAreaReviewer=a),placeholder:"请选择作业区审核人"},{default:o(()=>[(u(!0),c(A,null,w(l(ce),a=>(u(),d(y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"检测报告",prop:"file"},{default:o(()=>[t(ta,{modelValue:l(r).file,"onUpdate:modelValue":e[21]||(e[21]=a=>l(r).file=a),"attach-source-id":l(r).appId,disabled:!1,"attach-category":"transportApplication","attach-source-type":"njTransportApplication",onUploadSuccess:Je},null,8,["modelValue","attach-source-id"])]),_:1}),l(Q)?(u(),d(i,{key:0,label:"审核意见",prop:"rejectSuggestion"},{default:o(()=>[t(p,{modelValue:l(r).rejectSuggestion,"onUpdate:modelValue":e[22]||(e[22]=a=>l(r).rejectSuggestion=a),type:"textarea",disabled:"",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})):ee("",!0),t(i,{label:"备注信息",prop:"remark"},{default:o(()=>[t(p,{modelValue:l(r).remark,"onUpdate:modelValue":e[23]||(e[23]=a=>l(r).remark=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(re,{title:l(C).title,modelValue:l(C).visible,"onUpdate:modelValue":e[43]||(e[43]=a=>l(C).visible=a),width:"600px","append-to-body":""},{footer:o(()=>[R("div",$a,[t(g,{loading:l(k),type:"primary",onClick:Ke},{default:o(()=>e[64]||(e[64]=[s("确 定")])),_:1},8,["loading"]),t(g,{onClick:we},{default:o(()=>e[65]||(e[65]=[s("取 消")])),_:1})])]),default:o(()=>[t(X,{ref_key:"transportApplicationFormRef",ref:q,model:l(r),rules:l($e),"label-width":"140px"},{default:o(()=>[t(i,{label:"申请名称",prop:"appName"},{default:o(()=>[t(p,{disabled:"",modelValue:l(r).appName,"onUpdate:modelValue":e[27]||(e[27]=a=>l(r).appName=a),placeholder:"请输入申请名称"},null,8,["modelValue"])]),_:1}),t(i,{label:"申请分类",prop:"mediumCategory"},{default:o(()=>[t(V,{modelValue:l(r).mediumCategory,"onUpdate:modelValue":e[28]||(e[28]=a=>l(r).mediumCategory=a),placeholder:"请选择申请分类",disabled:""},{default:o(()=>[(u(!0),c(A,null,w(l(ue),a=>(u(),d(y,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"所属作业区",prop:"workAreaId"},{default:o(()=>[t(V,{modelValue:l(r).workAreaId,"onUpdate:modelValue":e[29]||(e[29]=a=>l(r).workAreaId=a),class:"searchDate",clearable:"",disabled:"",filterable:"",placeholder:"选择所属作业区"},{default:o(()=>[(u(!0),c(A,null,w(l(j),a=>(u(),d(y,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"集中站名称",prop:"relatedId"},{default:o(()=>[t(V,{modelValue:l(r).relatedId,"onUpdate:modelValue":e[30]||(e[30]=a=>l(r).relatedId=a),disabled:"",placeholder:"请选择集中站",onChange:Se},null,8,["modelValue"])]),_:1}),t(i,{label:"申请日期",prop:"applicationDate"},{default:o(()=>[t(H,{modelValue:l(r).applicationDate,"onUpdate:modelValue":e[31]||(e[31]=a=>l(r).applicationDate=a),clearable:"",disabled:"",placeholder:"请选择申请日期",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(i,{label:"装车地点",prop:"loadingLocation"},{default:o(()=>[t(p,{disabled:"",modelValue:l(r).loadingLocation,"onUpdate:modelValue":e[32]||(e[32]=a=>l(r).loadingLocation=a),placeholder:"请输入装车地点"},null,8,["modelValue"])]),_:1}),t(i,{label:"卸车地点",prop:"unloadLocation"},{default:o(()=>[t(p,{modelValue:l(r).unloadLocation,"onUpdate:modelValue":e[33]||(e[33]=a=>l(r).unloadLocation=a),disabled:"",placeholder:"请输入卸车地点"},null,8,["modelValue"])]),_:1}),t(i,{label:"归口部门审核人",prop:"superviseApprover",disabled:""},{default:o(()=>[t(V,{modelValue:l(r).superviseApprover,"onUpdate:modelValue":e[34]||(e[34]=a=>l(r).superviseApprover=a),disabled:"",placeholder:"请选择批准人"},{default:o(()=>[(u(!0),c(A,null,w(l(ye),a=>(u(),d(y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"归口部门审核状态",prop:"applicationStatus"},{default:o(()=>[t(De,{modelValue:l(r).applicationStatus,"onUpdate:modelValue":e[35]||(e[35]=a=>l(r).applicationStatus=a),disabled:l(F)!=1},{default:o(()=>[(u(!0),c(A,null,w(l(E),a=>(u(),d(Ce,{key:a.value,value:parseInt(a.value)},{default:o(()=>[s(U(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),t(i,{label:"归口部门审核时间",prop:"examineApproveTime"},{default:o(()=>[t(H,{modelValue:l(r).examineApproveTime,"onUpdate:modelValue":e[36]||(e[36]=a=>l(r).examineApproveTime=a),clearable:"",disabled:"",placeholder:"",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(i,{label:"作业区审核人",prop:"operationAreaReviewer",disabled:""},{default:o(()=>[t(V,{modelValue:l(r).operationAreaReviewer,"onUpdate:modelValue":e[37]||(e[37]=a=>l(r).operationAreaReviewer=a),placeholder:"请选择审核人",disabled:""},{default:o(()=>[(u(!0),c(A,null,w(l(ce),a=>(u(),d(y,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"作业区审核状态",prop:"operationAreaApproveStatus"},{default:o(()=>[t(De,{modelValue:l(r).operationAreaApproveStatus,"onUpdate:modelValue":e[38]||(e[38]=a=>l(r).operationAreaApproveStatus=a),disabled:l(F)!=2},{default:o(()=>[(u(!0),c(A,null,w(l(E),a=>(u(),d(Ce,{key:a.value,value:parseInt(a.value)},{default:o(()=>[s(U(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),t(i,{label:"作业区审核时间",prop:"operationAreaReviewedTime"},{default:o(()=>[t(H,{modelValue:l(r).operationAreaReviewedTime,"onUpdate:modelValue":e[39]||(e[39]=a=>l(r).operationAreaReviewedTime=a),clearable:"",disabled:"",placeholder:"",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),_((u(),d(i,{label:"预警时间",prop:"warnTime"},{default:o(()=>[t(p,{modelValue:l(r).warnTime,"onUpdate:modelValue":e[40]||(e[40]=a=>l(r).warnTime=a)},{append:o(()=>e[63]||(e[63]=[s("小时")])),_:1},8,["modelValue"])]),_:1})),[[S,["epfy:transportApplication:coordinate"]]]),t(i,{label:"审核意见",prop:"rejectSuggestion"},{default:o(()=>[t(p,{modelValue:l(r).rejectSuggestion,"onUpdate:modelValue":e[41]||(e[41]=a=>l(r).rejectSuggestion=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1}),t(i,{label:"备注信息",prop:"remark"},{default:o(()=>[t(p,{modelValue:l(r).remark,"onUpdate:modelValue":e[42]||(e[42]=a=>l(r).remark=a),type:"textarea",disabled:"",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(re,{modelValue:l(N).visible,"onUpdate:modelValue":e[46]||(e[46]=a=>l(N).visible=a),title:l(N).title,"append-to-body":"",width:"500px"},{footer:o(()=>[R("div",Fa,[t(g,{loading:l(k),type:"primary",onClick:Xe},{default:o(()=>e[67]||(e[67]=[s("确 定")])),_:1},8,["loading"]),t(g,{onClick:e[45]||(e[45]=a=>l(N).visible=!1)},{default:o(()=>e[68]||(e[68]=[s("取 消")])),_:1})])]),default:o(()=>[t(X,{ref_key:"transportApplicationFormRef",ref:q,model:l(r),"label-width":"110px"},{default:o(()=>[t(i,{label:"预警时间",prop:"warnTime"},{default:o(()=>[t(p,{modelValue:l(r).warnTime,"onUpdate:modelValue":e[44]||(e[44]=a=>l(r).warnTime=a)},{append:o(()=>e[66]||(e[66]=[s("小时")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});export{il as default};
