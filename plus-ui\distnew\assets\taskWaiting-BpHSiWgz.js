import{d as Z,h as ee,ak as ae,r as s,b as te,c as F,o as c,p as e,t,w as g,q as le,a7 as oe,M as ne,A as se,dc as ue,K as re,J as m,G as ie,H as C,am as k,e as w,aI as me,x as N,ay as pe,ax as de,F as ce,C as _e,aw as ve,z as fe,aJ as ge,v as we,az as ye,ai as be}from"./index-D07cMzhp.js";import{E as he}from"./el-row-CikYE3zA.js";import{_ as Ce}from"./index-BWMgqvQ9.js";import{k as ke}from"./index-3RY37FEX.js";import{w as Ne}from"./index-BpaKvpB0.js";import{_ as Se}from"./index.vue_vue_type_script_setup_true_lang-CzmK4ti3.js";import"./el-tree-DW6MoFaI.js";import"./el-col-BaG5Rg5z.js";import"./index-BdvXA74M.js";const Be={class:"p-2"},Ee={class:"mb-[10px]"},Je=Z({__name:"taskWaiting",setup(Ie){const{proxy:i}=ee(),{wf_business_status:R}=ae(i==null?void 0:i.useDict("wf_business_status")),S=s(),B=s(),y=s(!0),K=s([]),q=s(!0),J=s(!0),_=s(!0),b=s(0),E=s([]),p=s([]),v=s(0),n=s({pageNum:1,pageSize:10,nodeName:void 0,flowName:void 0,flowCode:void 0,createByIds:[]});te(()=>{I()});const r=()=>{I()},L=()=>{var l;(l=B.value)==null||l.resetFields(),n.value.pageNum=1,n.value.pageSize=10,n.value.createByIds=[],v.value=0,p.value=[],r()},P=l=>{K.value=l.map(a=>a.id),q.value=l.length!==1,J.value=!l.length},I=()=>{y.value=!0,ke(n.value).then(l=>{E.value=l.rows,b.value=l.total,y.value=!1})},A=async l=>{const a=be({businessId:l.businessId,taskId:l.id,type:"approval",formCustom:l.formCustom,formPath:l.formPath});Ne.routerJump(a,i)},D=()=>{S.value.open()},Q=l=>{v.value=0,p.value=[],n.value.createByIds=[],l&&l.length>0&&(v.value=l.length,p.value=l.map(a=>a.userId),n.value.createByIds=p.value)};return(l,a)=>{var T,U;const f=re,W=ue,d=se,h=ie,$=ne,V=oe,M=Ce,G=he,u=de,x=ve,H=ge,O=pe,j=ye,X=we;return c(),F("div",Be,[e(me,{"enter-active-class":(T=w(i))==null?void 0:T.animate.searchAnimate.enter,"leave-active-class":(U=w(i))==null?void 0:U.animate.searchAnimate.leave},{default:t(()=>[g(le("div",Ee,[e(V,{shadow:"hover"},{default:t(()=>[g(e($,{ref_key:"queryFormRef",ref:B,model:n.value,inline:!0},{default:t(()=>[e(d,null,{default:t(()=>[e(W,{value:v.value,max:10,class:"item"},{default:t(()=>[e(f,{type:"primary",onClick:D},{default:t(()=>a[6]||(a[6]=[m("选择申请人")])),_:1})]),_:1},8,["value"])]),_:1}),e(d,{label:"任务名称",prop:"nodeName"},{default:t(()=>[e(h,{modelValue:n.value.nodeName,"onUpdate:modelValue":a[0]||(a[0]=o=>n.value.nodeName=o),placeholder:"请输入任务名称",onKeyup:C(r,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"流程定义名称","label-width":"100",prop:"flowName"},{default:t(()=>[e(h,{modelValue:n.value.flowName,"onUpdate:modelValue":a[1]||(a[1]=o=>n.value.flowName=o),placeholder:"请输入流程定义名称",onKeyup:C(r,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"流程定义编码","label-width":"100",prop:"flowCode"},{default:t(()=>[e(h,{modelValue:n.value.flowCode,"onUpdate:modelValue":a[2]||(a[2]=o=>n.value.flowCode=o),placeholder:"请输入流程定义编码",onKeyup:C(r,["enter"])},null,8,["modelValue"])]),_:1}),e(d,null,{default:t(()=>[e(f,{type:"primary",icon:"Search",onClick:r},{default:t(()=>a[7]||(a[7]=[m("搜索")])),_:1}),e(f,{icon:"Refresh",onClick:L},{default:t(()=>a[8]||(a[8]=[m("重置")])),_:1})]),_:1})]),_:1},8,["model"]),[[k,_.value]])]),_:1})],512),[[k,_.value]])]),_:1},8,["enter-active-class","leave-active-class"]),e(V,{shadow:"hover"},{header:t(()=>[e(G,{gutter:10,class:"mb8"},{default:t(()=>[e(M,{"show-search":_.value,"onUpdate:showSearch":a[3]||(a[3]=o=>_.value=o),onQueryTable:r},null,8,["show-search"])]),_:1})]),default:t(()=>[g((c(),N(O,{data:E.value,border:"",onSelectionChange:P},{default:t(()=>[e(u,{type:"selection",width:"55",align:"center"}),e(u,{align:"center",type:"index",label:"序号",width:"60"}),e(u,{"show-overflow-tooltip":!0,prop:"flowName",align:"center",label:"流程定义名称"}),e(u,{align:"center",prop:"flowCode",label:"流程定义编码"}),e(u,{align:"center",prop:"categoryName",label:"流程分类"}),e(u,{align:"center",prop:"nodeName",label:"任务名称"}),e(u,{align:"center",prop:"createByName",label:"申请人"}),e(u,{align:"center",label:"办理人"},{default:t(o=>[o.row.assigneeNames?(c(!0),F(ce,{key:0},_e(o.row.assigneeNames.split(","),(z,Y)=>(c(),N(x,{key:Y,type:"success"},{default:t(()=>[m(fe(z),1)]),_:2},1024))),128)):(c(),N(x,{key:1,type:"success"},{default:t(()=>a[9]||(a[9]=[m(" 无")])),_:1}))]),_:1}),e(u,{align:"center",label:"流程状态",prop:"flowStatusName","min-width":"70"},{default:t(o=>[e(H,{options:w(R),value:o.row.flowStatus},null,8,["options","value"])]),_:1}),e(u,{align:"center",prop:"createTime",label:"创建时间",width:"160"}),e(u,{align:"center",label:"操作",width:"200"},{default:t(o=>[e(f,{type:"primary",size:"small",icon:"Edit",onClick:z=>A(o.row)},{default:t(()=>a[10]||(a[10]=[m("办理")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,y.value]]),g(e(j,{page:n.value.pageNum,"onUpdate:page":a[4]||(a[4]=o=>n.value.pageNum=o),limit:n.value.pageSize,"onUpdate:limit":a[5]||(a[5]=o=>n.value.pageSize=o),total:b.value,onPagination:r},null,8,["page","limit","total"]),[[k,b.value>0]])]),_:1}),e(w(Se),{ref_key:"userSelectRef",ref:S,multiple:!0,data:p.value,onConfirmCallBack:Q},null,8,["data"])])}}});export{Je as default};
