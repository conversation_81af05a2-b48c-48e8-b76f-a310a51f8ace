import{d as k,r as s,b as E,c as L,o as g,p as o,e as l,a8 as h,t as r,w as P,v as V,x as B,ax as U,aL as q,q as D,z as M,ay as R,bh as W,a_ as I,Q}from"./index-D07cMzhp.js";import{E as j,a as A}from"./el-tab-pane-B0KEvacl.js";const F={class:"descStyle"},G=k({__name:"warn",props:{systemType:{type:Number,default:0}},setup(v){const c=s("all"),i=s(!1),w=v,d=s(0),a=s({pageNum:1,pageSize:5,systemType:w.systemType,warnLevel:void 0,warnStatus:1}),m=s([]),b=(e,t)=>{e.props.name=="all"?a.value.warnLevel=void 0:a.value.warnLevel=e.props.name,p()},p=async()=>{i.value=!0;const e=await I(a.value);m.value=e.rows,d.value=e.total,i.value=!1},f=(e,t,u,_)=>{},y=(e,t)=>({cursor:"pointer"}),x=e=>(a.value.pageNum-1)*a.value.pageSize+e+1;return E(()=>{p()}),(e,t)=>{const u=U,_=q,C=R,N=W,z=A,S=j,T=V;return g(),L("div",null,[o(S,{type:"border-card",modelValue:l(c),"onUpdate:modelValue":t[2]||(t[2]=n=>h(c)?c.value=n:null),onTabClick:b},{default:r(()=>[o(z,{label:"全部",name:"all"},{default:r(()=>[P((g(),B(C,{stripe:"",class:"table_style",rowStyle:y,data:l(m),onCellClick:f,border:"",style:{width:"100%"}},{default:r(()=>[o(u,{index:x,align:"center",label:"序号",type:"index",width:"60px"}),o(u,{align:"center",label:"告警名称",prop:"warnName"},{default:r(n=>[o(_,{content:n.row.warnName,placement:"top"},{default:r(()=>[D("span",F,M(n.row.warnName),1)]),_:2},1032,["content"])]),_:1})]),_:1},8,["data"])),[[T,l(i)]]),o(N,{size:"small",background:"",layout:"prev, pager, next","current-page":l(a).pageNum,"onUpdate:currentPage":t[0]||(t[0]=n=>l(a).pageNum=n),"page-size":l(a).pageSize,"onUpdate:pageSize":t[1]||(t[1]=n=>l(a).pageSize=n),total:l(d),class:"mt-4 page",onSizeChange:p,onCurrentChange:p},null,8,["current-page","page-size","total"])]),_:1})]),_:1},8,["modelValue"])])}}}),K=Q(G,[["__scopeId","data-v-03f0622e"]]);export{K as default};
