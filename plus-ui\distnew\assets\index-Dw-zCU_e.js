import{aC as T,d as ie,h as Ce,r as c,ai as N,ak as De,b as he,aH as Re,c as F,o as r,p as l,t,w as y,q as I,a7 as Ee,M as Se,e as o,A as qe,B as Ue,H as Ne,F as L,C as z,x as m,D as Fe,K as xe,J as p,am as oe,aI as Ye,ay as Pe,ax as $e,z as B,aL as Me,v as Le,az as ze,a8 as Be,aA as Oe}from"./index-D07cMzhp.js";import{_ as Ke}from"./index-DVHplxfU.js";import{E as je}from"./el-row-CikYE3zA.js";import{_ as He}from"./index-BWMgqvQ9.js";import{E as Je}from"./el-col-BaG5Rg5z.js";import{E as Ge}from"./el-date-picker-HyhB9X9n.js";import{l as We}from"./index-BhIIZXqy.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./dayjs.min-Brw96_N0.js";import"./el-tree-DW6MoFaI.js";import"./index-VIEDZI2D.js";const Xe=f=>T({url:"/epms/dayQuality/list",method:"get",params:f}),ne=f=>T({url:"/epms/dayQuality/"+f,method:"get"}),Ze=f=>T({url:"/epms/dayQuality",method:"post",data:f}),ea=f=>T({url:"/epms/dayQuality",method:"put",data:f}),aa=f=>T({url:"/epms/dayQuality/"+f,method:"delete"}),ta={class:"p-2"},la={class:"mb-[10px]"},oa={class:"dialog-footer"},na={class:"dialog-footer"},ia=ie({name:"DayQuality"}),wa=ie({...ia,setup(f){const{proxy:d}=Ce(),O=c([]),C=c(!1),D=c(!0),h=c(!0),R=c([]),K=c(!0),j=c(!0),x=c(0),H=c(),E=c(),J=N({}),V=c([]),v=N({visible:!1,title:""}),w=N({visible:!1,title:""}),G={qualityReportId:void 0,detectionTime:void 0,file:void 0},re=N({form:{...G},queryParams:{pageNum:1,pageSize:10,detectionTime:void 0,file:void 0,params:{}},rules:{qualityReportId:[{required:!0,message:"监测报告id不能为空",trigger:"blur"}],detectionTime:[{required:!0,message:"检测时间不能为空",trigger:"blur"}]}}),{queryParams:s,form:i,rules:W}=De(re),A=async()=>{D.value=!0;const n=await Xe(s.value);O.value=n.rows,x.value=n.total,D.value=!1},de=()=>{Q(),v.visible=!1},se=()=>{Q(),w.visible=!1},Q=()=>{var n;i.value={...G},(n=E.value)==null||n.resetFields()},Y=()=>{s.value.pageNum=1,A()},ue=()=>{var n;(n=H.value)==null||n.resetFields(),Y()},pe=n=>{R.value=n.map(e=>e.qualityReportId),K.value=n.length!=1,j.value=!n.length},me=()=>{Q(),v.visible=!0,v.title="添加日度水质监测"},ce=async n=>{d.showAttachPreview({attachSourceId:n.qualityReportId,attachSourceType:"operationArea",attachCategory:"dayQuality"})},ye=async n=>{Q();const e=(n==null?void 0:n.qualityReportId)||R.value[0],_=await ne(e);Object.assign(i.value,_.data),w.visible=!0,w.title="日度水质监测详情"},X=async n=>{Q();const e=(n==null?void 0:n.qualityReportId)||R.value[0],_=await ne(e);Object.assign(i.value,_.data),v.visible=!0,v.title="修改日度水质监测"},fe=()=>{var n;(n=E.value)==null||n.validate(async e=>{e&&(C.value=!0,i.value.qualityReportId?await ea(i.value).finally(()=>C.value=!1):await Ze(i.value).finally(()=>C.value=!1),d==null||d.$modal.msgSuccess("操作成功"),v.visible=!1,await A())})},Z=async n=>{const e=(n==null?void 0:n.qualityReportId)||R.value;await(d==null?void 0:d.$modal.confirm('是否确认删除日度水质监测编号为"'+e+'"的数据项？').finally(()=>D.value=!1)),await aa(e),d==null||d.$modal.msgSuccess("删除成功"),await A()},ve=()=>{d==null||d.download("epms/dayQuality/export",{...s.value},`日度水质监测_${new Date().getTime()}.xlsx`)},_e=n=>{i.value.uploadTime=n.uploadTime,i.value.balanceReportName=n.name},ge=n=>{if(!n)return"未知";const e=V.value.find(_=>_.operationAreaId===n);return e?e.operationAreaName:"未知"},be=()=>{J.operationAreaParentId=0,We(J).then(n=>{V.value=n.rows})},we=n=>(s.value.pageNum-1)*s.value.pageSize+n+1;return he(()=>{A(),be()}),(n,e)=>{var te,le;const _=Fe,P=Ue,g=qe,S=Ge,u=xe,$=Se,ee=Ee,q=Je,Ae=He,ke=je,k=$e,U=Me,Ie=Pe,Ve=ze,Qe=Ke,ae=Oe,b=Re("hasPermi"),Te=Le;return r(),F("div",ta,[l(Ye,{"enter-active-class":(te=o(d))==null?void 0:te.animate.searchAnimate.enter,"leave-active-class":(le=o(d))==null?void 0:le.animate.searchAnimate.leave},{default:t(()=>[y(I("div",la,[l(ee,{shadow:"hover"},{default:t(()=>[l($,{ref_key:"queryFormRef",ref:H,model:o(s),inline:!0,"label-width":"85px"},{default:t(()=>[l(g,{label:"所属地",prop:"operationAreaId"},{default:t(()=>[l(P,{modelValue:o(s).operationAreaId,"onUpdate:modelValue":e[0]||(e[0]=a=>o(s).operationAreaId=a),placeholder:"请选择所属地",clearable:"",onKeyup:Ne(Y,["enter"])},{default:t(()=>[(r(!0),F(L,null,z(o(V),a=>(r(),m(_,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(g,{label:"监测日期",prop:"detectionTime"},{default:t(()=>[l(S,{clearable:"",modelValue:o(s).params,"onUpdate:modelValue":e[1]||(e[1]=a=>o(s).params=a),type:"daterange","value-format":"YYYY-MM-DD","start-placeholder":"起始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),l(g,null,{default:t(()=>[l(u,{type:"primary",icon:"Search",onClick:Y},{default:t(()=>e[15]||(e[15]=[p("搜索")])),_:1}),l(u,{icon:"Refresh",onClick:ue},{default:t(()=>e[16]||(e[16]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[oe,o(h)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(ee,{shadow:"never"},{header:t(()=>[l(ke,{gutter:10,class:"mb8"},{default:t(()=>[l(q,{span:1.5},{default:t(()=>[y((r(),m(u,{type:"primary",plain:"",icon:"Plus",onClick:me},{default:t(()=>e[17]||(e[17]=[p("新增")])),_:1})),[[b,["epms:dayQuality:add"]]])]),_:1}),l(q,{span:1.5},{default:t(()=>[y((r(),m(u,{disabled:o(K),icon:"Edit",plain:"",type:"success",onClick:e[2]||(e[2]=a=>X())},{default:t(()=>e[18]||(e[18]=[p("修改")])),_:1},8,["disabled"])),[[b,["epms:dayQuality:edit"]]])]),_:1}),l(q,{span:1.5},{default:t(()=>[y((r(),m(u,{disabled:o(j),icon:"Delete",plain:"",type:"danger",onClick:e[3]||(e[3]=a=>Z())},{default:t(()=>e[19]||(e[19]=[p("删除")])),_:1},8,["disabled"])),[[b,["epms:dayQuality:remove"]]])]),_:1}),l(q,{span:1.5},{default:t(()=>[y((r(),m(u,{type:"warning",plain:"",icon:"Download",onClick:ve},{default:t(()=>e[20]||(e[20]=[p("导出")])),_:1})),[[b,["epms:dayQuality:export"]]])]),_:1}),l(Ae,{showSearch:o(h),"onUpdate:showSearch":e[4]||(e[4]=a=>Be(h)?h.value=a:null),onQueryTable:A},null,8,["showSearch"])]),_:1})]),default:t(()=>[y((r(),m(Ie,{data:o(O),stripe:"",onSelectionChange:pe},{default:t(()=>[l(k,{type:"selection",width:"55",align:"center"}),l(k,{index:we,label:"序号",type:"index",width:"50"}),l(k,{align:"center",label:"所属地",prop:"operationAreaName"},{default:t(a=>[I("span",null,B(ge(a.row.operationAreaId)),1)]),_:1}),l(k,{label:"监测日期",align:"center",prop:"detectionTime",width:"180"},{default:t(a=>[I("span",null,B(n.parseTime(a.row.detectionTime,"{y}-{m}-{d}")),1)]),_:1}),l(k,{label:"上传时间",align:"center",prop:"uploadTime",width:""},{default:t(a=>[I("span",null,B(n.parseTime(a.row.uploadTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(k,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作"},{default:t(a=>[l(U,{content:"日度水质监测",placement:"top"},{default:t(()=>[y((r(),m(u,{icon:"Document",link:"",type:"primary",onClick:M=>ce(a.row)},{default:t(()=>e[21]||(e[21]=[p("监测预览")])),_:2},1032,["onClick"])),[[b,["epms:dayQuality:preview"]]])]),_:2},1024),l(U,{content:"详情",placement:"top"},{default:t(()=>[y((r(),m(u,{link:"",type:"primary",icon:"Postcard",onClick:M=>ye(a.row)},{default:t(()=>e[22]||(e[22]=[p("详情")])),_:2},1032,["onClick"])),[[b,["epms:dayQuality:detail"]]])]),_:2},1024),l(U,{content:"修改",placement:"top"},{default:t(()=>[y((r(),m(u,{link:"",type:"primary",icon:"Edit",onClick:M=>X(a.row)},{default:t(()=>e[23]||(e[23]=[p("修改")])),_:2},1032,["onClick"])),[[b,["epms:dayQuality:edit"]]])]),_:2},1024),l(U,{content:"删除",placement:"top"},{default:t(()=>[y((r(),m(u,{link:"",type:"primary",icon:"Delete",onClick:M=>Z(a.row)},{default:t(()=>e[24]||(e[24]=[p("删除")])),_:2},1032,["onClick"])),[[b,["epms:dayQuality:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Te,o(D)]]),y(l(Ve,{total:o(x),page:o(s).pageNum,"onUpdate:page":e[5]||(e[5]=a=>o(s).pageNum=a),limit:o(s).pageSize,"onUpdate:limit":e[6]||(e[6]=a=>o(s).pageSize=a),onPagination:A},null,8,["total","page","limit"]),[[oe,o(x)>0]])]),_:1}),l(ae,{title:o(v).title,modelValue:o(v).visible,"onUpdate:modelValue":e[10]||(e[10]=a=>o(v).visible=a),width:"550px","append-to-body":""},{footer:t(()=>[I("div",oa,[l(u,{loading:o(C),type:"primary",onClick:fe},{default:t(()=>e[25]||(e[25]=[p("确 定")])),_:1},8,["loading"]),l(u,{onClick:de},{default:t(()=>e[26]||(e[26]=[p("取 消")])),_:1})])]),default:t(()=>[l($,{ref_key:"dayQualityFormRef",ref:E,model:o(i),rules:o(W),"label-width":"100px"},{default:t(()=>[l(g,{label:"所属地",prop:"operationAreaId"},{default:t(()=>[l(P,{modelValue:o(i).operationAreaId,"onUpdate:modelValue":e[7]||(e[7]=a=>o(i).operationAreaId=a),filterable:"",placeholder:"请选择所属地"},{default:t(()=>[(r(!0),F(L,null,z(o(V),a=>(r(),m(_,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(g,{label:"监测日期",prop:"detectionTime"},{default:t(()=>[l(S,{modelValue:o(i).detectionTime,"onUpdate:modelValue":e[8]||(e[8]=a=>o(i).detectionTime=a),clearable:"",placeholder:"请选择监测日期",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),l(g,{label:"水质监测附件",prop:"file"},{default:t(()=>[l(Qe,{modelValue:o(i).file,"onUpdate:modelValue":e[9]||(e[9]=a=>o(i).file=a),"attach-source-id":o(i).qualityReportId,disabled:!1,"attach-category":"dayQuality","attach-source-type":"operationArea",onUploadSuccess:_e},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(ae,{title:o(w).title,modelValue:o(w).visible,"onUpdate:modelValue":e[14]||(e[14]=a=>o(w).visible=a),width:"550px","append-to-body":""},{footer:t(()=>[I("div",na,[l(u,{onClick:se},{default:t(()=>e[27]||(e[27]=[p("取 消")])),_:1})])]),default:t(()=>[l($,{ref_key:"dayQualityFormRef",ref:E,model:o(i),rules:o(W),"label-width":"100px",disabled:""},{default:t(()=>[l(g,{label:"所属地",prop:"operationAreaId"},{default:t(()=>[l(P,{modelValue:o(i).operationAreaId,"onUpdate:modelValue":e[11]||(e[11]=a=>o(i).operationAreaId=a),filterable:"",placeholder:"请选择所属地"},{default:t(()=>[(r(!0),F(L,null,z(o(V),a=>(r(),m(_,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(g,{label:"监测日期",prop:"detectionTime"},{default:t(()=>[l(S,{modelValue:o(i).detectionTime,"onUpdate:modelValue":e[12]||(e[12]=a=>o(i).detectionTime=a),clearable:"",placeholder:"请选择监测日期",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),l(g,{label:"上传时间",prop:"uploadTime"},{default:t(()=>[l(S,{modelValue:o(i).uploadTime,"onUpdate:modelValue":e[13]||(e[13]=a=>o(i).uploadTime=a),clearable:"",placeholder:"上传时间",type:"datetime"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{wa as default};
