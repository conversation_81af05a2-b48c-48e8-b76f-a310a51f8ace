import{d as pe,a as Ne,h as Ee,ak as ne,r as v,ai as Q,X as Se,b as Ye,aH as $e,c as T,o as u,p as l,t as o,w as y,q as z,a7 as qe,M as Le,e as t,A as Fe,B as xe,F as R,C as U,x as m,D as Ae,G as Me,H as D,K as He,J as f,am as ie,aI as Qe,ay as ze,ax as Ke,z as de,aJ as Be,aL as je,v as Je,az as Oe,a8 as Ge,aA as Xe}from"./index-Bm6k27Yz.js";import{_ as We}from"./index-CKygFLbI.js";import{E as Ze}from"./el-row-DPMJBKHh.js";import{_ as ea}from"./index-cPx5H1kc.js";import{E as aa}from"./el-col-BjQCqZOL.js";import{E as la}from"./el-date-picker-CIMm2f24.js";import{l as ta,g as oa,u as ra,a as na,d as ia}from"./index-DVbnU4Hd.js";import{l as da}from"./index-DngHU-w4.js";import{e as ue}from"./index-BwNwGGl7.js";import{m as ua}from"./dayjs.min-Dqr9lGM-.js";import"./el-link-6M7kQRy3.js";import"./el-upload-CSOgWUCs.js";import"./el-progress-D8KwIucv.js";import"./el-tree-BnI61tiD.js";import"./index-Cf_aQ1ZR.js";const pa={class:"p-2"},sa={class:"mb-[10px]"},ma={class:"dialog-footer"},ca=pe({name:"TransportRecord"}),Da=pe({...ca,setup(va){Ne();const{proxy:p}=Ee(),{epfy_medium_category:$,epnj_suizuan_guxiang_medium_type:se}=ne(p==null?void 0:p.useDict("epfy_medium_category","epnj_suizuan_guxiang_medium_type")),q=v([]),K=v([]),N=v(!1),E=v(!0),S=v(!0),L=v([]),B=v(!0),j=v(!0),I=Q({}),F=v(0),k=v([]),J=v([]),O=v(),x=v(),A=v(!1),_=Q({visible:!1,title:""}),G={transportId:void 0,applicationId:void 0,transportTime:ua().format("YYYY-MM-DD HH:mm:ss"),mediumCategory:1,mediumType:9,departurePoint:void 0,number:void 0,sender:"-",transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,remark:void 0,measurementVoucher:void 0,photo:void 0,flowType:2},me=Q({form:{...G},queryParams:{pageNum:1,pageSize:10,applicationId:void 0,transportTime:void 0,mediumCategory:1,mediumType:void 0,departurePoint:void 0,number:void 0,sender:void 0,transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,measurementVoucher:void 0,photo:void 0,flowType:2,unloadLocationType:2,appName:void 0,params:{}},rules:{applicationId:[{required:!0,message:"拉运申请不能为空",trigger:"change"}],transportTime:[{required:!0,message:"装运时间不能为空",trigger:"blur"}],mediumCategory:[{required:!0,message:"分类不能为空",trigger:"change"}],mediumType:[{required:!0,message:"介质类型不能为空",trigger:"change"}],departurePoint:[{required:!0,message:"起运点不能为空",trigger:"blur"}],number:[{required:!0,message:"数量不能为空",trigger:"blur"}],transporter:[{required:!0,message:"拉运人不能为空",trigger:"blur"}],licensePlate:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],arrivalPoint:[{required:!0,message:"接收点不能为空",trigger:"blur"}],receiver:[{required:!0,message:"接收人不能为空",trigger:"blur"}]}}),{queryParams:i,form:r,rules:ce}=ne(me),P=async()=>{E.value=!0;const n=await ta(i.value);K.value=n.rows,F.value=n.total,E.value=!1},ve=()=>{I.flowType=2,I.mediumCategory=1,I.unloadLocationType=2,k.value=[],ue(I).then(e=>{e.rows.forEach(s=>{s.appName=`${s.appName}-${s.wellName}-${s.applicationDate}`}),k.value=e.rows});let n={...I};n.transportStatus=0,ue(n).then(e=>{e.rows.forEach(s=>{s.appName=`${s.appName}-${s.wellName}-${s.applicationDate}`}),J.value=e.rows})},fe=()=>{M(),_.visible=!1},ge=Se(()=>k.value.reduce((n,e)=>(n[e.appId]=e.appName,n),{})),be=async()=>{const n=await da();q.value=n.rows,console.log(q)},M=()=>{var n;r.value={...G},(n=x.value)==null||n.resetFields(),be()},V=()=>{i.value.pageNum=1,P()},ye=()=>{var n;(n=O.value)==null||n.resetFields(),V()},_e=n=>{L.value=n.map(e=>e.transportId),B.value=n.length!=1,j.value=!n.length},Ve=()=>{M(),A.value=!1,_.visible=!0,_.title="添加拉运记录"},X=async n=>{M(),A.value=!0;const e=(n==null?void 0:n.transportId)||L.value[0],s=await oa(e);Object.assign(r.value,s.data),_.visible=!0,_.title="修改拉运记录"},we=async n=>{p.showAttachPreview({attachSourceId:n.transportId,attachSourceType:"gxTransportRecord"})},Pe=()=>{var n;(n=x.value)==null||n.validate(async e=>{e&&(N.value=!0,r.value.transportId?await ra(r.value).finally(()=>N.value=!1):await na(r.value).finally(()=>N.value=!1),p==null||p.$modal.msgSuccess("操作成功"),_.visible=!1,await P())})},W=async n=>{const e=(n==null?void 0:n.transportId)||L.value;await(p==null?void 0:p.$modal.confirm('是否确认删除拉运记录编号为"'+e+'"的数据项？').finally(()=>E.value=!1)),await ia(e),p==null||p.$modal.msgSuccess("删除成功"),await P()},Te=()=>{p==null||p.download("epfy/transportRecord/export",{...i.value},`拉运记录_${new Date().getTime()}.xlsx`)},Ie=()=>{let n=k.value.find(e=>e.appId===r.value.applicationId);r.value.departurePoint=n.loadingLocation,r.value.arrivalPoint=n.unloadLocation};return Ye(()=>{P(),ve()}),(n,e)=>{var te,oe;const s=la,d=Fe,C=Ae,h=xe,g=Me,b=He,Z=Le,ee=qe,Y=aa,ke=ea,Ce=Ze,c=Ke,ae=Be,H=je,he=ze,Re=Oe,le=We,Ue=Xe,w=$e("hasPermi"),De=Je;return u(),T("div",pa,[l(Qe,{"enter-active-class":(te=t(p))==null?void 0:te.animate.searchAnimate.enter,"leave-active-class":(oe=t(p))==null?void 0:oe.animate.searchAnimate.leave},{default:o(()=>[y(z("div",sa,[l(ee,{shadow:"hover"},{default:o(()=>[l(Z,{ref_key:"queryFormRef",ref:O,model:t(i),inline:!0,"label-width":"100px"},{default:o(()=>[l(d,{label:"装运时间",prop:"monthQuery"},{default:o(()=>[l(s,{modelValue:t(i).monthQuery,"onUpdate:modelValue":e[0]||(e[0]=a=>t(i).monthQuery=a),clearable:"",placeholder:"请选择装运时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),l(d,{label:"分类",prop:"mediumCategory"},{default:o(()=>[l(h,{modelValue:t(i).mediumCategory,"onUpdate:modelValue":e[1]||(e[1]=a=>t(i).mediumCategory=a),disabled:"",placeholder:"请选择分类"},{default:o(()=>[(u(!0),T(R,null,U(t($),a=>(u(),m(C,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(d,{label:"起运点",prop:"departurePoint"},{default:o(()=>[l(g,{modelValue:t(i).departurePoint,"onUpdate:modelValue":e[2]||(e[2]=a=>t(i).departurePoint=a),placeholder:"请输入起运点",clearable:"",onKeyup:D(V,["enter"])},null,8,["modelValue"])]),_:1}),l(d,{label:"车牌号",prop:"licensePlate"},{default:o(()=>[l(g,{modelValue:t(i).licensePlate,"onUpdate:modelValue":e[3]||(e[3]=a=>t(i).licensePlate=a),placeholder:"请输入车牌号",clearable:"",onKeyup:D(V,["enter"])},null,8,["modelValue"])]),_:1}),l(d,{label:"接收点",prop:"arrivalPoint"},{default:o(()=>[l(g,{modelValue:t(i).arrivalPoint,"onUpdate:modelValue":e[4]||(e[4]=a=>t(i).arrivalPoint=a),placeholder:"请输入接收点",clearable:"",onKeyup:D(V,["enter"])},null,8,["modelValue"])]),_:1}),l(d,{label:"接收人",prop:"receiver"},{default:o(()=>[l(g,{modelValue:t(i).receiver,"onUpdate:modelValue":e[5]||(e[5]=a=>t(i).receiver=a),placeholder:"请输入接收人",clearable:"",onKeyup:D(V,["enter"])},null,8,["modelValue"])]),_:1}),l(d,{label:"拉运申请名称",prop:"appName"},{default:o(()=>[l(g,{modelValue:t(i).appName,"onUpdate:modelValue":e[6]||(e[6]=a=>t(i).appName=a),placeholder:"请输入拉运申请名称",clearable:"",onKeyup:D(V,["enter"])},null,8,["modelValue"])]),_:1}),l(d,null,{default:o(()=>[l(b,{type:"primary",icon:"Search",onClick:V},{default:o(()=>e[27]||(e[27]=[f("搜索")])),_:1}),l(b,{icon:"Refresh",onClick:ye},{default:o(()=>e[28]||(e[28]=[f("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[ie,t(S)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(ee,{shadow:"never"},{header:o(()=>[l(Ce,{gutter:10,class:"mb8"},{default:o(()=>[l(Y,{span:1.5},{default:o(()=>[y((u(),m(b,{type:"primary",plain:"",icon:"Plus",onClick:Ve},{default:o(()=>e[29]||(e[29]=[f("新增")])),_:1})),[[w,["epfy:transportRecord:add"]]])]),_:1}),l(Y,{span:1.5},{default:o(()=>[y((u(),m(b,{disabled:t(B),icon:"Edit",plain:"",type:"success",onClick:e[7]||(e[7]=a=>X())},{default:o(()=>e[30]||(e[30]=[f("修改")])),_:1},8,["disabled"])),[[w,["epfy:transportRecord:edit"]]])]),_:1}),l(Y,{span:1.5},{default:o(()=>[y((u(),m(b,{disabled:t(j),icon:"Delete",plain:"",type:"danger",onClick:e[8]||(e[8]=a=>W())},{default:o(()=>e[31]||(e[31]=[f("删除")])),_:1},8,["disabled"])),[[w,["epfy:transportRecord:remove"]]])]),_:1}),l(Y,{span:1.5},{default:o(()=>[y((u(),m(b,{type:"warning",plain:"",icon:"Download",onClick:Te},{default:o(()=>e[32]||(e[32]=[f("导出")])),_:1})),[[w,["epfy:transportRecord:export"]]])]),_:1}),l(ke,{showSearch:t(S),"onUpdate:showSearch":e[9]||(e[9]=a=>Ge(S)?S.value=a:null),onQueryTable:P},null,8,["showSearch"])]),_:1})]),default:o(()=>[y((u(),m(he,{stripe:"",data:t(K),onSelectionChange:_e},{default:o(()=>[l(c,{type:"selection",width:"55",align:"center"}),l(c,{align:"center",label:"拉运申请","min-width":"400",prop:"applicationId"},{default:o(a=>[f(de(t(ge)[a.row.applicationId]||"未知"),1)]),_:1}),l(c,{align:"center",label:"装运时间",prop:"transportTime",width:"110"},{default:o(a=>[z("span",null,de(n.parseTime(a.row.transportTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(c,{label:"分类",align:"center",prop:"mediumCategory"},{default:o(a=>[l(ae,{options:t($),value:a.row.mediumCategory},null,8,["options","value"])]),_:1}),l(c,{label:"介质类型",align:"center",prop:"mediumType"},{default:o(a=>[l(ae,{options:t(se),value:a.row.mediumType},null,8,["options","value"])]),_:1}),l(c,{align:"center",label:"起运点","min-width":"130",prop:"departurePoint"}),l(c,{label:"数量(方)",align:"center",prop:"number"}),l(c,{label:"拉运人",align:"center",prop:"transporter"}),l(c,{align:"center",label:"车牌号",prop:"licensePlate",width:"100"}),l(c,{align:"center",label:"接收点","min-width":"130",prop:"arrivalPoint"}),l(c,{label:"接收人",align:"center",prop:"receiver"}),l(c,{align:"center",label:"卸液时间",prop:"unloadTime","min-width":"105"}),l(c,{label:"备注信息",align:"center",prop:"remark"}),l(c,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作",width:"240"},{default:o(a=>[l(H,{content:"拉运材料",placement:"top"},{default:o(()=>[y((u(),m(b,{icon:"Document",link:"",type:"primary",onClick:re=>we(a.row)},{default:o(()=>e[33]||(e[33]=[f("拉运材料")])),_:2},1032,["onClick"])),[[w,["epfy:transportRecord:preview"]]])]),_:2},1024),l(H,{content:"修改",placement:"top"},{default:o(()=>[y((u(),m(b,{link:"",type:"primary",icon:"Edit",onClick:re=>X(a.row)},{default:o(()=>e[34]||(e[34]=[f("修改")])),_:2},1032,["onClick"])),[[w,["epfy:transportRecord:edit"]]])]),_:2},1024),l(H,{content:"删除",placement:"top"},{default:o(()=>[y((u(),m(b,{icon:"Delete",link:"",type:"primary",onClick:re=>W(a.row)},{default:o(()=>e[35]||(e[35]=[f("删除")])),_:2},1032,["onClick"])),[[w,["epfy:transportRecord:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[De,t(E)]]),y(l(Re,{total:t(F),page:t(i).pageNum,"onUpdate:page":e[10]||(e[10]=a=>t(i).pageNum=a),limit:t(i).pageSize,"onUpdate:limit":e[11]||(e[11]=a=>t(i).pageSize=a),onPagination:P},null,8,["total","page","limit"]),[[ie,t(F)>0]])]),_:1}),l(Ue,{title:t(_).title,modelValue:t(_).visible,"onUpdate:modelValue":e[26]||(e[26]=a=>t(_).visible=a),width:"500px","append-to-body":""},{footer:o(()=>[z("div",ma,[l(b,{loading:t(N),type:"primary",onClick:Pe},{default:o(()=>e[37]||(e[37]=[f("确 定")])),_:1},8,["loading"]),l(b,{onClick:fe},{default:o(()=>e[38]||(e[38]=[f("取 消")])),_:1})])]),default:o(()=>[l(Z,{ref_key:"transportRecordFormRef",ref:x,model:t(r),rules:t(ce),"label-width":"130px"},{default:o(()=>[t(A)==!1?(u(),m(d,{key:0,label:"拉运申请",prop:"applicationId"},{default:o(()=>[l(h,{modelValue:t(r).applicationId,"onUpdate:modelValue":e[12]||(e[12]=a=>t(r).applicationId=a),filterable:"",placeholder:"请选择拉运申请",onChange:Ie},{default:o(()=>[(u(!0),T(R,null,U(t(J),a=>(u(),m(C,{key:a.appId,label:a.appName,value:a.appId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):(u(),m(d,{key:1,label:"拉运申请",prop:"applicationId"},{default:o(()=>[l(h,{modelValue:t(r).applicationId,"onUpdate:modelValue":e[13]||(e[13]=a=>t(r).applicationId=a),disabled:"",filterable:"",placeholder:"请选择拉运申请"},{default:o(()=>[(u(!0),T(R,null,U(t(k),a=>(u(),m(C,{key:a.appId,label:a.appName,value:a.appId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),l(d,{label:"装运时间",prop:"transportTime"},{default:o(()=>[l(s,{modelValue:t(r).transportTime,"onUpdate:modelValue":e[14]||(e[14]=a=>t(r).transportTime=a),clearable:"",placeholder:"请选择装运时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(d,{label:"分类",prop:"mediumCategory"},{default:o(()=>[l(h,{modelValue:t(r).mediumCategory,"onUpdate:modelValue":e[15]||(e[15]=a=>t(r).mediumCategory=a),placeholder:"请选择分类",disabled:""},{default:o(()=>[(u(!0),T(R,null,U(t($),a=>(u(),m(C,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(d,{label:"起运点",prop:"departurePoint"},{default:o(()=>[l(g,{modelValue:t(r).departurePoint,"onUpdate:modelValue":e[16]||(e[16]=a=>t(r).departurePoint=a),disabled:"",placeholder:"请输入起运点"},null,8,["modelValue"])]),_:1}),l(d,{label:"数量",prop:"number"},{default:o(()=>[l(g,{modelValue:t(r).number,"onUpdate:modelValue":e[17]||(e[17]=a=>t(r).number=a),placeholder:"请输入数量"},{append:o(()=>e[36]||(e[36]=[f("方")])),_:1},8,["modelValue"])]),_:1}),l(d,{label:"拉运人",prop:"transporter"},{default:o(()=>[l(g,{modelValue:t(r).transporter,"onUpdate:modelValue":e[18]||(e[18]=a=>t(r).transporter=a),placeholder:"请输入拉运人"},null,8,["modelValue"])]),_:1}),l(d,{label:"车牌号",prop:"licensePlate"},{default:o(()=>[l(h,{modelValue:t(r).licensePlate,"onUpdate:modelValue":e[19]||(e[19]=a=>t(r).licensePlate=a),placeholder:"请选择车牌号"},{default:o(()=>[(u(!0),T(R,null,U(t(q),a=>(u(),m(C,{key:a.carNumber,label:a.carNumber,value:a.carNumber},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(d,{label:"接收点",prop:"arrivalPoint"},{default:o(()=>[l(g,{modelValue:t(r).arrivalPoint,"onUpdate:modelValue":e[20]||(e[20]=a=>t(r).arrivalPoint=a),disabled:"",placeholder:"请输入接收点"},null,8,["modelValue"])]),_:1}),l(d,{label:"接收人",prop:"receiver"},{default:o(()=>[l(g,{modelValue:t(r).receiver,"onUpdate:modelValue":e[21]||(e[21]=a=>t(r).receiver=a),placeholder:"请输入接收人"},null,8,["modelValue"])]),_:1}),l(d,{label:"卸液时间",prop:"unloadTime"},{default:o(()=>[l(s,{modelValue:t(r).unloadTime,"onUpdate:modelValue":e[22]||(e[22]=a=>t(r).unloadTime=a),clearable:"",placeholder:"请选择卸液时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(d,{label:"备注信息",prop:"remark"},{default:o(()=>[l(g,{modelValue:t(r).remark,"onUpdate:modelValue":e[23]||(e[23]=a=>t(r).remark=a),placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1}),l(d,{label:"计量凭证或转运单",prop:"measurementVoucher"},{default:o(()=>[l(le,{modelValue:t(r).measurementVoucher,"onUpdate:modelValue":e[24]||(e[24]=a=>t(r).measurementVoucher=a),"attach-source-id":t(r).transportId,disabled:!1,"attach-category":"transportMeasurement","attach-source-type":"gxTransportRecord"},null,8,["modelValue","attach-source-id"])]),_:1}),l(d,{label:"装卸照片",prop:"photo"},{default:o(()=>[l(le,{modelValue:t(r).photo,"onUpdate:modelValue":e[25]||(e[25]=a=>t(r).photo=a),"attach-source-id":t(r).transportId,disabled:!1,"attach-category":"transportPhoto","attach-source-type":"gxTransportRecord"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Da as default};
