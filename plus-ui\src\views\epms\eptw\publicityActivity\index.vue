<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="活动时间" prop="activityTime">
              <el-date-picker
                v-model="queryParams.activityTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="活动主题" prop="activitySubject">
              <el-input v-model="queryParams.activitySubject" placeholder="请输入活动主题" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="活动类型" prop="activityType">
              <el-select v-model="queryParams.activityType" clearable placeholder="请选择活动类型">
                <el-option v-for="dict in eptw_activity_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epms:publicityActivity:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:publicityActivity:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:publicityActivity:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:publicityActivity:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="publicityActivityList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column label="活动时间" align="center" prop="activityTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.activityTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="活动主题" prop="activitySubject" width="300" />
        <el-table-column label="活动类型" align="center" prop="activityType">
          <template #default="scope">
            <dict-tag :options="eptw_activity_type" :value="scope.row.activityType" />
          </template>
        </el-table-column>
        <el-table-column label="活动内容" align="center" prop="activityContent" />
        <el-table-column label="活动状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="eptw_activity_status" :value="scope.row.status" />
          </template> </el-table-column
        ><el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="宣传活动" placement="top">
              <el-button v-hasPermi="['epms:publicityActivity:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >活动预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:publicityActivity:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epms:publicityActivity:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"
                >修改</el-button
              >
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:publicityActivity:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改宣传活动对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="publicityActivityFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="活动时间" prop="activityTime">
          <el-date-picker v-model="form.activityTime" clearable placeholder="请选择活动时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="活动主题" prop="activitySubject">
          <el-input v-model="form.activitySubject" placeholder="请输入活动主题" />
        </el-form-item>
        <el-form-item label="活动类型" prop="activityType">
          <el-select v-model="form.activityType" placeholder="请选择活动类型">
            <el-option v-for="dict in eptw_activity_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择活动状态">
            <el-option v-for="dict in eptw_activity_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动附件" prop="activityAttach">
          <attachFileUpload
            v-model="form.activityAttach"
            :attach-source-id="form.activityId"
            :disabled="false"
            attach-category="publicityActivity"
            attach-source-type="elCompany"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="活动内容">
          <editor v-model="form.activityContent" :min-height="50" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="publicityActivityFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <el-form-item label="活动时间" prop="activityTime">
          <el-date-picker v-model="form.activityTime" clearable placeholder="请选择活动时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="活动主题" prop="activitySubject">
          <el-input v-model="form.activitySubject" placeholder="请输入活动主题" />
        </el-form-item>
        <el-form-item label="活动类型" prop="activityType">
          <el-select v-model="form.activityType" placeholder="请选择活动类型">
            <el-option v-for="dict in eptw_activity_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择活动状态">
            <el-option v-for="dict in eptw_activity_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动内容" prop="activityContent">
          <editor v-model="form.activityContent" :min-height="192" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PublicityActivity" lang="ts">
import {
  addPublicityActivity,
  delPublicityActivity,
  getPublicityActivity,
  listPublicityActivity,
  updatePublicityActivity
} from '@/api/epms/eptw/publicityActivity';
import { PublicityActivityForm, PublicityActivityQuery, PublicityActivityVO } from '@/api/epms/eptw/publicityActivity/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_activity_type, eptw_activity_status } = toRefs<any>(proxy?.useDict('eptw_activity_type', 'eptw_activity_status'));

const publicityActivityList = ref<PublicityActivityVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const router = useRouter();
const queryFormRef = ref<ElFormInstance>();
const publicityActivityFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PublicityActivityForm = {
  activityId: undefined,
  activityTime: undefined,
  activitySubject: undefined,
  activityType: undefined,
  activityAttach: undefined,
  activityContent: undefined,
  status: undefined
};
const data = reactive<PageData<PublicityActivityForm, PublicityActivityQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    activityTime: [],
    activitySubject: undefined,
    activityType: undefined,
    params: {}
  },
  rules: {
    activityId: [{ required: true, message: '宣传id不能为空', trigger: 'blur' }],
    activityTime: [{ required: true, message: '活动时间不能为空', trigger: 'change' }],
    activitySubject: [{ required: true, message: '活动主题不能为空', trigger: 'blur' }],
    activityType: [{ required: true, message: '活动类型不能为空', trigger: 'change' }],
    activityContent: [{ required: true, message: '活动内容不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '活动状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询宣传活动列表 */
const getList = async () => {
  loading.value = true;
  if (queryParams.value.activityTime && queryParams.value.activityTime.length === 2) {
    queryParams.value.startTime = queryParams.value.activityTime[0].toString();
    queryParams.value.endTime = queryParams.value.activityTime[1].toString();
  } else {
    queryParams.value.startTime = undefined;
    queryParams.value.endTime = undefined;
  }
  queryParams.value.activityTime = undefined;
  const res = await listPublicityActivity(queryParams.value);
  publicityActivityList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  publicityActivityFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PublicityActivityVO[]) => {
  ids.value = selection.map((item) => item.activityId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加宣传活动';
};

const handlePreview = async (row?: PublicityActivityVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.activityId,
    attachSourceType: 'elCompany',
    attachCategory: 'publicityActivity'
  });
};
const handleDetail = async (row?: PublicityActivityVO) => {
  reset();
  const _activityId = row?.activityId || ids.value[0];
  const res = await getPublicityActivity(_activityId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '宣传活动详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: PublicityActivityVO) => {
  reset();
  const _activityId = row?.activityId || ids.value[0];
  const res = await getPublicityActivity(_activityId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改宣传活动';
};

/** 提交按钮 */
const submitForm = () => {
  publicityActivityFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.activityId) {
        await updatePublicityActivity(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addPublicityActivity(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PublicityActivityVO) => {
  const _activityIds = row?.activityId || ids.value;
  await proxy?.$modal.confirm('是否确认删除宣传活动编号为"' + _activityIds + '"的数据项？').finally(() => (loading.value = false));
  await delPublicityActivity(_activityIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/publicityActivity/export',
    {
      ...queryParams.value
    },
    `宣传活动_${new Date().getTime()}.xlsx`
  );
};
const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
:deep(.el-date-editor) {
  --el-date-editor-width: 100%;
}
</style>

<style lang="scss">
/* 全局样式表格内容最多显示两行，超出用省略号表示 */
.el-table .cell {
  display: -webkit-box; /* 使用 WebKit 盒模型 */
  -webkit-line-clamp: 2; /* 设置最多显示两行 */
  -webkit-box-orient: vertical; /* 设置盒模型的方向为垂直 */
  text-overflow: ellipsis; /* 使用省略号表示溢出内容 */
}
</style>
