import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DetectionResultForm, DetectionResultQuery, DetectionResultVO } from '@/api/epms/eppcs/detectionResult/types';

/**
 * 查询检测结果列表
 * @param query
 * @returns {*}
 */

export const listDetectionResult = (query?: DetectionResultQuery): AxiosPromise<DetectionResultVO[]> => {
  return request({
    url: '/epms/eppcs/detectionResult/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询检测结果详细
 * @param resultId
 */
export const getDetectionResult = (resultId: string | number): AxiosPromise<DetectionResultVO> => {
  return request({
    url: '/epms/eppcs/detectionResult/' + resultId,
    method: 'get'
  });
};

/**
 * 新增检测结果
 * @param data
 */
export const addDetectionResult = (data: DetectionResultForm) => {
  return request({
    url: '/epms/eppcs/detectionResult',
    method: 'post',
    data: data
  });
};

/**
 * 修改检测结果
 * @param data
 */
export const updateDetectionResult = (data: DetectionResultForm) => {
  return request({
    url: '/epms/eppcs/detectionResult',
    method: 'put',
    data: data
  });
};

/**
 * 删除检测结果
 * @param resultId
 */
export const delDetectionResult = (resultId: string | number | Array<string | number>) => {
  return request({
    url: '/epms/eppcs/detectionResult/' + resultId,
    method: 'delete'
  });
};

/**
 * 提交检测结果
 * @param resultId 检测结果ID
 * @param userType 用户类型
 */
export const submitDetectionResult = (resultId: string | number, userType: number = 1) => {
  return request({
    url: `/epms/eppcs/detectionResult/submit/${resultId}`,
    method: 'post',
    params: { userType }
  });
};

/**
 * 属地确认检测结果
 * @param resultId 检测结果ID
 * @param userType 用户类型
 * @param action 动作 (1达标确认, 2不达标报告)
 * @param approvalRemark 审批意见
 * @param approver 审批人
 */
export const localConfirmDetectionResult = (
  resultId: string | number,
  userType: number,
  action: number,
  approvalRemark: string,
  approver: string
) => {
  return request({
    url: `/epms/eppcs/detectionResult/localConfirm/${resultId}`,
    method: 'post',
    params: {
      userType,
      action,
      approvalRemark,
      approver
    }
  });
};

/**
 * 管理中心确认检测结果
 * @param resultId 检测结果ID
 * @param userType 用户类型
 * @param action 动作 (1结束, 2驳回)
 * @param approvalRemark 审批意见
 * @param approver 审批人
 */
export const centerConfirmDetectionResult = (
  resultId: string | number,
  userType: number,
  action: number,
  approvalRemark: string,
  approver: string
) => {
  return request({
    url: `/epms/eppcs/detectionResult/centerConfirm/${resultId}`,
    method: 'post',
    params: {
      userType,
      action,
      approvalRemark,
      approver
    }
  });
};
