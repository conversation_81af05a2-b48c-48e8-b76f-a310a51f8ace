<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="行政区域" prop="administrativeArea">
              <el-select v-model="queryParams.administrativeArea" clearable placeholder="请选择行政区域" @keyup.enter="handleQuery">
                <el-option v-for="dict in eptw_administrative_area" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="所属地" prop="operationAreaParentId">
              <el-select
                v-model="queryParams.operationAreaParentId"
                clearable
                placeholder="请选择所属地"
                @change="queryByParentId(queryParams.operationAreaParentId as number)"
                @keyup.enter="handleQuery"
              >
                <el-option
                  v-for="dict in operationAreaList"
                  :key="dict.operationAreaId"
                  :label="dict.operationAreaName"
                  :value="dict.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="取水区块" prop="operationAreaId">
              <el-select v-model="queryParams.operationAreaId" clearable placeholder="请选择取水区块" @keyup.enter="handleQuery">
                <el-option v-for="dict in queryList" :key="dict.operationAreaId" :label="dict.operationAreaName" :value="dict.operationAreaId" />
              </el-select>
            </el-form-item>
            <el-form-item label="报告状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择报告状态">
                <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="论证日期" prop="params">
              <el-date-picker
                v-model="queryParams.params"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:demonstration:add']" icon="Plus" plain type="primary" @click="handleAdd"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:demonstration:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:demonstration:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:demonstration:export']" icon="Download" plain type="warning" @click="handleExport">导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="demonstrationList" stripe @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column label="行政区域" align="center" prop="administrativeArea">
          <template #default="scope">
            {{ getAdministrativeAreaText(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="所属地" align="center" prop="operationAreaName">
          <template #default="scope">
            {{ getAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="取水区块" align="center" prop="blockName">
          <template #default="scope">
            {{ getBlockName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="报告状态" align="center" prop="status" width="180">
          <template #default="scope">
            <dict-tag :options="eptw_file_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="论证日期" align="center" prop="demonstrationTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.demonstrationTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="剩余时间(天)" prop="effectiveDay" />
        <el-table-column align="center" label="上传时间" prop="uploadTime" width="185">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="305">
          <template #default="scope">
            <el-tooltip content="论证报告" placement="top">
              <el-button v-hasPermi="['epms:demonstration:edit']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >报告预览
              </el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)">详情 </el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epms:demonstration:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)">修改 </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:demonstration:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改论证报告对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="demonstrationFormRef" :model="form" :rules="rules" label-width="100px">
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" filterable placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaParentId">
          <el-select
            v-model="form.operationAreaParentId"
            filterable
            placeholder="请选择所属地"
            @change="getOperationAreaList(form.operationAreaParentId as number, 'operationAreaParentId')"
          >
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水区块" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择取水区块">
            <el-option
              v-for="dict in blockQueryList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择报告状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="论证日期" prop="demonstrationTime">
          <el-date-picker v-model="form.demonstrationTime" clearable placeholder="请选择论证日期" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="论证报告附件" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.demonstrationId"
            :disabled="false"
            attach-category="demonstration"
            attach-source-type="waterBlock"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="demonstrationFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" filterable placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaParentId">
          <el-select
            v-model="form.operationAreaParentId"
            filterable
            placeholder="请选择所属地"
            @change="getOperationAreaList(form.operationAreaParentId as number, 'operationAreaParentId')"
          >
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水区块" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择取水区块">
            <el-option
              v-for="dict in blockQueryList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择报告状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="论证日期" prop="demonstrationTime">
          <el-date-picker v-model="form.demonstrationTime" clearable placeholder="请选择论证日期" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker v-model="form.uploadTime" clearable placeholder="上传时间" type="datetime"> </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Demonstration" lang="ts">
import { addDemonstration, delDemonstration, getDemonstration, listDemonstration, updateDemonstration } from '@/api/epms/eptw/demonstration';
import { DemonstrationForm, DemonstrationQuery, DemonstrationVO } from '@/api/epms/eptw/demonstration/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_file_status, eptw_administrative_area } = toRefs<any>(proxy?.useDict('eptw_file_status', 'eptw_administrative_area'));

const demonstrationList = ref<DemonstrationVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const demonstrationFormRef = ref<ElFormInstance>();

const operationAreaQuery = reactive<OperationAreaQuery>({});
// 渲染列表
const operationAreaList = ref([]);
const blockList = ref<OperationAreaVO[]>([]);
// 参数列表
const blockQueryList = ref([]);
const queryList = ref([]);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DemonstrationForm = {
  demonstrationId: undefined,
  demonstrationTime: undefined,
  operationAreaId: undefined,
  file: undefined
};
const data = reactive<PageData<DemonstrationForm, DemonstrationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    demonstrationTime: undefined,
    operationAreaId: undefined,
    administrativeArea: undefined,
    operationAreaParentId: undefined,
    file: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    demonstrationId: [{ required: true, message: '论证报告id不能为空', trigger: 'blur' }],
    demonstrationTime: [{ required: true, message: '论证日期不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '取水区块id不能为空', trigger: 'change' }],
    file: [{ required: true, message: '附件不能为空', trigger: 'blur' }],
    // operationAreaParentId: [
    //   {required: true, message: "所属地不能为空", trigger: "change"}
    // ],
    // administrativeArea: [
    //   {required: true, message: "行政区域不能为空", trigger: "change"}
    // ],
    status: [{ required: true, message: '论证状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询论证报告列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDemonstration(queryParams.value);
  demonstrationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  demonstrationFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.operationAreaId = null;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DemonstrationVO[]) => {
  ids.value = selection.map((item) => item.demonstrationId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  operationAreaQuery.operationAreaParentId = null;
  getOperationAreaList();
  reset();
  dialog.visible = true;
  dialog.title = '添加论证报告';
};

const handlePreview = async (row?: DemonstrationVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.demonstrationId,
    attachSourceType: 'waterBlock',
    attachCategory: 'demonstration'
  });
};
const handleDetail = async (row?: DemonstrationVO) => {
  reset();
  const _demonstrationId = row?.demonstrationId || ids.value[0];
  const res = await getDemonstration(_demonstrationId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '论证报告详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: DemonstrationVO) => {
  reset();
  const _demonstrationId = row?.demonstrationId || ids.value[0];
  const res = await getDemonstration(_demonstrationId);
  Object.assign(form.value, res.data);
  await getOperationAreaList(form.value.operationAreaParentId, 'operationAreaParentId');
  dialog.visible = true;
  dialog.title = '修改论证报告';
};

/** 提交按钮 */
const submitForm = () => {
  demonstrationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.demonstrationId) {
        await updateDemonstration(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addDemonstration(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DemonstrationVO) => {
  const _demonstrationIds = row?.demonstrationId || ids.value;
  await proxy?.$modal.confirm('是否确认删除论证报告编号为"' + _demonstrationIds + '"的数据项？').finally(() => (loading.value = false));
  await delDemonstration(_demonstrationIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/demonstration/export',
    {
      ...queryParams.value
    },
    `论证报告_${new Date().getTime()}.xlsx`
  );
};

const getAdministrativeAreaText = (blockId: number) => {
  if (!blockId) {
    return '未知';
  }
  const block = blockList.value.find((item) => item.operationAreaId === blockId);
  if (block) {
    const administrativeAreaItem = eptw_administrative_area.value.find((item) => item.value == block.administrativeArea);
    return administrativeAreaItem ? administrativeAreaItem.label : '未知';
  } else {
    return '未知';
  }
};

const getBlockName = (blockId: number) => {
  if (!blockId) {
    return '未知';
  }
  const block = blockList.value.find((item) => item.operationAreaId === blockId);
  return block ? block.operationAreaName : '未知';
};

const getAreaName = (blockId: number) => {
  if (!blockId) {
    return '未知';
  }
  const block = blockList.value.find((item) => item.operationAreaId === blockId);
  if (block) {
    const area = blockList.value.find((item) => item.operationAreaId === block.operationAreaParentId);
    return area ? area.operationAreaName : '未知';
  } else {
    return '未知';
  }
};

/**
 * 获取所属地下取水区块列表
 */
const getOperationAreaList = async (operationAreaId?: number, type?: string) => {
  if (type === 'operationAreaParentId') {
    queryParams.value.operationAreaId = null;
    operationAreaQuery.operationAreaParentId = operationAreaId;
  }
  blockQueryList.value = [];
  operationAreaQuery.operationAreaType = 1;
  listOperationArea(operationAreaQuery).then((res) => {
    blockQueryList.value = res.rows;
  });
};

const queryByParentId = async (operationAreaId?: number) => {
  operationAreaQuery.operationAreaParentId = operationAreaId;
  operationAreaQuery.operationAreaType = 1;
  listOperationArea(operationAreaQuery).then((res) => {
    queryList.value = res.rows;
  });
};

/**
 * 获取所属地列表
 */
const getAreaList = async () => {
  operationAreaList.value = [];
  operationAreaQuery.operationAreaType = 0;
  const resOperationArea = await listOperationArea(operationAreaQuery);
  operationAreaList.value = resOperationArea.rows;
};
const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
  form.value.balanceReportName = fielInfo.name;
};
/**
 * 获取全部取水区块列表
 */
const getBlockList = () => {
  blockList.value = [];
  listOperationArea().then((res) => {
    blockList.value = res.rows;
  });
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
  getAreaList();
  getBlockList();
  getOperationAreaList();
  queryByParentId();
});
</script>
