package com.biz.epms.eppcs.domain.bo;

import com.biz.epms.eppcs.domain.EppcsUnsealingInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 拆封信息业务对象 eppcs_unsealing_info
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = EppcsUnsealingInfo.class, reverseConvertGenerate = false)
public class EppcsUnsealingInfoBo extends BaseEntity {

    /**
     * 拆封id
     */
    private Long unsealingId;

    /**
     * 关联申请ID
     */
    private Long applicationId;


    /**
     * 实验室封条照片附件
     */
    private String unsealPhoto;

    /**
     * 拆封时间
     */
    private Date unsealTime;

    /**
     * 拆封人
     */
    private String unsealer;

    /**
     * 拆封状态 (0草稿,1驳回,2待确认,3已确认)
     */
    private Integer unsealingStatus;

    /**
     * 审批时间
     */
    private Date approvalTime;
    /**
     * 审批人
     */
    private String approver;

    /**
     * 审批建议
     */
    private String approvalRemark;

    /**
     * 备注
     */
    private String remark;


}
