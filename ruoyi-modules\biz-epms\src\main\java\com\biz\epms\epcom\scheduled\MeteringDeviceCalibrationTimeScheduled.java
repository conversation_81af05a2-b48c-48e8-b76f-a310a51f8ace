package com.biz.epms.epcom.scheduled;

import cn.zoneday.scada.model.EnergyProp;
import cn.zoneday.scada.service.EnergyTableService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.biz.epms.common.enums.EpmsBjlx;
import com.biz.epms.common.enums.JiLiangQiJuParam;
import com.biz.epms.epcom.common.enums.EpcomMeteringDeviceStatusEnum;
import com.biz.epms.epcom.domain.EpcomMeteringDevice;
import com.biz.epms.epcom.mapper.EpcomMeteringDeviceMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计量器具校准时间存到属性表定时任务
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MeteringDeviceCalibrationTimeScheduled {

    private final EpcomMeteringDeviceMapper baseMapper;
    private final EnergyTableService energyTableService;

    @Scheduled(cron = "0 0 * * * ?")
    public void updateWaterStatus() {

        try {
            // 获取所有在用的计量器具
            List<EpcomMeteringDevice> meteringDevices = baseMapper.selectList(new LambdaQueryWrapper<EpcomMeteringDevice>()
                    .eq(EpcomMeteringDevice::getStatus, EpcomMeteringDeviceStatusEnum.IN_USE.getValue()));
            if (CollectionUtils.isEmpty(meteringDevices)) {
                return;
            }

            // 获取所有计量器具属性表数据
            EnergyProp energyPropQuery = new EnergyProp();
            energyPropQuery.setDevice_param(JiLiangQiJuParam.jiaoDingShiJian.getValue());
            energyPropQuery.setDevice_type(EpmsBjlx.jiliangqiju.getValue());

            List<EnergyProp> energyProps = energyTableService.queryEnergyPropList(energyPropQuery);
            // 转map
            Map<Integer, EnergyProp> energyPropMap = StreamUtils.toMap(energyProps, EnergyProp::getDevice_id, Function.identity());

            Map<String, List<EnergyProp>> collect = meteringDevices.stream()
                    .filter(v -> v.getIotId() != null)
                    .map(v -> {

                        EnergyProp energyProp = new EnergyProp();
                        energyProp.setDevice_id(v.getIotId());
                        energyProp.setDevice_param(JiLiangQiJuParam.jiaoDingShiJian.getValue());
                        energyProp.setDevice_type(EpmsBjlx.jiliangqiju.getValue());
                        energyProp.setQuality(100);
                        energyProp.setValue(v.getCalibrationTime() != null ? DateUtils.formatDate(v.getCalibrationTime()) : "");
                        EnergyProp oldEenergyProp = energyPropMap.get(v.getIotId());
                        // 如果已存在但是相同返回null, 避免无用的更新
                        if (oldEenergyProp != null && oldEenergyProp.getValue().equals(energyProp.getValue())){
                            return null;
                        }
                        return energyProp;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(v -> energyPropMap.containsKey(v.getDevice_id()) ? "update" : "insert"));

            List<EnergyProp> insertList = collect.get("insert");
            List<EnergyProp> updateList = collect.get("update");

            if (CollectionUtils.isNotEmpty(insertList)){
                energyTableService.addEnergyPropList(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)){
                energyTableService.updateEnergyPropList(updateList);
            }

            // 处理已停用的
            Map<Integer, EpcomMeteringDevice> epcomMeteringDeviceMap = StreamUtils.toMap(meteringDevices, EpcomMeteringDevice::getIotId, Function.identity());

            List<EnergyProp> stopList = energyPropMap.entrySet().stream()
                    .filter(v -> !epcomMeteringDeviceMap.containsKey(v.getKey()))
                    .map(Map.Entry::getValue)
                    .filter(v -> !"2000-01-01".equals(v.getValue()))
                    .peek( v -> v.setValue("2000-01-01"))
                    .toList();

            // 删除非正常计量器具的校订时间， 避免已停用还在继续告警
            if (CollectionUtils.isNotEmpty(stopList)){
                energyTableService.updateEnergyPropList(stopList);
            }
        } catch (Exception e) {
            log.error("转存计量器具校订异常异常", e);
        }
    }
}
