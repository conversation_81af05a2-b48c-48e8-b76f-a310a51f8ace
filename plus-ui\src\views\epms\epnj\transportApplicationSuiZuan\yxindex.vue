<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="96px">
            <el-form-item label="申请名称" prop="appName">
              <el-input v-model="queryParams.appName" placeholder="请输入申请名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="申请分类" prop="mediumCategory">
              <el-select v-model="queryParams.mediumCategory" placeholder="请选择申请分类" disabled>
                <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>
              </el-select>
            </el-form-item>-->
            <el-form-item label="所属作业区" prop="workAreaId">
              <el-select v-model="queryParams.workAreaId" class="searchDate" clearable filterable placeholder="选择所属作业区">
                <el-option
                  v-for="operationArea in operationAreaList"
                  :key="operationArea.operationAreaId"
                  :label="operationArea.operationAreaName"
                  :value="operationArea.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="单井名称" prop="wellName">
              <el-input v-model="queryParams.wellName" placeholder="请输入单井名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请日期" prop="params">
              <el-date-picker
                clearable
                v-model="queryParams.params"
                type="datetimerange"
                start-placeholder="开始时间"
                range-separator="至"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="装车地点" prop="loadingLocation">
              <el-input v-model="queryParams.loadingLocation" placeholder="请输入装车地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卸车地点" prop="unloadLocation">
              <el-input v-model="queryParams.unloadLocation" placeholder="请输入卸车地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="applicationStatus">
              <el-select v-model="queryParams.applicationStatus" clearable placeholder="请选择状态">
                <el-option v-for="dict in epfy_application_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epfy:transportApplication:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epfy:transportApplication:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['epfy:transportApplication:remove']"
              :disabled="multiple"
              icon="Delete"
              plain
              type="danger"
              @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epfy:transportApplication:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="transportApplicationList"
        :row-class-name="tableRowClassName"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="申请名称" prop="appName" width="200" />
        <!--<el-table-column label="申请分类" align="center" prop="mediumCategory">
          <template #default="scope">
            <dict-tag :options="epfy_medium_category" :value="scope.row.mediumCategory"/>
          </template>
        </el-table-column>-->
        <el-table-column align="center" label="所属作业区" prop="workAreaId" width="130">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.workAreaId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="单井名称" prop="wellName" width="110" />
        <el-table-column align="center" label="申请日期" prop="applicationDate" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="装车地点" prop="loadingLocation" width="110" />
        <!--<el-table-column label="卸车地点类型" align="center" width="120" prop="unloadLocationType">
          <template #default="scope">
            <dict-tag :options="epfy_unload_location_type" :value="scope.row.unloadLocationType"/>
          </template>
        </el-table-column>-->
        <el-table-column align="center" label="卸车地点" prop="unloadLocation" width="135" />
        <el-table-column align="center" label="归口部门审核人" prop="superviseApprover" width="80">
          <template #default="scope">
            {{ getApproverName(transport_app_regulatory_authorities_approver, scope.row.superviseApprover) }}
          </template>
        </el-table-column>
        <el-table-column label="归口部门审核" align="center" prop="applicationStatus">
          <template #default="scope">
            <dict-tag :options="epfy_application_status" :value="scope.row.applicationStatus" />
          </template>
        </el-table-column>
        <el-table-column label="归口部门审核时间" align="center" prop="examineApproveTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.examineApproveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="作业区审核人" align="center" prop="operationAreaReviewer">
          <template #default="scope">
            {{ getApproverName(transport_app_operationarea_examine, scope.row.operationAreaReviewer) }}
          </template>
        </el-table-column>
        <el-table-column label="作业区审核" align="center" prop="operationAreaApproveStatus">
          <template #default="scope">
            <dict-tag :options="epfy_application_status" :value="scope.row.operationAreaApproveStatus" />
          </template>
        </el-table-column>
        <el-table-column label="作业区审核时间" align="center" prop="operationAreaReviewedTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.operationAreaReviewedTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核意见" align="center" prop="rejectSuggestion" />
        <el-table-column align="center" label="拉运状态" prop="transportStatus" width="120">
          <template #default="scope">
            <dict-tag :options="transport_application_status" :value="scope.row.transportStatus" />
          </template>
        </el-table-column>
        <el-table-column label="备注信息" align="center" prop="remark" />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="160">
          <template #default="scope">
            <!--            <el-tooltip content="报告预览" placement="top">
              <el-button link type="primary" icon="Document" @click="handlePreview(scope.row)" v-hasPermi="['epfy:transportApplication:preview']">报告预览</el-button>
            </el-tooltip>-->
            <el-tooltip
              v-if="[1].includes(scope.row.applicationStatus) && [2].includes(scope.row.operationAreaApproveStatus)"
              content="归口部门审核"
              placement="top"
            >
              <el-button
                v-hasPermi="['epfy:transportApplication:guikou']"
                icon="Coordinate"
                link
                type="primary"
                @click="handleCoordinate(scope.row, 1)"
                >归口部门审核</el-button
              >
            </el-tooltip>
            <el-tooltip content="作业区审核" placement="top" v-if="[1].includes(scope.row.operationAreaApproveStatus)">
              <el-button
                v-hasPermi="['epfy:transportApplication:zuoyequ']"
                icon="Coordinate"
                link
                type="primary"
                @click="handleCoordinate(scope.row, 2)"
                >作业区审核</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改预警时间" placement="top">
              <el-button
                v-hasPermi="['epfy:transportApplication:coordinate']"
                icon="Edit"
                link
                type="primary"
                @click="handleUpdateWarnTime(scope.row)"
                >修改预警时间</el-button
              >
            </el-tooltip>
            <el-tooltip content="计量凭证下载" placement="top" v-if="scope.row.operationAreaApproveStatus === 2 && scope.row.applicationStatus === 2">
              <el-button icon="Download" link type="primary" @click="handleDownload(scope.row)">计量凭证下载</el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epfy:transportApplication:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"
                >修改</el-button
              >
            </el-tooltip>

            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epfy:transportApplication:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改拉运申请对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="transportApplicationFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="申请名称" prop="appName">
          <el-input v-model="form.appName" placeholder="请输入申请名称" />
        </el-form-item>
        <el-form-item label="申请分类" prop="mediumCategory">
          <el-select v-model="form.mediumCategory" placeholder="请选择申请分类" disabled>
            <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属作业区" prop="workAreaId">
          <el-select filterable v-model="form.workAreaId" placeholder="选择所属作业区" clearable @change="changeSuoShuDi">
            <el-option
              v-for="operationArea in operationAreaList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="单井名称" prop="relatedId">
          <el-select v-model="form.relatedId" placeholder="请选择单井" @change="changeWellPreparation">
            <el-option v-for="item in wellPreparationList" :key="item.prepId" :label="item.wellName" :value="item.prepId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请日期" prop="applicationDate">
          <el-date-picker v-model="form.applicationDate" clearable placeholder="请选择申请日期" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="装车地点" prop="loadingLocation">
          <el-input v-model="form.loadingLocation" disabled placeholder="请输入装车地点" />
        </el-form-item>
        <!--        <el-form-item label="卸车地点类型" prop="unloadLocationType">-->
        <!--          <el-select v-model="form.unloadLocationType" placeholder="请选择卸车地点类型">-->
        <!--            <el-option-->
        <!--                v-for="dict in epfy_unload_location_type"-->
        <!--                :key="dict.value"-->
        <!--                :label="dict.label"-->
        <!--                :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="卸车地点" prop="unloadLocation">
          <el-select filterable v-model="form.unloadLocation" placeholder="选择卸车地点" clearable allow-create>
            <el-option
              v-for="operationArea in pianquList"
              :key="operationArea.operationAreaName"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaName"
            />
          </el-select>
        </el-form-item>
        <!--<el-form-item label="介质类型" prop="mediumType">
          <el-select v-model="form.mediumType" placeholder="请选择介质类型">
            <el-option
              v-for="dict in epnj_suizuan_yexiang_medium_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>-->
        <el-form-item label="归口部门审批人" prop="superviseApprover">
          <el-select v-model="form.superviseApprover" placeholder="请选择归口部门批准人">
            <el-option v-for="item in filteredJianGuan" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="作业区审核人" prop="operationAreaReviewer">
          <el-select v-model="form.operationAreaReviewer" placeholder="请选择作业区审核人">
            <el-option v-for="item in filteredZuoYeQu" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检测报告" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.appId"
            :disabled="false"
            attach-category="transportApplication"
            attach-source-type="njTransportApplication"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
        <el-form-item v-if="suggestion" label="审核意见" prop="rejectSuggestion">
          <el-input v-model="form.rejectSuggestion" disabled placeholder="请输入内容" type="textarea" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入内容" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm(false)">确 定</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm(true)">重新提交审核</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核申请对话框 -->
    <el-dialog :title="cooDialog.title" v-model="cooDialog.visible" width="600px" append-to-body>
      <el-form ref="transportApplicationFormRef" :model="form" :rules="coorules" label-width="140px">
        <el-form-item label="申请名称" prop="appName">
          <el-input disabled v-model="form.appName" placeholder="请输入申请名称" />
        </el-form-item>
        <el-form-item label="申请分类" prop="mediumCategory">
          <el-select v-model="form.mediumCategory" placeholder="请选择申请分类" disabled>
            <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属作业区" prop="workAreaId">
          <el-select disabled filterable v-model="form.workAreaId" class="searchDate" placeholder="选择所属作业区" clearable>
            <el-option
              v-for="operationArea in operationAreaList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="单井名称" prop="relatedId">
          <el-select disabled v-model="form.relatedId" placeholder="请选择单井" @change="changeWellPreparation">
            <el-option v-for="item in wellPreparationList" :key="item.prepId" :label="item.wellName" :value="item.prepId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请日期" prop="applicationDate">
          <el-date-picker
            v-model="form.applicationDate"
            clearable
            disabled
            placeholder="请选择申请日期"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="装车地点" prop="loadingLocation">
          <el-input disabled v-model="form.loadingLocation" placeholder="请输入装车地点" />
        </el-form-item>
        <!--        <el-form-item label="卸车地点类型" prop="unloadLocationType">-->
        <!--          <el-select v-model="form.unloadLocationType" disabled placeholder="请选择卸车地点类型">-->
        <!--            <el-option-->
        <!--              v-for="dict in epfy_unload_location_type"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="卸车地点" prop="unloadLocation">
          <el-input v-model="form.unloadLocation" disabled placeholder="请输入卸车地点" />
        </el-form-item>

        <!-- <el-form-item label="介质类型" prop="mediumType">
          <el-select v-model="form.mediumType" placeholder="请选择介质类型" disabled>
            <el-option
              v-for="dict in epnj_suizuan_yexiang_medium_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>-->

        <el-form-item label="归口部门审核人" prop="superviseApprover" disabled>
          <el-select v-model="form.superviseApprover" disabled placeholder="请选择批准人">
            <el-option v-for="item in filteredJianGuan" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="归口部门审核状态" prop="applicationStatus">
          <el-radio-group v-model="form.applicationStatus" :disabled="approveType != 1">
            <el-radio v-for="dict in epfy_application_status" :key="dict.value" :value="parseInt(dict.value)">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="归口部门审核时间" prop="examineApproveTime">
          <el-date-picker v-model="form.examineApproveTime" clearable disabled placeholder="" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="作业区审核人" prop="operationAreaReviewer" disabled>
          <el-select v-model="form.operationAreaReviewer" placeholder="请选择审核人" disabled>
            <el-option v-for="item in filteredZuoYeQu" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="作业区审核状态" prop="operationAreaApproveStatus">
          <el-radio-group v-model="form.operationAreaApproveStatus" :disabled="approveType != 2">
            <el-radio v-for="dict in epfy_application_status" :key="dict.value" :value="parseInt(dict.value)">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="作业区审核时间" prop="operationAreaReviewedTime">
          <el-date-picker
            v-model="form.operationAreaReviewedTime"
            clearable
            disabled
            placeholder=""
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预警时间" prop="warnTime" v-hasPermi="['epfy:transportApplication:coordinate']">
          <el-input v-model="form.warnTime">
            <template #append>小时</template>
          </el-input>
        </el-form-item>
        <el-form-item label="审核意见" prop="rejectSuggestion">
          <el-input v-model="form.rejectSuggestion" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" type="textarea" disabled placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="cooSubmitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改预警时间 -->
    <el-dialog v-model="warnTimeDialog.visible" :title="warnTimeDialog.title" append-to-body width="500px">
      <el-form ref="transportApplicationFormRef" :model="form" label-width="110px">
        <el-form-item label="预警时间" prop="warnTime">
          <el-input v-model="form.warnTime">
            <template #append>小时</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="warnTimeSubmitForm">确 定</el-button>
          <el-button @click="warnTimeDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TransportApplication" lang="ts">
import {
  addTransportApplication,
  delTransportApplication,
  getTransportApplication,
  listTransportApplication,
  updateTransportApplication,
  updateTransportApplicationCoo
} from '@/api/epms/epfy/transportApplication';
import { TransportApplicationForm, TransportApplicationQuery, TransportApplicationVO } from '@/api/epms/epfy/transportApplication/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { WellPreparationQuery, WellPreparationVO } from '@/api/epms/epnj/wellPreparation/types';
import { listWellPreparation } from '@/api/epms/epnj/wellPreparation';
import dayjs from 'dayjs';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  epfy_medium_category,
  epfy_application_status,
  transport_application_status,
  transport_app_regulatory_authorities_approver,
  transport_app_operationarea_examine
} = toRefs<any>(
  proxy?.useDict(
    'epfy_medium_category',
    'epfy_application_status',
    'transport_application_status',
    'transport_app_regulatory_authorities_approver',
    'transport_app_operationarea_examine'
  )
);

const transportApplicationList = ref<TransportApplicationVO[]>([]);
const operationAreaQuery = ref<OperationAreaQuery>({});
const operationAreaList = ref<OperationAreaVO[]>([]); // 所属作业区列表
const pianquList = ref<OperationAreaVO[]>([]); // 卸车地点列表
const allPianquList = ref<OperationAreaVO[]>([]); // 卸车地点列表
const wellPreparationList = ref<WellPreparationVO[]>([]); //井场准备
const allWellPreparationList = ref<WellPreparationVO[]>([]); //井场准备
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const approveType = ref<number>(1);
const suggestion = ref(false); //拉运申请审核意见是否显示

const queryFormRef = ref<ElFormInstance>();
const transportApplicationFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const cooDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: TransportApplicationForm = {
  appId: undefined,
  relatedId: undefined,
  appName: undefined,
  workAreaId: undefined,
  wellName: undefined,
  // 默认当前年月日 小时分钟
  applicationDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  loadingLocation: undefined,
  unloadLocationType: 1,
  unloadLocation: undefined,
  file: undefined,
  applicationStatus: 1,
  rejectSuggestion: undefined,
  flowType: 2, //泥浆不落地拉运申请
  mediumCategory: 2, //分类: 1固相, 2液相, 3混合相
  remark: undefined,
  superviseApprover: undefined,
  operationAreaReviewer: undefined,
  examineApproveTime: undefined,
  operationAreaReviewedTime: undefined,
  operationAreaApproveStatus: 1,
  mediumType: 8
};
const data = reactive<PageData<TransportApplicationForm, TransportApplicationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    relatedId: undefined,
    appName: undefined,
    workAreaId: undefined,
    wellName: undefined,
    loadingLocation: undefined,
    unloadLocationType: 1,
    unloadLocation: undefined,
    file: undefined,
    applicationStatus: undefined,
    rejectSuggestion: undefined,
    flowType: 2,
    mediumCategory: 2, //分类: 1固相, 2液相, 3混合相
    mediumType: undefined,
    params: {}
  },
  rules: {
    appId: [{ required: true, message: '申请ID，自动递增不能为空', trigger: 'blur' }],
    appName: [{ required: true, message: '申请名称不能为空', trigger: 'blur' }],
    mediumCategory: [{ required: true, message: '申请分类不能为空', trigger: 'change' }],
    workAreaId: [{ required: true, message: '所属作业区不能为空', trigger: 'blur' }],
    relatedId: [{ required: true, message: '单井名称不能为空', trigger: 'change' }],
    applicationDate: [{ required: true, message: '申请日期不能为空', trigger: 'blur' }],
    loadingLocation: [{ required: true, message: '装车地点不能为空', trigger: 'blur' }],
    unloadLocationType: [{ required: true, message: '卸车地点类型不能为空', trigger: 'change' }],
    unloadLocation: [{ required: true, message: '卸车地点不能为空', trigger: 'blur' }]
    /*    mediumType: [
      { required: true, message: "介质类型不能为空", trigger: "blur" }
    ],*/
    /*    operationAreaReviewer: [
      { required: true, message: "审批人不能为空", trigger: "blur" }
    ],
    superviseApprover: [
      { required: true, message: "审批人不能为空", trigger: "blur" }
    ],*/
    // file: [
    //   { required: true, message: "检测报告不能为空", trigger: "blur" }
    // ],
  }
});
const { queryParams, form, rules } = toRefs(data);

const coorules = reactive<ElFormRules>({
  appId: [{ required: true, message: '申请ID，自动递增不能为空', trigger: 'blur' }],
  appName: [{ required: true, message: '申请名称不能为空', trigger: 'blur' }],
  workAreaId: [{ required: true, message: '所属作业区不能为空', trigger: 'blur' }],
  relatedId: [{ required: true, message: '单井名称不能为空', trigger: 'change' }],
  wellName: [{ required: true, message: '单井名称不能为空', trigger: 'blur' }],
  applicationDate: [{ required: true, message: '申请日期不能为空', trigger: 'blur' }],
  loadingLocation: [{ required: true, message: '装车地点不能为空', trigger: 'blur' }],
  unloadLocationType: [{ required: true, message: '卸车地点类型不能为空', trigger: 'change' }],
  unloadLocation: [{ required: true, message: '卸车地点不能为空', trigger: 'blur' }],
  applicationStatus: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  operationAreaApproveStatus: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  rejectSuggestion: [{ required: true, message: '审核意见不能为空', trigger: 'blur' }]
  // file: [
  //   { required: true, message: "检测报告不能为空", trigger: "blur" }
  // ],
});

const wellQuery = reactive<WellPreparationQuery>(<WellPreparationQuery>{
  pageSize: -1,
  handlingType: 2,
  workArea: undefined
});
// 判断是否超期
const isOverdue = (row) => {
  if (row.transportRecordCount != 0) {
    return false;
  }
  if (row.examineApproveTime != null && row.operationAreaReviewedTime != null) {
    const lastDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const examineApproveTime = new Date(row.examineApproveTime);
    const operationAreaReviewedTime = new Date(row.operationAreaReviewedTime);
    if (examineApproveTime < lastDate && operationAreaReviewedTime < lastDate) {
      return true;
    }
  }
  return false;
};
//监管部门批准人字典筛选条件
const filterJianGuan = ref({
  mediumType: 'nj' //根据流程区分
});
//作业区审核人字典筛选条件
const filterZuoYeQu = ref({
  operationAreaId: undefined
});
const getApproverName = (dict, key) => {
  return dict?.find((item) => {
    return item.value == key;
  })?.label;
};

//过滤作业区审核人
const filteredZuoYeQu = computed(() => {
  filterZuoYeQu.value.operationAreaId = form.value.workAreaId;

  const result = transport_app_operationarea_examine.value?.filter((item) => {
    const matchStatus =
      filterZuoYeQu.value.operationAreaId !== undefined && filterZuoYeQu.value.operationAreaId !== null
        ? item.value.includes(filterZuoYeQu.value.operationAreaId)
        : true;
    return matchStatus;
  });
  return result;
});

//过滤直线监管部门批准人
const filteredJianGuan = computed(() => {
  const result = transport_app_regulatory_authorities_approver.value?.filter((item) => {
    return filterJianGuan.value.mediumType ? item.value.includes(filterJianGuan.value.mediumType) : true;
  });
  return result;
});

/** 查询拉运申请列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTransportApplication(queryParams.value);
  transportApplicationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  cooDialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  transportApplicationFormRef.value?.resetFields();
  changeSuoShuDi(null);
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TransportApplicationVO[]) => {
  ids.value = selection.map((item) => item.appId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  suggestion.value = false;
  dialog.visible = true;
  dialog.title = '添加拉运申请';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TransportApplicationVO) => {
  reset();
  const _appId = row?.appId || ids.value[0];
  const res = await getTransportApplication(_appId);
  Object.assign(form.value, res.data);
  await changeSuoShuDi(res.data.workAreaId);
  suggestion.value = true;
  dialog.visible = true;
  dialog.title = '修改拉运申请';
};

/** 提交按钮 */
const submitForm = (reApprove) => {
  transportApplicationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.appId) {
        form.value.reApprove = reApprove;
        await updateTransportApplication(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTransportApplication(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      cooDialog.visible = false;
      await getList();
    }
  });
};

/** 提交按钮 */
const cooSubmitForm = () => {
  transportApplicationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.appId) {
        const appId = form.value.appId;
        const rejectSuggestion = form.value.rejectSuggestion;
        const status = approveType.value == 1 ? form.value.applicationStatus : form.value.operationAreaApproveStatus;

        await updateTransportApplicationCoo(appId, status, rejectSuggestion, form.value.warnTime, approveType.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      cooDialog.visible = false;
      await getList();
    }
  });
};

/** 审核按钮操作 */
const handleCoordinate = async (row?: TransportApplicationVO, type) => {
  reset();
  const _appId = row?.appId || ids.value[0];
  const res = await getTransportApplication(_appId);
  Object.assign(form.value, res.data);
  await changeSuoShuDi(res.data.workAreaId);
  approveType.value = type;
  suggestion.value = true;
  cooDialog.visible = true;
  cooDialog.title = '审核拉运申请';
};

/** 删除按钮操作 */
const handleDelete = async (row?: TransportApplicationVO) => {
  const _appIds = row?.appId || ids.value;
  await proxy?.$modal.confirm('是否确认删除拉运申请编号为"' + _appIds + '"的数据项？').finally(() => (loading.value = false));
  await delTransportApplication(_appIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

const handleDownload = async (row?: TransportApplicationVO) => {
  proxy?.download(
    'epfy/transportApplication/downloadMeasurementVoucher',
    {
      appId: row?.appId
    },
    '二连分公司废液收集计量凭证.xls'
  );
};
/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epfy/transportApplication/export',
    {
      ...queryParams.value
    },
    `transportApplication_${new Date().getTime()}.xlsx`
  );
};

/**
 * 报告预览
 * @param row
 */
const handlePreview = async (row?: TransportApplicationVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.appId,
    attachSourceType: 'njTransportApplication',
    attachCategory: 'transportApplication'
  });
};
/**
 * 文件上传成功处理
 * @param fielInfo
 */
const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
};

const changeWellPreparation = (prepId: number | string) => {
  const wellPreparation = wellPreparationList.value.find((item) => item.prepId === form.value.relatedId);
  if (wellPreparation) {
    form.value.wellName = wellPreparation.wellName;
  }
  form.value.loadingLocation = form.value.wellName;
};

/** 查询所属作业区、单井、液相相卸车地点列表 */
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaParentId = 0;
  operationAreaQuery.value.operationAreaType = 0;
  const res = await listOperationArea(operationAreaQuery.value);
  operationAreaList.value = res.rows;

  operationAreaQuery.value.operationAreaParentId = null;
  operationAreaQuery.value.operationAreaType = 5;
  const pianquRes = await listOperationArea(operationAreaQuery.value); //查询液相卸车地点片区
  allPianquList.value = pianquRes.rows;
  pianquList.value = pianquRes.rows;

  const wellRes = await listWellPreparation(wellQuery); //查询单井
  allWellPreparationList.value = wellRes.rows;
  wellPreparationList.value = wellRes.rows;
};
const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationAreaItem = operationAreaList.value.find((item) => item.operationAreaId === operationAreaId);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};
/** 查询单井和卸车地点 */
const changeSuoShuDi = async (workAreaId: number | string) => {
  if (workAreaId != null) {
    pianquList.value = allPianquList.value.filter((item) => item.operationAreaParentId === workAreaId);
    wellPreparationList.value = allWellPreparationList.value.filter((item) => item.workArea === workAreaId);
  } else {
    pianquList.value = allPianquList.value;
    wellPreparationList.value = allWellPreparationList.value;
  }
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};

const tableRowClassName = ({ row, rowIndex }) => {
  const warn = isOverdue(row);
  if (warn) {
    return 'warn-row';
  }
  return '';
};

const warnTimeDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
/** 修改预警时间按钮操作 */
const handleUpdateWarnTime = async (row?: TransportApplicationVO) => {
  reset();
  const _appId = row?.appId || ids.value[0];
  const res = await getTransportApplication(_appId);
  Object.assign(form.value, res.data);

  warnTimeDialog.visible = true;
  warnTimeDialog.title = '修改预警时间';
};

/** 提交预警时间 */
const warnTimeSubmitForm = async () => {
  buttonLoading.value = true;
  if (form.value.appId) {
    await updateTransportApplication(form.value).finally(() => (buttonLoading.value = false));
  }
  proxy?.$modal.msgSuccess('操作成功');
  dialog.visible = false;
  warnTimeDialog.visible = false;
  await getList();
};
onMounted(() => {
  getoperationAreaList();
  getList();
});
</script>
