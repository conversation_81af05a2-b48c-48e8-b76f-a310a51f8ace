<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="批复函编号" prop="waterDrawingLicenseCode">
              <el-input v-model="queryParams.waterDrawingLicenseCode" placeholder="请输入批复函编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属地" prop="operationAreaId">
              <el-select v-model="queryParams.operationAreaId" clearable placeholder="请选择所属地" @keyup.enter="handleQuery">
                <el-option
                  v-for="dict in operationAreaList"
                  :key="dict.operationAreaId"
                  :label="dict.operationAreaName"
                  :value="dict.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="水源类型" prop="waterType">
              <el-select v-model="queryParams.waterType" clearable placeholder="请选择水源类型">
                <el-option v-for="dict in eptw_water_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="取水类型" prop="intakeType">
              <el-select v-model="queryParams.intakeType" clearable placeholder="请选择取水类型">
                <el-option v-for="dict in eptw_intake_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="取水用途" prop="waterPurpose">
              <el-select v-model="queryParams.waterPurpose" clearable multiple placeholder="请选择取水用途">
                <el-option v-for="dict in eptw_water_purpose" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="证件状态" prop="status">
              <el-select v-model="queryParams.status" clearable placeholder="请选择状态">
                <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epms:license:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['epms:license:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:license:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:license:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="licenseList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="批复函编号" min-width="200" prop="waterDrawingLicenseCode" />
        <el-table-column label="所属地" align="center" prop="operationAreaName" min-width="100px">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="取水地点" align="center" prop="intakeLocation" />
        <el-table-column align="center" label="取水量（立方米/年）" min-width="100px" prop="waterDrawingAmount" />
        <el-table-column label="水源类型" align="center" prop="waterType">
          <template #default="scope">
            <dict-tag :options="eptw_water_type" :value="scope.row.waterType" />
          </template>
        </el-table-column>
        <el-table-column label="取水类型" align="center" prop="intakeType">
          <template #default="scope">
            <dict-tag :options="eptw_intake_type" :value="scope.row.intakeType" />
          </template>
        </el-table-column>
        <el-table-column label="取水用途" align="center" prop="waterPurpose">
          <template #default="scope">
            <dict-tag :options="eptw_water_purpose" :value="scope.row.waterPurpose" />
          </template>
        </el-table-column>
        <el-table-column label="证件状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="eptw_file_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="有效期结束时间" align="center" prop="endTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="剩余时间(天)" prop="effectiveDay" />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-tooltip content="批复函预览" placement="top">
              <el-button v-hasPermi="['epms:license:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row, 'license')"
                >批复函预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="停用批复文件预览" placement="top">
              <el-button v-hasPermi="['epms:license:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row, 'stopLicense')"
                >停用批复文件预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="Postcard" @click="handleDetail(scope.row)" v-hasPermi="['epms:license:detail']">详情</el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epms:license:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['epms:license:remove']">删除</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改批复函对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="licenseFormRef" :model="form" :rules="rules" label-width="124px">
        <el-form-item label="批复函编号" prop="waterDrawingLicenseCode">
          <el-input v-model="form.waterDrawingLicenseCode" placeholder="请输入批复函编号" />
        </el-form-item>
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择所属地">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水量" prop="waterDrawingAmount">
          <el-input v-model="form.waterDrawingAmount" placeholder="请输入取水量">
            <template #append>立方米/年</template>
          </el-input>
        </el-form-item>
        <el-form-item label="有效期开始时间" prop="startTime">
          <el-date-picker v-model="form.startTime" clearable placeholder="请选择有效期开始时间" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="有效期结束时间" prop="endTime">
          <el-date-picker v-model="form.endTime" clearable placeholder="请选择有效期结束时间" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="停用时间" prop="stopTime">
          <el-date-picker v-model="form.stopTime" clearable placeholder="请选择停用时间" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="证件状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择证件状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位名称" prop="organName">
          <el-input v-model="form.organName" placeholder="请输入单位名称" />
        </el-form-item>
        <!--        <el-form-item label="统一社会信用代码" prop="creditCode">-->
        <!--          <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />-->
        <!--        </el-form-item>-->
        <el-form-item label="取水地点" prop="intakeLocation">
          <el-input v-model="form.intakeLocation" placeholder="请输入取水地点" />
        </el-form-item>
        <el-form-item label="水源类型" prop="waterType">
          <el-select v-model="form.waterType" placeholder="请选择水源类型">
            <el-option v-for="dict in eptw_water_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水类型" prop="intakeType">
          <el-select v-model="form.intakeType" placeholder="请选择取水类型">
            <el-option v-for="dict in eptw_intake_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水用途" prop="waterPurpose">
          <el-select v-model="form.waterPurpose" multiple placeholder="请选择取水用途">
            <el-option v-for="dict in eptw_water_purpose" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="临时取水井" prop="tempWellIds">
          <el-select v-model="form.tempWellIds" multiple filterable placeholder="请选择临时取水井">
            <el-option v-for="dict in wellList" :key="dict.waterWellId" :label="dict.waterWellName" :value="dict.waterWellId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="批复函" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.waterDrawingLicenseId"
            :disabled="false"
            attach-category="license"
            attach-source-type="waterBlock"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="停用批复文件" prop="file">
          <attachFileUpload
            v-model="form.stopFile"
            :attach-source-id="form.waterDrawingLicenseId"
            :disabled="false"
            attach-category="stopLicense"
            attach-source-type="waterBlock"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="licenseFormRef" :model="form" :rules="rules" label-width="124px" disabled>
        <el-form-item label="批复函编号" prop="waterDrawingLicenseCode">
          <el-input v-model="form.waterDrawingLicenseCode" placeholder="请输入批复函编号" />
        </el-form-item>
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择所属地">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水量" prop="waterDrawingAmount">
          <el-input v-model="form.waterDrawingAmount" placeholder="请输入取水量">
            <template #append>立方米/年</template>
          </el-input>
        </el-form-item>
        <el-form-item label="有效期开始时间" prop="startTime">
          <el-date-picker v-model="form.startTime" clearable placeholder="请选择有效期开始时间" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="有效期结束时间" prop="endTime">
          <el-date-picker v-model="form.endTime" clearable placeholder="请选择有效期结束时间" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="停用时间" prop="stopTime">
          <el-date-picker v-model="form.stopTime" clearable placeholder="请选择停用时间" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="证件状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择证件状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位名称" prop="organName">
          <el-input v-model="form.organName" placeholder="请输入单位名称" />
        </el-form-item>
        <!--        <el-form-item label="统一社会信用代码" prop="creditCode">-->
        <!--          <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />-->
        <!--        </el-form-item>-->
        <el-form-item label="取水地点" prop="intakeLocation">
          <el-input v-model="form.intakeLocation" placeholder="请输入取水地点" />
        </el-form-item>
        <el-form-item label="水源类型" prop="waterType">
          <el-select v-model="form.waterType" placeholder="请选择水源类型">
            <el-option v-for="dict in eptw_water_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水类型" prop="intakeType">
          <el-select v-model="form.intakeType" placeholder="请选择取水类型">
            <el-option v-for="dict in eptw_intake_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水用途" prop="waterPurpose">
          <el-select v-model="form.waterPurpose" multiple placeholder="请选择取水用途">
            <el-option v-for="dict in eptw_water_purpose" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="临时取水井" prop="tempWellIds">
          <el-select v-model="form.tempWellIds" multiple placeholder="请选择临时取水井">
            <el-option v-for="dict in wellList" :key="dict.waterWellId" :label="dict.waterWellName" :value="dict.waterWellId"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="License" lang="ts">
import { addLicense, delLicense, getLicense, listLicense, updateLicense } from '@/api/epms/eptw/license';
import { LicenseForm, LicenseQuery, LicenseVO } from '@/api/epms/eptw/license/types';
import { parseTime } from '@/utils/ruoyi';
import { listWell } from '@/api/epms/eptw/well';
import { WellVO } from '@/api/epms/eptw/well/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_water_purpose, eptw_file_status, eptw_intake_type, eptw_water_type, eptw_administrative_area } = toRefs<any>(
  proxy?.useDict('eptw_water_purpose', 'eptw_file_status', 'eptw_intake_type', 'eptw_water_type', 'eptw_administrative_area')
);

const licenseList = ref<LicenseVO[]>([]);
const wellList = ref<WellVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const route = useRoute();
const queryFormRef = ref<ElFormInstance>();
const licenseFormRef = ref<ElFormInstance>();

const operationAreaQuery = reactive<OperationAreaQuery>({});
// 所属地列表
const operationAreaList = ref<OperationAreaVO[]>([]);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: LicenseForm = {
  waterDrawingLicenseId: undefined,
  waterDrawingLicenseCode: undefined,
  licenseType: 1,
  operationAreaId: undefined,
  waterDrawingAmount: undefined,
  startTime: undefined,
  endTime: undefined,
  status: undefined,
  organName: undefined,
  creditCode: undefined,
  intakeLocation: undefined,
  waterType: undefined,
  intakeType: undefined,
  waterPurpose: undefined,
  file: undefined,
  tempWellIds: undefined,
  tempWelliotIds: []
};
const data = reactive<PageData<LicenseForm, LicenseQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    waterDrawingLicenseCode: undefined,
    operationAreaParentId: undefined,
    licenseType: 1,
    waterDrawingAmount: undefined,
    startTime: undefined,
    endTime: undefined,
    status: undefined,
    organName: undefined,
    creditCode: undefined,
    intakeLocation: undefined,
    waterType: undefined,
    intakeType: undefined,
    waterPurpose: undefined,
    params: {}
  },
  rules: {
    waterDrawingLicenseId: [{ required: true, message: '批复函id不能为空', trigger: 'blur' }],
    waterDrawingLicenseCode: [{ required: true, message: '批复函编号不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '所属地不能为空', trigger: 'change' }],
    licenseType: [{ required: true, message: '证件类型不能为空', trigger: 'change' }],
    waterDrawingAmount: [{ required: true, message: '取水量不能为空', trigger: 'blur' }],
    startTime: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
    endTime: [{ required: true, message: '结束时间不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
    organName: [{ required: true, message: '单位名称不能为空', trigger: 'blur' }],
    // creditCode: [
    //   { required: true, message: "信用代码不能为空", trigger: "blur" }
    // ],
    intakeLocation: [{ required: true, message: '取水地点不能为空', trigger: 'blur' }],
    waterType: [{ required: true, message: '水源类型不能为空', trigger: 'change' }],
    intakeType: [{ required: true, message: '取水类型不能为空', trigger: 'change' }],
    waterPurpose: [{ required: true, message: '取水用途不能为空', trigger: 'blur' }],
    tempWellIds: [{ required: true, message: '临时取水井不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const getTempWell = async () => {
  const res = await listWell({
    pageNum: 1,
    pageSize: undefined,
    wellType: 1
  });
  wellList.value = res.rows;
};

/** 查询批复函列表 */
const getList = async () => {
  if (queryParams.value.waterPurpose) {
    queryParams.value.waterPurpose = queryParams.value.waterPurpose?.toString();
  }
  loading.value = true;
  const res = await listLicense(queryParams.value);
  licenseList.value = res.rows;
  total.value = res.total;
  if (queryParams.value.waterPurpose !== '' && queryParams.value.waterPurpose !== undefined) {
    queryParams.value.waterPurpose = queryParams.value.waterPurpose?.split(',');
  }
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  licenseFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: LicenseVO[]) => {
  ids.value = selection.map((item) => item.waterDrawingLicenseId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加批复函';
};
/** 文件预览操作 */
const handlePreview = async (row?: LicenseVO, type?: string) => {
  proxy.showAttachPreview({
    attachSourceId: row.waterDrawingLicenseId,
    attachSourceType: 'waterBlock',
    attachCategory: type
  });
};
const handleDetail = async (row?: LicenseVO) => {
  reset();
  const _waterDrawingLicenseId = row?.waterDrawingLicenseId || ids.value[0];
  const res = await getLicense(_waterDrawingLicenseId);
  Object.assign(form.value, res.data);
  form.value.waterPurpose = form.value.waterPurpose?.split(',').map(Number);
  form.value.tempWellIds = form.value.tempWellIds?.split(',');
  detaildialog.visible = true;
  detaildialog.title = '批复函详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: LicenseVO) => {
  reset();
  const _waterDrawingLicenseId = row?.waterDrawingLicenseId || ids.value[0];
  const res = await getLicense(_waterDrawingLicenseId);
  Object.assign(form.value, res.data);
  form.value.waterPurpose = form.value.waterPurpose?.split(',').map(Number);
  form.value.tempWellIds = form.value.tempWellIds?.split(',');
  dialog.visible = true;
  dialog.title = '修改批复函';
};

const resetQueryParam = () => {
  queryParams.value.pageNum = 1;
  queryParams.value.pageSize = 10;
  queryParams.value.waterDrawingLicenseCode = undefined;
  queryParams.value.waterDrawingAmount = undefined;
  queryParams.value.startTime = undefined;
  queryParams.value.endTime = undefined;
  queryParams.value.status = undefined;
  queryParams.value.organName = undefined;
  queryParams.value.creditCode = undefined;
  queryParams.value.intakeLocation = undefined;
  queryParams.value.waterType = undefined;
  queryParams.value.intakeType = undefined;
  queryParams.value.waterPurpose = undefined;
};
/** 提交按钮 */
const submitForm = () => {
  licenseFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      form.value.waterPurpose = form.value.waterPurpose?.toString();
      form.value.tempWellIds = form.value.tempWellIds?.toString();
      const wellIds = form.value.tempWellIds?.split(',').map((id) => id.trim());
      wellList.value.forEach((item) => {
        if (wellIds?.includes(item.waterWellId.toString())) {
          form.value.tempWelliotIds.push(item.iotId);
        }
      });
      buttonLoading.value = true;
      if (form.value.waterDrawingLicenseId) {
        await updateLicense(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addLicense(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: LicenseVO) => {
  const _waterDrawingLicenseIds = row?.waterDrawingLicenseId || ids.value;
  await proxy?.$modal.confirm('是否确认删除批复函编号为"' + _waterDrawingLicenseIds + '"的数据项？').finally(() => (loading.value = false));
  await delLicense(_waterDrawingLicenseIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/license/export',
    {
      ...queryParams.value
    },
    `批复函_${new Date().getTime()}.xlsx`
  );
};

const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
  form.value.balanceReportName = fielInfo.name;
};

/**
 * 获取所属地列表
 */
const getAreaList = () => {
  operationAreaQuery.operationAreaType = 0;
  listOperationArea(operationAreaQuery).then((res) => {
    operationAreaList.value = res.rows;
  });
};
const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationAreaItem = operationAreaList.value.find((item) => item.operationAreaId === operationAreaId);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  if (route.query.operationAreaId !== undefined) {
    queryParams.value.operationAreaId = route.query.operationAreaId?.toString() || '';
  }
  getAreaList();
  getTempWell();
  getList();
});

onActivated(() => {
  resetQueryParam();
  getList();
});
</script>
<style scoped>
.el-data-editor {
  --el-date-editor-width: 100%;
}
</style>
