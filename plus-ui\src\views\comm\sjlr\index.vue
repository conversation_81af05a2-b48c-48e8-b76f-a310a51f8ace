<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="mb-[10px]">
        <el-card shadow="hover">
          <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px">
            <el-form-item label="日期范围" prop="occurTimeStart">
              <el-date-picker
                v-model="queryParams.startDate"
                :append-to-body="false"
                :model-value="queryParams.startDate"
                class="searchDate"
                clearable
                format="YYYY-MM-DD"
                type="date"
                value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="—" prop="occurTimeEnd" label-width="30px">
              <el-date-picker
                v-model="queryParams.endDate"
                :append-to-body="false"
                :model-value="queryParams.endDate"
                class="searchDate"
                clearable
                format="YYYY-MM-DD"
                type="date"
                value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="类型" prop="leiXingId">
              <el-select
                v-model="queryParams.leiXingId"
                :popper-append-to-body="false"
                class="searchDate"
                clearable
                filterable
                placeholder="选择类型"
                @change="changeleixing"
              >
                <el-option v-for="leixinglist in shebeileixingList" :key="leixinglist.id" :label="leixinglist.mingzi" :value="leixinglist.id" />
              </el-select>
            </el-form-item>

            <el-form-item label="设备" prop="bujianId">
              <el-select v-model="queryParams.bujianId" :popper-append-to-body="false" class="searchDate" clearable filterable placeholder="选择设备">
                <el-option v-for="namelist in shebeiList" :key="namelist.id" :label="namelist.mingZi" :value="namelist.id" />
              </el-select>
            </el-form-item>

            <el-form-item label="参数" prop="canshuId">
              <el-select
                v-model="queryParams.canshuId"
                :popper-append-to-body="false"
                class="searchDate"
                clearable
                filterable
                placeholder="选择参数"
                @change="changecanshu"
              >
                <el-option v-for="(namelist, item) in canshuList" :key="namelist.id" :label="namelist.mingZi" :value="namelist.id" />
              </el-select>
            </el-form-item>

            <!--            <el-form-item label="周期" prop="interval">-->
            <!--              <el-select filterable v-model="queryParams.interval" class="searchDate" :popper-append-to-body="false" placeholder="选择参数">-->
            <!--                <el-option v-for="namelist in intervalList"-->
            <!--                           :key="namelist.value"-->
            <!--                           :label="namelist.lable"-->
            <!--                           :value="namelist.value"-->
            <!--                />-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->

            <el-form-item style="padding-left: 20px">
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>
    <el-card shadow="never">
      <template #header>
        <div class="button-group">
          <el-row :gutter="15" class="mb8">
            <el-col :span="1.5">
              <el-button icon="Plus" type="primary" @click="handleAdd">新增</el-button>
            </el-col>

            <el-col :span="1.5">
              <el-button icon="Upload" type="primary" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button icon="Download" type="warning" @click="handleExport">导出</el-button>
            </el-col>
          </el-row>
        </div>
      </template>
      <div class="tables">
        <el-table ref="table" v-loading="loading" :data="currentDlfhList" height="100%" stripe @selection-change="handleSelectionChange">
          <!--<el-table-column type="selection" width="55" align="center" />-->
          <el-table-column align="center" label="日期" prop="date" />
          <el-table-column align="center" label="类型" prop="bujianleixingName"></el-table-column>
          <el-table-column align="center" label="名称" prop="bujianName"></el-table-column>
          <el-table-column align="center" label="参数" prop="canshuName"></el-table-column>
          <el-table-column align="center" label="参数值" prop="value"></el-table-column>
          <el-table-column align="center" label="周期" prop="interval"></el-table-column>
          <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
            <template #default="scope">
              <el-button icon="Edit" size="mini" type="text" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button icon="Delete" size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
      </div>
    </el-card>

    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="时间" prop="occurTimeStart" style="margin-bottom: 18px">
          <el-date-picker
            v-if="form.interval <= 2"
            v-model="form.startDate"
            :append-to-body="false"
            :disabled="!addflag"
            class="searchDate2"
            clearable
            format="YYYY-MM-DD HH:mm:00"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:00"
          >
          </el-date-picker>
          <el-date-picker
            v-else
            v-model="form.startDate"
            :disabled="!addflag"
            class="searchDate2"
            clearable
            format="YYYY-MM-DD"
            type="date"
            value-format="YYYY-MM-DD"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="类型" prop="leiXingId">
          <el-select
            v-model="form.leiXingId"
            :popper-append-to-body="false"
            class="searchDate"
            clearable
            filterable
            placeholder="选择类型"
            @change="changeDialogleixing"
          >
            <el-option v-for="leixinglist in shebeileixingList" :key="leixinglist.id" :label="leixinglist.mingzi" :value="leixinglist.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="设备" prop="bujianId">
          <el-select v-model="form.bujianId" :popper-append-to-body="false" class="searchDate" clearable filterable placeholder="选择设备">
            <el-option v-for="namelist in shebeiDialogList" :key="namelist.id" :label="namelist.mingZi" :value="namelist.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="参数" prop="canshuId">
          <el-select
            v-model="form.canshuId"
            :popper-append-to-body="false"
            class="searchDate"
            clearable
            filterable
            placeholder="选择参数"
            @change="changeDialogCanshu"
          >
            <el-option v-for="namelist in canshuDialogList" :key="namelist.id" :label="namelist.mingZi" :value="namelist.id" />
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="周期" prop="interval" style="margin-bottom: 18px">-->
        <!--          <el-select filterable v-model="form.interval"  class="searchDate2" placeholder="选择周期" :disabled="!addflag" :popper-append-to-body="false" @change="changeIntervalDialog">-->
        <!--            <el-option v-for="namelist in intervalList"-->
        <!--                       :key="namelist.value"-->
        <!--                       :label="namelist.lable"-->
        <!--                       :value="namelist.value"-->
        <!--            />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="参数值" prop="value" style="margin-bottom: 18px">
          <el-input v-model="form.value" :popper-append-to-body="false" class="searchDate2" clearable placeholder="输入参数值" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" class="sure">确 定</el-button>
        <el-button @click="cancel" class="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog v-dialog-drag :title="uploadConfig.title" v-model="uploadConfig.open" :append-to-body="true" width="400px">
      <el-form ref="upload" :inline="true" :model="upload" label-width="68px">
        <el-form-item label="类型" prop="leiXingId">
          <el-select
            v-model="upload.leiXingId"
            :popper-append-to-body="false"
            class="searchDate2"
            filterable
            placeholder="选择类型"
            @change="changeUploadleixing"
          >
            <el-option v-for="leixinglist in shebeileixingList" :key="leixinglist.id" :label="leixinglist.mingzi" :value="leixinglist.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="设备" prop="bujianId">
          <el-select v-model="upload.bujianId" :popper-append-to-body="false" class="searchDate2" clearable filterable placeholder="选择设备">
            <el-option v-for="namelist in shebeiUploadList" :key="namelist.id" :label="namelist.mingZi" :value="namelist.id" />
          </el-select>
        </el-form-item>

        <!--        <el-form-item label="周期" prop="interval">-->
        <!--          <el-select filterable v-model="upload.interval"  class="searchDate2" :popper-append-to-body="false" placeholder="选择周期" @change="changeIntervalupload">-->
        <!--            <el-option v-for="namelist in intervalList"-->
        <!--                       :key="namelist.value"-->
        <!--                       :label="namelist.lable"-->
        <!--                       :value="namelist.value"-->
        <!--            />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->

        <el-form-item label="参数" prop="canshuId">
          <el-select
            v-model="upload.canshuId"
            :popper-append-to-body="false"
            class="searchDate2"
            clearable
            placeholder="选择参数"
            @change="changeUploadCanshu"
          >
            <el-option v-for="namelist in canshuUploadList" :key="namelist.id" :label="namelist.mingZi" :value="namelist.id" />
          </el-select>
        </el-form-item>
        <el-button class="createTemplate" @click="downloadTemp">生成模板</el-button>
      </el-form>
      <el-upload
        ref="upload"
        :limit="1"
        drag
        accept=".xlsx, .xls"
        :headers="uploadConfig.headers"
        :action="uploadConfig.url + '?updateSupport=' + uploadConfig.updateSupport"
        :disabled="uploadConfig.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或 <em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
        </template>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm" class="sure">确 定</el-button>
        <el-button @click="uploadConfig.open = false" class="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'dayjs';
import { addData, deleteData, getCanshuList, getDataList, getShebeileixingList, getshebeiList, updateData } from '@/api/comm/sjlr/index.js';
import { getToken } from '@/utils/auth.ts';

export default {
  name: 'sjlr',
  dicts: [
    { type: 'biz_startDate', data: '起始时间边界' } //字典中名称
  ],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //电力负荷表格数据
      dlfhList: [],
      currentDlfhList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      addflag: null, //弹出框为新增还是修改，true为新增，false为修改
      shebeileixingList: [], //设备类型列表
      shebeiMap: new Map(),
      canshuMap: new Map(),
      shebeiList: [], //设备列表
      canshuList: [], //参数列表

      //新增修改弹窗数据
      shebeiDialogList: [],
      canshuDialogList: [],

      //上传参数列表
      shebeiUploadList: [], //上传参数列表
      canshuUploadList: [], //上传参数列表

      intervalList: [
        { value: 1, lable: '5分钟' },
        { value: 5, lable: '日' },
        { value: 6, lable: '月' },
        { value: 7, lable: '年' }
      ],
      tablemaxheight: 0,
      //查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        leiXingId: null,
        bujianId: null,
        canshuId: null,
        startDate: moment().subtract(7, 'days').format('YYYY-MM-DD'),
        endDate: moment().format('YYYY-MM-DD'),
        interval: 5
      },
      //表单参数
      form: {
        startDate: moment().format('YYYY-MM-DD HH:mm:ss'),
        leiXingId: null,
        bujianId: null,
        canshuId: null,
        interval: 1,
        value: null
      },
      //表单校验
      rules: {},
      upload: {
        leiXingId: null,
        canshuId: null,
        bujianId: null,
        interval: 1
      },
      uploadConfig: {
        // 是否展示弹出层
        open: false,
        //弹出层标题
        title: '',
        //是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头
        headers: {
          Authorization: 'Bearer ' + getToken(),
          clientid: import.meta.env.VITE_APP_CLIENT_ID
        },
        // 上传的地址
        url: import.meta.env.VITE_APP_BASE_API + '/epms/dataReport/importData',
        loading: null
      }
    };
  },
  async created() {
    await this.initializeData();
    this.handleRouteParams();
  },
  watch: {
    $route: 'handleRouteParams'
  },
  mounted() {},
  methods: {
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /**
     * 查询条件
     */
    async initializeData() {
      try {
        const response = await getShebeileixingList();
        this.shebeileixingList = response.data;

        // 初始化 shebeiMap 和 canshuMap
        const [shebeiResponse, canshuResponse] = await Promise.all([getshebeiList(), getCanshuList()]);
        this.shebeiMap = shebeiResponse.data;
        this.canshuMap = canshuResponse.data;

        // 设置默认的设备列表和参数列表
        if (this.shebeileixingList.length > 0) {
          const defaultLeiXingId = this.shebeileixingList[0]?.id;
          this.updateShebeiAndCanshuLists(defaultLeiXingId);
        }
      } catch (error) {
        console.error('Error initializeData:', error);
      }
    },
    updateShebeiAndCanshuLists(leiXingId) {
      this.shebeiList = this.shebeiMap[leiXingId] || [];
      this.canshuList = this.canshuMap[leiXingId] || [];
    },
    handleRouteParams() {
      const route = this.$route;

      //如果有路由传参，则使用路由传参的值
      if (route.query.leiXingId !== undefined) {
        this.queryParams.leiXingId = Number(route.query.leiXingId);
        this.updateShebeiAndCanshuLists(this.queryParams.leiXingId);
      } else {
        // 如果没有路由传参，使用默认值
        this.queryParams.leiXingId = this.shebeileixingList[0]?.id;
        this.updateShebeiAndCanshuLists(this.queryParams.leiXingId);
      }

      // 设置 bujianId 和 canshuId
      if (route.query.bujianId !== undefined) {
        this.queryParams.bujianId = Number(route.query.bujianId);
      } else {
        this.queryParams.bujianId = this.shebeiList[0]?.id;
      }

      if (route.query.canshuId !== undefined) {
        this.queryParams.canshuId = Number(route.query.canshuId);
      } else {
        this.queryParams.canshuId = this.canshuList[0]?.id;
      }
      const selectedNamelist = this.canshuList.find((namelist) => namelist.id === Number(route.query.canshuId));
      if (selectedNamelist != null) {
        this.queryParams.interval = selectedNamelist.dType;
      }
      this.$nextTick(() => {
        this.getList();
      });
    },
    //参数类型切换
    changeleixing() {
      this.queryParams.bujianId = null;
      this.queryParams.canshuId = null;
      if (this.queryParams.leiXingId != null && this.queryParams.leiXingId !== '') {
        this.shebeiList = this.shebeiMap[this.queryParams.leiXingId];
        this.canshuList = this.canshuMap[this.queryParams.leiXingId];
      }
    },
    changecanshu(selectedId) {
      this.queryParams.interval = null;
      const selectedNamelist = this.canshuList.find((namelist) => namelist.id === selectedId);
      if (selectedNamelist != null) {
        this.queryParams.interval = selectedNamelist.dType;
      }
    },
    changeDialogleixing() {
      this.form.bujianId = null;
      this.form.canshuId = null;
      if (this.form.leiXingId != null && this.form.leiXingId !== '') {
        this.shebeiDialogList = this.shebeiMap[this.form.leiXingId];
        this.canshuDialogList = this.canshuMap[this.form.leiXingId];
      }
    },
    changeDialogCanshu(selectedId) {
      this.form.interval = null;
      const selectedNamelist = this.canshuDialogList.find((namelist) => namelist.id === selectedId);
      if (selectedNamelist != null) {
        this.form.interval = selectedNamelist.dType;
      }
    },
    /** 切换时期选择*/
    changeIntervalDialog() {
      if (this.form.interval === 1) {
        this.form.startDate = moment().format('YYYY-MM-DD HH:mm:ss');
      } else {
        this.form.startDate = moment().format('YYYY-MM-DD');
      }
    },
    changeUploadleixing() {
      this.upload.bujianId = null;
      this.upload.canshuId = null;
      if (this.upload.leiXingId != null && this.upload.leiXingId !== '') {
        this.shebeiUploadList = this.shebeiMap[this.upload.leiXingId];
        this.canshuUploadList = this.canshuMap[this.upload.leiXingId];
      }
    },
    changeUploadCanshu(selectedId) {
      this.upload.interval = null;
      const selectedNamelist = this.canshuUploadList.find((namelist) => namelist.id === selectedId);
      if (selectedNamelist != null) {
        this.form.interval = selectedNamelist.dType;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 查询列表 */
    getList() {
      if (!this.queryParams.leiXingId) {
        this.$modal.msgError('请选择类型');
        return;
      }
      if (!this.queryParams.bujianId) {
        this.$modal.msgError('请选择设备');
        return;
      }
      if (!this.queryParams.canshuId) {
        this.$modal.msgError('请选择参数');
        return;
      }
      this.loading = true;
      getDataList(this.queryParams).then((response) => {
        this.dlfhList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.getCurrentData();
      });
    },
    //计算分页数据
    getCurrentData() {
      this.currentDlfhList = this.dlfhList;
      let endNum = (this.queryParams.pageNum - 1) * this.queryParams.pageSize + this.queryParams.pageSize;
      endNum = endNum > this.dlfhList.length ? this.dlfhList.length : endNum;
      this.currentDlfhList = this.dlfhList.slice((this.queryParams.pageNum - 1) * this.queryParams.pageSize, endNum);
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    //表单重置
    reset() {
      this.form = {
        startDate: moment().format('YYYY-MM-DD HH:mm:ss'),
        leiXingId: null,
        bujianId: null,
        canshuId: null,
        interval: 1,
        value: null
      };
    },

    /** 新增按钮操作*/
    async handleAdd() {
      this.reset();
      this.open = true;
      this.addflag = true;
      this.title = '添加数据';
      this.shebeiDialogList = [];
      this.canshuDialogList = [];
    },

    /** 修改按钮操作*/
    async handleUpdate(row) {
      this.reset();
      this.open = true;
      this.addflag = false;
      this.title = '修改数据';
      this.shebeiDialogList = this.shebeiMap[row.bujianleixingId];
      this.canshuDialogList = this.canshuMap[row.bujianleixingId];
      this.form.interval = row.intervalId;
      console.log(row);
      if (this.form.interval <= 2) {
        this.form.startDate = moment(row.date).format('YYYY-MM-DD HH:mm:ss');
      } else {
        this.form.startDate = moment(row.date).format('YYYY-MM-DD');
      }
      this.form.leiXingId = row.bujianleixingId;
      this.form.bujianId = row.bujianId;
      this.form.canshuId = row.canshuId;
      this.form.value = row.value;
    },

    /** 提交按钮*/
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.addflag) {
            console.log(this.form);
            addData(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
            });
          } else {
            updateData(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
            });
          }
        }
        this.loading = false;
        this.shebeiList = this.shebeiMap[this.form.leiXingId];
        this.canshuList = this.canshuMap[this.form.leiXingId];
        this.queryParams.leiXingId = this.form.leiXingId;
        this.queryParams.bujianId = this.form.bujianId;
        this.queryParams.canshuId = this.form.canshuId;
        this.queryParams.interval = this.form.interval;
        this.reset();
        this.getList();
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除数据项？')
        .then(function () {
          return deleteData({
            'startDate': row.date,
            'leiXingId': row.bujianleixingId,
            'bujianId': row.bujianId,
            'canshuId': row.canshuId,
            'interval': row.intervalId
          });
        })
        .then(() => {
          this.getList();
          this.ids = null;
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => {});
    },

    /** 导入按钮操作*/
    async handleImport() {
      this.uploadConfig.title = '数据导入';
      this.uploadConfig.open = true;
      this.upload.leiXingId = null;
      this.changeUploadleixing();
    },
    //修改导入周期
    changeIntervalupload() {
      if (this.upload.interval === 1) {
        this.upload.startDate = moment().format('YYYY-MM-DD HH:mm:ss');
      } else {
        this.upload.startDate = moment().format('YYYY-MM-DD');
      }
    },
    //下载模板
    downloadTemp() {
      if (this.upload.leiXingId === null || this.upload.leiXingId === '') {
        this.$modal.msgError('请选择类型');
        /*}else if (this.upload.bujianId===null||this.upload.bujianId===""){
        this.$modal.msgError("请选择设备");*/
      } else if (this.upload.canshuId === null || this.upload.canshuId === '') {
        this.$modal.msgError('请选择参数');
      } else {
        this.loading = true;
        this.download(
          'epms/dataReport/downloadTemplate',
          {
            ...this.upload
          },
          `导入模板_${new Date().getTime()}.xlsx`
        );
      }
      this.loading = false;
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.uploadConfig.isUploading = true;
      this.uploadConfig.loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      console.log('suceess');
      this.uploadConfig.open = false;
      this.uploadConfig.isUploading = false;
      console.log(this.uploadConfig);
      this.$refs.upload.clearFiles();
      if (this.uploadConfig.loading) {
        this.uploadConfig.loading.close(); // 确保 loading 存在
      }
      ElMessageBox.alert(response.msg, {
        title: '导入结果',
        dangerouslyUseHtml: true // 注意这里属性名也变化了
      });
      this.getList();
    },
    // 上传文件后提交
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.queryParams.canshuId === null || this.queryParams.canshuId === '') {
        this.$modal.msgError('请选择参数');
      } else {
        this.download(
          'epms/dataReport/exportData',
          {
            ...this.queryParams
          },
          `数据报表_${new Date().getTime()}.xlsx`
        );
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.el-date-editor {
  --el-date-editor-width: 100%;
}
</style>
