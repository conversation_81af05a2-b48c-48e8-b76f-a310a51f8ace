import{Q as f,m as c,c as _,o as D,p as e,t as o,q as V,b7 as v,A as S,G as h,M as b}from"./index-D07cMzhp.js";import{E as g}from"./el-row-CikYE3zA.js";import{E as F}from"./el-col-BaG5Rg5z.js";import I from"./AceInupt-BbFCPjvh.js";const w={name:"WarnBizRuleForm",components:{AceInput:I},props:{modelValue:String},data(){return{opt:{useWorker:!0,enableBasicAutocompletion:!0,enableLiveAutocompletion:!0,enableSnippets:!0,showPrintMargin:!1,highlightActiveLine:!0,highlightSelectedWord:!0,tabSize:4,fontSize:14,wrap:!1,readonly:!1},formData:{sourceDataSql:"",warnRule:"",deviceNameField:"",deviceIdField:""}}},watch:{formData:{deep:!0,handler(n){const l=JSON.stringify(n);l!==this.modelValue&&this.$emit("update:modelValue",l)}},modelValue:{immediate:!0,deep:!0,handler(n){if(n!=null){const l=JSON.parse(n);this.formData={...l}}}}},methods:{}};function E(n,l,N,x,t,B){const s=v,d=c("ace-input"),r=S,u=F,m=g,i=h,p=b;return D(),_("div",null,[e(s,null,{default:o(()=>l[4]||(l[4]=[V("span",{style:{"font-size":"15px"}},"业务告警规则",-1)])),_:1}),e(p,{ref:"form",model:t.formData,"label-width":"auto"},{default:o(()=>[e(m,null,{default:o(()=>[e(u,{span:24},{default:o(()=>[e(r,{label:"获取数据SQL"},{default:o(()=>[e(d,{ref:"aceInput",modelValue:t.formData.sourceDataSql,"onUpdate:modelValue":l[0]||(l[0]=a=>t.formData.sourceDataSql=a),lang:"sql",placeholder:"请输入获取数据SQL语句"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,null,{default:o(()=>[e(u,{span:24},{default:o(()=>[e(r,{label:"告警规则"},{default:o(()=>[e(d,{ref:"aceInput",modelValue:t.formData.warnRule,"onUpdate:modelValue":l[1]||(l[1]=a=>t.formData.warnRule=a),format:!0,lang:"groovy",placeholder:"请输入告警规则"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(m,null,{default:o(()=>[e(u,{span:12},{default:o(()=>[e(r,{label:"设备名称字段"},{default:o(()=>[e(i,{modelValue:t.formData.deviceNameField,"onUpdate:modelValue":l[2]||(l[2]=a=>t.formData.deviceNameField=a),placeholder:"请输入设备名称字段名",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:o(()=>[e(r,{label:"设备ID字段"},{default:o(()=>[e(i,{modelValue:t.formData.deviceIdField,"onUpdate:modelValue":l[3]||(l[3]=a=>t.formData.deviceIdField=a),placeholder:"请输入设备ID字段名"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])])}const A=f(w,[["render",E]]);export{A as default};
