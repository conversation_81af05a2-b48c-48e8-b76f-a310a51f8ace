import{d as f,bz as _,h as b,X as v,c as w,o as k,p as s,q as e,t as d,ax as C,K as x,J as $,e as E,ay as T,_ as y,W as B,i as I,Q as G}from"./index-D07cMzhp.js";import{a as N,b as z}from"./auth-Bn5oc0C3.js";const K=["src"],M={id:"git-user-binding"},P={id:"authlist",class:"user-bind"},U={class:"git-other-login-icon"},V={class:"git-other-login-icon"},q={class:"git-other-login-icon"},S={class:"git-other-login-icon"},W={class:"git-other-login-icon"},X=f({__name:"thirdParty",props:{auths:_.any.isRequired},setup(p){const{proxy:a}=b(),c=p,m=v(()=>c.auths),u=l=>{B.confirm('您确定要解除"'+l.source+'"的账号绑定吗？').then(()=>N(l.id)).then(t=>{t.code===200?(a==null||a.$modal.msgSuccess("解绑成功"),a==null||a.$tab.refreshPage()):a==null||a.$modal.msgError(t.msg)}).catch(()=>{})},i=l=>{z(l,I().tenantId).then(t=>{t.code===200?window.location.href=t.data:a==null||a.$modal.msgError(t.msg)})};return(l,t)=>{const o=C,h=x,g=T,r=y;return k(),w("div",null,[s(g,{data:E(m),border:"",style:{width:"100%",height:"100%","font-size":"14px"}},{default:d(()=>[s(o,{label:"序号",width:"50",type:"index"}),s(o,{label:"绑定账号平台",width:"140",align:"center",prop:"source","show-overflow-tooltip":""}),s(o,{label:"头像",width:"120",align:"center",prop:"avatar"},{default:d(n=>[e("img",{src:n.row.avatar,style:{width:"45px",height:"45px"}},null,8,K)]),_:1}),s(o,{label:"系统账号",width:"180",align:"center",prop:"userName","show-overflow-tooltip":!0}),s(o,{label:"绑定时间",width:"180",align:"center",prop:"createTime"}),s(o,{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"80"},{default:d(n=>[s(h,{size:"small",type:"text",onClick:A=>u(n.row)},{default:d(()=>t[5]||(t[5]=[$("解绑")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),e("div",M,[t[11]||(t[11]=e("h4",{class:"provider-desc"},"你可以绑定以下第三方帐号",-1)),e("div",P,[e("a",{class:"third-app",href:"#",title:"使用 微信 账号授权登录",onClick:t[0]||(t[0]=n=>i("wechat"))},[e("div",U,[s(r,{"icon-class":"wechat"})]),t[6]||(t[6]=e("span",{class:"app-name"},"WeiXin",-1))]),e("a",{class:"third-app",href:"#",title:"使用 MaxKey 账号授权登录",onClick:t[1]||(t[1]=n=>i("maxkey"))},[e("div",V,[s(r,{"icon-class":"maxkey"})]),t[7]||(t[7]=e("span",{class:"app-name"},"MaxKey",-1))]),e("a",{class:"third-app",href:"#",title:"使用 TopIam 账号授权登录",onClick:t[2]||(t[2]=n=>i("topiam"))},[e("div",q,[s(r,{"icon-class":"topiam"})]),t[8]||(t[8]=e("span",{class:"app-name"},"TopIam",-1))]),e("a",{class:"third-app",href:"#",title:"使用 Gitee 账号授权登录",onClick:t[3]||(t[3]=n=>i("gitee"))},[e("div",S,[s(r,{"icon-class":"gitee"})]),t[9]||(t[9]=e("span",{class:"app-name"},"Gitee",-1))]),e("a",{class:"third-app",href:"#",title:"使用 GitHub 账号授权登录",onClick:t[4]||(t[4]=n=>i("github"))},[e("div",W,[s(r,{"icon-class":"github"})]),t[10]||(t[10]=e("span",{class:"app-name"},"Github",-1))])])])])}}}),Q=G(X,[["__scopeId","data-v-cfad0f7c"]]);export{Q as default};
