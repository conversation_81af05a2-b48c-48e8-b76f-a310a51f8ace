import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PublicityActivityForm, PublicityActivityQuery, PublicityActivityVO } from '@/api/epms/eptw/publicityActivity/types';

/**
 * 查询宣传活动列表
 * @param query
 * @returns {*}
 */

export const listPublicityActivity = (query?: PublicityActivityQuery): AxiosPromise<PublicityActivityVO[]> => {
  return request({
    url: '/epms/publicityActivity/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询宣传活动详细
 * @param activityId
 */
export const getPublicityActivity = (activityId: string | number): AxiosPromise<PublicityActivityVO> => {
  return request({
    url: '/epms/publicityActivity/' + activityId,
    method: 'get'
  });
};

/**
 * 新增宣传活动
 * @param data
 */
export const addPublicityActivity = (data: PublicityActivityForm) => {
  return request({
    url: '/epms/publicityActivity',
    method: 'post',
    data: data
  });
};

/**
 * 修改宣传活动
 * @param data
 */
export const updatePublicityActivity = (data: PublicityActivityForm) => {
  return request({
    url: '/epms/publicityActivity',
    method: 'put',
    data: data
  });
};

/**
 * 删除宣传活动
 * @param activityId
 */
export const delPublicityActivity = (activityId: string | number | Array<string | number>) => {
  return request({
    url: '/epms/publicityActivity/' + activityId,
    method: 'delete'
  });
};
