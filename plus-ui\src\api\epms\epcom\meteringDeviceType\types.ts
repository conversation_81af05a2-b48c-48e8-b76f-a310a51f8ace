export interface MeteringDeviceTypeVO {
  /**
   * 类型id
   */
  deviceTypeId: string | number;

  /**
   * 类型名称
   */
  deviceTypeName: string;

  /**
   * 父id
   */
  parentId: string | number;

  /**
   * 子对象
   */
  children: MeteringDeviceTypeVO[];
}

export interface MeteringDeviceTypeForm extends BaseEntity {
  /**
   * 类型id
   */
  deviceTypeId?: string | number;

  /**
   * 类型名称
   */
  deviceTypeName?: string;

  /**
   * 父id
   */
  parentId?: string | number;
}

export interface MeteringDeviceTypeQuery {
  /**
   * 类型名称
   */
  deviceTypeName?: string;

  /**
   * 父id
   */
  parentId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
