export interface PublicityActivityVO {
  /**
   * 宣传id
   */
  activityId: string | number;

  /**
   * 活动时间
   */
  activityTime: string;

  /**
   * 活动主题
   */
  activitySubject: string;

  /**
   * 活动类型
   */
  activityType: number;

  /**
   * 活动附件
   */
  activityAttach: string;

  /**
   * 活动内容
   */
  activityContent: string;

  /**
   * 活动状态
   */
  status: number;
}

export interface PublicityActivityForm extends BaseEntity {
  /**
   * 宣传id
   */
  activityId?: string | number;

  /**
   * 活动时间
   */
  activityTime?: string;

  /**
   * 活动主题
   */
  activitySubject?: string;

  /**
   * 活动类型
   */
  activityType?: number;

  /**
   * 活动附件
   */
  activityAttach?: string;

  /**
   * 活动内容
   */
  activityContent?: string;

  /**
   * 活动状态
   */
  status?: number;
}

export interface PublicityActivityQuery extends PageQuery {
  /**
   * 活动时间
   */
  activityTime?: Date[];

  startTime?: string;
  endTime?: string;

  /**
   * 活动主题
   */
  activitySubject?: string;

  /**
   * 活动类型
   */
  activityType?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
