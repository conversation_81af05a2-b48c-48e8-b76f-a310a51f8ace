import{r as i,d as fe,h as ve,ak as ge,X as _e,cR as he,k as be,m as H,c as J,o as U,p as l,aA as ke,e as a,t as o,a7 as we,G as ye,a8 as xe,aI as Ie,w as O,q as P,M as Ce,A as Ne,H as Q,K as Re,J as w,am as $,bY as Ve,aJ as Se,z as G,az as Ee,F as De,C as Te,x as Ue,aw as Be,n as Fe}from"./index-D07cMzhp.js";import{E as Ae}from"./el-row-CikYE3zA.js";import{E as ze}from"./el-col-BaG5Rg5z.js";import{E as Ke}from"./el-tree-DW6MoFaI.js";import{d as B}from"./index-BdvXA74M.js";const Le=C=>{const h=i(!1);return{title:i(C.title||""),visible:h,openDialog:()=>{h.value=!0},closeDialog:()=>{h.value=!1}}},qe={class:"mb-[10px]"},Ge=fe({__name:"index",props:{modelValue:{default:void 0},multiple:{type:Boolean,default:!0},data:{default:void 0},userIds:{default:void 0}},emits:["update:modelValue","confirmCallBack"],setup(C,{expose:h,emit:F}){const v=C,N=F,{proxy:d}=ve(),{sys_normal_disable:M}=ge(d==null?void 0:d.useDict("sys_normal_disable")),b=i(),R=i(!0),X=i(!0),V=i(0),A=i(["",""]),y=i(""),z=i([]),s=i([]),S=i(),K=i(),m=i(),c=Le({title:"用户选择"}),r=i({pageNum:1,pageSize:10,userName:"",phonenumber:"",status:"",deptId:"",roleId:"",userIds:""}),E=_e(()=>j(v.data));he(()=>{var t;(t=S.value)==null||t.filter(y.value)},{flush:"post"});const Y=()=>{N("update:modelValue",s.value),N("confirmCallBack",s.value),c.closeDialog()},j=t=>t instanceof Array?t.map(e=>String(e)):typeof t=="string"?t.split(","):typeof t=="number"?[t]:(console.warn("<UserSelect> The data type of data should be array or string or number, but I received other"),[]),W=(t,e)=>t?e.label.indexOf(t)!==-1:!0,Z=async()=>{const t=await B.deptTreeSelect();z.value=t.data},D=async()=>{R.value=!0,r.value.userIds=v.userIds;const t=await B.listUser(d==null?void 0:d.addDateRange(r.value,A.value));R.value=!1,b.value=t.rows,V.value=t.total},ee=async()=>{await D();const t=b.value.filter(e=>s.value.some(n=>n.userId===e.userId));await m.value.setCheckboxRow(t,!0)},te=t=>{r.value.deptId=t.id,k()},k=()=>{r.value.pageNum=1,D()},L=(t=!0)=>{var e,n;A.value=["",""],(e=K.value)==null||e.resetFields(),r.value.pageNum=1,r.value.deptId=void 0,(n=S.value)==null||n.setCurrentKey(void 0),t&&k()},le=t=>{!v.multiple&&t.checked&&(m.value.setCheckboxRow(s.value,!1),s.value=[]);const e=t.row;t.checked?s.value.push(e):s.value=s.value.filter(n=>n.userId!==e.userId)},ae=t=>{const e=b.value;t.checked?e.forEach(n=>{s.value.some(f=>f.userId===n.userId)||s.value.push(n)}):s.value=s.value.filter(n=>!e.some(f=>f.userId===n.userId))},oe=t=>{var g;const e=t.userId,n=s.value.findIndex(x=>x.userId===e),f=s.value[n];(g=m.value)==null||g.setCheckboxRow(f,!1),s.value.splice(n,1)},ne=async()=>{if(E.value.length>0){const{data:t}=await B.optionSelect(E.value);s.value=t;const e=b.value.filter(n=>E.value.includes(String(n.userId)));await Fe(()=>{m.value.setCheckboxRow(e,!0)})}},se=()=>{c.closeDialog()};return be(()=>c.visible.value,async t=>{t?(await Z(),await D(),await ne()):(m.value.clearCheckboxReserve(),m.value.clearCheckboxRow(),L(!1),s.value=[])}),h({open:c.openDialog,close:c.closeDialog}),(t,e)=>{const n=ye,f=Ke,g=we,x=ze,T=Ne,I=Re,re=Ce,ue=Be,p=H("vxe-column"),ie=Se,de=H("vxe-table"),ce=Ee,pe=Ae,me=ke;return U(),J("div",null,[l(me,{modelValue:a(c).visible.value,"onUpdate:modelValue":e[6]||(e[6]=_=>a(c).visible.value=_),title:a(c).title.value,width:"80%","append-to-body":""},{footer:o(()=>[l(I,{onClick:se},{default:o(()=>e[9]||(e[9]=[w("取消")])),_:1}),l(I,{type:"primary",onClick:Y},{default:o(()=>e[10]||(e[10]=[w("确定")])),_:1})]),default:o(()=>[l(pe,{gutter:20},{default:o(()=>[l(x,{lg:4,xs:24,style:{}},{default:o(()=>[l(g,{shadow:"hover"},{default:o(()=>[l(n,{modelValue:a(y),"onUpdate:modelValue":e[0]||(e[0]=_=>xe(y)?y.value=_:null),placeholder:"请输入部门名称","prefix-icon":"Search",clearable:""},null,8,["modelValue"]),l(f,{ref_key:"deptTreeRef",ref:S,class:"mt-2","node-key":"id",data:a(z),props:{label:"label",children:"children"},"expand-on-click-node":!1,"filter-node-method":W,"highlight-current":"","default-expand-all":"",onNodeClick:te},null,8,["data"])]),_:1})]),_:1}),l(x,{lg:20,xs:24},{default:o(()=>{var _,q;return[l(Ie,{"enter-active-class":(_=a(d))==null?void 0:_.animate.searchAnimate.enter,"leave-active-class":(q=a(d))==null?void 0:q.animate.searchAnimate.leave},{default:o(()=>[O(P("div",qe,[l(g,{shadow:"hover"},{default:o(()=>[l(re,{ref_key:"queryFormRef",ref:K,model:a(r),inline:!0},{default:o(()=>[l(T,{label:"用户名称",prop:"userName"},{default:o(()=>[l(n,{modelValue:a(r).userName,"onUpdate:modelValue":e[1]||(e[1]=u=>a(r).userName=u),placeholder:"请输入用户名称",clearable:"",onKeyup:Q(k,["enter"])},null,8,["modelValue"])]),_:1}),l(T,{label:"手机号码",prop:"phonenumber"},{default:o(()=>[l(n,{modelValue:a(r).phonenumber,"onUpdate:modelValue":e[2]||(e[2]=u=>a(r).phonenumber=u),placeholder:"请输入手机号码",clearable:"",onKeyup:Q(k,["enter"])},null,8,["modelValue"])]),_:1}),l(T,null,{default:o(()=>[l(I,{type:"primary",icon:"Search",onClick:k},{default:o(()=>e[7]||(e[7]=[w("搜索")])),_:1}),l(I,{icon:"Refresh",onClick:e[3]||(e[3]=()=>L())},{default:o(()=>e[8]||(e[8]=[w("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[$,a(X)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(g,{shadow:"hover"},Ve({default:o(()=>[l(de,{ref_key:"tableRef",ref:m,height:"400px",border:"","show-overflow":"",data:a(b),loading:a(R),"row-config":{keyField:"userId",isHover:!0},"checkbox-config":{reserve:!0,trigger:"row",highlight:!0,showHeader:v.multiple},onCheckboxAll:ae,onCheckboxChange:le},{default:o(()=>[l(p,{type:"checkbox",width:"50",align:"center"}),l(p,{key:"userId",title:"用户编号",align:"center",field:"userId"}),l(p,{key:"userName",title:"用户名称",align:"center",field:"userName"}),l(p,{key:"nickName",title:"用户昵称",align:"center",field:"nickName"}),l(p,{key:"deptName",title:"部门",align:"center",field:"deptName"}),l(p,{key:"phonenumber",title:"手机号码",align:"center",field:"phonenumber",width:"120"}),l(p,{key:"status",title:"状态",align:"center"},{default:o(u=>[l(ie,{options:a(M),value:u.row.status},null,8,["options","value"])]),_:1}),l(p,{title:"创建时间",align:"center",width:"160"},{default:o(u=>[P("span",null,G(u.row.createTime),1)]),_:1})]),_:1},8,["data","loading","checkbox-config"]),O(l(ce,{page:a(r).pageNum,"onUpdate:page":e[4]||(e[4]=u=>a(r).pageNum=u),limit:a(r).pageSize,"onUpdate:limit":e[5]||(e[5]=u=>a(r).pageSize=u),total:a(V),onPagination:ee},null,8,["page","limit","total"]),[[$,a(V)>0]])]),_:2},[v.multiple?{name:"header",fn:o(()=>[(U(!0),J(De,null,Te(a(s),u=>(U(),Ue(ue,{key:u.userId,closable:"",style:{margin:"2px"},onClose:He=>oe(u)},{default:o(()=>[w(G(u.nickName),1)]),_:2},1032,["onClose"]))),128))]),key:"0"}:void 0]),1024)]}),_:1})]),_:1})]),_:1},8,["modelValue","title"])])}}});export{Ge as _};
