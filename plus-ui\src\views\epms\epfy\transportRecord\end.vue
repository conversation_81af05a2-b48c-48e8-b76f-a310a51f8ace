<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="装液时间" prop="transportTime">
              <el-date-picker
                v-model="queryParams.transportTime"
                clearable
                placeholder="请选择装液时间"
                type="date"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="过磅时间" prop="pumpTime">
              <el-date-picker v-model="queryParams.pumpTime" clearable placeholder="请选择过磅时间" type="date" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item label="过磅地点" prop="pumpLocation">
              <el-input v-model="queryParams.pumpLocation" placeholder="请输入过磅地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卸液地点" prop="unloadLocation">
              <el-input v-model="queryParams.unloadLocation" placeholder="请输入卸液地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卸液点负责人" prop="unloadDirector">
              <el-input v-model="queryParams.unloadDirector" placeholder="请输入卸液点负责人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!--            <el-form-item label="拉运申请名称" prop="appName">-->
            <!--              <el-input v-model="queryParams.appName" placeholder="请输入拉运申请名称" clearable-->
            <!--                        @keyup.enter="handleQuery"/>-->
            <!--            </el-form-item>-->
            <el-form-item label="拉运申请" prop="applicationId">
              <el-select v-model="queryParams.applicationId" clearable filterable placeholder="请选择拉运申请">
                <el-option v-for="dict in queryApplicationList" :key="dict.appId" :label="dict.splicedName" :value="dict.appId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['epfy:transportRecord:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epfy:transportRecord:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="transportRecordList" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column
          :index="(index) => (queryParams.pageNum - 1) * queryParams.pageSize + index + 1"
          align="center"
          label="序号"
          type="index"
          width="50"
        />
        <el-table-column align="center" label="拉运申请" prop="applicationId" width="300">
          <template #default="scope">
            {{ toApplicationName[scope.row.applicationId] || '未知' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="车牌号" prop="licensePlate" width="100" />
        <el-table-column label="司机" align="center" prop="transporter" />
        <el-table-column align="center" label="装液时间" prop="transportTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.transportTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="过磅时间" prop="pumpTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.pumpTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="过磅地点" prop="pumpLocation" width="110" />
        <el-table-column align="center" label="卸液地点" prop="unloadLocation" width="110" />
        <el-table-column align="center" label="卸液量(方)" prop="unloadNumber" width="90" />
        <el-table-column label="铅封号" align="center" prop="sealNo" />
        <!--        <el-table-column label="卸液时间" align="center" prop="unloadTime" width="140">-->
        <!--          <template #default="scope">-->
        <!--            <span>{{ parseTime(scope.row.unloadTime, '{y}-{m}-{d}') }}</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column align="center" label="审核信息" min-width="240px">
          <template #default="scope">
            卸液点负责人:
            <span v-if="[2].includes(scope.row?.unloadApproval)">
              {{ scope.row.unloadDirector }}
              {{ parseTime(scope.row.unloadApprovalTime, '{y}-{m}-{d}') }}
            </span>
            <span v-else style="display: inline-flex; margin-top: 5px">
              <dict-tag :options="epfy_application_status" :value="scope.row?.unloadApproval ?? ''" />
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="备注信息" prop="remark" width="120" />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="300">
          <template #default="scope">
            <el-tooltip v-if="scope.row.unloadApproval === 2" content="计量凭证下载" placement="top">
              <el-button link type="primary" icon="Download" @click="handleDownload(scope.row)">计量凭证下载</el-button>
            </el-tooltip>
            <el-tooltip v-if="scope.row.unloadDirector && scope.row?.unloadApproval !== 2" content="卸液点负责人审核" placement="top">
              <el-button v-hasPermi="['epfy:transportRecord:unload']" icon="Coordinate" link type="primary" @click="handleCoordinate(scope.row, 2)">
                施工负责人审核
              </el-button>
            </el-tooltip>
            <el-tooltip content="填写卸液记录" placement="top">
              <el-button v-hasPermi="['epfy:transportRecord:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"
                >填写卸液记录</el-button
              >
            </el-tooltip>
            <el-tooltip content="附件预览" placement="top">
              <el-dropdown style="height: 100%; line-height: inherit; padding: 2px">
                <el-button icon="Folder" link type="primary">附件预览</el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      ><el-tooltip content="重车吨数" placement="top">
                        <el-button
                          v-hasPermi="['epfy:transportRecord:preview']"
                          icon="Document"
                          link
                          type="primary"
                          @click="handlePreview(scope.row, 'heavyVehicleTonnage')"
                          >重车吨数</el-button
                        >
                      </el-tooltip></el-dropdown-item
                    >
                    <el-dropdown-item
                      ><el-tooltip content="卸液口铅封" placement="top">
                        <el-button
                          v-hasPermi="['epfy:transportRecord:preview']"
                          icon="Document"
                          link
                          type="primary"
                          @click="handlePreview(scope.row, 'leadSealDischargePort')"
                          >卸液口铅封</el-button
                        >
                      </el-tooltip></el-dropdown-item
                    >
                    <el-dropdown-item
                      ><el-tooltip content="空车尺量" placement="top">
                        <el-button
                          v-hasPermi="['epfy:transportRecord:preview']"
                          icon="Document"
                          link
                          type="primary"
                          @click="handlePreview(scope.row, 'emptyVehicleMeasurement')"
                          >空车尺量</el-button
                        >
                      </el-tooltip></el-dropdown-item
                    >
                    <el-dropdown-item
                      ><el-tooltip content="空车吨位" placement="top">
                        <el-button
                          v-hasPermi="['epfy:transportRecord:preview']"
                          icon="Document"
                          link
                          type="primary"
                          @click="handlePreview(scope.row, 'emptyTonnageVehicles')"
                          >空车吨位</el-button
                        >
                      </el-tooltip></el-dropdown-item
                    >
                    <el-dropdown-item
                      ><el-tooltip content="计量凭证" placement="top">
                        <el-button
                          v-hasPermi="['epfy:transportRecord:preview']"
                          icon="Document"
                          link
                          type="primary"
                          @click="handlePreview(scope.row, 'transportMeasurement')"
                          >计量凭证</el-button
                        >
                      </el-tooltip></el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改拉运记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="transportRecordFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="拉运申请" prop="applicationId">
          <el-select v-model="form.applicationId" placeholder="请选择拉运申请" disabled>
            <el-option v-for="dict in applicationList" :key="dict.appId" :label="dict.appName" :value="dict.appId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.transportId" label="装液时间" prop="transportTime">
          <el-date-picker v-model="form.transportTime" clearable disabled type="datetime" value-format="YYYY-MM-DD HH:mm:ss"> </el-date-picker>
        </el-form-item>
        <el-form-item label="过磅时间" prop="pumpTime">
          <el-date-picker v-model="form.pumpTime" clearable placeholder="请选择过磅时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="过磅地点" prop="pumpLocation">
          <el-select filterable v-model="form.pumpLocation" placeholder="选择过磅地点" clearable allow-create>
            <el-option
              v-for="operationArea in pianquList"
              :key="operationArea.operationAreaName"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="重车吨数" prop="heavyVehicleTonnageNum">
          <el-input v-model="form.heavyVehicleTonnageNum" placeholder="请输入重车吨数">
            <template #append>吨</template>
          </el-input>
        </el-form-item>
        <el-form-item label="空车吨数" prop="emptyTonnageVehiclesNum">
          <el-input v-model="form.emptyTonnageVehiclesNum" placeholder="请输入空车吨数">
            <template #append>吨</template>
          </el-input>
        </el-form-item>
        <el-form-item label="介质类型" prop="mediumType" v-if="form.transportId">
          <el-select v-model="form.mediumType" placeholder="请选择介质类型" disabled>
            <el-option v-for="dict in epfy_yexiang_medium_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="卸液时间" prop="unloadTime">
          <el-date-picker v-model="form.unloadTime" clearable placeholder="请选择卸液时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="卸液量(方)" prop="unloadNumber">
          <el-input v-model="form.unloadNumber" placeholder="请输入卸液量">
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <!--        <el-form-item label="发送人" prop="sender">-->
        <!--          <el-input v-model="form.sender" placeholder="请输入发送人" />-->
        <!--        </el-form-item>-->
        <el-form-item label="司机" prop="transporter">
          <el-select filterable v-model="form.transporter" placeholder="选择司机" allow-create default-first-option>
            <el-option v-for="item in epfy_driver" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="车牌号" prop="licensePlate">
          <el-input v-model="form.licensePlate" placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="铅封号" prop="sealNo">
          <el-input v-model="form.sealNo" placeholder="请输入铅封号" />
        </el-form-item>

        <el-form-item label="卸液地点" prop="unloadLocation">
          <el-select filterable v-model="form.unloadLocation" placeholder="选择卸液地点" clearable allow-create>
            <el-option
              v-for="operationArea in pianquList"
              :key="operationArea.operationAreaName"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="卸液点负责人" prop="unloadDirector">
          <el-input v-model="form.unloadDirector" placeholder="请输入卸液点负责人" />
        </el-form-item>
        <el-form-item label="施工进度" prop="unloadProcess">
          <el-select v-model="form.unloadProcess" placeholder="请选择施工进度">
            <el-option v-for="dict in epfy_liquid_progress" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注信息" />
        </el-form-item>
        <el-form-item label="重车吨数" prop="heavyVehicleTonnage">
          <attachFileUpload
            v-model="form.heavyVehicleTonnage"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="heavyVehicleTonnage"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>
        <el-form-item label="卸液口铅封" prop="leadSealDischargePort">
          <attachFileUpload
            v-model="form.leadSealDischargePort"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="leadSealDischargePort"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>
        <el-form-item label="空车量尺" prop="emptyVehicleMeasurement">
          <attachFileUpload
            v-model="form.emptyVehicleMeasurement"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="emptyVehicleMeasurement"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>
        <el-form-item label="空车吨位" prop="emptyTonnageVehicles">
          <attachFileUpload
            v-model="form.emptyTonnageVehicles"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="emptyTonnageVehicles"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>

        <el-form-item label="计量凭证" prop="measurementVoucher">
          <attachFileUpload
            v-model="form.measurementVoucher"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="transportMeasurement"
            attach-source-type="yxTransportRecord"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="warning" @click="submitForm(true)">重新提交审核</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm(false)">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改拉运记录对话框 -->
    <el-dialog v-model="cooDialog.visible" :title="cooDialog.title" append-to-body width="600px">
      <el-form ref="transportRecordFormRef" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="拉运申请" prop="applicationId">
          <el-select v-model="form.applicationId" placeholder="请选择拉运申请" disabled>
            <el-option v-for="dict in applicationList" :key="dict.appId" :label="dict.appName" :value="dict.appId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.transportId" label="装液时间" prop="transportTime">
          <el-date-picker v-model="form.transportTime" clearable disabled type="datetime" value-format="YYYY-MM-DD HH:mm:ss"> </el-date-picker>
        </el-form-item>
        <el-form-item label="过磅时间" prop="pumpTime">
          <el-date-picker v-model="form.pumpTime" clearable disabled placeholder="请选择过磅时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="过磅地点" prop="pumpLocation">
          <el-select filterable v-model="form.pumpLocation" placeholder="选择过磅地点" disabled clearable allow-create>
            <el-option
              v-for="operationArea in pianquList"
              :key="operationArea.operationAreaName"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="介质类型" prop="mediumType" v-if="form.transportId">
          <el-select v-model="form.mediumType" placeholder="请选择介质类型" disabled>
            <el-option v-for="dict in epfy_yexiang_medium_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="卸液时间" prop="unloadTime">
          <el-date-picker
            v-model="form.unloadTime"
            clearable
            disabled
            placeholder="请选择卸液时间"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="卸液量(方)" prop="unloadNumber">
          <el-input v-model="form.unloadNumber" placeholder="请输入卸液量" disabled>
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <!--        <el-form-item label="发送人" prop="sender">-->
        <!--          <el-input v-model="form.sender" placeholder="请输入发送人" />-->
        <!--        </el-form-item>-->
        <el-form-item label="司机" prop="transporter">
          <el-select filterable v-model="form.transporter" placeholder="选择司机" allow-create disabled default-first-option>
            <el-option v-for="item in epfy_driver" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="车牌号" prop="licensePlate">
          <el-input v-model="form.licensePlate" disabled placeholder="请输入车牌号" />
        </el-form-item>
        <el-form-item label="铅封号" prop="sealNo">
          <el-input v-model="form.sealNo" disabled placeholder="请输入铅封号" />
        </el-form-item>

        <el-form-item label="卸液地点" prop="unloadLocation">
          <el-select filterable v-model="form.unloadLocation" placeholder="选择卸液地点" clearable disabled allow-create>
            <el-option
              v-for="operationArea in pianquList"
              :key="operationArea.operationAreaName"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="卸液点负责人" prop="unloadDirector">
          <el-input v-model="form.unloadDirector" placeholder="请输入卸液点负责人" disabled />
        </el-form-item>
        <el-form-item label="施工进度" prop="unloadProcess">
          <el-select v-model="form.unloadProcess" placeholder="请选择施工进度" disabled>
            <el-option v-for="dict in epfy_liquid_progress" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" disabled placeholder="请输入备注信息" />
        </el-form-item>
        <el-form-item label="卸液点负责人审核状态" prop="unloadApproval">
          <el-radio-group v-model="form.unloadApproval">
            <el-radio v-for="dict in epfy_application_status" :key="dict.value" :value="parseInt(dict.value)">{{ dict.label }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="卸液点负责人审核时间" prop="senderApprovalTime">
          <el-date-picker v-model="form.senderApprovalTime" clearable disabled placeholder="" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm(false)">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TransportRecord" lang="ts">
import {
  addTransportRecord,
  delTransportRecord,
  getTransportRecord,
  listTransportRecord,
  updateTransportRecord
} from '@/api/epms/epfy/transportRecord';
import { TransportRecordForm, TransportRecordQuery, TransportRecordVO } from '@/api/epms/epfy/transportRecord/types';
import { getTransportApplication, listAllTransportApplication, optionselectApplication } from '@/api/epms/epfy/transportApplication';
import { TransportApplicationVO } from '@/api/epms/epfy/transportApplication/types';
import dayjs from 'dayjs';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';

const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epfy_medium_category, epfy_guxiang_medium_type, epfy_yexiang_medium_type, epfy_liquid_progress, epfy_driver, epfy_application_status } =
  toRefs<any>(
    proxy?.useDict(
      'epfy_medium_category',
      'epfy_guxiang_medium_type',
      'epfy_yexiang_medium_type',
      'epfy_liquid_progress',
      'epfy_driver',
      'epfy_application_status'
    )
  );

const transportRecordList = ref<TransportRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const pianquList = ref<OperationAreaVO[]>([]);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const operationAreaQuery = ref<OperationAreaQuery>({});
const total = ref(0);
const applicationList = ref<TransportApplicationVO[]>([]);
const queryApplicationList = ref<TransportApplicationVO[]>([]);
const queryFormRef = ref<ElFormInstance>();
const transportRecordFormRef = ref<ElFormInstance>();
const toApplicationName = ref({});
const approveType = ref<number>(1);
const suggestion = ref(false); //拉运申请审核意见是否显示
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const cooDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const initFormData: TransportRecordForm = {
  transportId: undefined,
  applicationId: undefined,
  transportTime: undefined,
  pumpTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  mediumCategory: 2,
  mediumType: undefined,
  departurePoint: undefined,
  number: undefined,
  sender: undefined,
  transporter: undefined,
  licensePlate: undefined,
  arrivalPoint: undefined,
  receiver: undefined,
  remark: undefined,
  measurementVoucher: undefined,
  photo: undefined,
  unloadNumber: undefined,
  flowType: 1
};
const data = reactive<PageData<TransportRecordForm, TransportRecordQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    applicationId: undefined,
    transportTime: undefined,
    mediumCategory: 2,
    mediumType: undefined,
    departurePoint: undefined,
    number: undefined,
    sender: undefined,
    transporter: undefined,
    licensePlate: undefined,
    arrivalPoint: undefined,
    receiver: undefined,
    measurementVoucher: undefined,
    photo: undefined,
    flowType: 1,

    unloadLocationType: 1, // 卸车地点 作业区
    params: {}
  },
  rules: {
    applicationId: [{ required: true, message: '拉运申请不能为空', trigger: 'change' }],
    pumpTime: [{ required: true, message: '过磅时间不能为空', trigger: 'blur' }],
    pumpLocation: [{ required: true, message: '过磅地点不能为空', trigger: 'blur' }],
    unloadLocation: [{ required: true, message: '卸液地点不能为空', trigger: 'blur' }],
    unloadDirector: [{ required: true, message: '卸液点负责人不能为空', trigger: 'blur' }],
    transporter: [{ required: true, message: '司机不能为空', trigger: 'blur' }],
    licensePlate: [{ required: true, message: '车牌号不能为空', trigger: 'blur' }],
    sealNo: [{ required: true, message: '铅封号不能为空', trigger: 'blur' }],
    unloadTime: [{ required: true, message: '卸液时间不能为空', trigger: 'blur' }],
    unloadNumber: [{ required: true, message: '卸液量不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询拉运记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTransportRecord(queryParams.value);
  transportRecordList.value = res.rows;

  const appIdList = transportRecordList.value.map((item) => item.applicationId);
  const resApp = await listAllTransportApplication({
    appIds: appIdList.join(',')
  });

  toApplicationName.value = resApp.rows.reduce((map, item) => {
    map[item.appId] = `${item.appName}-${item.wellName}-${item.applicationDate}`; // 使用模板字符串拼接
    return map;
  }, {});

  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  cooDialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  transportRecordFormRef.value?.resetFields();
};

/** 审核按钮操作 */
const handleCoordinate = async (row?: TransportRecordVO, type?: number) => {
  reset();
  const _transportId = row?.transportId || ids.value[0];
  const res = await getTransportRecord(_transportId);
  Object.assign(form.value, res.data);
  approveType.value = type;
  suggestion.value = true;
  cooDialog.visible = true;
  cooDialog.title = '审核卸液记录';
};

const handleDownload = async (row?: TransportRecordVO) => {
  proxy?.download(
    'epfy/transportRecord/downloadMeasurementVoucher',
    {
      transportId: row?.transportId
    },
    '二连分公司废液收集计量凭证.xls'
  );
};
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
const getPianquList = async () => {
  operationAreaQuery.value.operationAreaType = 3;
  const pianquRes = await listOperationArea(operationAreaQuery.value); //查询片区
  pianquList.value = pianquRes.rows;
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TransportRecordVO[]) => {
  ids.value = selection.map((item) => item.transportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加拉运记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TransportRecordVO) => {
  reset();
  const _transportId = row?.transportId || ids.value[0];
  const res = await getTransportRecord(_transportId);
  Object.assign(form.value, res.data);
  const applicationRes = await getTransportApplication(res.data.applicationId);
  const application = applicationRes.data;

  if (application) {
    application.appName = `${application.appName}-${application.wellName}-${application.applicationDate}`;
  }
  applicationList.value = [application];
  form.value.unloadTime = form.value.unloadTime ? form.value.unloadTime : dayjs().format('YYYY-MM-DD HH:mm:ss');
  form.value.pumpTime = form.value.pumpTime ? form.value.pumpTime : dayjs().format('YYYY-MM-DD HH:mm:ss');
  form.value.unloadNumber = form.value.unloadNumber ? form.value.unloadNumber : res.data.number;
  dialog.visible = true;
  dialog.title = '填写卸液记录';
};

const handlePreview = async (row?: TransportRecordVO, type?: string) => {
  proxy.showAttachPreview({
    attachSourceId: row.transportId,
    attachSourceType: 'yxTransportRecord',
    attachCategory: type
  });
};

/** 提交按钮 */
const submitForm = (flag?: boolean) => {
  if (flag) {
    form.value.unloadApproval = 1;
  } else {
    form.value.unloadApproval = form.value.unloadApproval ? form.value.unloadApproval : 1;
  }
  if (form.value.unloadApproval == 2 && !form.value.unloadApprovalTime) {
    form.value.unloadApprovalTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  }

  transportRecordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.transportId) {
        await updateTransportRecord(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTransportRecord(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      cooDialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: TransportRecordVO) => {
  const _transportIds = row?.transportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除拉运记录编号为"' + _transportIds + '"的数据项？').finally(() => (loading.value = false));
  await delTransportRecord(_transportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

const getQueryApplicationList = async () => {
  const res = await optionselectApplication();
  queryApplicationList.value = res.rows;
};
/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epfy/transportRecord/export',
    {
      ...queryParams.value
    },
    `拉运记录_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getQueryApplicationList();
  getPianquList();
  getList();
});
</script>
