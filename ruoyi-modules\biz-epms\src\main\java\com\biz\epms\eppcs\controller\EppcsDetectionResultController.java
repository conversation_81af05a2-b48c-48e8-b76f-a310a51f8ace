package com.biz.epms.eppcs.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.biz.comm.attach.annotation.AttachDelete;
import com.biz.comm.attach.annotation.AttachUpload;
import com.biz.epms.eppcs.domain.bo.EppcsDetectionResultBo;
import com.biz.epms.eppcs.domain.vo.EppcsDetectionResultVo;
import com.biz.epms.eppcs.service.IEppcsDetectionResultService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 检测结果
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/epms/eppcs/detectionResult")
public class EppcsDetectionResultController extends BaseController {

    private final IEppcsDetectionResultService eppcsDetectionResultService;

    /**
     * 查询检测结果列表
     */
    @SaCheckPermission("eppcs:detectionResult:list")
    @GetMapping("/list")
    public TableDataInfo<EppcsDetectionResultVo> list(EppcsDetectionResultBo bo, PageQuery pageQuery) {
        return eppcsDetectionResultService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出检测结果列表
     */
    @SaCheckPermission("eppcs:detectionResult:export")
    @Log(title = "检测结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EppcsDetectionResultBo bo, HttpServletResponse response) {
        List<EppcsDetectionResultVo> list = eppcsDetectionResultService.queryList(bo);
        ExcelUtil.exportExcel(list, "检测结果", EppcsDetectionResultVo.class, response);
    }

    /**
     * 获取检测结果详细信息
     *
     * @param resultId 主键
     */
    @SaCheckPermission("eppcs:detectionResult:query")
    @GetMapping("/{resultId}")
    public R<EppcsDetectionResultVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long resultId) {
        return R.ok(eppcsDetectionResultService.queryById(resultId));
    }

    /**
     * 新增检测结果
     */
    @SaCheckPermission("eppcs:detectionResult:add")
    @Log(title = "检测结果", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    @AttachUpload(
        sourceIdExpression = "#bo.resultId",
        attachIdExpression = {"#bo.reportFile"}
    )
    public R<Long> add(@Validated(AddGroup.class) @RequestBody EppcsDetectionResultBo bo) {
        boolean result = eppcsDetectionResultService.insertByBo(bo);
        if (result) {
            return R.ok("新增成功", bo.getResultId());
        } else {
            return R.fail("新增失败");
        }
    }

    /**
     * 修改检测结果
     */
    @SaCheckPermission("eppcs:detectionResult:edit")
    @Log(title = "检测结果", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EppcsDetectionResultBo bo) {
        return toAjax(eppcsDetectionResultService.updateByBo(bo));
    }

    /**
     * 删除检测结果
     *
     * @param resultIds 主键串
     */
    @SaCheckPermission("eppcs:detectionResult:remove")
    @Log(title = "检测结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/{resultIds}")
    @AttachDelete(
        sourceIdExpression = "#resultIds"
    )
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] resultIds) {
        return toAjax(eppcsDetectionResultService.deleteWithValidByIds(List.of(resultIds), true));
    }

    /**
     * 提交检测结果
     *
     * @param resultId 检测结果ID
     * @param userType 用户类型
     */
    @SaCheckPermission("eppcs:detectionResult:submit")
    @Log(title = "提交检测结果", businessType = BusinessType.UPDATE)
    @PostMapping("/submit/{resultId}")
    public R<Void> submitDetectionResult(@NotNull(message = "检测结果ID不能为空") @PathVariable Long resultId,
                                        @RequestParam(defaultValue = "1") Integer userType) {
        return toAjax(eppcsDetectionResultService.submitDetectionResult(resultId, userType));
    }

    /**
     * 属地确认检测结果
     *
     * @param resultId 检测结果ID
     * @param userType 用户类型
     * @param action 动作 (1达标确认, 2不达标报告)
     * @param approvalRemark 审批意见
     * @param approver 审批人
     */
    @SaCheckPermission("eppcs:detectionResult:localConfirm")
    @Log(title = "属地确认检测结果", businessType = BusinessType.UPDATE)
    @PostMapping("/localConfirm/{resultId}")
    public R<Void> localConfirmDetectionResult(@NotNull(message = "检测结果ID不能为空") @PathVariable Long resultId,
                                              @RequestParam Integer userType,
                                              @RequestParam Integer action,
                                              @RequestParam(required = false) String approvalRemark) {
        return toAjax(eppcsDetectionResultService.localConfirmDetectionResult(
            resultId, userType, action, approvalRemark, LoginHelper.getLoginUser().getNickname()));
    }

    /**
     * 管理中心确认检测结果
     *
     * @param resultId 检测结果ID
     * @param userType 用户类型
     * @param action 动作 (1结束, 2驳回)
     * @param approvalRemark 审批意见
     */
    @SaCheckPermission("eppcs:detectionResult:centerConfirm")
    @Log(title = "管理中心确认检测结果", businessType = BusinessType.UPDATE)
    @PostMapping("/centerConfirm/{resultId}")
    public R<Void> centerConfirmDetectionResult(@NotNull(message = "检测结果ID不能为空") @PathVariable Long resultId,
                                               @RequestParam Integer userType,
                                               @RequestParam Integer action,
                                               @RequestParam(required = false) String approvalRemark) {
        return toAjax(eppcsDetectionResultService.centerConfirmDetectionResult(
            resultId, userType, action, approvalRemark, LoginHelper.getLoginUser().getNickname()));
    }
}
