package com.biz.epms.epfy.common.enums;

import lombok.Getter;

/**
 * 卸车地点类型，1作业区，2集中站
 * 根据初次拉运的卸车地点类型确定，针对泥浆模块，随钻处理始终为 1，集中站处理始终为 2
 * <AUTHOR>
 */
@Getter
public enum EpfyUnloadLocationTypeEnum {

    /**
     * 作业区 (废液、泥浆随钻)
     */
    ZUO_YE_QU(1, "作业区"),

    /**
     * 集中站 (泥浆集中站)
     */
    JI_ZHONG_ZHAN(2, "集中站"),

    ;


    private final int value;
    private final String name;

    EpfyUnloadLocationTypeEnum(int code, String name) {
        this.value = code;
        this.name = name;
    }
}
