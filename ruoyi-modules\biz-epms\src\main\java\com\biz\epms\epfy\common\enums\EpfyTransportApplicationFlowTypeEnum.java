package com.biz.epms.epfy.common.enums;

import lombok.Getter;

/**
 * 拉运申请，流程类型
 * <AUTHOR>
 */
@Getter
public enum EpfyTransportApplicationFlowTypeEnum {

    /**
     * 废液
     */
    FEI_YE(1, "废液"),

    /**
     * 泥浆
     */
    NI_JIANG(2, "泥浆"),

    ;


    private final int value;
    private final String name;

    EpfyTransportApplicationFlowTypeEnum(int code, String name) {
        this.value = code;
        this.name = name;
    }
}
