<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="时间">
              <el-date-picker
                v-model="queryParams.queryStartTime"
                type="date"
                value-format="YYYY-MM-DD"
                :clearable="false"
                class="searchDate"
                placeholder="开始日期"
              >
              </el-date-picker>
              至
              <el-date-picker
                v-model="queryParams.queryEndTime"
                type="date"
                value-format="YYYY-MM-DD"
                :clearable="false"
                class="searchDate"
                placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="分类" prop="mediumCategory">
              <el-select v-model="queryParams.mediumCategory" clearable placeholder="请选择分类">
                <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <!--            <el-form-item label="介质类型" prop="mediumType">-->
            <!--              <el-select v-model="queryParams.mediumType" placeholder="请选择介质类型">-->
            <!--                <el-option v-for="dict in epfy_medium_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <!--            <el-form-item label="处理类型" prop="mediumCategory">-->
            <!--              <el-select v-model="queryParams.unloadLocationType" placeholder="请选择处理类型" disabled >-->
            <!--                <el-option v-for="dict in epnj_handling_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="井名称" prop="wellName">
              <el-input v-model="queryParams.departurePoint" clearable placeholder="请输入井名称" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table
        ref="reportTable"
        v-loading="loading"
        :data="transportRecordList"
        :show-summary="true"
        :span-method="handleSpan"
        :summary-method="customSummary"
        border
        stripe
      >
        <el-table-column label="日期" align="center" prop="transportTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.transportTime, '{y}-{m}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="作业区" align="center" prop="workArea">
          <template #default="scope">
            {{ toWarkAreaName[scope.row.workArea] || '未知' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="井" prop="wellName" />
        <el-table-column label="分类" align="center" prop="mediumCategory">
          <template #default="scope">
            <dict-tag :options="epfy_medium_category" :value="scope.row.mediumCategory" />
          </template>
        </el-table-column>
        <el-table-column label="介质类型" align="center" prop="mediumType">
          <template #default="scope">
            <dict-tag :options="epfy_medium_type" :value="scope.row.mediumType" />
          </template>
        </el-table-column>
        <el-table-column label="数量（方）" align="center" prop="number">
          <template #default="scope">
            <span>{{ scope.row.number }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="TransportRecord" lang="ts">
import { addTransportRecord, delTransportRecord, getTransportRecord, monthStat, updateTransportRecord } from '@/api/epms/epfy/transportRecord';
import { TransportRecordForm, TransportRecordQuery, TransportRecordVO } from '@/api/epms/epfy/transportRecord/types';
import { listAllTransportApplication } from '@/api/epms/epfy/transportApplication';
import { TransportApplicationVO } from '@/api/epms/epfy/transportApplication/types';
import * as XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style-vite';
import { saveAs } from 'file-saver';
import { ref } from 'vue';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import moment from 'dayjs';

const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epfy_medium_category, epfy_medium_type, epnj_handling_type } = toRefs<any>(
  proxy?.useDict('epfy_medium_category', 'epfy_medium_type', 'epnj_handling_type')
);
const currentMonth = `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, '0')}`;
const transportRecordList = ref<TransportRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const reportTable = ref(null);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const applicationList = ref<TransportApplicationVO[]>([]);
const queryFormRef = ref<ElFormInstance>();
const transportRecordFormRef = ref<ElFormInstance>();
const workAreaList = ref<OperationAreaVO[]>([]);
const areaQuery = ref<OperationAreaQuery>({
  operationAreaType: 0
});
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: TransportRecordForm = {
  transportId: undefined,
  applicationId: undefined,
  transportTime: undefined,
  mediumCategory: undefined,
  mediumType: undefined,
  departurePoint: undefined,
  number: undefined,
  sender: undefined,
  transporter: undefined,
  licensePlate: undefined,
  arrivalPoint: undefined,
  receiver: undefined,
  remark: undefined,
  measurementVoucher: undefined,
  photo: undefined
};
const data = reactive<PageData<TransportRecordForm, TransportRecordQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: -1,
    // pageSize: 10,
    applicationId: undefined,
    mediumCategory: undefined,
    mediumType: undefined,
    departurePoint: undefined,
    number: undefined,
    sender: undefined,
    transporter: undefined,
    licensePlate: undefined,
    arrivalPoint: undefined,
    receiver: undefined,
    measurementVoucher: undefined,
    photo: undefined,
    unloadLocationType: 1, //集中处理
    flowType: 2,
    queryStartTime: moment().subtract(1, 'months').format('YYYY-MM-DD'),
    queryEndTime: moment().format('YYYY-MM-DD'),
    params: {}
  },
  rules: {
    transportId: [{ required: true, message: '拉运ID不能为空', trigger: 'blur' }],
    applicationId: [{ required: true, message: '拉运申请不能为空', trigger: 'change' }],
    transportTime: [{ required: true, message: '拉运时间不能为空', trigger: 'blur' }],
    mediumCategory: [{ required: true, message: '分类不能为空', trigger: 'change' }],
    mediumType: [{ required: true, message: '介质类型不能为空', trigger: 'change' }],
    departurePoint: [{ required: true, message: '起运点不能为空', trigger: 'blur' }],
    number: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
    sender: [{ required: true, message: '发送人不能为空', trigger: 'blur' }],
    transporter: [{ required: true, message: '拉运人不能为空', trigger: 'blur' }],
    licensePlate: [{ required: true, message: '车牌号不能为空', trigger: 'blur' }],
    arrivalPoint: [{ required: true, message: '接收点不能为空', trigger: 'blur' }],
    receiver: [{ required: true, message: '接收人不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const getList = async () => {
  loading.value = true;
  const res = await monthStat(queryParams.value);
  const rawData = res.rows.map((item) => ({
    ...item,
    transportTime: item.transportTime?.replace(/(\d{4}-\d{2})-\d{2}/, '$1') || ''
  }));
  transportRecordList.value = rawData; // 预处理数据
  total.value = res.total;
  loading.value = false;
};

// 判断两行是否属于同一组
const isSameGroup = (a: any, b: any) => {
  return (
    a.transportTime === b.transportTime &&
    a.workArea === b.workArea &&
    a.wellName === b.wellName &&
    a.mediumCategory === b.mediumCategory &&
    a.mediumType === b.mediumType
  );
};
/**
 * 获取全部取水区块列表
 */
const getApplicationList = () => {
  applicationList.value = [];
  listAllTransportApplication().then((res) => {
    applicationList.value = res.rows;
  });
};
const formatAmount = (row, column, value) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00';
  }
  const num = Number(value);
  return num.toFixed(2); // 保留两位小数
};
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

const getWorkAreaList = async () => {
  const res = await listOperationArea(areaQuery.value);
  workAreaList.value = res.rows;
};

// 自定义合计方法
const customSummary = ({ columns, data }) => {
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (column.property === 'number') {
      const sum = data.reduce((prev, curr) => {
        const value = Number(curr[column.property]);
        return prev + (isNaN(value) ? 0 : value);
      }, 0);
      sums[index] = Number(sum.toFixed(2));
    } else {
      sums[index] = '';
    }
  });
  return sums;
};

const handleSpan = ({ row, column, rowIndex, columnIndex }) => {
  const mergeColumns = ['workArea', 'wellName', 'mediumCategory', 'mediumType', 'transportTime'];
  const property = column.property;
  if (mergeColumns.includes(property)) {
    // 获取当前行数据
    const currentValue = row[property];
    // 查找合并起始行
    let startRow = rowIndex;
    while (startRow > 0 && transportRecordList.value[startRow - 1][property] === currentValue) {
      startRow--;
    }
    // 仅当是连续区间的第一行时计算合并行数
    if (startRow === rowIndex) {
      let mergeRows = 1;
      while (rowIndex + mergeRows < transportRecordList.value.length && transportRecordList.value[rowIndex + mergeRows][property] === currentValue) {
        mergeRows++;
      }
      return { rowspan: mergeRows, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
  return { rowspan: 1, colspan: 1 };
};
/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  transportRecordFormRef.value?.resetFields();
};

const toWarkAreaName = computed(() => {
  return workAreaList.value.reduce((map, item) => {
    map[item.operationAreaId] = item.operationAreaName;
    return map;
  }, {});
});
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加拉运记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TransportRecordVO) => {
  reset();
  const _transportId = row?.transportId || ids.value[0];
  const res = await getTransportRecord(_transportId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改拉运记录';
};

const handlePreview = async (row?: TransportRecordVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.transportId,
    attachSourceType: 'transportRecord',
    attachCategory: 'transportRecord'
  });
};

/** 提交按钮 */
const submitForm = () => {
  transportRecordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.transportId) {
        await updateTransportRecord(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTransportRecord(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: TransportRecordVO) => {
  const _transportIds = row?.transportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除拉运记录编号为"' + _transportIds + '"的数据项？').finally(() => (loading.value = false));
  await delTransportRecord(_transportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  try {
    // 使用 ref 值需要加上 .value
    const $e = reportTable.value?.$el;
    let $table = $e.querySelector('.el-table__fixed');
    if (!$table) {
      $table = $e;
    }

    const wb = XLSX.utils.table_to_book($table, { raw: true });
    const ws = wb.Sheets[wb.SheetNames[0]];
    // 1. 设置列宽自适应
    const colWidths = [];
    // 遍历所有单元格计算最大列宽
    const range = XLSX.utils.decode_range(ws['!ref']);
    for (let col = range.s.c; col <= range.e.c; col++) {
      let maxWidth = 0;
      for (let row = range.s.r; row <= range.e.r; row++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = ws[cellAddress];
        if (cell && cell.v) {
          const cellText = String(cell.v);
          // 粗略计算宽度：中文占2字符，英文占1字符
          const width = cellText.split('').reduce((acc, char) => acc + (char.charCodeAt(0) > 255 ? 2 : 1), 0);
          if (width > maxWidth) maxWidth = width;
        }
      }
      // 设置列宽（加缓冲值）
      colWidths.push({ wch: Math.min(maxWidth + 2, 60) }); // 限制最大宽度
    }
    ws['!cols'] = colWidths;

    // 2. 设置单元格样式（居中+边框）
    const style = {
      alignment: {
        horizontal: 'center',
        vertical: 'center',
        wrapText: true // 增加自动换行
      },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      },
      font: {
        // 必须包含基础字体设置
        sz: 11,
        name: '宋体',
        color: { rgb: '000000' }
      }
    };

    // 遍历所有单元格应用样式
    Object.keys(ws).forEach((cellAddress) => {
      if (!cellAddress.startsWith('!')) {
        const cell = ws[cellAddress];
        // 保留原始单元格样式（如果有）
        cell.s = cell.s ? { ...cell.s, ...style } : { ...style };
        // 处理数字格式（保留两位小数）
        if (typeof cell.v === 'number') {
          cell.z = cell.z || '0.00';
        }
      }
    });
    if (ws['!merges']) {
      ws['!merges'].forEach((merge) => {
        for (let r = merge.s.r; r <= merge.e.r; r++) {
          for (let c = merge.s.c; c <= merge.e.c; c++) {
            const cellAddr = XLSX.utils.encode_cell({ r, c });
            if (!ws[cellAddr]) ws[cellAddr] = { t: 's', v: '' }; // 填充空单元格
            ws[cellAddr].s = { ...style };
          }
        }
      });
    }
    const wbout = XLSXStyle.write(wb, {
      bookType: 'xlsx',
      bookSST: false,
      type: 'binary',
      cellStyles: true // 必须启用样式支持
    });
    saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), `拉运台账_${new Date().getTime()}.xlsx`);
  } catch (e) {
    if (typeof console !== 'undefined') console.error(e);
  }
};

const s2ab = (s) => {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xff;
  return buf;
};
onMounted(() => {
  getList();
  getApplicationList();
  getWorkAreaList();
});
</script>
