import{d as A,r as b,b as oe,g as fe,c as F,o as f,q as S,e as t,Q as se,bz as E,h as K,ak as ne,p as e,aA as re,a8 as z,t as a,w as J,x as h,y as R,v as ie,ay as ce,ax as be,F as W,C as G,aw as ge,J as k,z as _e,aJ as ye,cl as we,K as Q,X as ae,a1 as he,ai as te,n as le,a7 as ke,M as De,A as Ve,B as Te,D as $e,G as Ce}from"./index-D07cMzhp.js";import{E as Le}from"./el-date-picker-HyhB9X9n.js";import{g as Ee,u as Fe,a as xe}from"./index-Hk09RqHx.js";import{s as Ie}from"./index-3RY37FEX.js";import{_ as Re}from"./submitVerify.vue_vue_type_script_setup_true_lang-Q_6E6W-r.js";import{E as Se,a as Be}from"./el-tab-pane-B0KEvacl.js";import{f as Ne}from"./index-BNvpwQ8u.js";import{l as Ae}from"./index-Bd4DOhoC.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";/* empty css                          */import"./index.vue_vue_type_script_setup_true_lang-CzmK4ti3.js";import"./el-row-CikYE3zA.js";import"./el-col-BaG5Rg5z.js";import"./el-tree-DW6MoFaI.js";import"./index-BdvXA74M.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";const Ue={class:"iframe-wrapper",style:{height:"68vh"}},qe=["src"],Oe=A({__name:"flowChart",props:{insId:{type:[String,Number],default:null}},setup(x){const p=x,o=b(""),n="/prod-api";return oe(async()=>{const r=n+`/warm-flow-ui/index.html?id=${p.insId}&type=FlowChart`;o.value=r+"&Authorization=Bearer "+fe()+"&clientid=d03a2cc250450eba90621e43da78686d"}),(r,m)=>(f(),F("div",null,[S("div",Ue,[S("iframe",{src:t(o),class:"custom-iframe",frameborder:"0",scrolling:"no",style:{width:"100%",height:"100%"}},null,8,qe)])]))}}),Pe=se(Oe,[["__scopeId","data-v-5c101660"]]),Me={class:"container"},ze=A({__name:"approvalRecord",props:{width:E.string.def("80%"),height:E.string.def("100%")},setup(x,{expose:p}){const{proxy:o}=K(),{wf_task_status:n}=ne(o==null?void 0:o.useDict("wf_task_status")),r=x,m=b(!1),T=b(!1),D=b([]),v=b("image"),y=b(null),$=async c=>{T.value=!0,m.value=!0,v.value="image",D.value=[],Ne(c).then(u=>{u.data&&(D.value=u.data.list,y.value=u.data.instanceId,D.value.length>0&&D.value.forEach(V=>{V.ext?C(V.ext).then(g=>{V.attachmentList=g.data}):V.attachmentList=[]}),m.value=!1)})},C=async c=>await Ae(c),i=c=>{o==null||o.$download.oss(c)};return p({init:$}),(c,u)=>{const V=Be,g=be,B=ge,s=ye,U=Q,q=ce,H=we,Y=Se,j=re,O=ie;return f(),F("div",Me,[e(j,{modelValue:t(T),"onUpdate:modelValue":u[1]||(u[1]=_=>z(T)?T.value=_:null),draggable:"",title:"审批记录",width:r.width,height:r.height,"close-on-click-modal":!1},{default:a(()=>[e(Y,{modelValue:t(v),"onUpdate:modelValue":u[0]||(u[0]=_=>z(v)?v.value=_:null),class:"demo-tabs"},{default:a(()=>[J((f(),h(V,{label:"流程图",name:"image",style:{height:"68vh"}},{default:a(()=>[t(y)?(f(),h(Pe,{key:0,"ins-id":t(y)},null,8,["ins-id"])):R("",!0)]),_:1})),[[O,t(m)]]),J((f(),h(V,{label:"审批信息",name:"info"},{default:a(()=>[S("div",null,[e(q,{data:t(D),border:"",fit:"",style:{width:"100%"}},{default:a(()=>[e(g,{type:"index",label:"序号",align:"center",width:"60"}),e(g,{prop:"nodeName",label:"任务名称",sortable:"",align:"center"}),e(g,{prop:"approveName","show-overflow-tooltip":!0,label:"办理人",sortable:"",align:"center"},{default:a(_=>[_.row.approveName?(f(!0),F(W,{key:0},G(_.row.approveName.split(","),(N,P)=>(f(),h(B,{key:P,type:"success"},{default:a(()=>[k(_e(N),1)]),_:2},1024))),128)):(f(),h(B,{key:1,type:"success"},{default:a(()=>u[2]||(u[2]=[k("无")])),_:1}))]),_:1}),e(g,{prop:"flowStatus",label:"状态",width:"80",sortable:"",align:"center"},{default:a(_=>[e(s,{options:t(n),value:_.row.flowStatus},null,8,["options","value"])]),_:1}),e(g,{prop:"message",label:"审批意见","show-overflow-tooltip":!0,sortable:"",align:"center"}),e(g,{prop:"createTime",label:"开始时间",width:"160","show-overflow-tooltip":!0,sortable:"",align:"center"}),e(g,{prop:"updateTime",label:"结束时间",width:"160","show-overflow-tooltip":!0,sortable:"",align:"center"}),e(g,{prop:"runDuration",label:"运行时长",width:"140","show-overflow-tooltip":!0,sortable:"",align:"center"}),e(g,{prop:"attachmentList",width:"120",label:"附件",align:"center"},{default:a(_=>[_.row.attachmentList&&_.row.attachmentList.length>0?(f(),h(H,{key:0,placement:"right",width:310,trigger:"click"},{reference:a(()=>[e(U,{type:"primary",style:{"margin-right":"16px"}},{default:a(()=>u[3]||(u[3]=[k("附件")])),_:1})]),default:a(()=>[e(q,{data:_.row.attachmentList,border:""},{default:a(()=>[e(g,{prop:"originalName",width:"202","show-overflow-tooltip":!0,label:"附件名称"}),e(g,{prop:"name",width:"80",align:"center","show-overflow-tooltip":!0,label:"操作"},{default:a(N=>[e(U,{type:"text",onClick:P=>i(N.row.ossId)},{default:a(()=>u[4]||(u[4]=[k("下载")])),_:2},1032,["onClick"])]),_:1})]),_:2},1032,["data"])]),_:2},1024)):R("",!0)]),_:1})]),_:1},8,["data"])])]),_:1})),[[O,t(m)]])]),_:1},8,["modelValue"])]),_:1},8,["modelValue","width","height"])])}}}),He=se(ze,[["__scopeId","data-v-44bf2ede"]]),Ye={style:{display:"flex","justify-content":"space-between"}},je=A({__name:"approvalButton",props:{status:E.string.def(""),pageType:E.string.def(""),buttonLoading:E.bool.def(!1),id:E.string.def("")||E.number.def()},emits:["submitForm","approvalVerifyOpen","handleApprovalRecord"],setup(x,{emit:p}){const{proxy:o}=K(),n=x,r=p,m=async C=>{r("submitForm",C)},T=async()=>{r("approvalVerifyOpen")},D=()=>{r("handleApprovalRecord")},v=ae(()=>n.pageType==="add"||n.pageType==="update"&&n.status&&(n.status==="draft"||n.status==="cancel"||n.status==="back")),y=ae(()=>n.pageType==="approval"&&n.status&&n.status==="waiting"),$=()=>{o.$tab.closePage(o.$route),o.$router.go(-1)};return(C,i)=>{const c=Q;return f(),F("div",Ye,[S("div",null,[t(v)?(f(),h(c,{key:0,loading:n.buttonLoading,type:"info",onClick:i[0]||(i[0]=u=>m("draft"))},{default:a(()=>i[3]||(i[3]=[k("暂存")])),_:1},8,["loading"])):R("",!0),t(v)?(f(),h(c,{key:1,loading:n.buttonLoading,type:"primary",onClick:i[1]||(i[1]=u=>m("submit"))},{default:a(()=>i[4]||(i[4]=[k("提 交")])),_:1},8,["loading"])):R("",!0),t(y)?(f(),h(c,{key:2,loading:n.buttonLoading,type:"primary",onClick:T},{default:a(()=>i[5]||(i[5]=[k("审批")])),_:1},8,["loading"])):R("",!0),n.id&&n.status!=="draft"?(f(),h(c,{key:3,type:"primary",onClick:D},{default:a(()=>i[6]||(i[6]=[k("流程进度")])),_:1})):R("",!0),he(C.$slots,"default")]),S("div",null,[e(c,{style:{float:"right"},onClick:i[2]||(i[2]=u=>$())},{default:a(()=>i[7]||(i[7]=[k("返回")])),_:1})])])}}}),Je={class:"p-2"},We={class:"dialog-footer"},Ge=A({name:"Leave"}),ca=A({...Ge,setup(x){const{proxy:p}=K(),o=b(!1),n=b(!0),r=b([]),m=b({}),T=[{value:"1",label:"事假"},{value:"2",label:"调休"},{value:"3",label:"病假"},{value:"4",label:"婚假"}],D=[{value:"leave1",label:"请假申请-普通"},{value:"leave2",label:"请假申请-排他网关"},{value:"leave3",label:"请假申请-并行网关"},{value:"leave4",label:"请假申请-会签"},{value:"leave5",label:"请假申请-并行会签网关"},{value:"leave6",label:"请假申请-排他并行会签"}],v=b(""),y=te({visible:!1,title:"流程定义"}),$=b(),C=b();b();const i=b(),c=b({businessId:"",flowCode:"",variables:{}}),u=b({}),V={id:void 0,leaveType:void 0,startDate:void 0,endDate:void 0,leaveDays:void 0,remark:void 0,status:void 0},g=te({form:{...V},queryParams:{pageNum:1,pageSize:10,startLeaveDays:void 0,endLeaveDays:void 0},rules:{id:[{required:!0,message:"主键不能为空",trigger:"blur"}],leaveType:[{required:!0,message:"请假类型不能为空",trigger:"blur"}],leaveTime:[{required:!0,message:"请假时间不能为空",trigger:"blur"}],leaveDays:[{required:!0,message:"请假天数不能为空",trigger:"blur"}]}}),B=()=>{y.visible=!1,v.value="",o.value=!1},{form:s,rules:U}=ne(g),q=()=>{var w;s.value={...V},r.value=[],(w=i.value)==null||w.resetFields()},H=()=>{const w=new Date(r.value[0]).getTime(),I=new Date(r.value[1]).getTime()-w;s.value.leaveDays=Math.floor(I/(1e3*60*60*24))+1},Y=()=>{n.value=!0,o.value=!1,le(async()=>{const w=await Ee(m.value.id);Object.assign(s.value,w.data),r.value=[],r.value.push(s.value.startDate),r.value.push(s.value.endDate),n.value=!1,o.value=!1})},j=w=>{var l;if(r.value.length===0){p==null||p.$modal.msgError("请假时间不能为空");return}try{(l=i.value)==null||l.validate(async I=>{if(s.value.startDate=r.value[0],s.value.endDate=r.value[1],I){o.value=!0;let L;if(s.value.id?L=await Fe(s.value).finally(()=>o.value=!1):L=await xe(s.value).finally(()=>o.value=!1),s.value=L.data,w==="draft")o.value=!1,p==null||p.$modal.msgSuccess("暂存成功"),p.$tab.closePage(p.$route),p.$router.go(-1);else{if(s.value.status==="draft"&&(v.value===""||v.value===null)||m.value.type==="add"){v.value=D[0].value,y.visible=!0;return}(v.value===""||v.value===null)&&(v.value="xx"),await _(L.data)}}})}finally{o.value=!1}},O=async()=>{_(s.value),y.visible=!1},_=async w=>{try{c.value.flowCode=v.value,c.value.businessId=w.id,u.value={leaveDays:w.leaveDays,userList:["1","3","4"]},c.value.variables=u.value;const l=await Ie(c.value);$.value&&(o.value=!1,$.value.openDialog(l.data.taskId))}finally{o.value=!1}},N=()=>{C.value.init(s.value.id)},P=async()=>{await p.$tab.closePage(p.$route),p.$router.go(-1)},ue=async()=>{$.value.openDialog(m.value.taskId)};return oe(()=>{le(async()=>{m.value=p.$route.query,q(),n.value=!1,(m.value.type==="update"||m.value.type==="view"||m.value.type==="approval")&&Y()})}),(w,l)=>{const I=ke,L=$e,X=Te,M=Ve,de=Le,Z=Ce,pe=De,ee=Q,ve=re,me=ie;return f(),F("div",Je,[e(I,{shadow:"never"},{default:a(()=>[e(je,{id:t(s).id,onApprovalVerifyOpen:ue,onHandleApprovalRecord:N,buttonLoading:t(o),pageType:t(m).type,status:t(s).status,onSubmitForm:j},null,8,["id","buttonLoading","pageType","status"])]),_:1}),e(I,{shadow:"never",style:{height:"78vh","overflow-y":"auto"}},{default:a(()=>[J((f(),h(pe,{ref_key:"leaveFormRef",ref:i,disabled:t(m).type==="view",model:t(s),rules:t(U),"label-width":"80px"},{default:a(()=>[e(M,{label:"请假类型",prop:"leaveType"},{default:a(()=>[e(X,{modelValue:t(s).leaveType,"onUpdate:modelValue":l[0]||(l[0]=d=>t(s).leaveType=d),placeholder:"请选择请假类型",style:{width:"100%"}},{default:a(()=>[(f(),F(W,null,G(T,d=>e(L,{key:d.value,label:d.label,value:d.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(M,{label:"请假时间",required:""},{default:a(()=>[e(de,{modelValue:t(r),"onUpdate:modelValue":l[1]||(l[1]=d=>z(r)?r.value=d:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","range-separator":"To","start-placeholder":"开始时间","end-placeholder":"结束时间","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)],onChange:l[2]||(l[2]=d=>H())},null,8,["modelValue","default-time"])]),_:1}),e(M,{label:"请假天数",prop:"leaveDays"},{default:a(()=>[e(Z,{modelValue:t(s).leaveDays,"onUpdate:modelValue":l[3]||(l[3]=d=>t(s).leaveDays=d),disabled:"",type:"number",placeholder:"请输入请假天数"},null,8,["modelValue"])]),_:1}),e(M,{label:"请假原因",prop:"remark"},{default:a(()=>[e(Z,{modelValue:t(s).remark,"onUpdate:modelValue":l[4]||(l[4]=d=>t(s).remark=d),type:"textarea",rows:3,placeholder:"请输入请假原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["disabled","model","rules"])),[[me,t(n)]])]),_:1}),e(Re,{ref_key:"submitVerifyRef",ref:$,"task-variables":t(u),onSubmitCallback:P},null,8,["task-variables"]),e(He,{ref_key:"approvalRecordRef",ref:C},null,512),e(ve,{modelValue:t(y).visible,"onUpdate:modelValue":l[7]||(l[7]=d=>t(y).visible=d),title:t(y).title,"before-close":B,width:"500"},{footer:a(()=>[S("div",We,[e(ee,{onClick:B},{default:a(()=>l[8]||(l[8]=[k("取消")])),_:1}),e(ee,{type:"primary",onClick:l[6]||(l[6]=d=>O())},{default:a(()=>l[9]||(l[9]=[k(" 确认 ")])),_:1})])]),default:a(()=>[e(X,{modelValue:t(v),"onUpdate:modelValue":l[5]||(l[5]=d=>z(v)?v.value=d:null),placeholder:"Select",style:{width:"240px"}},{default:a(()=>[(f(),F(W,null,G(D,d=>e(L,{key:d.value,label:d.label,value:d.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","title"])])}}});export{ca as default};
