export interface StandardDocumentVO {
  /**
   * 规范文档id
   */
  documentId: string | number;

  /**
   * 文档名称
   */
  documentName: string;

  /**
   * 文档版本
   */
  version: string;

  /**
   * 文档类型
   */
  documentType: number;

  /**
   * 附件
   */
  file: string;

  /**
   * 上传时间
   */
  upload_time: Date;

  /**
   * 类别
   */
  category: number;
}

export interface StandardDocumentForm extends BaseEntity {
  /**
   * 规范文档id
   */
  documentId?: string | number;

  /**
   * 文档名称
   */
  documentName?: string;

  /**
   * 文档版本
   */
  version?: string;

  /**
   * 文档类型
   */
  documentType?: number;

  /**
   * 附件
   */
  file?: string;

  /**
   * 上传时间
   */
  upload_time?: Date;

  /**
   * 类别
   */
  category?: number;
}

export interface StandardDocumentQuery extends PageQuery {
  /**
   * 文档名称
   */
  documentName?: string;

  /**
   * 文档类型
   */
  documentType?: number;

  /**
   * 附件
   */
  file?: string;

  /**
   * 类别
   */
  category?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
