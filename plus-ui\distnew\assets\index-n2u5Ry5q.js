import{d as se,a as Ae,r as p,h as Pe,ak as X,bj as Ue,ai as U,b as Ee,aH as Re,c as ee,o as c,p as a,t as o,w as b,q as E,a7 as Fe,M as xe,e as l,A as je,G as $e,H as le,B as Le,F as We,C as qe,x as g,D as Te,a8 as ae,K as ze,J as m,am as te,aI as Be,ay as He,ax as Me,aJ as Oe,aL as Qe,y as W,v as Je,az as Ke,aA as Ye,bc as Ge,cA as Ze,aD as Xe,Q as el}from"./index-D07cMzhp.js";import{_ as ll}from"./index-DVHplxfU.js";import{E as al}from"./el-row-CikYE3zA.js";import{_ as tl}from"./index-BWMgqvQ9.js";import{E as ol}from"./el-date-picker-HyhB9X9n.js";import{l as nl,g as oe,u as ne,s as il,a as sl}from"./index-DjjjlEwF.js";import{f as ie}from"./types-DSjC7ANO.js";import{l as dl}from"./index-BhIIZXqy.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./dayjs.min-Brw96_N0.js";import"./el-tree-DW6MoFaI.js";import"./index-VIEDZI2D.js";const rl={class:"p-2"},ul={class:"mb-[10px]"},pl={class:"dialog-footer"},ml={class:"dialog-footer"},cl={style:{height:"68vh"}},vl=se({name:"wellSealDesign"}),gl=se({...vl,setup(fl){Ae();const R=p({}),{proxy:s}=Pe(),{epfj_approval_status:de}=X(s==null?void 0:s.useDict("epfj_approval_status")),q=p([]),T=p([]),w=p(!1),F=p(!0),I=p(!0),re=p([]),ue=p(!0),pe=p(!0),x=p(0),k=p(["",""]),z=p(),N=p(),B=Ue(),me=p({}),_=U({visible:!1,title:""}),y=U({visible:!1,title:""}),H=U({visible:!1,url:""}),M={sealId:void 0,wellId:void 0,wellName:void 0,applicationId:void 0,applicationName:void 0,scrapId:void 0,scrapName:void 0,qukuaiId:void 0,qukuaiName:void 0,workAreaId:void 0,workAreaName:void 0,environmentalLicense:void 0,hsePlan:void 0,wellHandoverReport:void 0,emergencyPlan:void 0,shiftMeetingRecords:void 0,siteAcceptanceReport:void 0,dailyReport:void 0,supervisionRecord:void 0,workSummary:void 0,wellSealDescription:void 0,geologicalDesign:void 0,engineeringDesign:void 0,constructionPlanDesign:void 0,approvalStatus:void 0,approvalOpinions:void 0},ce=U({form:{...M},queryParams:{pageNum:1,pageSize:10,workAreaId:void 0,applicationName:void 0,wellName:void 0,params:{}},rules:{wellId:[{required:!0,message:"井不能为空",trigger:"blur"}]}}),{queryParams:d,form:n,rules:O}=X(ce),S=async()=>{F.value=!0,d.value.params={},s==null||s.addDateRange(d.value,k.value,"ApplicationDate");const i=await nl(d.value);T.value=i.rows,x.value=i.total,F.value=!1},Q=()=>{j(),_.visible=!1,y.visible=!1},ve=i=>(d.value.pageNum-1)*d.value.pageSize+i+1,ge=async i=>{j();const e=i==null?void 0:i.sealId,v=await oe(e);Object.assign(n.value,v.data),_.visible=!0,_.title="地质方案设计"},fe=async i=>{j();const e=i==null?void 0:i.sealId,v=await oe(e);Object.assign(n.value,v.data),y.visible=!0,y.title="工程方案设计"},J=()=>{var i;(i=N.value)==null||i.validate(async e=>{e&&(w.value=!0,n.value.sealId?(await ne(n.value).finally(()=>w.value=!1),await il(n.value.sealId,n.value.approvalStatus,0)):s==null||s.$modal.msgError("数据异常"),s==null||s.$modal.msgSuccess("操作成功"),_.visible=!1,y.visible=!1,await S())})},C=async(i,e)=>{i&&s.showAttachPreview({attachSourceId:i,attachSourceType:"wasteWellSealProcess",attachCategory:e})},we=async()=>{R.value.operationAreaParentId=0,R.value.operationAreaType=0;const i=await dl(R.value);q.value=i.rows},j=()=>{var i;n.value={...M},(i=N.value)==null||i.resetFields()},A=()=>{d.value.pageNum=1,S()},be=()=>{var i;k.value=["",""],(i=z.value)==null||i.resetFields(),A()},_e=i=>{re.value=i.map(e=>e.wellId),ue.value=i.length!=1,pe.value=!i.length},K=()=>{var i;(i=N.value)==null||i.validate(async e=>{e&&(w.value=!0,n.value.sealId?await ne(n.value).finally(()=>w.value=!1):await sl(n.value).finally(()=>w.value=!1),s==null||s.$modal.msgSuccess("操作成功"),_.visible=!1,y.visible=!1,await S())})};return Ee(()=>{we(),S()}),(i,e)=>{var G,Z;const v=$e,r=je,ye=Te,Se=Le,De=ol,u=ze,$=xe,Y=Fe,Ve=tl,ke=al,f=Me,Ne=Oe,D=Qe,he=He,Ie=Ke,P=ll,L=Ye,V=Re("hasPermi"),Ce=Je;return c(),ee("div",rl,[a(Be,{"enter-active-class":(G=l(s))==null?void 0:G.animate.searchAnimate.enter,"leave-active-class":(Z=l(s))==null?void 0:Z.animate.searchAnimate.leave},{default:o(()=>[b(E("div",ul,[a(Y,{shadow:"hover"},{default:o(()=>[a($,{ref_key:"queryFormRef",ref:z,model:l(d),inline:!0,"label-width":"100px"},{default:o(()=>[a(r,{label:"封井申请名称",prop:"applicationName"},{default:o(()=>[a(v,{modelValue:l(d).applicationName,"onUpdate:modelValue":e[0]||(e[0]=t=>l(d).applicationName=t),placeholder:"请输入封井申请名称",clearable:"",onKeyup:le(A,["enter"])},null,8,["modelValue"])]),_:1}),a(r,{label:"井名称",prop:"wellName"},{default:o(()=>[a(v,{modelValue:l(d).wellName,"onUpdate:modelValue":e[1]||(e[1]=t=>l(d).wellName=t),placeholder:"请输入井名称",clearable:"",onKeyup:le(A,["enter"])},null,8,["modelValue"])]),_:1}),a(r,{label:"所属地",prop:"workAreaId"},{default:o(()=>[a(Se,{modelValue:l(d).workAreaId,"onUpdate:modelValue":e[2]||(e[2]=t=>l(d).workAreaId=t),class:"searchDate",clearable:"",filterable:"",placeholder:"选择所属地"},{default:o(()=>[(c(!0),ee(We,null,qe(l(q),t=>(c(),g(ye,{key:t.operationAreaId,label:t.operationAreaName,value:t.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"封井申请时间",style:{width:"400px"}},{default:o(()=>[a(De,{modelValue:l(k),"onUpdate:modelValue":e[3]||(e[3]=t=>ae(k)?k.value=t:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]},null,8,["modelValue","default-time"])]),_:1}),a(r,null,{default:o(()=>[a(u,{type:"primary",icon:"Search",onClick:A},{default:o(()=>e[20]||(e[20]=[m("搜索")])),_:1}),a(u,{icon:"Refresh",onClick:be},{default:o(()=>e[21]||(e[21]=[m("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[te,l(I)]])]),_:1},8,["enter-active-class","leave-active-class"]),a(Y,{shadow:"never"},{header:o(()=>[a(ke,{gutter:10,class:"mb8"},{default:o(()=>[a(Ve,{showSearch:l(I),"onUpdate:showSearch":e[4]||(e[4]=t=>ae(I)?I.value=t:null),onQueryTable:S},null,8,["showSearch"])]),_:1})]),default:o(()=>[b((c(),g(he,{data:l(T),onSelectionChange:_e},{default:o(()=>[a(f,{type:"selection",width:"55",align:"center"}),a(f,{label:"序号",align:"center",type:"index",width:"55",index:ve}),a(f,{align:"center",label:"井名称",prop:"wellName"}),a(f,{align:"center",label:"所属地",prop:"workAreaName"}),a(f,{align:"center",label:"资产报废名称",prop:"scrapName"}),a(f,{align:"center",label:"封井申请名称",prop:"applicationName"}),a(f,{label:"审批状态",align:"center",prop:"approvalStatus"},{default:o(t=>[a(Ne,{options:l(de),value:t.row.approvalStatus},null,8,["options","value"])]),_:1}),a(f,{label:"地质方案设计文件",align:"center","class-name":"small-padding fixed-width",fixed:"right","min-width":"130"},{default:o(t=>[a(D,{content:"查看封井情况说明",placement:"top"},{default:o(()=>[b((c(),g(u,{disabled:!t.row.wellSealDescription,icon:"Document",link:"",type:"primary",onClick:h=>C(t.row.sealId,"wellSealDescription")},{default:o(()=>e[22]||(e[22]=[m("封井情况说明")])),_:2},1032,["disabled","onClick"])),[[V,["epfj:wellSeal:preview"]]])]),_:2},1024),a(D,{content:"查看封井地质设计",placement:"top"},{default:o(()=>[b((c(),g(u,{disabled:!t.row.geologicalDesign,icon:"Document",link:"",type:"primary",onClick:h=>C(t.row.sealId,"geologicalDesign")},{default:o(()=>e[23]||(e[23]=[m("封井地质设计")])),_:2},1032,["disabled","onClick"])),[[V,["epfj:wellSeal:preview"]]])]),_:2},1024)]),_:1}),a(f,{label:"工程方案设计文件",align:"center","class-name":"small-padding fixed-width",fixed:"right","min-width":"140"},{default:o(t=>[a(D,{content:"查看封井工程设计",placement:"top"},{default:o(()=>[b((c(),g(u,{disabled:!t.row.engineeringDesign,icon:"Document",link:"",type:"primary",onClick:h=>C(t.row.sealId,"engineeringDesign")},{default:o(()=>e[24]||(e[24]=[m("封井工程设计")])),_:2},1032,["disabled","onClick"])),[[V,["epfj:wellSeal:preview"]]])]),_:2},1024),a(D,{content:"查看措施作业施工设计",placement:"top"},{default:o(()=>[b((c(),g(u,{disabled:!t.row.constructionPlanDesign,icon:"Document",link:"",type:"primary",onClick:h=>C(t.row.sealId,"constructionPlanDesign")},{default:o(()=>e[25]||(e[25]=[m("措施作业施工设计")])),_:2},1032,["disabled","onClick"])),[[V,["epfj:wellSeal:preview"]]])]),_:2},1024)]),_:1}),a(f,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作","min-width":"90"},{default:o(t=>[t.row.approvalStatus==l(ie).diZhiFangAnSheJi.value?(c(),g(D,{key:0,content:"地质方案设计",placement:"top"},{default:o(()=>[b((c(),g(u,{icon:"Edit",link:"",type:"primary",onClick:h=>ge(t.row)},{default:o(()=>e[26]||(e[26]=[m("地质方案设计")])),_:2},1032,["onClick"])),[[V,["epfj:wellSealDesign:dizhi"]]])]),_:2},1024)):W("",!0),t.row.approvalStatus==l(ie).gongChengFangAnSheJi.value?(c(),g(D,{key:1,content:"工程方案设计",placement:"top"},{default:o(()=>[b((c(),g(u,{icon:"Edit",link:"",type:"primary",onClick:h=>fe(t.row)},{default:o(()=>e[27]||(e[27]=[m("工程方案设计")])),_:2},1032,["onClick"])),[[V,["epfj:wellSealDesign:gongcheng"]]])]),_:2},1024)):W("",!0)]),_:1})]),_:1},8,["data"])),[[Ce,l(F)]]),b(a(Ie,{total:l(x),page:l(d).pageNum,"onUpdate:page":e[5]||(e[5]=t=>l(d).pageNum=t),limit:l(d).pageSize,"onUpdate:limit":e[6]||(e[6]=t=>l(d).pageSize=t),onPagination:S},null,8,["total","page","limit"]),[[te,l(x)>0]])]),_:1}),a(L,{title:l(_).title,modelValue:l(_).visible,"onUpdate:modelValue":e[12]||(e[12]=t=>l(_).visible=t),width:"550px","append-to-body":""},{footer:o(()=>[E("div",pl,[a(u,{loading:l(w),type:"warning",onClick:J},{default:o(()=>e[28]||(e[28]=[m("提 交")])),_:1},8,["loading"]),a(u,{loading:l(w),type:"primary",onClick:K},{default:o(()=>e[29]||(e[29]=[m("保 存")])),_:1},8,["loading"]),a(u,{onClick:Q},{default:o(()=>e[30]||(e[30]=[m("取 消")])),_:1})])]),default:o(()=>[a($,{ref_key:"wellSealFormRef",ref:N,model:l(n),rules:l(O),"label-width":"115px"},{default:o(()=>[a(r,{label:"封井名称",prop:"wellName"},{default:o(()=>[a(v,{modelValue:l(n).wellName,"onUpdate:modelValue":e[7]||(e[7]=t=>l(n).wellName=t),placeholder:"请输入封井名称",disabled:""},null,8,["modelValue"])]),_:1}),a(r,{label:"资产报废名称",prop:"scrapName"},{default:o(()=>[a(v,{modelValue:l(n).scrapName,"onUpdate:modelValue":e[8]||(e[8]=t=>l(n).scrapName=t),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),a(r,{label:"封井申请名称",prop:"applicationName"},{default:o(()=>[a(v,{modelValue:l(n).applicationName,"onUpdate:modelValue":e[9]||(e[9]=t=>l(n).applicationName=t),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),a(r,{label:"封井情况说明",prop:"wellSealDescription"},{default:o(()=>[a(P,{modelValue:l(n).wellSealDescription,"onUpdate:modelValue":e[10]||(e[10]=t=>l(n).wellSealDescription=t),"attach-source-id":l(n).sealId,disabled:!1,"attach-category":"wellSealDescription","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1}),a(r,{label:"封井地质设计",prop:"geologicalDesign"},{default:o(()=>[a(P,{modelValue:l(n).geologicalDesign,"onUpdate:modelValue":e[11]||(e[11]=t=>l(n).geologicalDesign=t),"attach-source-id":l(n).sealId,disabled:!1,"attach-category":"geologicalDesign","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),a(L,{title:l(y).title,modelValue:l(y).visible,"onUpdate:modelValue":e[18]||(e[18]=t=>l(y).visible=t),width:"550px","append-to-body":""},{footer:o(()=>[E("div",ml,[a(u,{loading:l(w),type:"warning",onClick:J},{default:o(()=>e[31]||(e[31]=[m("提 交")])),_:1},8,["loading"]),a(u,{loading:l(w),type:"primary",onClick:K},{default:o(()=>e[32]||(e[32]=[m("保 存")])),_:1},8,["loading"]),a(u,{onClick:Q},{default:o(()=>e[33]||(e[33]=[m("取 消")])),_:1})])]),default:o(()=>[a($,{ref_key:"wellSealFormRef",ref:N,model:l(n),rules:l(O),"label-width":"115px"},{default:o(()=>[a(r,{label:"封井名称",prop:"wellName"},{default:o(()=>[a(v,{modelValue:l(n).wellName,"onUpdate:modelValue":e[13]||(e[13]=t=>l(n).wellName=t),placeholder:"请输入封井名称",disabled:""},null,8,["modelValue"])]),_:1}),a(r,{label:"资产报废名称",prop:"scrapName"},{default:o(()=>[a(v,{modelValue:l(n).scrapName,"onUpdate:modelValue":e[14]||(e[14]=t=>l(n).scrapName=t),placeholder:"请输入资产报废名称",disabled:""},null,8,["modelValue"])]),_:1}),a(r,{label:"封井申请名称"},{default:o(()=>[a(v,{modelValue:l(n).applicationName,"onUpdate:modelValue":e[15]||(e[15]=t=>l(n).applicationName=t),placeholder:"请输入申请名称",disabled:""},null,8,["modelValue"])]),_:1}),a(r,{label:"封井工程设计",prop:"engineeringDesign"},{default:o(()=>[a(P,{modelValue:l(n).engineeringDesign,"onUpdate:modelValue":e[16]||(e[16]=t=>l(n).engineeringDesign=t),"attach-source-id":l(n).sealId,disabled:!1,"attach-category":"engineeringDesign","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1}),a(r,{label:"措施作业施工设计",prop:"constructionPlanDesign"},{default:o(()=>[a(P,{modelValue:l(n).constructionPlanDesign,"onUpdate:modelValue":e[17]||(e[17]=t=>l(n).constructionPlanDesign=t),"attach-source-id":l(n).sealId,disabled:!1,"attach-category":"constructionPlanDesign","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),a(L,{title:"附件预览",modelValue:l(H).visible,"onUpdate:modelValue":e[19]||(e[19]=t=>l(H).visible=t),width:"80%",he:"","append-to-body":""},{default:o(()=>[E("div",cl,[B.value?(c(),g(Ge(B.value),Ze(Xe({key:0},l(me))),null,16)):W("",!0)])]),_:1},8,["modelValue"])])}}}),Ul=el(gl,[["__scopeId","data-v-b1a23beb"]]);export{Ul as default};
