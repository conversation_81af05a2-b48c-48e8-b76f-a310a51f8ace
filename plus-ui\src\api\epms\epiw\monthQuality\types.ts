export interface MonthQualityVO {
  /**
   * 监测报告id
   */
  qualityReportId: string | number;

  /**
   * 检测时间
   */
  detectionTime: string;

  /**
   * 片区id
   */
  operationAreaId: string | number;

  /**
   * 附件
   */
  file: string;
}

export interface MonthQualityForm extends BaseEntity {
  /**
   * 监测报告id
   */
  qualityReportId?: string | number;

  /**
   * 检测时间
   */
  detectionTime?: string;

  /**
   * 片区id
   */
  operationAreaId?: string | number;

  /**
   * 附件
   */
  file?: string;
}

export interface MonthQualityQuery extends PageQuery {
  /**
   * 检测时间
   */
  detectionTime?: string;

  /**
   * 附件
   */
  file?: string;

  /**
   * 片区id
   */
  operationAreaId?: string | number;
  /**
   * 日期范围参数
   */
  params?: any;
}
