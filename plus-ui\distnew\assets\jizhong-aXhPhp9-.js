import{d as U,a as oe,h as le,ak as L,r as d,ai as I,X as ne,b as se,c as R,o as k,p as a,t as i,w as B,q as C,a7 as ie,M as ue,e as u,A as me,J as q,B as ce,F as de,C as pe,x as $,D as ve,G as fe,K as ge,am as _e,aI as ye,ay as be,ax as he,z as E,aJ as we,v as Te,cE as qe}from"./index-D07cMzhp.js";import{E as ke}from"./el-date-picker-HyhB9X9n.js";import{m as De}from"./index-DAD2mDUi.js";import{e as Se}from"./index-M7LxucW_.js";import{u as D,X as Ae}from"./xlsx-BexUIDLF.js";import{l as Ce}from"./index-BhIIZXqy.js";import{m as z}from"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";const Ee={class:"p-2"},Ve={class:"mb-[10px]"},Ne=U({name:"TransportRecord"}),Xe=U({...Ne,setup(Ye){oe();const{proxy:b}=le(),{epfy_medium_category:V,epfy_medium_type:W,epnj_handling_type:xe}=L(b==null?void 0:b.useDict("epfy_medium_category","epfy_medium_type","epnj_handling_type"));`${new Date().getFullYear()}${String(new Date().getMonth()+1).padStart(2,"0")}`;const h=d([]);d(!1);const S=d(!0),X=d(!0);d([]);const N=d(null);d(!0),d(!0);const j=d(0),Y=d([]),x=d();d();const F=d([]),O=d({operationAreaType:0});I({visible:!1,title:""});const Q=I({form:{...{transportId:void 0,applicationId:void 0,transportTime:void 0,mediumCategory:void 0,mediumType:void 0,unloadLocationType:1,departurePoint:void 0,number:void 0,sender:void 0,transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,remark:void 0,measurementVoucher:void 0,photo:void 0}},queryParams:{pageNum:-1,applicationId:void 0,mediumCategory:void 0,mediumType:void 0,departurePoint:void 0,number:void 0,sender:void 0,transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,measurementVoucher:void 0,photo:void 0,unloadLocationType:2,flowType:2,queryStartTime:z().subtract(1,"months").format("YYYY-MM-DD"),queryEndTime:z().format("YYYY-MM-DD"),params:{}},rules:{transportId:[{required:!0,message:"拉运ID不能为空",trigger:"blur"}],applicationId:[{required:!0,message:"拉运申请不能为空",trigger:"change"}],transportTime:[{required:!0,message:"拉运时间不能为空",trigger:"blur"}],mediumCategory:[{required:!0,message:"分类不能为空",trigger:"change"}],mediumType:[{required:!0,message:"介质类型不能为空",trigger:"change"}],departurePoint:[{required:!0,message:"起运点不能为空",trigger:"blur"}],number:[{required:!0,message:"数量不能为空",trigger:"blur"}],sender:[{required:!0,message:"发送人不能为空",trigger:"blur"}],transporter:[{required:!0,message:"拉运人不能为空",trigger:"blur"}],licensePlate:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],arrivalPoint:[{required:!0,message:"接收点不能为空",trigger:"blur"}],receiver:[{required:!0,message:"接收人不能为空",trigger:"blur"}]}}),{queryParams:p,form:Me,rules:Pe}=L(Q),M=async()=>{S.value=!0;const t=await De(p.value),e=t.rows.map(o=>{var n;return{...o,transportTime:((n=o.transportTime)==null?void 0:n.replace(/(\d{4}-\d{2})-\d{2}/,"$1"))||""}});h.value=e,j.value=t.total,S.value=!1},J=()=>{Y.value=[],Se().then(t=>{Y.value=t.rows})},G=async()=>{const t=await Ce(O.value);F.value=t.rows},K=({columns:t,data:e})=>{const o=[];return t.forEach((n,s)=>{if(s===0){o[s]="合计";return}if(n.property==="number"){const v=e.reduce((f,m)=>{const g=Number(m[n.property]);return f+(isNaN(g)?0:g)},0);o[s]=Number(v.toFixed(2))}else o[s]=""}),o},H=({row:t,column:e,rowIndex:o,columnIndex:n})=>{const s=["workArea","wellName","mediumCategory","mediumType","transportTime"],v=e.property;if(s.includes(v)){const f=t[v];let m=o;for(;m>0&&h.value[m-1][v]===f;)m--;if(m===o){let g=1;for(;o+g<h.value.length&&h.value[o+g][v]===f;)g++;return{rowspan:g,colspan:1}}else return{rowspan:0,colspan:0}}return{rowspan:1,colspan:1}},Z=ne(()=>F.value.reduce((t,e)=>(t[e.operationAreaId]=e.operationAreaName,t),{})),P=()=>{p.value.pageNum=1,M()},ee=()=>{var t;(t=x.value)==null||t.resetFields(),P()},te=()=>{var t;try{const e=(t=N.value)==null?void 0:t.$el;let o=e.querySelector(".el-table__fixed");o||(o=e);const n=D.table_to_book(o,{raw:!0}),s=n.Sheets[n.SheetNames[0]],v=[],f=D.decode_range(s["!ref"]);for(let c=f.s.c;c<=f.e.c;c++){let l=0;for(let _=f.s.r;_<=f.e.r;_++){const y=D.encode_cell({r:_,c}),w=s[y];if(w&&w.v){const T=String(w.v).split("").reduce((r,ae)=>r+(ae.charCodeAt(0)>255?2:1),0);T>l&&(l=T)}}v.push({wch:Math.min(l+2,60)})}s["!cols"]=v;const m={alignment:{horizontal:"center",vertical:"center",wrapText:!0},border:{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},font:{sz:11,name:"宋体",color:{rgb:"000000"}}};Object.keys(s).forEach(c=>{if(!c.startsWith("!")){const l=s[c];l.s=l.s?{...l.s,...m}:{...m},typeof l.v=="number"&&(l.z=l.z||"0.00")}}),s["!merges"]&&s["!merges"].forEach(c=>{for(let l=c.s.r;l<=c.e.r;l++)for(let _=c.s.c;_<=c.e.c;_++){const y=D.encode_cell({r:l,c:_});s[y]||(s[y]={t:"s",v:""}),s[y].s={...m}}});const g=Ae.write(n,{bookType:"xlsx",bookSST:!1,type:"binary",cellStyles:!0});qe.saveAs(new Blob([re(g)],{type:"application/octet-stream"}),`拉运台账_${new Date().getTime()}.xlsx`)}catch(e){typeof console<"u"&&console.error(e)}},re=t=>{const e=new ArrayBuffer(t.length),o=new Uint8Array(e);for(let n=0;n<t.length;n++)o[n]=t.charCodeAt(n)&255;return e};return se(()=>{M(),J(),G()}),(t,e)=>{var A,T;const o=ke,n=me,s=ve,v=ce,f=fe,m=ge,g=ue,c=ie,l=he,_=we,y=be,w=Te;return k(),R("div",Ee,[a(ye,{"enter-active-class":(A=u(b))==null?void 0:A.animate.searchAnimate.enter,"leave-active-class":(T=u(b))==null?void 0:T.animate.searchAnimate.leave},{default:i(()=>[B(C("div",Ve,[a(c,{shadow:"hover"},{default:i(()=>[a(g,{ref_key:"queryFormRef",ref:x,model:u(p),inline:!0,"label-width":"100px"},{default:i(()=>[a(n,{label:"时间"},{default:i(()=>[a(o,{modelValue:u(p).queryStartTime,"onUpdate:modelValue":e[0]||(e[0]=r=>u(p).queryStartTime=r),type:"date","value-format":"YYYY-MM-DD",clearable:!1,class:"searchDate",placeholder:"开始日期"},null,8,["modelValue"]),e[4]||(e[4]=q(" 至 ")),a(o,{modelValue:u(p).queryEndTime,"onUpdate:modelValue":e[1]||(e[1]=r=>u(p).queryEndTime=r),type:"date","value-format":"YYYY-MM-DD",clearable:!1,class:"searchDate",placeholder:"结束日期"},null,8,["modelValue"])]),_:1}),a(n,{label:"分类",prop:"mediumCategory"},{default:i(()=>[a(v,{modelValue:u(p).mediumCategory,"onUpdate:modelValue":e[2]||(e[2]=r=>u(p).mediumCategory=r),clearable:"",placeholder:"请选择分类"},{default:i(()=>[(k(!0),R(de,null,pe(u(V),r=>(k(),$(s,{key:r.value,label:r.label,value:parseInt(r.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"集中站名称",prop:"wellName"},{default:i(()=>[a(f,{modelValue:u(p).wellName,"onUpdate:modelValue":e[3]||(e[3]=r=>u(p).wellName=r),clearable:"",placeholder:"请输入集中站名称"},null,8,["modelValue"])]),_:1}),a(n,null,{default:i(()=>[a(m,{type:"primary",icon:"Search",onClick:P},{default:i(()=>e[5]||(e[5]=[q("搜索")])),_:1}),a(m,{icon:"Refresh",onClick:ee},{default:i(()=>e[6]||(e[6]=[q("重置")])),_:1}),a(m,{type:"warning",plain:"",icon:"Download",onClick:te},{default:i(()=>e[7]||(e[7]=[q("导出")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[_e,X.value]])]),_:1},8,["enter-active-class","leave-active-class"]),a(c,{shadow:"never"},{default:i(()=>[B((k(),$(y,{ref_key:"reportTable",ref:N,data:h.value,"show-summary":!0,"span-method":H,"summary-method":K,border:"",stripe:""},{default:i(()=>[a(l,{label:"日期",align:"center",prop:"transportTime",width:"180"},{default:i(r=>[C("span",null,E(t.parseTime(r.row.transportTime,"{y}-{m}")),1)]),_:1}),a(l,{label:"作业区",align:"center",prop:"workArea"},{default:i(r=>[q(E(u(Z)[r.row.workArea]||"未知"),1)]),_:1}),a(l,{align:"center",label:"集中站",prop:"wellName"}),a(l,{label:"分类",align:"center",prop:"mediumCategory"},{default:i(r=>[a(_,{options:u(V),value:r.row.mediumCategory},null,8,["options","value"])]),_:1}),a(l,{label:"介质类型",align:"center",prop:"mediumType"},{default:i(r=>[a(_,{options:u(W),value:r.row.mediumType},null,8,["options","value"])]),_:1}),a(l,{label:"数量（方）",align:"center",prop:"number"},{default:i(r=>[C("span",null,E(r.row.number),1)]),_:1})]),_:1},8,["data"])),[[w,S.value]])]),_:1})])}}});export{Xe as default};
