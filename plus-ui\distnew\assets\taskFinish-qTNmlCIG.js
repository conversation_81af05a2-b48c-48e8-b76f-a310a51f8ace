import{d as Y,r as u,h as Z,ak as F,b as ee,c as ae,o as U,p as e,t as l,w as g,q as le,a7 as te,M as oe,A as ne,dc as se,K as ue,J as p,G as re,H as y,am as C,e as c,aI as ie,x as pe,ay as me,ax as de,z,aw as ce,aJ as _e,v as ve,az as fe,ai as ge}from"./index-D07cMzhp.js";import{E as we}from"./el-row-CikYE3zA.js";import{_ as be}from"./index-BWMgqvQ9.js";import{j as he}from"./index-3RY37FEX.js";import{w as ye}from"./index-BpaKvpB0.js";import{_ as Ce}from"./index.vue_vue_type_script_setup_true_lang-CzmK4ti3.js";import"./el-tree-DW6MoFaI.js";import"./el-col-BaG5Rg5z.js";import"./index-BdvXA74M.js";const ke={class:"p-2"},Ne={class:"mb-[10px]"},Ke=Y({__name:"taskFinish",setup(Se){const k=u(),{proxy:r}=Z(),{wf_business_status:R}=F(r==null?void 0:r.useDict("wf_business_status")),{wf_task_status:x}=F(r==null?void 0:r.useDict("wf_task_status")),N=u(),w=u(!0),K=u([]),q=u(!0),D=u(!0),_=u(!0),b=u(0),S=u([]),n=u({pageNum:1,pageSize:10,nodeName:void 0,flowName:void 0,flowCode:void 0,createByIds:[]}),m=u([]),v=u(0),i=()=>{V()},J=()=>{var o;(o=k.value)==null||o.resetFields(),n.value.pageNum=1,n.value.pageSize=10,n.value.createByIds=[],v.value=0,m.value=[],i()},P=o=>{K.value=o.map(a=>a.id),q.value=o.length!==1,D.value=!o.length},V=()=>{w.value=!0,he(n.value).then(o=>{S.value=o.rows,b.value=o.total,w.value=!1})},A=o=>{const a=ge({businessId:o.businessId,taskId:o.id,type:"view",formCustom:o.formCustom,formPath:o.formPath});ye.routerJump(a,r)},L=()=>{N.value.open()},Q=o=>{v.value=0,m.value=[],n.value.createByIds=[],o&&o.length>0&&(v.value=o.length,m.value=o.map(a=>a.userId),n.value.createByIds=m.value)};return ee(()=>{V()}),(o,a)=>{var E,T;const f=ue,$=se,d=ne,h=re,M=oe,B=te,j=be,G=we,s=de,H=ce,I=_e,O=me,W=fe,X=ve;return U(),ae("div",ke,[e(ie,{"enter-active-class":(E=c(r))==null?void 0:E.animate.searchAnimate.enter,"leave-active-class":(T=c(r))==null?void 0:T.animate.searchAnimate.leave},{default:l(()=>[g(le("div",Ne,[e(B,{shadow:"hover"},{default:l(()=>[g(e(M,{ref_key:"queryFormRef",ref:k,model:n.value,inline:!0},{default:l(()=>[e(d,null,{default:l(()=>[e($,{value:v.value,max:10,class:"item"},{default:l(()=>[e(f,{type:"primary",onClick:L},{default:l(()=>a[6]||(a[6]=[p("选择申请人")])),_:1})]),_:1},8,["value"])]),_:1}),e(d,{label:"任务名称",prop:"nodeName"},{default:l(()=>[e(h,{modelValue:n.value.nodeName,"onUpdate:modelValue":a[0]||(a[0]=t=>n.value.nodeName=t),placeholder:"请输入任务名称",onKeyup:y(i,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"流程定义名称","label-width":"100",prop:"flowName"},{default:l(()=>[e(h,{modelValue:n.value.flowName,"onUpdate:modelValue":a[1]||(a[1]=t=>n.value.flowName=t),placeholder:"请输入流程定义名称",onKeyup:y(i,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"流程定义编码","label-width":"100",prop:"flowCode"},{default:l(()=>[e(h,{modelValue:n.value.flowCode,"onUpdate:modelValue":a[2]||(a[2]=t=>n.value.flowCode=t),placeholder:"请输入流程定义编码",onKeyup:y(i,["enter"])},null,8,["modelValue"])]),_:1}),e(d,null,{default:l(()=>[e(f,{type:"primary",icon:"Search",onClick:i},{default:l(()=>a[7]||(a[7]=[p("搜索")])),_:1}),e(f,{icon:"Refresh",onClick:J},{default:l(()=>a[8]||(a[8]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model"]),[[C,_.value]])]),_:1})],512),[[C,_.value]])]),_:1},8,["enter-active-class","leave-active-class"]),e(B,{shadow:"hover"},{header:l(()=>[e(G,{gutter:10,class:"mb8"},{default:l(()=>[e(j,{"show-search":_.value,"onUpdate:showSearch":a[3]||(a[3]=t=>_.value=t),onQueryTable:i},null,8,["show-search"])]),_:1})]),default:l(()=>[g((U(),pe(O,{data:S.value,border:"",onSelectionChange:P},{default:l(()=>[e(s,{type:"selection",width:"55",align:"center"}),e(s,{align:"center",type:"index",label:"序号",width:"60"}),e(s,{align:"center",prop:"flowName",label:"流程定义名称"}),e(s,{align:"center",prop:"flowCode",label:"流程定义编码"}),e(s,{align:"center",prop:"categoryName",label:"流程分类"}),e(s,{align:"center",prop:"version",label:"版本号",width:"90"},{default:l(t=>[p(" v"+z(t.row.version)+".0",1)]),_:1}),e(s,{align:"center",prop:"nodeName",label:"任务名称"}),e(s,{align:"center",prop:"createByName",label:"申请人"}),e(s,{align:"center",prop:"approverName",label:"办理人"},{default:l(t=>[e(H,{type:"success"},{default:l(()=>[p(z(t.row.approveName||"无"),1)]),_:2},1024)]),_:1}),e(s,{align:"center",label:"流程状态",prop:"flowStatus","min-width":"70"},{default:l(t=>[e(I,{options:c(R),value:t.row.flowStatus},null,8,["options","value"])]),_:1}),e(s,{align:"center",label:"任务状态",prop:"flowTaskStatus","min-width":"70"},{default:l(t=>[e(I,{options:c(x),value:t.row.flowTaskStatus},null,8,["options","value"])]),_:1}),e(s,{align:"center",prop:"createTime",label:"创建时间",width:"160"}),e(s,{align:"center",label:"操作",width:"200"},{default:l(t=>[e(f,{type:"primary",size:"small",icon:"View",onClick:Ve=>A(t.row)},{default:l(()=>a[9]||(a[9]=[p("查看")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,w.value]]),g(e(W,{page:n.value.pageNum,"onUpdate:page":a[4]||(a[4]=t=>n.value.pageNum=t),limit:n.value.pageSize,"onUpdate:limit":a[5]||(a[5]=t=>n.value.pageSize=t),total:b.value,onPagination:i},null,8,["page","limit","total"]),[[C,b.value>0]])]),_:1}),e(c(Ce),{ref_key:"userSelectRef",ref:N,multiple:!0,data:m.value,onConfirmCallBack:Q},null,8,["data"])])}}});export{Ke as default};
