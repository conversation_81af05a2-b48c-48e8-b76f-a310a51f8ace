import{_ as a}from"./HmiPreview-B_QeCfjX.js";import{Q as r,c as s,o as i,p as l}from"./index-Bm6k27Yz.js";const m={name:"HmiRun",data(){return{fileName:""}},created(){this.fileName=this.$route.query.filename}},c={class:"app-container"};function p(_,e,u,f,o,d){const n=a;return i(),s("div",c,[l(n,{modelValue:o.fileName,"onUpdate:modelValue":e[0]||(e[0]=t=>o.fileName=t)},null,8,["modelValue"])])}const v=r(m,[["render",p]]);export{v as default};
