package com.biz.epms.eppcs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.epms.eppcs.common.enums.EppcsCurrentPhaseEnums;
import com.biz.epms.eppcs.domain.EppcsSamplingApplication;
import com.biz.epms.eppcs.domain.EppcsUnsealingInfo;
import com.biz.epms.eppcs.domain.bo.EppcsUnsealingInfoBo;
import com.biz.epms.eppcs.domain.vo.EppcsUnsealingInfoVo;
import com.biz.epms.eppcs.mapper.EppcsSamplingApplicationMapper;
import com.biz.epms.eppcs.mapper.EppcsUnsealingInfoMapper;
import com.biz.epms.eppcs.service.IEppcsUnsealingInfoService;
import com.biz.epms.eppcs.state.EppcsStateMachineManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 拆封信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class EppcsUnsealingInfoServiceImpl implements IEppcsUnsealingInfoService {

    private final EppcsUnsealingInfoMapper baseMapper;
    private final EppcsStateMachineManager stateMachineManager;
    private final EppcsSamplingApplicationMapper samplingApplicationMapper;

    /**
     * 查询拆封信息
     *
     * @param unsealingId 主键
     * @return 拆封信息
     */
    @Override
    public EppcsUnsealingInfoVo queryById(Long unsealingId){
        return baseMapper.selectVoById(unsealingId);
    }

    /**
     * 分页查询拆封信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 拆封信息分页列表
     */
    @Override
    public TableDataInfo<EppcsUnsealingInfoVo> queryPageList(EppcsUnsealingInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EppcsUnsealingInfo> lqw = buildQueryWrapper(bo);
        Page<EppcsUnsealingInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的拆封信息列表
     *
     * @param bo 查询条件
     * @return 拆封信息列表
     */
    @Override
    public List<EppcsUnsealingInfoVo> queryList(EppcsUnsealingInfoBo bo) {
        LambdaQueryWrapper<EppcsUnsealingInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<EppcsUnsealingInfo> buildQueryWrapper(EppcsUnsealingInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<EppcsUnsealingInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(EppcsUnsealingInfo::getUnsealingId);
        lqw.eq(bo.getApplicationId() != null, EppcsUnsealingInfo::getApplicationId, bo.getApplicationId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnsealPhoto()), EppcsUnsealingInfo::getUnsealPhoto, bo.getUnsealPhoto());
        lqw.eq(bo.getUnsealTime() != null, EppcsUnsealingInfo::getUnsealTime, bo.getUnsealTime());
        lqw.eq(StringUtils.isNotBlank(bo.getUnsealer()), EppcsUnsealingInfo::getUnsealer, bo.getUnsealer());
        lqw.eq(bo.getUnsealingStatus() != null, EppcsUnsealingInfo::getUnsealingStatus, bo.getUnsealingStatus());
        lqw.eq(bo.getApprovalTime() != null, EppcsUnsealingInfo::getApprovalTime, bo.getApprovalTime());
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalRemark()), EppcsUnsealingInfo::getApprovalRemark, bo.getApprovalRemark());
        return lqw;
    }

    /**
     * 新增拆封信息
     *
     * @param bo 拆封信息
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(EppcsUnsealingInfoBo bo) {
        EppcsUnsealingInfo add = MapstructUtils.convert(bo, EppcsUnsealingInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setUnsealingId(add.getUnsealingId());

            // 更新取样申请的当前阶段为2（样品拆封）
            if (add.getApplicationId() != null) {
                EppcsSamplingApplication application = samplingApplicationMapper.selectById(add.getApplicationId());
                if (application != null && application.getCurrentPhase() != EppcsCurrentPhaseEnums.SAMPLE_UNSEALING.getValue()) {
                    application.setCurrentPhase(EppcsCurrentPhaseEnums.SAMPLE_UNSEALING.getValue());
                    samplingApplicationMapper.updateById(application);
                }
            }
        }
        return flag;
    }

    /**
     * 修改拆封信息
     *
     * @param bo 拆封信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(EppcsUnsealingInfoBo bo) {
        EppcsUnsealingInfo update = MapstructUtils.convert(bo, EppcsUnsealingInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(EppcsUnsealingInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除拆封信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 提交拆封信息
     *
     * @param unsealingId 拆封信息ID
     * @param userType 用户类型
     * @return 是否成功
     */
    @Override
    public Boolean submitUnsealingInfo(Long unsealingId, Integer userType) {
        EppcsUnsealingInfo unsealingInfo = baseMapper.selectById(unsealingId);
        if (unsealingInfo == null) {
            throw new ServiceException("拆封信息不存在");
        }

        try {
            // 使用状态机获取下一状态
            int nextStatus = stateMachineManager.getNextUnsealingStatus(userType, unsealingInfo.getUnsealingStatus(), EppcsStateMachineManager.ACTION_SUBMIT);
            // 更新状态
            unsealingInfo.setUnsealingStatus(nextStatus);

            return baseMapper.updateById(unsealingInfo) > 0;
        } catch (Exception e) {
            log.error("提交拆封信息失败: {}", e.getMessage());
            throw new ServiceException("提交拆封信息失败: " + e.getMessage());
        }
    }

    /**
     * 确认拆封信息
     *
     * @param unsealingId 拆封信息ID
     * @param userType 用户类型
     * @param action 动作 (1通过, 2驳回)
     * @param approvalRemark 审批意见
     * @param approver 审批人
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmUnsealingInfo(Long unsealingId, Integer userType, Integer action,
                                       String approvalRemark, String approver) {
        EppcsUnsealingInfo unsealingInfo = baseMapper.selectById(unsealingId);
        if (unsealingInfo == null) {
            throw new ServiceException("拆封信息不存在");
        }

        try {
            // 使用状态机获取下一状态
            int nextStatus = stateMachineManager.getNextUnsealingStatus(userType, unsealingInfo.getUnsealingStatus(), action);

            // 更新状态和审批信息
            unsealingInfo.setUnsealingStatus(nextStatus);
            unsealingInfo.setApprovalRemark(approvalRemark);
            unsealingInfo.setApprovalTime(new java.util.Date());
            unsealingInfo.setApprover(StringUtils.isNotBlank(unsealingInfo.getApprover()) ? unsealingInfo.getApprover() + "," + approver : approver);


            if (nextStatus == EppcsStateMachineManager.UNSEALING_CONFIRMED){
                EppcsSamplingApplication application = new EppcsSamplingApplication();
                application.setApplicationId(unsealingInfo.getApplicationId());
                application.setCurrentPhase(EppcsCurrentPhaseEnums.UNSEALING_COMPLETED.getValue());
                samplingApplicationMapper.updateById(application);

            }

            return baseMapper.updateById(unsealingInfo) > 0;
        } catch (Exception e) {
            log.error("确认拆封信息失败: {}", e.getMessage());
            throw new ServiceException("确认拆封信息失败: " + e.getMessage());
        }
    }


}
