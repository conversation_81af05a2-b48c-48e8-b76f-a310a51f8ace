import{d as I,h as q,ai as p,ak as L,r as _,aY as M,b as R,c as A,o as k,F as $,q as r,p as o,aL as H,t as n,K,e as t,aA as O,M as Q,A as Y,J as j,x as Z,y as G,a_ as W,aD as X,aZ as ee,Q as te}from"./index-Bm6k27Yz.js";import{_ as oe}from"./index-CKygFLbI.js";import{_ as ae}from"./index.vue_vue_type_style_index_0_lang-BJD3xYAG.js";import{g as F,u as ne}from"./index-DkwKf-VW.js";import"./el-link-6M7kQRy3.js";import"./el-upload-CSOgWUCs.js";import"./el-progress-D8KwIucv.js";import"./dayjs.min-Dqr9lGM-.js";const se={class:"waterline p-2"},le={class:"bianji"},ie={style:{float:"right"}},re=["innerHTML"],ce={class:"dialog-footer"},de={style:{height:"68vh"}},me=I({name:"Jianjie"}),ue=I({...me,props:{attachSourceType:{},noticeId:{}},setup(T){const c=T,f=async()=>{const a=await F(c.noticeId);h.value=a.data.noticeContent},{proxy:d}=q(),v={noticeId:void 0,noticeTitle:"",noticeType:"",noticeContent:"",status:"",remark:"",createByName:""},E=p({form:{...v},queryParams:{pageNum:1,pageSize:10},rules:{noticeContent:[{required:!0,message:"内容不能为空",trigger:"blur"}]}}),{queryParams:pe,form:s,rules:N}=L(E),u=_(),i=p({visible:!1,title:""}),g=M(),x=_({}),b=p({visible:!1,url:""}),y=()=>{var a;s.value={...v},(a=u.value)==null||a.resetFields()},h=_(),P=async()=>{y();const a=await F(c.noticeId);Object.assign(s.value,a.data),i.visible=!0,i.title="编辑简介"},B=()=>{var a;(a=u.value)==null||a.validate(async e=>{e&&(await ne(s.value),d==null||d.$modal.msgSuccess("操作成功"),i.visible=!1,await f())})},D=()=>{i.visible=!1,y()},J=async a=>{d.showAttachPreview({attachSourceId:c.noticeId,attachSourceType:c.attachSourceType,attachCategory:a})};return R(()=>{f()}),(a,e)=>{const m=K,w=H,S=ae,C=Y,z=oe,U=Q,V=O;return k(),A($,null,[r("div",se,[r("div",le,[r("div",ie,[o(w,{class:"item",placement:"bottom",content:"编辑"},{default:n(()=>[o(m,{circle:"",icon:"EditPen",onClick:P})]),_:1}),o(w,{class:"item",placement:"bottom-end",content:"下载"},{default:n(()=>[o(m,{circle:"",icon:"Download",onClick:e[0]||(e[0]=l=>J("zonglanJianjie"))})]),_:1})])]),r("div",{class:"jianjie",innerHTML:t(h)},null,8,re)]),o(V,{modelValue:t(i).visible,"onUpdate:modelValue":e[3]||(e[3]=l=>t(i).visible=l),title:t(i).title,width:"600px","append-to-body":""},{footer:n(()=>[r("div",ce,[o(m,{type:"primary",onClick:B},{default:n(()=>e[5]||(e[5]=[j("确 定")])),_:1}),o(m,{onClick:D},{default:n(()=>e[6]||(e[6]=[j("取 消")])),_:1})])]),default:n(()=>[o(U,{ref_key:"configFormRef",ref:u,model:t(s),rules:t(N),"label-width":"80px"},{default:n(()=>[o(C,{label:"页面简介",prop:"noticeContent"},{default:n(()=>[o(S,{modelValue:t(s).noticeContent,"onUpdate:modelValue":e[1]||(e[1]=l=>t(s).noticeContent=l),"min-height":100,placeholder:"请输入页面简介内容",type:"base64"},null,8,["modelValue"])]),_:1}),o(C,{label:"简介附件",prop:"remark"},{default:n(()=>[o(z,{modelValue:t(s).remark,"onUpdate:modelValue":e[2]||(e[2]=l=>t(s).remark=l),"attach-source-id":t(s).noticeId,disabled:!1,"attach-category":"zonglanJianjie","attach-source-type":"zongLan"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),o(V,{title:"附件预览",modelValue:t(b).visible,"onUpdate:modelValue":e[4]||(e[4]=l=>t(b).visible=l),width:"80%",he:"","append-to-body":""},{default:n(()=>[r("div",de,[g.value?(k(),Z(ee(g.value),W(X({key:0},t(x))),null,16)):G("",!0)])]),_:1},8,["modelValue"])],64)}}}),Ce=te(ue,[["__scopeId","data-v-1d270135"]]);export{Ce as default};
