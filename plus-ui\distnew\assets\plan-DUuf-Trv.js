import{aC as a}from"./index-Bm6k27Yz.js";function n(t){return a({url:"/epms/plan",method:"post",data:t})}function r(t){return a({url:"/epms/plan",method:"put",data:t})}function s(t){return a({url:"/epms/plan",method:"delete",params:t})}function u(t){return a({url:"/epms/plan/listWithIndicator",method:"get",params:t})}function o(t){return a({url:"/epms/indicatortemplatedetail/queryTempIndicatorMap",method:"post",data:t})}function l(t){return a({url:"/epms/plandetail/insertOrUpdateDetail",method:"post",data:t})}function p(t){return a({url:"/epms/operationArea/list",method:"get",params:t})}function i(t){return a({url:"/epms/plan/initMonthPlan",method:"post",data:t})}function d(t){return a({url:"/epms/plan/queryMonthvalues",method:"post",data:t})}export{n as a,d as b,i as c,s as d,l as i,u as l,o as q,p as s,r as u};
