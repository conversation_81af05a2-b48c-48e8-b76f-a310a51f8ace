<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item v-if="queryParams.disposeType == 2" label-width="90" label="单井名称" prop="disposeId">
              <el-select v-model="queryParams.disposeId" clearable filterable placeholder="请选择单井" style="width: 240px">
                <el-option v-for="item in allList" :key="item.id" :label="item.label" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table
        ref="reportTable"
        v-loading="loading"
        :data="mudDailyList"
        :cell-class-name="handleCellClass"
        :span-method="handleSpanMethod"
        border
        stripe
      >
        <el-table-column label="单井井号" align="center" prop="disposeId">
          <template #default="scope">
            <el-link @click="handleShowSystemSelect(scope.row)">
              {{ getKayName(scope.row) }} <el-icon class="el-icon--right"><View /></el-icon>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" label="泥浆处理量（方）" prop="slurryDisposeAmount" />
        <el-table-column align="center" label="泥浆暂存量（方）" prop="slurryStagingAmount" />
        <el-table-column align="center" label="泥饼拉运量（方）" prop="mudPullingAmount" />
        <el-table-column align="center" label="泥饼暂存量（方）" prop="mudStagingAmount" />
        <el-table-column align="center" label="滤水拉运量（方）" prop="waterPullingAmount" />
        <el-table-column align="center" label="滤水暂存量（方）" prop="waterStagingAmount" />
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>

    <el-dialog v-model="showSystemSelect" title="系统查询" width="1600px">
      <DrillingDaily :dispose-id="showSystemSelectDisposeId" />
    </el-dialog>
  </div>
</template>

<script lang="ts" name="MonthlyStatisticsSuiZuan" setup>
import { suiZuanMonthStat } from '@/api/epms/epnj/mudDaily';
import { MudDailyForm, MudDailyQuery, MudDailyVO } from '@/api/epms/epnj/mudDaily/types';
import { WellPreparationVO } from '@/api/epms/epnj/wellPreparation/types';
import { listWellPreparation } from '@/api/epms/epnj/wellPreparation';
import * as XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style-vite';
import { ref } from 'vue';
import DrillingDaily from '@/views/epms/epnj/drillingDaily/index.vue';
import { View } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const mudDailyList = ref<MudDailyVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const dateRangeDate = ref<[DateModelType, DateModelType]>(['', '']);
const reportTable = ref(null);
const wellList = ref<WellPreparationVO[]>([]);

const queryFormRef = ref<ElFormInstance>();
const mudDailyFormRef = ref<ElFormInstance>();
const allList = ref([]);
const showSystemSelect = ref(false);
const showSystemSelectDisposeId = ref('');

const initFormData: MudDailyForm = {
  id: undefined,
  date: undefined,
  disposeType: 2,
  disposeId: undefined,
  slurryDisposeAmount: undefined,
  mudPullingAmount: undefined,
  mudStagingAmount: undefined,
  waterPullingAmount: undefined,
  waterStagingAmount: undefined,
  loss: undefined
};
const data = reactive<PageData<MudDailyForm, MudDailyQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    disposeType: 2,
    disposeId: undefined,
    slurryDisposeAmount: undefined,
    mudPullingAmount: undefined,
    mudStagingAmount: undefined,
    waterPullingAmount: undefined,
    waterStagingAmount: undefined,
    loss: undefined,
    params: {
      date: undefined
    }
  },
  rules: {
    id: [{ required: true, message: '主键ID不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const getWellList = async () => {
  const param = {
    handlingType: 2
  };
  const res = await listWellPreparation(param);
  wellList.value = res.rows;
  wellList.value.forEach((item) => {
    item.id = item.prepId;
    item.label = item.wellName;
    allList.value.push(item);
  });
};
/**
 * 打开系统查询弹窗
 * @param row
 */
const handleShowSystemSelect = async (row: any) => {
  // 获取当前选中的井id
  const disposeId = row?.disposeId;
  if (disposeId) {
    // 显示系统选择弹窗
    showSystemSelect.value = true;
    // 设置当前选中的井id
    showSystemSelectDisposeId.value = disposeId;
  }
};
/** 导出按钮操作 */
const handleExport = () => {
  try {
    // 使用 ref 值需要加上 .value
    const $e = reportTable.value?.$el;
    let $table = $e.querySelector('.el-table__fixed');
    if (!$table) {
      $table = $e;
    }

    const wb = XLSX.utils.table_to_book($table, { raw: true });
    const ws = wb.Sheets[wb.SheetNames[0]];
    // 1. 设置列宽自适应
    const colWidths = [];
    // 遍历所有单元格计算最大列宽
    const range = XLSX.utils.decode_range(ws['!ref']);
    for (let col = range.s.c; col <= range.e.c; col++) {
      let maxWidth = 0;
      for (let row = range.s.r; row <= range.e.r; row++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = ws[cellAddress];
        if (cell && cell.v) {
          const cellText = String(cell.v);
          // 粗略计算宽度：中文占2字符，英文占1字符
          const width = cellText.split('').reduce((acc, char) => acc + (char.charCodeAt(0) > 255 ? 2 : 1), 0);
          if (width > maxWidth) maxWidth = width;
        }
      }
      // 设置列宽（加缓冲值）
      colWidths.push({ wch: Math.min(maxWidth + 2, 60) }); // 限制最大宽度
    }
    ws['!cols'] = colWidths;

    // 2. 设置单元格样式（居中+边框）
    const style = {
      alignment: {
        horizontal: 'center',
        vertical: 'center',
        wrapText: true // 增加自动换行
      },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      },
      font: {
        // 必须包含基础字体设置
        sz: 11,
        name: '宋体',
        color: { rgb: '000000' }
      }
    };

    // 遍历所有单元格应用样式
    Object.keys(ws).forEach((cellAddress) => {
      if (!cellAddress.startsWith('!')) {
        const cell = ws[cellAddress];
        // 保留原始单元格样式（如果有）
        cell.s = cell.s ? { ...cell.s, ...style } : { ...style };
        // 处理数字格式（保留两位小数）
        if (typeof cell.v === 'number') {
          cell.z = cell.z || '0.00';
        }
      }
    });
    if (ws['!merges']) {
      ws['!merges'].forEach((merge) => {
        for (let r = merge.s.r; r <= merge.e.r; r++) {
          for (let c = merge.s.c; c <= merge.e.c; c++) {
            const cellAddr = XLSX.utils.encode_cell({ r, c });
            if (!ws[cellAddr]) ws[cellAddr] = { t: 's', v: '' }; // 填充空单元格
            ws[cellAddr].s = { ...style };
          }
        }
      });
    }
    const wbout = XLSXStyle.write(wb, {
      bookType: 'xlsx',
      bookSST: false,
      type: 'binary',
      cellStyles: true // 必须启用样式支持
    });
    saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), `随钻_月度统计_${new Date().getTime()}.xlsx`);
  } catch (e) {
    if (typeof console !== 'undefined') console.error(e);
  }
};

const s2ab = (s) => {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xff;
  return buf;
};

const getKayName = (row) => {
  if (row.isLoss || row.isTotal) {
    return row.disposeId;
  }
  // let name = areaList.value.find((item: any) => item.operationAreaId == id) ? areaList.value.find((item: any) => item.operationAreaId == id).operationAreaName : null
  return allList.value.find((item: any) => item.id == row.disposeId) ? allList.value.find((item: any) => item.id == row.disposeId).label : '未知';
  // return name != null ? name : name1
};
/** 查询泥浆日报列表 */
const getList = async () => {
  loading.value = true;
  const res = await suiZuanMonthStat(queryParams.value);
  mudDailyList.value = res.data;
  if (res.total) {
    const totalRow = {
      isTotal: true,
      disposeId: res.total.wellNumber,
      slurryDisposeAmount: res.total.mudTotal,
      mudPullingAmount: res.total.cakeTotal,
      waterPullingAmount: res.total.filtrateTotal
    };
    // 将总量行添加到列表末尾
    mudDailyList.value.push(totalRow);
  }
  if (res.loss) {
    const lossRow = {
      isLoss: true,
      disposeId: '损耗≤10%',
      slurryDisposeAmount: res.loss
    };
    mudDailyList.value.push(lossRow);
  }
  loading.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  mudDailyFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const isLossExceeded = (lossText: string) => {
  try {
    const currentLossMatch = lossText.match(/当前损耗[:：]\s*([+-]?\d+(\.\d+)?)/);
    const maxLossMatch = lossText.match(/损耗最大值[:：]\s*(\d+(\.\d+)?)/);
    if (currentLossMatch && maxLossMatch) {
      const currentLoss = parseFloat(currentLossMatch[1]);
      const maxLoss = parseFloat(maxLossMatch[1]);

      return currentLoss > maxLoss;
    } else {
      console.log('error');
    }
  } catch (error) {
    console.error('解析损耗数值失败:', error);
  }
  return false;
};

const handleCellClass = ({ row, columnIndex }: { row: TableRow; columnIndex: number }) => {
  if (row.isLoss && columnIndex === 1) {
    console.log('jinru');
    if (isLossExceeded(row.slurryDisposeAmount)) {
      return 'text-red';
    }
  }
  return '';
};

const handleSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (row.isTotal) {
    if (columnIndex === 0) {
      return {
        rowspan: 1,
        colspan: 1
      };
    }
    if (columnIndex % 2 === 1) {
      return {
        rowspan: 1,
        colspan: 2 // 合并2列
      };
    }

    if (columnIndex % 2 === 0) {
      return {
        rowspan: 0,
        colspan: 0
      };
    }
  }

  // 损耗行的合并
  if (row.isLoss) {
    if (columnIndex === 1) {
      return {
        rowspan: 1,
        colspan: 6 // 合并6列
      };
    }
  }
  return {
    rowspan: 1,
    colspan: 1
  };
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeDate.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

onMounted(() => {
  getWellList();
  getList();
});
</script>
<style scoped>
.allowClick {
  color: #1e9fff;
  background-color: #fdfdfd;
  cursor: pointer;
}
:deep(.el-table__cell.el-table__cell--merge) {
  /* 超限红色 */
  &.text-red {
    color: #ff4d4f !important;
    font-weight: 500;
  }
}

/* 确保单元格内的文本元素继承样式 */
:deep(.el-table__cell.el-table__cell--merge .cell) {
  color: inherit !important;
}
</style>
