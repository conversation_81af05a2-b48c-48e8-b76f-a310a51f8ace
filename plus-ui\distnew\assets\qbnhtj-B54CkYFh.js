import{Q as n,c as o,o as h,w as d,v as l,q as c,E as u,a6 as m}from"./index-D07cMzhp.js";import{E as p}from"./jhtj-C9I-8AIF.js";import{i as a}from"./index-C0_-menq.js";const g=m({storageKey:"useDarkKey",valueDark:"dark",valueLight:"light"}),y={name:"qbnhtj",props:{fatherQueryForm:{type:Object,required:!0},currentNengYuanName:{type:String,required:!0}},data(){return{isDark:g,loading:!1,echartItem:null,resizeHandler:null}},watch:{isDark:{handler(e,t){this.showChart()},deep:!0}},mounted(){this.initChart(),this.resizeHandler=()=>{this.echartItem&&this.echartItem.resize()},window.addEventListener("resize",this.resizeHandler)},beforeD<PERSON>roy(){this.resizeHandler&&window.removeEventListener("resize",this.resizeHandler),this.disposeChart()},methods:{disposeChart(){this.echartItem&&(this.echartItem.dispose(),this.echartItem=null)},initChart(){this.$refs.qbnhtjChart&&(this.disposeChart(),this.echartItem=a(this.$refs.qbnhtjChart,this.isDark?"dark":"light"))},async showChart(){this.loading=!0,this.$emit("loadingFinish",{type:"qbLoading",loading:this.loading});let e=null;try{e=await p({danwei:this.fatherQueryForm.org,indicatorId:this.fatherQueryForm.nengYuanId,dateTime:this.fatherQueryForm.date})}catch{u.error(`获取${this.currentNengYuanName}统计情况的数据失败`);return}finally{this.loading=!1,this.$emit("loadingFinish",{type:"qbLoading",loading:this.loading})}this.initChart();let t={backgroundColor:"rgba(255,255,255,0)",tooltip:{trigger:"axis",axisPointer:{type:"cross",crossStyle:{color:"#999"}}},title:{left:"center",text:this.currentNengYuanName+"统计情况"},legend:{top:"bottom",data:["实际","计划","同期","环比"]},xAxis:[{type:"category",data:e.data.XAxis,axisPointer:{type:"shadow"}}],yAxis:[{type:"value",name:"",axisLabel:{formatter:"{value}"}},{type:"value",name:"",axisLabel:{formatter:"{value} %"}}],series:[{name:"实际",type:"bar",data:e.data.shiJi},{name:"计划",type:"bar",data:e.data.plan},{name:"同期",type:"bar",data:e.data.lastYear},{name:"环比",type:"line",yAxisIndex:1,data:e.data.huanBi}]};a(this.$refs.qbnhtjChart,this.isDark?"dark":"light").setOption(t,!0)}}},f={class:"app-container"},b={ref:"qbnhtjChart",style:{width:"100%",height:"260px"}};function x(e,t,i,v,r,k){const s=l;return h(),o("div",f,[d(c("div",b,null,512),[[s,r.loading]])])}const q=n(y,[["render",x]]);export{q as default};
