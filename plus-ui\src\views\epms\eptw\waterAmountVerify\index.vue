<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="年度" prop="queryYear">
              <el-date-picker
                v-model="queryParams.queryYear"
                type="yearrange"
                value-format="YYYY"
                range-separator="至"
                start-placeholder="开始年度"
                end-placeholder="结束年度"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="季度" prop="quarter">
              <el-select v-model="queryParams.quarter" placeholder="请选择季度" clearable>
                <el-option v-for="dict in eptw_quarter" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择报告状态">
                <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterAmountVerify:add']" icon="Plus" plain type="primary" @click="handleAdd"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterAmountVerify:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterAmountVerify:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterAmountVerify:export']" icon="Download" plain type="warning" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="waterAmountVerifyList" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column label="年度" align="center" prop="year" />

        <el-table-column label="季度" align="center" prop="quarter">
          <template #default="scope">
            <dict-tag :options="eptw_quarter" :value="scope.row.quarter" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="所属地" min-width="135" prop="operationAreaId">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>

        <el-table-column align="center" label="取水区块" min-width="100" prop="blockId">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.blockId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="开始日期" prop="verifyStartTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.verifyStartTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="结束日期" prop="verifyEndTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.verifyEndTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="status" width="60">
          <template #default="scope">
            <dict-tag :options="eptw_file_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="剩余时间(天)" prop="effectiveDay" width="110" />
        <el-table-column align="center" label="抄表日期" prop="meterReadingTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.meterReadingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="本期取水量合计(m³)" prop="currentWaterIntake" width="140" />
        <el-table-column align="center" label="累计取水量合计(m³)" prop="cumulativeWaterIntake" width="140" />
        <el-table-column label="上传时间" align="center" prop="uploadTime" width="155">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="320">
          <template #default="scope">
            <el-tooltip content="用水核定书" placement="top">
              <el-button v-hasPermi="['epms:waterAmountVerify:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >核定书预览
              </el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:waterAmountVerify:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情
              </el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epms:waterAmountVerify:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"
                >修改
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:waterAmountVerify:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改用水核定书对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="waterAmountVerifyFormRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="年度" prop="year">
          <el-date-picker v-model="formYearProxy" placeholder="请选择年度" type="year" />
        </el-form-item>
        <el-form-item label="季度" prop="quarter">
          <el-select v-model="form.quarter" placeholder="请选择季度">
            <el-option v-for="dict in eptw_quarter" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select filterable v-model="form.operationAreaId" placeholder="选择所属地" clearable allow-create @change="getBlockList(true)">
            <el-option
              v-for="operationArea in opeartionList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="取水区块" prop="blockId">
          <el-select filterable v-model="form.blockId" placeholder="选择取水区块" clearable allow-create>
            <el-option
              v-for="operationArea in blockList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="verifyStartTime">
          <el-date-picker v-model="form.verifyStartTime" clearable placeholder="请选择开始日期" type="date" value-format="YYYY-MM-DD">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" prop="verifyEndTime">
          <el-date-picker v-model="form.verifyEndTime" clearable placeholder="请选择结束日期" type="date" value-format="YYYY-MM-DD"> </el-date-picker>
        </el-form-item>
        <el-form-item label="抄表日期" prop="meterReadingTime">
          <el-date-picker v-model="form.meterReadingTime" clearable placeholder="请选择抄表日期" type="date" value-format="YYYY-MM-DD">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择报告状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="本期取水量合计" prop="currentWaterIntake">
          <el-input v-model="form.currentWaterIntake" placeholder="请输入本期取水量合计">
            <template #append> m³ </template>
          </el-input>
        </el-form-item>
        <el-form-item label="累计取水量合计" prop="cumulativeWaterIntake">
          <el-input v-model="form.cumulativeWaterIntake" placeholder="请输入累计取水量合计">
            <template #append> m³ </template>
          </el-input>
        </el-form-item>
        <el-form-item label="文件列表" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.verifyReportId"
            :disabled="false"
            attach-category="waterAmountVerify"
            attach-source-type="elCompany"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="waterAmountVerifyFormRef" :model="form" :rules="rules" label-width="110px" disabled>
        <el-form-item label="年度" prop="year">
          <el-date-picker v-model.number="form.year" placeholder="请输入年度" type="year" />
        </el-form-item>
        <el-form-item label="季度" prop="quarter">
          <el-select v-model="form.quarter" placeholder="请选择季度">
            <el-option v-for="dict in eptw_quarter" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select filterable v-model="form.operationAreaId" placeholder="选择所属地" clearable allow-create>
            <el-option
              v-for="operationArea in opeartionList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="取水区块" prop="blockId">
          <el-select filterable v-model="form.blockId" placeholder="选择取水区块" clearable allow-create>
            <el-option
              v-for="operationArea in blockList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="verifyStartTime">
          <el-date-picker v-model="form.verifyStartTime" clearable placeholder="请选择开始日期" type="date" value-format="YYYY-MM-DD">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" prop="verifyEndTime">
          <el-date-picker v-model="form.verifyEndTime" clearable placeholder="请选择结束日期" type="date" value-format="YYYY-MM-DD"> </el-date-picker>
        </el-form-item>
        <el-form-item label="抄表日期" prop="meterReadingTime">
          <el-date-picker v-model="form.meterReadingTime" clearable placeholder="请选择抄表日期" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择报告状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="本期取水量合计" prop="currentWaterIntake">
          <el-input v-model="form.currentWaterIntake" placeholder="请输入本期取水量合计">
            <template #append> m³ </template>
          </el-input>
        </el-form-item>
        <el-form-item label="累计取水量合计" prop="cumulativeWaterIntake">
          <el-input v-model="form.cumulativeWaterIntake" placeholder="请输入累计取水量合计">
            <template #append> m³ </template>
          </el-input>
        </el-form-item>
        <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker v-model="form.uploadTime" clearable placeholder="上传时间" type="datetime"> </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WaterAmountVerify" lang="ts">
import {
  addWaterAmountVerify,
  delWaterAmountVerify,
  getWaterAmountVerify,
  listWaterAmountVerify,
  updateWaterAmountVerify
} from '@/api/epms/eptw/waterAmountVerify';
import { WaterAmountVerifyForm, WaterAmountVerifyQuery, WaterAmountVerifyVO } from '@/api/epms/eptw/waterAmountVerify/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_file_status, eptw_quarter } = toRefs<any>(proxy?.useDict('eptw_file_status', 'eptw_quarter'));
const waterAmountVerifyList = ref<WaterAmountVerifyVO[]>([]);
const operationAreaQuery = ref<OperationAreaQuery>({});
const opeartionAllList = ref<OperationAreaVO[]>([]);
const opeartionList = ref<OperationAreaVO[]>([]);
const blockList = ref<OperationAreaVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const router = useRouter();
const queryFormRef = ref<ElFormInstance>();
const waterAmountVerifyFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WaterAmountVerifyForm = {
  verifyReportId: undefined,
  year: undefined,
  meterReadingTime: undefined,
  quarter: undefined,
  file: undefined
};
const data = reactive<PageData<WaterAmountVerifyForm, WaterAmountVerifyQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    queryYear: undefined,
    quarter: undefined,
    startYear: undefined,
    endYear: undefined,
    params: {}
  },
  rules: {
    verifyReportId: [{ required: true, message: '核定报告id不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '所属地不能为空', trigger: 'blur' }],
    year: [{ required: true, message: '年度不能为空', trigger: 'blur' }],
    meterReadingTime: [{ required: true, message: '抄表日期不能为空', trigger: 'blur' }],
    quarter: [{ required: true, message: '季度不能为空', trigger: 'change' }],
    blockId: [{ required: true, message: '取水区块不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用水核定书列表 */
const getList = async () => {
  const query = queryParams;
  console.log(query.value.queryYear);
  if (typeof query.value.queryYear !== 'undefined' && query.value.queryYear !== null) {
    query.value.startYear = query.value.queryYear[0] as number;
    query.value.endYear = query.value.queryYear[1] as number;
  } else {
    query.value.startYear = undefined;
    query.value.endYear = undefined;
  }
  loading.value = true;
  const res = await listWaterAmountVerify(query.value);
  waterAmountVerifyList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

const formYearProxy = computed({
  get: () => (form.value.year ? new Date(form.value.year, 0) : null),
  set: (val) => {
    form.value.year = val ? val.getFullYear() : undefined;
  }
});
/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  waterAmountVerifyFormRef.value?.resetFields();
};

const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '';
  }
  const operationAreaItem = opeartionAllList.value.find((item) => item.operationAreaId === operationAreaId);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};

const allOperationArea = async () => {
  const opeartionRes = await listOperationArea();
  opeartionAllList.value = opeartionRes.rows;
};

const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaType = 0;
  const opeartionRes = await listOperationArea(operationAreaQuery.value); //查询所属地
  opeartionList.value = opeartionRes.rows;
};

const getBlockList = async (flag?: boolean) => {
  if (flag) {
    form.value.blockId = undefined;
  }
  operationAreaQuery.value.operationAreaParentId = form.value.operationAreaId ? form.value.operationAreaId : undefined;
  operationAreaQuery.value.operationAreaType = 1;
  const blockRes = await listOperationArea(operationAreaQuery.value);
  blockList.value = blockRes.rows;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WaterAmountVerifyVO[]) => {
  ids.value = selection.map((item) => item.verifyReportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加用水核定书';
};

/** 预览按钮操作 */
const handlePreview = async (row?: WaterAmountVerifyVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.verifyReportId,
    attachSourceType: 'elCompany',
    attachCategory: 'waterAmountVerify'
  });
};
/** 详情按钮操作 */
const handleDetail = async (row?: WaterAmountVerifyVO) => {
  reset();
  const _verifyReportId = row?.verifyReportId || ids.value[0];
  const res = await getWaterAmountVerify(_verifyReportId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '用水核定书详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: WaterAmountVerifyVO) => {
  reset();
  const _verifyReportId = row?.verifyReportId || ids.value[0];
  const res = await getWaterAmountVerify(_verifyReportId);
  Object.assign(form.value, res.data);
  await getBlockList();
  dialog.visible = true;
  dialog.title = '修改用水核定书';
};

/** 提交按钮 */
const submitForm = () => {
  waterAmountVerifyFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.verifyReportId) {
        await updateWaterAmountVerify(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWaterAmountVerify(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};
const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
};
/** 删除按钮操作 */
const handleDelete = async (row?: WaterAmountVerifyVO) => {
  const _verifyReportIds = row?.verifyReportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除用水核定书编号为"' + _verifyReportIds + '"的数据项？').finally(() => (loading.value = false));
  await delWaterAmountVerify(_verifyReportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/waterAmountVerify/export',
    {
      ...queryParams.value
    },
    `用水核定书_${new Date().getTime()}.xlsx`
  );
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  allOperationArea();
  getoperationAreaList();
  getList();
});
</script>
