<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="报告名称" prop="qualityReportName">
              <el-input v-model="queryParams.qualityReportName" placeholder="请输入水质监测报告名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="监测年度" prop="year">
              <el-date-picker
                v-model="queryParams.year"
                type="yearrange"
                value-format="YYYY"
                range-separator="至"
                start-placeholder="开始年度"
                end-placeholder="结束年度"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epms:qualityTrilateral:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:qualityTrilateral:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:qualityTrilateral:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:qualityTrilateral:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="qualityTrilateralList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="监测报告名称" min-width="200" prop="qualityReportName" />
        <el-table-column label="注水站" align="center" min-width="200">
          <template #default="scope">
            <span>{{ formatIotIds(scope.row.iotId) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="监测年度" min-width="100" prop="year" />
        <el-table-column align="center" label="监测第三方" min-width="260" prop="monitorParty" />
        <el-table-column align="center" label="监测日期" min-width="100" prop="detectionTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.detectionTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上传时间" align="center" prop="uploadTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="305">
          <template #default="scope">
            <el-tooltip content="采出水监测报告" placement="top">
              <el-button v-hasPermi="['epms:qualityTrilateral:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >报告预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:qualityTrilateral:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epms:qualityTrilateral:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"
                >修改</el-button
              >
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:qualityTrilateral:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改采出水第三方水质监测报告对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="qualityTrilateralFormRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="报告名称" prop="qualityReportName">
          <el-input v-model="form.qualityReportName" placeholder="请输入水质监测报告" />
        </el-form-item>
        <el-form-item label="监测年度" prop="year">
          <el-date-picker v-model="form.year" format="YYYY" placeholder="请选择监测年度" type="year" value-format="YYYY" />
        </el-form-item>
        <el-form-item label="监测第三方" prop="monitorParty">
          <el-input v-model="form.monitorParty" placeholder="请输入监测第三方" />
        </el-form-item>
        <el-form-item label="监测日期" prop="detectionTime">
          <el-date-picker v-model="form.detectionTime" clearable placeholder="请选择监测日期" type="date" value-format="YYYY-MM-DD"> </el-date-picker>
        </el-form-item>
        <el-form-item label="注水站" prop="iotId">
          <el-select v-model="form.iotId" multiple placeholder="请选择注水站">
            <el-option v-for="item in injectionStationList" :key="item.id" :label="item.mingZi" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="水质监测报告" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.qualityReportId"
            :disabled="false"
            attach-category="qualityReport"
            attach-source-type="elCompany"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="qualityTrilateralFormRef" :model="form" :rules="rules" label-width="110px" disabled>
        <el-form-item label="报告名称" prop="qualityReportName">
          <el-input v-model="form.qualityReportName" placeholder="请输入水质监测报告" />
        </el-form-item>
        <el-form-item label="监测年度" prop="year">
          <el-date-picker v-model="form.year" placeholder="请选择监测年度" type="year" value-format="YYYY" />
        </el-form-item>
        <el-form-item label="监测第三方" prop="monitorParty">
          <el-input v-model="form.monitorParty" placeholder="请输入监测第三方" />
        </el-form-item>
        <el-form-item label="监测日期" prop="detectionTime">
          <el-date-picker v-model="form.detectionTime" clearable placeholder="请选择监测日期" type="date" value-format="YYYY-MM-DD"> </el-date-picker>
        </el-form-item>
        <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker v-model="form.uploadTime" clearable placeholder="上传时间" type="datetime"> </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="QualityTrilateral" lang="ts">
import {
  addQualityTrilateral,
  delQualityTrilateral,
  getQualityTrilateral,
  listQualityTrilateral,
  updateQualityTrilateral
} from '@/api/epms/eptw/qualityTrilateral';
import { QualityTrilateralForm, QualityTrilateralQuery, QualityTrilateralVO } from '@/api/epms/eptw/qualityTrilateral/types';
import { selectAllCommdevData } from '@/api/comm/hmi';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const qualityTrilateralList = ref<QualityTrilateralVO[]>([]);
const injectionStationList = ref<any[]>([]);
const buttonLoading = ref(false);
const stationMap = ref(new Map());
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const router = useRouter();
const queryFormRef = ref<ElFormInstance>();
const qualityTrilateralFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: QualityTrilateralForm = {
  qualityReportId: undefined,
  detectionTime: undefined,
  file: undefined,
  qualityReportName: undefined,
  year: null,
  monitorParty: undefined,
  uploadTime: undefined,
  iotId: undefined,
  status: 0 //设置默认状态正常
};
const data = reactive<PageData<QualityTrilateralForm, QualityTrilateralQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    year: [],
    params: {}
  },
  rules: {
    qualityReportId: [{ required: true, message: '监测报告id不能为空', trigger: 'blur' }],
    detectionTime: [{ required: true, message: '监测日期不能为空', trigger: 'blur' }],
    qualityReportName: [{ required: true, message: '报告名称不能为空', trigger: 'blur' }],
    year: [{ required: true, message: '监测年度不能为空', trigger: 'change' }],
    monitorParty: [{ required: true, message: '监测第三方不能为空', trigger: 'blur' }],
    iotId: [{ required: true, message: '注水站不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询采出水第三方水质监测报告列表 */
const getList = async () => {
  loading.value = true;
  if (queryParams.value.year && queryParams.value.year.length === 2) {
    queryParams.value.startYear = queryParams.value.year[0];
    queryParams.value.endYear = queryParams.value.year[1];
  } else {
    queryParams.value.startYear = undefined;
    queryParams.value.endYear = undefined;
  }
  queryParams.value.year = undefined;
  const res = await listQualityTrilateral(queryParams.value);
  qualityTrilateralList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const getInjectionStation = () => {
  const query = {
    bjlxId: 93
  };
  selectAllCommdevData(query).then((res) => {
    injectionStationList.value = res.data;
    stationMap.value = new Map(res.data.map((item) => [item.id, item.mingZi]));
  });
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  qualityTrilateralFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.year = [];
  queryParams.value.startYear = undefined;
  queryParams.value.endYear = undefined;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: QualityTrilateralVO[]) => {
  ids.value = selection.map((item) => item.qualityReportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

const formatIotIds = (iotIds: string | undefined): string => {
  if (!iotIds) return '-';

  const idArray = iotIds.split(',');
  return idArray
    .map((id) => {
      const stationName = stationMap.value.get(Number(id));
      return stationName || `未知站点(${id})`;
    })
    .join('、');
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加采出水第三方水质监测报告';
};

const handlePreview = async (row?: QualityTrilateralVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.qualityReportId,
    attachSourceType: 'elCompany',
    attachCategory: 'qualityReport'
  });
};
const handleDetail = async (row?: QualityTrilateralVO) => {
  reset();
  const _qualityReportId = row?.qualityReportId || ids.value[0];
  const res = await getQualityTrilateral(_qualityReportId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '采出水第三方水质监测报告详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: QualityTrilateralVO) => {
  reset();
  const _qualityReportId = row?.qualityReportId || ids.value[0];
  const res = await getQualityTrilateral(_qualityReportId);
  Object.assign(form.value, res.data);
  if (form.value.iotId) {
    form.value.iotId = form.value.iotId.split(',').map(Number);
  }
  dialog.visible = true;
  dialog.title = '修改采出水第三方水质监测报告';
};

/** 提交按钮 */
const submitForm = () => {
  qualityTrilateralFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      form.value.iotId = form.value.iotId.join(',');
      buttonLoading.value = true;
      if (form.value.qualityReportId) {
        await updateQualityTrilateral(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addQualityTrilateral(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: QualityTrilateralVO) => {
  const _qualityReportIds = row?.qualityReportId || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除采出水第三方水质监测报告编号为"' + _qualityReportIds + '"的数据项？')
    .finally(() => (loading.value = false));
  await delQualityTrilateral(_qualityReportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/qualityTrilateral/export',
    {
      ...queryParams.value
    },
    `水质监测报告_${new Date().getTime()}.xlsx`
  );
};

const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
  getInjectionStation();
});
</script>
<style lang="scss" scoped>
:deep(.el-date-editor) {
  --el-date-editor-width: 100%;
}
</style>
