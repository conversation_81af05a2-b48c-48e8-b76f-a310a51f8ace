import{d as ne,h as se,ak as re,r,b as ie,cR as ue,c as ce,o as d,p as t,t as a,a7 as de,G as me,a8 as U,e as n,aI as pe,w as S,q as fe,M as _e,A as ge,H as we,K as ve,J as c,am as T,x as p,ay as ye,y,ax as be,z as he,aw as ke,aJ as Ce,v as Se,az as Ee,ai as Ne}from"./index-D07cMzhp.js";import{_ as Ie}from"./submitVerify.vue_vue_type_script_setup_true_lang-Q_6E6W-r.js";import{E as Ve}from"./el-row-CikYE3zA.js";import{_ as Te}from"./index-BWMgqvQ9.js";import{E as ze}from"./el-col-BaG5Rg5z.js";import{E as Re}from"./el-tree-DW6MoFaI.js";import{e as $e,d as Be,c as xe}from"./index-BNvpwQ8u.js";import{c as De}from"./index-Byo3q9o3.js";import{w as Pe}from"./index-BpaKvpB0.js";import"./index-Bd4DOhoC.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";/* empty css                          */import"./index-3RY37FEX.js";import"./index.vue_vue_type_script_setup_true_lang-CzmK4ti3.js";import"./index-BdvXA74M.js";const Ae={class:"p-2"},Fe={class:"mb-[10px]"},lt=ne({__name:"myDocument",setup(Ue){const{proxy:s}=se(),{wf_business_status:q}=re(s==null?void 0:s.useDict("wf_business_status")),z=r(),R=r(),m=r(!0),J=r([]),$=r([]),L=r(!0),K=r(!0),w=r(!0),E=r(0),B=r([]),x=r([]),b=r(""),N=r("running"),i=r({pageNum:1,pageSize:10,flowCode:void 0,category:void 0});ie(()=>{f(),M()});const O=l=>{i.value.category=l.id,l.id==="0"&&(i.value.category=""),v()},Q=(l,e)=>l?e.categoryName.indexOf(l)!==-1:!0;ue(()=>{R.value.filter(b.value)},{flush:"post"});const M=async()=>{const l=await De();x.value=l.data},v=()=>{f()},G=()=>{var l;(l=z.value)==null||l.resetFields(),i.value.category="",i.value.pageNum=1,i.value.pageSize=10,v()},H=l=>{J.value=l.map(e=>e.businessId),$.value=l.map(e=>e.id),L.value=l.length!==1,K.value=!l.length},f=()=>{m.value=!0,$e(i.value).then(l=>{B.value=l.rows,E.value=l.total,m.value=!1})},j=async l=>{const e=l.id||$.value;await(s==null?void 0:s.$modal.confirm("是否确认删除？")),m.value=!0,N.value==="running"&&(await Be(e).finally(()=>m.value=!1),f()),s==null||s.$modal.msgSuccess("删除成功")},W=async l=>{await(s==null?void 0:s.$modal.confirm("是否确认撤销当前单据？")),m.value=!0,N.value==="running"&&(await xe({businessId:l,message:"申请人撤销流程！"}).finally(()=>m.value=!1),f()),s==null||s.$modal.msgSuccess("撤销成功")},D=async(l,e)=>{const h=Ne({businessId:l.businessId,taskId:l.id,type:e,formCustom:l.formCustom,formPath:l.formPath});Pe.routerJump(h,s)};return(l,e)=>{const h=me,X=Re,I=de,_=ze,P=ge,g=ve,Y=_e,Z=Te,k=Ve,u=be,A=ke,ee=Ce,te=ye,ae=Ee,le=Ie,oe=Se;return d(),ce("div",Ae,[t(k,{gutter:20},{default:a(()=>[t(_,{lg:4,xs:24,style:{}},{default:a(()=>[t(I,{shadow:"hover"},{default:a(()=>[t(h,{modelValue:n(b),"onUpdate:modelValue":e[0]||(e[0]=C=>U(b)?b.value=C:null),placeholder:"请输入流程分类名","prefix-icon":"Search",clearable:""},null,8,["modelValue"]),t(X,{ref_key:"categoryTreeRef",ref:R,class:"mt-2","node-key":"id",data:n(x),props:{label:"label",children:"children"},"expand-on-click-node":!1,"filter-node-method":Q,"highlight-current":"","default-expand-all":"",onNodeClick:O},null,8,["data"])]),_:1})]),_:1}),t(_,{lg:20,xs:24},{default:a(()=>{var C,F;return[t(pe,{"enter-active-class":(C=n(s))==null?void 0:C.animate.searchAnimate.enter,"leave-active-class":(F=n(s))==null?void 0:F.animate.searchAnimate.leave},{default:a(()=>[S(fe("div",Fe,[t(I,{shadow:"hover"},{default:a(()=>[S(t(Y,{ref_key:"queryFormRef",ref:z,model:n(i),inline:!0,"label-width":"120px"},{default:a(()=>[t(P,{label:"流程定义编码",prop:"flowCode"},{default:a(()=>[t(h,{modelValue:n(i).flowCode,"onUpdate:modelValue":e[1]||(e[1]=o=>n(i).flowCode=o),placeholder:"请输入流程定义编码",onKeyup:we(v,["enter"])},null,8,["modelValue"])]),_:1}),t(P,null,{default:a(()=>[t(g,{type:"primary",icon:"Search",onClick:v},{default:a(()=>e[5]||(e[5]=[c("搜索")])),_:1}),t(g,{icon:"Refresh",onClick:G},{default:a(()=>e[6]||(e[6]=[c("重置")])),_:1})]),_:1})]),_:1},8,["model"]),[[T,n(w)]])]),_:1})],512),[[T,n(w)]])]),_:1},8,["enter-active-class","leave-active-class"]),t(I,{shadow:"hover"},{header:a(()=>[t(k,{gutter:10,class:"mb8"},{default:a(()=>[t(Z,{"show-search":n(w),"onUpdate:showSearch":e[2]||(e[2]=o=>U(w)?w.value=o:null),onQueryTable:v},null,8,["show-search"])]),_:1})]),default:a(()=>[S((d(),p(te,{data:n(B),border:"",onSelectionChange:H},{default:a(()=>[t(u,{type:"selection",width:"55",align:"center"}),t(u,{align:"center",type:"index",label:"序号",width:"60"}),y("",!0),t(u,{"show-overflow-tooltip":!0,prop:"flowName",align:"center",label:"流程定义名称"}),t(u,{align:"center",prop:"flowCode",label:"流程定义编码"}),t(u,{align:"center",prop:"categoryName",label:"流程分类"}),t(u,{align:"center",prop:"version",label:"版本号",width:"90"},{default:a(o=>[c(" v"+he(o.row.version)+".0",1)]),_:1}),n(N)==="running"?(d(),p(u,{key:1,align:"center",prop:"isSuspended",label:"状态","min-width":"70"},{default:a(o=>[o.row.isSuspended?(d(),p(A,{key:1,type:"danger"},{default:a(()=>e[8]||(e[8]=[c("挂起")])),_:1})):(d(),p(A,{key:0,type:"success"},{default:a(()=>e[7]||(e[7]=[c("激活")])),_:1}))]),_:1})):y("",!0),t(u,{align:"center",label:"流程状态","min-width":"70"},{default:a(o=>[t(ee,{options:n(q),value:o.row.flowStatus},null,8,["options","value"])]),_:1}),t(u,{align:"center",prop:"createTime",label:"启动时间",width:"160"}),t(u,{align:"center",label:"操作",width:"162"},{default:a(o=>[t(k,{gutter:10,class:"mb8"},{default:a(()=>[o.row.flowStatus==="draft"||o.row.flowStatus==="cancel"||o.row.flowStatus==="back"?(d(),p(_,{key:0,span:1.5},{default:a(()=>[t(g,{type:"primary",size:"small",icon:"Edit",onClick:V=>D(o.row,"update")},{default:a(()=>e[9]||(e[9]=[c("编辑")])),_:2},1032,["onClick"])]),_:2},1024)):y("",!0),o.row.flowStatus==="draft"||o.row.flowStatus==="cancel"||o.row.flowStatus==="back"?(d(),p(_,{key:1,span:1.5},{default:a(()=>[t(g,{type:"primary",size:"small",icon:"Delete",onClick:V=>j(o.row)},{default:a(()=>e[10]||(e[10]=[c("删除")])),_:2},1032,["onClick"])]),_:2},1024)):y("",!0)]),_:2},1024),t(k,{gutter:10,class:"mb8"},{default:a(()=>[t(_,{span:1.5},{default:a(()=>[t(g,{type:"primary",size:"small",icon:"View",onClick:V=>D(o.row,"view")},{default:a(()=>e[11]||(e[11]=[c("查看")])),_:2},1032,["onClick"])]),_:2},1024),o.row.flowStatus==="waiting"?(d(),p(_,{key:0,span:1.5},{default:a(()=>[t(g,{type:"primary",size:"small",icon:"Notification",onClick:V=>W(o.row.businessId)},{default:a(()=>e[12]||(e[12]=[c("撤销")])),_:2},1032,["onClick"])]),_:2},1024)):y("",!0)]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[oe,n(m)]]),S(t(ae,{page:n(i).pageNum,"onUpdate:page":e[3]||(e[3]=o=>n(i).pageNum=o),limit:n(i).pageSize,"onUpdate:limit":e[4]||(e[4]=o=>n(i).pageSize=o),total:n(E),onPagination:f},null,8,["page","limit","total"]),[[T,n(E)>0]])]),_:1})]}),_:1})]),_:1}),t(le,{ref:"submitVerifyRef",onSubmitCallback:f},null,512)])}}});export{lt as default};
