<template>
  <div class="p-3">
    <el-form :inline="true" :model="queryParams" label-width="120">
      <el-form-item label="临时用水批复函">
        <el-select v-model="queryParams.areaId" placeholder="全部" clearable @change="getWaterPlan">
          <el-option
            v-for="deptment in licenseList"
            :key="deptment.waterDrawingLicenseId"
            :label="deptment.waterDrawingLicenseCode"
            :value="deptment.waterDrawingLicenseId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-hasPermi="['epms:plan:listWithIndicator']" icon="Search" type="primary" @click="queryJiHua">查询</el-button>
        <el-button type="primary" plain icon="Plus" v-hasPermi="['epms:plan:add']" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table stripe ref="table" :data="scjhList" border height="100%">
      <el-table-column type="index" label="序号" width="80px" align="center" fixed="left"></el-table-column>
      <el-table-column align="center" label="临时用水批复函" prop="areaId" fixed="left">
        <template v-slot:default="scope">
          <span>{{ getCode(scope.row.areaId) }}</span>
        </template>
      </el-table-column>
      <el-table-column v-for="item in indicatorList" :key="item.id" :label="item.indicatorName + '(' + item.indicatorUnitName + ')'" align="center">
        <template v-slot:default="scope">
          <span>{{ showIndicator(scope.row, item) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" prop="remark"></el-table-column>

      <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
        <template #default="scope">
          <el-button v-hasPermi="['epms:plan:edit']" icon="Edit" type="text" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['epms:plan:remove']" icon="Delete" type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="640px">
      <el-form ref="form" :model="form" label-width="140px" :rules="addFormRules">
        <el-form-item label="取水批复函" prop="areaId">
          <el-select v-model="form.areaId" placeholder="选择取水批复函" style="width: 100%" @change="getWaterPlan">
            <el-option
              v-for="deptment in licenseList"
              :key="deptment.waterDrawingLicenseId"
              :label="deptment.waterDrawingLicenseCode"
              :value="deptment.waterDrawingLicenseId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="form.remark"></el-input>
        </el-form-item>

        <template v-for="item in indicatorList" :key="item.id">
          <el-form-item :label="item.indicatorName">
            <el-input type="number" v-model="form.indicatorDetail[item.indicatorId]">
              <template #append>{{ item.indicatorUnitName }}</template>
            </el-input>
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { addPlan, delPlan, listPlanWithIndicator, queryTempIndicatorMap, updatePlan } from '@/api/epms/eptw/plan/plan.js';
import { listLicense } from '@/api/epms/eptw/license/index';

export default {
  name: 'areaPlan',

  data() {
    let that = this;
    return {
      licenseQuery: {
        licenseType: 1
      },
      licenseList: [],
      PlanType: 2,
      PeriodType: 1,
      indicatorList: [],
      scjhList: [],
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        areaId: null,
        indicatorPlanType: 2, //取水计划
        periodType: 1, //年度计划
        areaType: 103 //部件类型-取水证
      },
      // 弹出层标题
      title: '',
      isEdit: false,
      // 是否显示弹出层
      open: false,
      form: {
        // planSourceType: 1,
        indicatorPlanId: null,
        yearId: null,
        areaId: null,
        remark: null,
        indicatorPlanType: 2,
        periodType: 1,
        areaType: 103, //部件类型-取水证
        indicatorDetail: {}
      },
      addFormRules: {
        areaId: [{ required: true, message: '请选择取水批复函', trigger: 'blur' }]
      }
    };
  },

  created() {
    this.getParkList();
    this.getList();
    this.getindicatorList();
  },
  methods: {
    /** 读取生产计划+年计划的指标库列表 */
    async getindicatorList() {
      queryTempIndicatorMap({ templatePlanType: this.PlanType, periodType: this.PeriodType }).then((res) => {
        this.indicatorList = res.data.data;
      });

      if (this.indicatorList.length < 1) {
        return;
      }
      this.indicatorList.forEach((item) => {
        this.form.indicatorDetail[item.indicatorId] = 0;
      });
      await this.$nextTick(() => {
        this.$refs['table'].doLayout();
      });
    },
    /** 查询计划列表 */
    queryJiHua() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    async getList() {
      listPlanWithIndicator(this.queryParams).then((res) => {
        this.total = res.total;
        this.scjhList = res.rows;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isEdit = true;
      // this.form.planSourceType = 1
      this.form.areaType = 103;
      this.title = '取水计划';
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.isEdit = true;
      this.getParkList();
      // 深拷贝行数据
      this.form = JSON.parse(JSON.stringify(row));
      this.form.yearId = this.form.yearId + '';
      this.open = true;
      this.title = '修改计划';
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.indicatorPlanId != null) {
            updatePlan(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.reset();
              this.getList();
            });
          } else {
            addPlan(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.reset();
              this.getList();
            });
          }
        }
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        indicatorPlanId: null,
        areaId: null,
        remark: null,
        indicatorPlanType: this.PlanType,
        periodType: this.PeriodType,
        areaType: this.areaType,
        indicatorDetail: {}
      };
      this.form.resetFields;
    },
    handleFj(row) {
      this.$router.push({ path: '/intake/plan/monthPlan', query: { planId: row.indicatorPlanId } });
    },
    showIndicator(row, indicator) {
      //显示
      return row.indicatorDetail[indicator.indicatorId];
    },
    handleDelete(row) {
      ElMessageBox.confirm('确认删除吗？', '删除确认', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then((action) => {
        if (action === 'confirm') {
          let delParam = {
            indicatorPlanType: 2,
            indicatorPlanId: row.indicatorPlanId,
            periodType: 1,
            yearId: row.yearId,
            areaType: 103 //部件类型-作业区级
          };
          delPlan(delParam).then((res) => {
            ElMessage({
              message: '删除成功',
              type: 'success'
            });
            this.getList();
          });
        }
      });
    },
    handleExport() {
      this.download(
        'epms/plan/export',
        {
          ...this.queryParams
        },
        `plan_${new Date().getTime()}.xlsx`
      );
    },
    /** 计划调度页面添加计划单位下拉框管理列表 */
    async getParkList() {
      await listLicense(this.licenseQuery).then((res) => {
        this.licenseList = res.rows;
        console.log(this.licenseList);
      });
    },
    getCode(areaId) {
      const foundItem = this.licenseList.find((item) => item.waterDrawingLicenseId === areaId);
      return foundItem ? foundItem.waterDrawingLicenseCode : '';
    },
    getWaterPlan() {
      let query = {};
      query.operationAreaId = this.form.areaId;
      listLicense(query).then((res) => {
        if (res.rows && res.rows.length > 0) {
          const latestItem = res.rows.reduce((latest, current) => {
            return new Date(current.startTime) > new Date(latest.startTime) ? current : latest;
          });
          console.log(latestItem);
          this.form.indicatorDetail = { 1: latestItem.waterDrawingAmount / 10000 };
          // this.form.waterDrawingLicenseId = latestItem.waterDrawingLicenseId;
        } else {
          console.log('没有找到数据');
        }
      });
    }
  }
};
</script>
<style scoped>
input[type='number'] {
  -moz-appearance: textfield;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
