import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StandardDocumentForm, StandardDocumentQuery, StandardDocumentVO } from '@/api/epms/epcom/standardDocument/types';

/**
 * 查询管理依据列表
 * @param query
 * @returns {*}
 */

export const listStandardDocument = (query?: StandardDocumentQuery): AxiosPromise<StandardDocumentVO[]> => {
  return request({
    url: '/epms/standardDocument/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询管理依据详细
 * @param documentId
 */
export const getStandardDocument = (documentId: string | number): AxiosPromise<StandardDocumentVO> => {
  return request({
    url: '/epms/standardDocument/' + documentId,
    method: 'get'
  });
};

/**
 * 新增管理依据
 * @param data
 */
export const addStandardDocument = (data: StandardDocumentForm) => {
  return request({
    url: '/epms/standardDocument',
    method: 'post',
    data: data
  });
};

/**
 * 修改管理依据
 * @param data
 */
export const updateStandardDocument = (data: StandardDocumentForm) => {
  return request({
    url: '/epms/standardDocument',
    method: 'put',
    data: data
  });
};

/**
 * 删除管理依据
 * @param documentId
 */
export const delStandardDocument = (documentId: string | number | Array<string | number>) => {
  return request({
    url: '/epms/standardDocument/' + documentId,
    method: 'delete'
  });
};
