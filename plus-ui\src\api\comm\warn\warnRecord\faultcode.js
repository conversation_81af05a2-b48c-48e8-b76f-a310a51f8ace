import request from '@/utils/request.js';

// 查询故障码配置列表
export function listFaultcode(query) {
  return request({
    url: '/dbedit/faultcode/list',
    method: 'get',
    params: query
  });
}

// 查询故障码配置详细
export function getFaultcode(faultcodeid) {
  return request({
    url: '/dbedit/faultcode/' + faultcodeid,
    method: 'get'
  });
}

// 新增故障码配置
export function addFaultcode(data) {
  return request({
    url: '/dbedit/faultcode',
    method: 'post',
    data: data
  });
}

// 修改故障码配置
export function updateFaultcode(data) {
  return request({
    url: '/dbedit/faultcode',
    method: 'put',
    data: data
  });
}

// 删除故障码配置
export function delFaultcode(faultcodeid) {
  return request({
    url: '/dbedit/faultcode/' + faultcodeid,
    method: 'delete'
  });
}

// 查询pmWarnSet列表
export function allListPmWarnSet(query) {
  return request({
    url: '/dbedit/set/allList',
    method: 'get',
    params: query
  });
}
// 查询bizConfig列表
export function allListBizConfig(query) {
  return request({
    url: '/dbedit/config/allList',
    method: 'get',
    params: query
  });
}

// 查询参数列表
export function getConfigList(data) {
  return request({
    url: '/dbedit/bizconfig/dict',
    method: 'get',
    params: data
  });
}
//根据config_id和config_sub_id查询业务字典
export function getDict(data) {
  return request({
    url: '/dbedit/bizconfig/getDict',
    method: 'get',
    params: data
  });
}
