import{aC as t}from"./index-D07cMzhp.js";const l=p=>t({url:"/epms/eppcs/samplingApplication/list",method:"get",params:p}),o=p=>t({url:"/epms/eppcs/samplingApplication/"+p,method:"get"}),m=p=>t({url:"/epms/eppcs/samplingApplication",method:"post",data:p}),r=p=>t({url:"/epms/eppcs/samplingApplication",method:"put",data:p}),c=p=>t({url:"/epms/eppcs/samplingApplication/"+p,method:"delete"}),u=(p,a=1)=>t({url:"/epms/eppcs/samplingApplication/submit/"+p,method:"post",params:{userType:a}}),g=(p,a,i,s,n)=>t({url:"/epms/eppcs/samplingApplication/approve/"+p,method:"post",params:{userType:a,action:i,approvalRemark:s,approver:n}});export{m as a,g as b,c as d,o as g,l,u as s,r as u};
