package com.biz.epms.eppcs.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.biz.epms.eppcs.domain.EppcsUnsealingInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 拆封信息视图对象 eppcs_unsealing_info
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = EppcsUnsealingInfo.class)
public class EppcsUnsealingInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 拆封id
     */
    @ExcelProperty(value = "拆封id")
    private Long unsealingId;

    /**
     * 关联申请ID
     */
    @ExcelProperty(value = "关联申请ID")
    private Long applicationId;


    /**
     * 实验室封条照片附件
     */
    @ExcelProperty(value = "实验室封条照片附件")
    private String unsealPhoto;

    /**
     * 拆封时间
     */
    @ExcelProperty(value = "拆封时间")
    private Date unsealTime;

    /**
     * 拆封人
     */
    @ExcelProperty(value = "拆封人")
    private String unsealer;

    /**
     * 拆封状态 (0草稿,1驳回,2待确认,3已确认)
     */
    @ExcelProperty(value = "拆封状态")
    private Integer unsealingStatus;

    /**
     * 拆封状态描述
     */
    @ExcelProperty(value = "拆封状态描述")
    private String unsealingStatusDesc;

    /**
     * 审批时间
     */
    @ExcelProperty(value = "审批时间")
    private Date approvalTime;

    /**
     * 审批建议
     */
    @ExcelProperty(value = "审批建议")
    private String approvalRemark;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 检测项目名称
     */
    @ExcelProperty(value = "检测项目名称")
    private String projectName;

    /**
     * 审批人
     */
    @ExcelProperty(value = "审批人")
    private String approver;

}
