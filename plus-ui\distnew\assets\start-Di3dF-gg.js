import{d as $e,a as ll,h as al,ak as De,r as g,ai as H,b as tl,aH as ol,c as f,o as n,p as t,t as o,w,q as N,a7 as rl,M as nl,e as a,A as dl,B as pl,F as V,C as c,x as u,D as ul,G as il,H as L,K as sl,J as m,am as Ye,aI as ml,ay as vl,ax as fl,z as R,aJ as bl,aL as yl,y as de,v as gl,az as Vl,a8 as cl,aA as _l,cu as kl}from"./index-Bm6k27Yz.js";/* empty css                       */import{E as wl,a as Tl}from"./el-radio-ChAULzCj.js";import{_ as Il}from"./index-CKygFLbI.js";import{E as Cl}from"./el-row-DPMJBKHh.js";import{_ as Ul}from"./index-cPx5H1kc.js";import{E as Al}from"./el-col-BjQCqZOL.js";import{E as Pl}from"./el-date-picker-CIMm2f24.js";import{l as Rl,c as Dl,g as Ee,u as Yl,a as El,d as $l}from"./index-DVbnU4Hd.js";import{e as pe}from"./index-BwNwGGl7.js";import{m as ue}from"./dayjs.min-Dqr9lGM-.js";import"./el-link-6M7kQRy3.js";import"./el-upload-CSOgWUCs.js";import"./el-progress-D8KwIucv.js";import"./el-tree-BnI61tiD.js";import"./index-Cf_aQ1ZR.js";const Nl={class:"p-2"},Sl={class:"mb-[10px]"},ql={key:0},Ml={key:1,style:{display:"inline-flex"}},Hl={key:2},xl={key:3,style:{display:"inline-flex","margin-top":"5px"}},Fl={class:"dialog-footer"},Ll={class:"dialog-footer"},Ql={class:"dialog-footer"},hl=$e({name:"TransportRecord"}),da=$e({...hl,setup(Kl){ll();const{proxy:s}=al(),{epfy_medium_category:Q,epfy_yexiang_medium_type:h,epfy_liquid_progress:Z,epfy_driver:Ne,epfy_application_status:K,epfy_sender:ie,epfy_superintendent:se}=De(s==null?void 0:s.useDict("epfy_medium_category","epfy_yexiang_medium_type","epfy_liquid_progress","epfy_driver","epfy_application_status","epfy_sender","epfy_superintendent")),ee=g([]),S=g(!1),z=g(!0),B=g(!0),j=g([]),q=H({}),me=g(!0),ve=g(!0),fe=g(),le=g(0),ae=g(""),D=g([]),be=g([]),ye=g(),O=g(),ge=g({}),te=g(1),Se=g(!1),oe=g(!1),Ve=g(null),A=H({visible:!1,title:""}),Y=H({visible:!1,title:""}),M=H({visible:!1,title:"选择拉运申请"}),C=H({applicationId:void 0,departurePoint:void 0}),ce={transportId:void 0,applicationId:void 0,transportTime:ue().format("YYYY-MM-DD HH:mm:ss"),mediumCategory:2,mediumType:void 0,departurePoint:void 0,number:void 0,sender:void 0,transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,remark:void 0,measurementVoucher:void 0,flowType:1,photo:void 0,unloadApproval:1,superintendentApproval:1,senderApproval:1},qe=H({form:{...ce},queryParams:{pageNum:1,pageSize:10,applicationId:void 0,transportTime:void 0,mediumCategory:2,mediumType:void 0,departurePoint:void 0,number:void 0,sender:void 0,transporter:void 0,licensePlate:void 0,arrivalPoint:void 0,receiver:void 0,measurementVoucher:void 0,photo:void 0,flowType:1,unloadLocationType:1,appName:void 0,params:{}},rules:{applicationId:[{required:!0,message:"拉运申请不能为空",trigger:"change"}],transportTime:[{required:!0,message:"拉运时间不能为空",trigger:"blur"}],mediumCategory:[{required:!0,message:"分类不能为空",trigger:"change"}],mediumType:[{required:!0,message:"介质类型不能为空",trigger:"change"}],departurePoint:[{required:!0,message:"起运点不能为空",trigger:"blur"}],number:[{required:!0,message:"数量不能为空",trigger:"blur"}],sender:[{required:!0,message:"施工单位负责人不能为空",trigger:"blur"}],transporter:[{required:!0,message:"司机不能为空",trigger:"blur"}],licensePlate:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],arrivalPoint:[{required:!0,message:"接收点不能为空",trigger:"blur"}],receiver:[{required:!0,message:"卸液点负责人不能为空",trigger:"blur"}]}}),{queryParams:v,form:r,rules:_e}=De(qe),Me=()=>{kl().then(d=>{ae.value=d.data.user.nickName})},E=async()=>{z.value=!0;const d=await Rl(v.value);Le(),ee.value=d.rows;const l=ee.value.map(p=>p.applicationId),i=await pe({appIds:l.join(",")});ge.value=i.rows.reduce((p,y)=>(p[y.appId]=`${y.appName}-${y.wellName}-${y.applicationDate}`,p),{}),le.value=d.total,z.value=!1},He=()=>{if(C.applicationId){const d=D.value.find(l=>l.appId===C.applicationId);d&&(C.departurePoint=d.loadingLocation)}},xe=async()=>{var d;(d=fe.value)==null||d.validate(async l=>{if(l){const i=Ve.value;if(!i){s==null||s.$modal.msgError("复制失败：未找到原始记录");return}i.applicationId=C.applicationId,i.departurePoint=C.departurePoint;try{const p=await Dl(i);M.visible=!1,s==null||s.$modal.msgSuccess("复制成功，请在最新记录中编辑"),await E()}catch{s==null||s.$modal.msgError("复制失败")}}})},Fe=()=>{M.visible=!1},Le=()=>{q.flowType=1,q.unloadLocationType=1,q.mediumCategory=2,q.applicationStatus=2,D.value=[],pe(q).then(l=>{l.rows.forEach(i=>{i.appName=`${i.appName}-${i.wellName}-${i.applicationDate}`}),D.value=l.rows});let d={...q};d.transportStatus=0,pe(d).then(l=>{l.rows.forEach(i=>{i.appName=`${i.appName}-${i.wellName}-${i.applicationDate}`}),be.value=l.rows})},ke=()=>{G(),Y.visible=!1,A.visible=!1},G=()=>{var d;r.value={...ce},(d=O.value)==null||d.resetFields()},$=()=>{v.value.pageNum=1,E()},Qe=()=>{var d;(d=ye.value)==null||d.resetFields(),$()},he=d=>{D.value.forEach(l=>{l.appId===d&&(r.value.mediumType=l.mediumType?l.mediumType:void 0)})},Ke=d=>{j.value=d.map(l=>l.transportId),me.value=d.length!=1,ve.value=!d.length},ze=()=>{G(),oe.value=!1,r.value.superintendent=ae.value,A.visible=!0,A.title="添加装液记录"},Be=async d=>{s==null||s.download("epfy/transportApplication/downloadMeasurementVoucher",{appId:d==null?void 0:d.applicationId},"二连分公司废液收集计量凭证.xls")},we=async d=>{G(),oe.value=!0;const l=(d==null?void 0:d.transportId)||j.value[0],i=await Ee(l);Object.assign(r.value,i.data),r.value.superintendent=ae.value,A.visible=!0,A.title="修改装液记录"},je=async d=>{Ve.value=d,M.visible=!0,C.applicationId=void 0,C.departurePoint=void 0},Te=async(d,l)=>{G();const i=(d==null?void 0:d.transportId)||j.value[0],p=await Ee(i);Object.assign(r.value,p.data),te.value=l,Se.value=!0,Y.visible=!0,Y.title="审核装液记录"},J=async(d,l,i)=>{s.showAttachPreview({attachSourceId:i?null:d==null?void 0:d.transportId,attachSourceType:"yxTransportRecord",attachCategory:i?null:l,ids:i})},re=d=>{var l;d?(r.value.senderApproval=1,r.value.superintendentApproval=1):(r.value.senderApproval=r.value.senderApproval?r.value.senderApproval:1,r.value.superintendentApproval=r.value.superintendentApproval?r.value.superintendentApproval:1),r.value.senderApproval==2&&!r.value.senderApprovalTime&&(r.value.senderApprovalTime=ue().format("YYYY-MM-DD HH:mm:ss")),r.value.superintendentApproval==2&&!r.value.superintendentApprovalTime&&(r.value.superintendentApprovalTime=ue().format("YYYY-MM-DD HH:mm:ss")),(l=O.value)==null||l.validate(async i=>{i&&(S.value=!0,r.value.transportId?await Yl(r.value).finally(()=>S.value=!1):await El(r.value).finally(()=>S.value=!1),s==null||s.$modal.msgSuccess("操作成功"),A.visible=!1,Y.visible=!1,await E())})},Ie=async d=>{const l=(d==null?void 0:d.transportId)||j.value;await(s==null?void 0:s.$modal.confirm('是否确认删除拉运记录编号为"'+l+'"的数据项？').finally(()=>z.value=!1)),await $l(l),s==null||s.$modal.msgSuccess("删除成功"),await E()},Oe=()=>{s==null||s.download("epfy/transportRecord/export",{...v.value},`拉运记录_${new Date().getTime()}.xlsx`)},Ge=()=>{let d=D.value.find(l=>l.appId===r.value.applicationId);r.value.departurePoint=d.loadingLocation};return tl(()=>{E(),Me()}),(d,l)=>{var Pe,Re;const i=Pl,p=dl,y=ul,_=pl,T=il,b=sl,W=nl,Ce=rl,X=Al,Je=Ul,We=Cl,k=fl,x=bl,U=yl,Xe=vl,Ze=Vl,F=Il,ne=_l,Ue=Tl,Ae=wl,I=ol("hasPermi"),el=gl;return n(),f("div",Nl,[t(ml,{"enter-active-class":(Pe=a(s))==null?void 0:Pe.animate.searchAnimate.enter,"leave-active-class":(Re=a(s))==null?void 0:Re.animate.searchAnimate.leave},{default:o(()=>[w(N("div",Sl,[t(Ce,{shadow:"hover"},{default:o(()=>[t(W,{ref_key:"queryFormRef",ref:ye,model:a(v),inline:!0,"label-width":"100px"},{default:o(()=>[t(p,{label:"拉运时间",prop:"monthQuery"},{default:o(()=>[t(i,{modelValue:a(v).monthQuery,"onUpdate:modelValue":l[0]||(l[0]=e=>a(v).monthQuery=e),clearable:"",placeholder:"请选择拉运时间",type:"date","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),t(p,{label:"分类",prop:"mediumCategory"},{default:o(()=>[t(_,{modelValue:a(v).mediumCategory,"onUpdate:modelValue":l[1]||(l[1]=e=>a(v).mediumCategory=e),placeholder:"请选择分类",clearable:"",disabled:""},{default:o(()=>[(n(!0),f(V,null,c(a(Q),e=>(n(),u(y,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"介质类型",prop:"mediumType"},{default:o(()=>[t(_,{modelValue:a(v).mediumType,"onUpdate:modelValue":l[2]||(l[2]=e=>a(v).mediumType=e),placeholder:"请选择介质类型",clearable:""},{default:o(()=>[(n(!0),f(V,null,c(a(h),e=>(n(),u(y,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"起运点",prop:"departurePoint"},{default:o(()=>[t(T,{modelValue:a(v).departurePoint,"onUpdate:modelValue":l[3]||(l[3]=e=>a(v).departurePoint=e),clearable:"",placeholder:"请输入起运点",onKeyup:L($,["enter"])},null,8,["modelValue"])]),_:1}),t(p,{label:"车牌号",prop:"licensePlate"},{default:o(()=>[t(T,{modelValue:a(v).licensePlate,"onUpdate:modelValue":l[4]||(l[4]=e=>a(v).licensePlate=e),clearable:"",placeholder:"请输入车牌号",onKeyup:L($,["enter"])},null,8,["modelValue"])]),_:1}),t(p,{label:"接收点",prop:"arrivalPoint"},{default:o(()=>[t(T,{modelValue:a(v).arrivalPoint,"onUpdate:modelValue":l[5]||(l[5]=e=>a(v).arrivalPoint=e),clearable:"",placeholder:"请输入接收点",onKeyup:L($,["enter"])},null,8,["modelValue"])]),_:1}),t(p,{label:"卸液点负责人",prop:"receiver"},{default:o(()=>[t(T,{modelValue:a(v).receiver,"onUpdate:modelValue":l[6]||(l[6]=e=>a(v).receiver=e),clearable:"",placeholder:"请输入卸液点负责人",onKeyup:L($,["enter"])},null,8,["modelValue"])]),_:1}),t(p,{label:"拉运申请名称",prop:"appName"},{default:o(()=>[t(T,{modelValue:a(v).appName,"onUpdate:modelValue":l[7]||(l[7]=e=>a(v).appName=e),clearable:"",placeholder:"请输入拉运申请名称",onKeyup:L($,["enter"])},null,8,["modelValue"])]),_:1}),t(p,null,{default:o(()=>[t(b,{type:"primary",icon:"Search",onClick:$},{default:o(()=>l[57]||(l[57]=[m("搜索")])),_:1}),t(b,{icon:"Refresh",onClick:Qe},{default:o(()=>l[58]||(l[58]=[m("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[Ye,a(B)]])]),_:1},8,["enter-active-class","leave-active-class"]),t(Ce,{shadow:"never"},{header:o(()=>[t(We,{gutter:10,class:"mb8"},{default:o(()=>[t(X,{span:1.5},{default:o(()=>[w((n(),u(b,{icon:"Plus",plain:"",type:"primary",onClick:ze},{default:o(()=>l[59]||(l[59]=[m(" 新增 ")])),_:1})),[[I,["epfy:transportRecord:add"]]])]),_:1}),t(X,{span:1.5},{default:o(()=>[w((n(),u(b,{disabled:a(me),icon:"Edit",plain:"",type:"success",onClick:l[8]||(l[8]=e=>we())},{default:o(()=>l[60]||(l[60]=[m("修改 ")])),_:1},8,["disabled"])),[[I,["epfy:transportRecord:edit"]]])]),_:1}),t(X,{span:1.5},{default:o(()=>[w((n(),u(b,{disabled:a(ve),icon:"Delete",plain:"",type:"danger",onClick:l[9]||(l[9]=e=>Ie())},{default:o(()=>l[61]||(l[61]=[m("删除 ")])),_:1},8,["disabled"])),[[I,["epfy:transportRecord:remove"]]])]),_:1}),t(X,{span:1.5},{default:o(()=>[w((n(),u(b,{icon:"Download",plain:"",type:"warning",onClick:Oe},{default:o(()=>l[62]||(l[62]=[m("导出 ")])),_:1})),[[I,["epfy:transportRecord:export"]]])]),_:1}),t(Je,{showSearch:a(B),"onUpdate:showSearch":l[10]||(l[10]=e=>cl(B)?B.value=e:null),onQueryTable:E},null,8,["showSearch"])]),_:1})]),default:o(()=>[w((n(),u(Xe,{stripe:"",data:a(ee),onSelectionChange:Ke},{default:o(()=>[t(k,{align:"center",type:"selection",width:"55"}),t(k,{index:e=>(a(v).pageNum-1)*a(v).pageSize+e+1,align:"center",label:"序号",type:"index",width:"50"},null,8,["index"]),t(k,{align:"center",label:"拉运申请",prop:"applicationId",width:"300"},{default:o(e=>[m(R(a(ge)[e.row.applicationId]||"未知"),1)]),_:1}),t(k,{align:"center",label:"装液地点",prop:"departurePoint",width:"110"}),t(k,{align:"center",label:"分类",prop:"mediumCategory",width:"60"},{default:o(e=>[t(x,{options:a(Q),value:e.row.mediumCategory},null,8,["options","value"])]),_:1}),t(k,{label:"介质类型",align:"center",width:"120",prop:"mediumType"},{default:o(e=>[t(x,{options:a(h),value:e.row.mediumType},null,8,["options","value"])]),_:1}),t(k,{align:"center",label:"装液量(方)",prop:"number"}),t(k,{align:"center",label:"开始装液时间",prop:"transportTime",width:"110"},{default:o(e=>[N("span",null,R(d.parseTime(e.row.transportTime,"{y}-{m}-{d} {h}:{m}:{s}")),1)]),_:1}),t(k,{align:"center",label:"结束装液时间",prop:"transportEndTime",width:"110"},{default:o(e=>[N("span",null,R(d.parseTime(e.row.transportEndTime,"{y}-{m}-{d} {h}:{m}:{s}")),1)]),_:1}),t(k,{align:"center",label:"司机",prop:"transporter",width:"80"}),t(k,{align:"center",label:"车牌号",prop:"licensePlate",width:"100"}),t(k,{label:"施工进度",align:"center",prop:"progress"},{default:o(e=>[t(x,{options:a(Z),value:e.row.progress},null,8,["options","value"])]),_:1}),t(k,{label:"审核信息",align:"center","min-width":"230px"},{default:o(e=>[l[63]||(l[63]=m(" 现场监督: ")),[2].includes(e.row.superintendentApproval)?(n(),f("span",ql,R(e.row.superintendent)+" "+R(d.parseTime(e.row.superintendentApprovalTime,"{y}-{m}-{d}")),1)):(n(),f("span",Ml,[t(x,{options:a(K),value:e.row.senderApproval},null,8,["options","value"])])),l[64]||(l[64]=N("br",null,null,-1)),l[65]||(l[65]=m(" 施工负责人: ")),[2].includes(e.row.senderApproval)?(n(),f("span",Hl,R(e.row.sender)+" "+R(d.parseTime(e.row.senderApprovalTime,"{y}-{m}-{d}")),1)):(n(),f("span",xl,[t(x,{options:a(K),value:e.row.senderApproval},null,8,["options","value"])]))]),_:1}),t(k,{align:"center",label:"备注信息",prop:"remark",width:"100"}),t(k,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"附件预览",width:"220"},{default:o(e=>[t(U,{content:"装液图片",placement:"top"},{default:o(()=>[w((n(),u(b,{icon:"Document",link:"",type:"primary",onClick:P=>J(e.row,"photo",e.row.photo)},{default:o(()=>l[66]||(l[66]=[m("装液图片 ")])),_:2},1032,["onClick"])),[[I,["epfy:transportRecord:preview"]]])]),_:2},1024),t(U,{content:"量尺图片",placement:"top"},{default:o(()=>[w((n(),u(b,{icon:"Document",link:"",type:"primary",onClick:P=>J(e.row,"measuringPhoto",e.row.measuringPhoto)},{default:o(()=>l[67]||(l[67]=[m("量尺图片 ")])),_:2},1032,["onClick"])),[[I,["epfy:transportRecord:preview"]]])]),_:2},1024),t(U,{content:"罐车方量表",placement:"top"},{default:o(()=>[w((n(),u(b,{icon:"Document",link:"",type:"primary",onClick:P=>J(e.row,"tankerSquare",e.row.tankerSquare)},{default:o(()=>l[68]||(l[68]=[m("罐车方量表 ")])),_:2},1032,["onClick"])),[[I,["epfy:transportRecord:preview"]]])]),_:2},1024),t(U,{content:"铅封",placement:"top"},{default:o(()=>[w((n(),u(b,{icon:"Document",link:"",type:"primary",onClick:P=>J(e.row,"accessories",e.row.accessories)},{default:o(()=>l[69]||(l[69]=[m("铅封 ")])),_:2},1032,["onClick"])),[[I,["epfy:transportRecord:preview"]]])]),_:2},1024)]),_:1}),t(k,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作",width:"160"},{default:o(e=>[e.row.superintendent&&e.row.superintendentApproval!==2?(n(),u(U,{key:0,content:"现场监督审核",placement:"top"},{default:o(()=>[w((n(),u(b,{icon:"Coordinate",link:"",type:"primary",onClick:P=>Te(e.row,1)},{default:o(()=>l[70]||(l[70]=[m(" 现场监督审核 ")])),_:2},1032,["onClick"])),[[I,["epfy:transportRecord:super"]]])]),_:2},1024)):de("",!0),e.row.sender&&e.row.senderApproval!==2?(n(),u(U,{key:1,content:"施工负责人审核",placement:"top"},{default:o(()=>[w((n(),u(b,{icon:"Coordinate",link:"",type:"primary",onClick:P=>Te(e.row,2)},{default:o(()=>l[71]||(l[71]=[m(" 施工负责人审核 ")])),_:2},1032,["onClick"])),[[I,["epfy:transportRecord:send"]]])]),_:2},1024)):de("",!0),e.row.superintendentApproval===2&&e.row.senderApproval===2?(n(),u(U,{key:2,content:"计量凭证下载",placement:"top"},{default:o(()=>[t(b,{link:"",type:"primary",icon:"Download",onClick:P=>Be(e.row)},{default:o(()=>l[72]||(l[72]=[m("计量凭证下载")])),_:2},1032,["onClick"])]),_:2},1024)):de("",!0),t(U,{content:"复制",placement:"top"},{default:o(()=>[w((n(),u(b,{icon:"Edit",link:"",type:"primary",onClick:P=>je(e.row)},{default:o(()=>l[73]||(l[73]=[m("复制 ")])),_:2},1032,["onClick"])),[[I,["epfy:transportRecord:copy"]]])]),_:2},1024),t(U,{content:"修改",placement:"top"},{default:o(()=>[w((n(),u(b,{icon:"Edit",link:"",type:"primary",onClick:P=>we(e.row)},{default:o(()=>l[74]||(l[74]=[m("修改 ")])),_:2},1032,["onClick"])),[[I,["epfy:transportRecord:edit"]]])]),_:2},1024),t(U,{content:"删除",placement:"top"},{default:o(()=>[w((n(),u(b,{icon:"Delete",link:"",type:"primary",onClick:P=>Ie(e.row)},{default:o(()=>l[75]||(l[75]=[m("删除 ")])),_:2},1032,["onClick"])),[[I,["epfy:transportRecord:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[el,a(z)]]),w(t(Ze,{limit:a(v).pageSize,"onUpdate:limit":l[11]||(l[11]=e=>a(v).pageSize=e),page:a(v).pageNum,"onUpdate:page":l[12]||(l[12]=e=>a(v).pageNum=e),total:a(le),onPagination:E},null,8,["limit","page","total"]),[[Ye,a(le)>0]])]),_:1}),t(ne,{title:a(A).title,modelValue:a(A).visible,"onUpdate:modelValue":l[34]||(l[34]=e=>a(A).visible=e),width:"500px","append-to-body":""},{footer:o(()=>[N("div",Fl,[t(b,{loading:a(S),type:"warning",onClick:l[32]||(l[32]=e=>re(!0))},{default:o(()=>l[77]||(l[77]=[m("重新提交审核")])),_:1},8,["loading"]),t(b,{loading:a(S),type:"primary",onClick:l[33]||(l[33]=e=>re(!1))},{default:o(()=>l[78]||(l[78]=[m("确 定")])),_:1},8,["loading"]),t(b,{onClick:ke},{default:o(()=>l[79]||(l[79]=[m("取 消")])),_:1})])]),default:o(()=>[t(W,{ref_key:"transportRecordFormRef",ref:O,model:a(r),rules:a(_e),"label-width":"130px"},{default:o(()=>[a(oe)==!1?(n(),u(p,{key:0,label:"拉运申请",prop:"applicationId"},{default:o(()=>[t(_,{modelValue:a(r).applicationId,"onUpdate:modelValue":l[13]||(l[13]=e=>a(r).applicationId=e),filterable:"",placeholder:"请选择拉运申请",onChange:Ge},{default:o(()=>[(n(!0),f(V,null,c(a(be),e=>(n(),u(y,{key:e.appId,label:e.appName,value:e.appId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):(n(),u(p,{key:1,label:"拉运申请",prop:"applicationId"},{default:o(()=>[t(_,{modelValue:a(r).applicationId,"onUpdate:modelValue":l[14]||(l[14]=e=>a(r).applicationId=e),disabled:"",filterable:"",placeholder:"请选择拉运申请"},{default:o(()=>[(n(!0),f(V,null,c(a(D),e=>(n(),u(y,{key:e.appId,label:e.appName,value:e.appId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),t(p,{label:"装液地点",prop:"departurePoint"},{default:o(()=>[t(T,{modelValue:a(r).departurePoint,"onUpdate:modelValue":l[15]||(l[15]=e=>a(r).departurePoint=e),disabled:"",placeholder:"请输入起运点"},null,8,["modelValue"])]),_:1}),t(p,{label:"分类",prop:"mediumCategory"},{default:o(()=>[t(_,{modelValue:a(r).mediumCategory,"onUpdate:modelValue":l[16]||(l[16]=e=>a(r).mediumCategory=e),placeholder:"请选择分类",disabled:""},{default:o(()=>[(n(!0),f(V,null,c(a(Q),e=>(n(),u(y,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"介质类型",prop:"mediumType"},{default:o(()=>[t(_,{modelValue:a(r).mediumType,"onUpdate:modelValue":l[17]||(l[17]=e=>a(r).mediumType=e),placeholder:"请选择介质类型"},{default:o(()=>[(n(!0),f(V,null,c(a(h),e=>(n(),u(y,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"装液量",prop:"number"},{default:o(()=>[t(T,{modelValue:a(r).number,"onUpdate:modelValue":l[18]||(l[18]=e=>a(r).number=e),placeholder:"请输入装液量"},{append:o(()=>l[76]||(l[76]=[m("方")])),_:1},8,["modelValue"])]),_:1}),t(p,{label:"开始装液时间",prop:"transportTime"},{default:o(()=>[t(i,{modelValue:a(r).transportTime,"onUpdate:modelValue":l[19]||(l[19]=e=>a(r).transportTime=e),clearable:"",placeholder:"请选择拉运时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(p,{label:"结束装液时间",prop:"transportEndTime"},{default:o(()=>[t(i,{modelValue:a(r).transportEndTime,"onUpdate:modelValue":l[20]||(l[20]=e=>a(r).transportEndTime=e),clearable:"",placeholder:"请选择拉运时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(p,{label:"司机",prop:"transporter"},{default:o(()=>[t(_,{filterable:"",modelValue:a(r).transporter,"onUpdate:modelValue":l[21]||(l[21]=e=>a(r).transporter=e),placeholder:"选择司机","allow-create":"","default-first-option":""},{default:o(()=>[(n(!0),f(V,null,c(a(Ne),e=>(n(),u(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"车牌号",prop:"licensePlate"},{default:o(()=>[t(T,{modelValue:a(r).licensePlate,"onUpdate:modelValue":l[22]||(l[22]=e=>a(r).licensePlate=e),placeholder:"请输入车牌号"},null,8,["modelValue"])]),_:1}),t(p,{label:"施工进度",prop:"progress"},{default:o(()=>[t(_,{modelValue:a(r).progress,"onUpdate:modelValue":l[23]||(l[23]=e=>a(r).progress=e),placeholder:"请选择施工进度"},{default:o(()=>[(n(!0),f(V,null,c(a(Z),e=>(n(),u(y,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"现场监督审核人",prop:"superintendent"},{default:o(()=>[t(_,{filterable:"",modelValue:a(r).superintendent,"onUpdate:modelValue":l[24]||(l[24]=e=>a(r).superintendent=e),placeholder:"选择施工负责人","allow-create":"","default-first-option":""},{default:o(()=>[(n(!0),f(V,null,c(a(se),e=>(n(),u(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"施工负责人",prop:"sender"},{default:o(()=>[t(_,{filterable:"",modelValue:a(r).sender,"onUpdate:modelValue":l[25]||(l[25]=e=>a(r).sender=e),placeholder:"选择施工负责人","allow-create":"","default-first-option":""},{default:o(()=>[(n(!0),f(V,null,c(a(ie),e=>(n(),u(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"备注信息",prop:"remark"},{default:o(()=>[t(T,{modelValue:a(r).remark,"onUpdate:modelValue":l[26]||(l[26]=e=>a(r).remark=e),placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1}),t(p,{label:"装液照片",prop:"photo"},{default:o(()=>[t(F,{modelValue:a(r).photo,"onUpdate:modelValue":l[27]||(l[27]=e=>a(r).photo=e),"attach-source-id":a(r).transportId,disabled:!1,"attach-category":"photo","attach-source-type":"yxTransportRecord"},null,8,["modelValue","attach-source-id"])]),_:1}),t(p,{label:"量尺照片",prop:"measuringPhoto"},{default:o(()=>[t(F,{modelValue:a(r).measuringPhoto,"onUpdate:modelValue":l[28]||(l[28]=e=>a(r).measuringPhoto=e),"attach-source-id":a(r).transportId,disabled:!1,"attach-category":"measuringPhoto","attach-source-type":"yxTransportRecord"},null,8,["modelValue","attach-source-id"])]),_:1}),t(p,{label:"罐车方量表",prop:"tankerSquare"},{default:o(()=>[t(F,{modelValue:a(r).tankerSquare,"onUpdate:modelValue":l[29]||(l[29]=e=>a(r).tankerSquare=e),"attach-source-id":a(r).transportId,disabled:!1,"attach-category":"tankerSquare","attach-source-type":"yxTransportRecord"},null,8,["modelValue","attach-source-id"])]),_:1}),t(p,{label:"铅封",prop:"accessories"},{default:o(()=>[t(F,{modelValue:a(r).accessories,"onUpdate:modelValue":l[30]||(l[30]=e=>a(r).accessories=e),"attach-source-id":a(r).transportId,disabled:!1,"attach-category":"accessories","attach-source-type":"yxTransportRecord"},null,8,["modelValue","attach-source-id"])]),_:1}),t(p,{label:"计量凭证或转运单",prop:"measurementVoucher"},{default:o(()=>[t(F,{modelValue:a(r).measurementVoucher,"onUpdate:modelValue":l[31]||(l[31]=e=>a(r).measurementVoucher=e),"attach-source-id":a(r).transportId,disabled:!1,"attach-category":"transportMeasurement","attach-source-type":"yxTransportRecord"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(ne,{title:a(Y).title,modelValue:a(Y).visible,"onUpdate:modelValue":l[54]||(l[54]=e=>a(Y).visible=e),width:"500px","append-to-body":""},{footer:o(()=>[N("div",Ll,[t(b,{loading:a(S),type:"primary",onClick:l[53]||(l[53]=e=>re(!1))},{default:o(()=>l[81]||(l[81]=[m("确 定")])),_:1},8,["loading"]),t(b,{onClick:ke},{default:o(()=>l[82]||(l[82]=[m("取 消")])),_:1})])]),default:o(()=>[t(W,{ref_key:"transportRecordFormRef",ref:O,model:a(r),rules:a(_e),"label-width":"130px"},{default:o(()=>[t(p,{label:"拉运申请",prop:"applicationId"},{default:o(()=>[t(_,{modelValue:a(r).applicationId,"onUpdate:modelValue":l[35]||(l[35]=e=>a(r).applicationId=e),disabled:"",filterable:"",placeholder:"请选择拉运申请",onChange:l[36]||(l[36]=e=>he(a(r).applicationId))},{default:o(()=>[(n(!0),f(V,null,c(a(D),e=>(n(),u(y,{key:e.appId,label:e.appName,value:e.appId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"装液地点",prop:"departurePoint"},{default:o(()=>[t(T,{modelValue:a(r).departurePoint,"onUpdate:modelValue":l[37]||(l[37]=e=>a(r).departurePoint=e),disabled:"",placeholder:"请输入起运点"},null,8,["modelValue"])]),_:1}),t(p,{label:"分类",prop:"mediumCategory"},{default:o(()=>[t(_,{modelValue:a(r).mediumCategory,"onUpdate:modelValue":l[38]||(l[38]=e=>a(r).mediumCategory=e),disabled:"",placeholder:"请选择分类"},{default:o(()=>[(n(!0),f(V,null,c(a(Q),e=>(n(),u(y,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"介质类型",prop:"mediumType"},{default:o(()=>[t(_,{modelValue:a(r).mediumType,"onUpdate:modelValue":l[39]||(l[39]=e=>a(r).mediumType=e),disabled:"",placeholder:"请选择介质类型"},{default:o(()=>[(n(!0),f(V,null,c(a(h),e=>(n(),u(y,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"装液量",prop:"number"},{default:o(()=>[t(T,{modelValue:a(r).number,"onUpdate:modelValue":l[40]||(l[40]=e=>a(r).number=e),disabled:"",placeholder:"请输入装液量"},{append:o(()=>l[80]||(l[80]=[m("方")])),_:1},8,["modelValue"])]),_:1}),t(p,{label:"开始装液时间",prop:"transportTime"},{default:o(()=>[t(i,{modelValue:a(r).transportTime,"onUpdate:modelValue":l[41]||(l[41]=e=>a(r).transportTime=e),clearable:"",disabled:"",placeholder:"请选择拉运时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(p,{label:"结束装液时间",prop:"transportEndTime"},{default:o(()=>[t(i,{modelValue:a(r).transportEndTime,"onUpdate:modelValue":l[42]||(l[42]=e=>a(r).transportEndTime=e),clearable:"",disabled:"",placeholder:"请选择拉运时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(p,{label:"司机",prop:"transporter"},{default:o(()=>[t(T,{modelValue:a(r).transporter,"onUpdate:modelValue":l[43]||(l[43]=e=>a(r).transporter=e),disabled:"",placeholder:"请输入司机"},null,8,["modelValue"])]),_:1}),t(p,{label:"车牌号",prop:"licensePlate"},{default:o(()=>[t(T,{modelValue:a(r).licensePlate,"onUpdate:modelValue":l[44]||(l[44]=e=>a(r).licensePlate=e),disabled:"",placeholder:"请输入车牌号"},null,8,["modelValue"])]),_:1}),t(p,{label:"施工进度",prop:"progress"},{default:o(()=>[t(_,{modelValue:a(r).progress,"onUpdate:modelValue":l[45]||(l[45]=e=>a(r).progress=e),disabled:"",placeholder:"请选择施工进度"},{default:o(()=>[(n(!0),f(V,null,c(a(Z),e=>(n(),u(y,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"现场监督审核人",prop:"superintendent"},{default:o(()=>[t(_,{filterable:"",modelValue:a(r).superintendent,"onUpdate:modelValue":l[46]||(l[46]=e=>a(r).superintendent=e),disabled:"",placeholder:"选择现场监督审核人","allow-create":"","default-first-option":""},{default:o(()=>[(n(!0),f(V,null,c(a(se),e=>(n(),u(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"现场监督审核状态",prop:"superintendentApproval"},{default:o(()=>[t(Ae,{modelValue:a(r).superintendentApproval,"onUpdate:modelValue":l[47]||(l[47]=e=>a(r).superintendentApproval=e),disabled:a(te)!=1},{default:o(()=>[(n(!0),f(V,null,c(a(K),e=>(n(),u(Ue,{key:e.value,value:parseInt(e.value)},{default:o(()=>[m(R(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),t(p,{label:"现场监督审核时间",prop:"superintendentApprovalTime"},{default:o(()=>[t(i,{modelValue:a(r).superintendentApprovalTime,"onUpdate:modelValue":l[48]||(l[48]=e=>a(r).superintendentApprovalTime=e),clearable:"",disabled:"",placeholder:"",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(p,{label:"施工负责人",prop:"sender"},{default:o(()=>[t(_,{filterable:"",modelValue:a(r).sender,"onUpdate:modelValue":l[49]||(l[49]=e=>a(r).sender=e),disabled:"",placeholder:"选择施工负责人","allow-create":"","default-first-option":""},{default:o(()=>[(n(!0),f(V,null,c(a(ie),e=>(n(),u(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"施工负责人审核状态",prop:"senderApproval"},{default:o(()=>[t(Ae,{modelValue:a(r).senderApproval,"onUpdate:modelValue":l[50]||(l[50]=e=>a(r).senderApproval=e),disabled:a(te)!=2},{default:o(()=>[(n(!0),f(V,null,c(a(K),e=>(n(),u(Ue,{key:e.value,value:parseInt(e.value)},{default:o(()=>[m(R(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),t(p,{label:"施工负责人审核时间",prop:"senderApprovalTime"},{default:o(()=>[t(i,{modelValue:a(r).senderApprovalTime,"onUpdate:modelValue":l[51]||(l[51]=e=>a(r).senderApprovalTime=e),clearable:"",disabled:"",placeholder:"",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),t(p,{label:"备注信息",prop:"remark"},{default:o(()=>[t(T,{modelValue:a(r).remark,"onUpdate:modelValue":l[52]||(l[52]=e=>a(r).remark=e),disabled:"",placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(ne,{title:a(M).title,modelValue:a(M).visible,"onUpdate:modelValue":l[56]||(l[56]=e=>a(M).visible=e),width:"500px","append-to-body":""},{footer:o(()=>[N("div",Ql,[t(b,{type:"primary",onClick:xe},{default:o(()=>l[83]||(l[83]=[m("确 定")])),_:1}),t(b,{onClick:Fe},{default:o(()=>l[84]||(l[84]=[m("取 消")])),_:1})])]),default:o(()=>[t(W,{ref_key:"copyFormRef",ref:fe,model:a(C),"label-width":"100px"},{default:o(()=>[t(p,{label:"拉运申请",prop:"applicationId"},{default:o(()=>[t(_,{modelValue:a(C).applicationId,"onUpdate:modelValue":l[55]||(l[55]=e=>a(C).applicationId=e),filterable:"",placeholder:"请选择拉运申请",onChange:He},{default:o(()=>[(n(!0),f(V,null,c(a(D),e=>(n(),u(y,{key:e.appId,label:e.appName,value:e.appId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}});export{da as default};
