package com.biz.epms.epcom.common.enums;

/**
 * 设备状态枚举类
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
public enum EpcomMeteringDeviceStatusEnum {
    /**
     * 在用状态
     */
    IN_USE(0, "在用"),

    /**
     * 停用状态
     */
    DISABLED(1, "停用"),

    /**
     * 故障状态
     */
    FAULT(2, "故障");

    private final Integer value;
    private final String description;

    EpcomMeteringDeviceStatusEnum(Integer code, String description) {
        this.value = code;
        this.description = description;
    }

    /**
     * 根据code获取对应的枚举值
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static EpcomMeteringDeviceStatusEnum getByCode(Integer code) {
        for (EpcomMeteringDeviceStatusEnum status : values()) {
            if (status.getValue().equals(code)) {
                return status;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}