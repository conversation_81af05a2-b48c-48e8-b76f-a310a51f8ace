<template>
  <div class="app-container">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px">
            <el-form-item label="部件类型" prop="partsType">
              <el-select
                v-model="queryParams.partsType"
                clearable
                placeholder="请选择部件类型"
                @change="getBujiancanshuListByBjlxid(queryParams.partsType)"
              >
                <el-option
                  v-for="bujianleixing in bujianleixingList"
                  :key="bujianleixing.id"
                  :label="bujianleixing.mingzi"
                  :value="bujianleixing.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="部件名称" prop="devName">
              <el-input v-model="queryParams.devName" clearable placeholder="请输入部件名称" @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="部件参数" prop="warnId">
              <el-select v-model="queryParams.warnId" clearable placeholder="请选择部件参数">
                <el-option v-for="bujiancanshu in bujiancanshuList" :key="bujiancanshu.id" :label="bujiancanshu.mingzi" :value="bujiancanshu.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="异常级别" prop="warnLevel">
              <el-select v-model="queryParams.warnLevel" clearable placeholder="请选择异常级别">
                <el-option v-for="dict in dictData.warn_level" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="异常名称" prop="warnName">
              <el-input v-model="queryParams.warnName" clearable placeholder="请输入异常名称" @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="所属模块" prop="systemType">
              <el-select v-model="queryParams.systemType" :disabled="notSelectSystemType" clearable placeholder="请选择所属模块">
                <el-option
                  v-for="dict in dictData.warn_system_type"
                  :key="dict.value"
                  :disabled="notSelectSystemType"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button icon="Search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['dbedit:warnsource:add']" icon="Plus" plain size="mini" type="primary" @click="handleAdd">新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['dbedit:warnsource:remove']"
              :disabled="multiple"
              icon="Delete"
              plain
              size="mini"
              type="danger"
              @click="handleDelete"
              >删除
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table stripe v-loading="loading" :data="warnsourceList" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column align="center" label="异常配置ID" prop="warnSetId" />
        <el-table-column align="center" label="异常名称" prop="warnName" width="250" />
        <el-table-column align="center" label="部件类型" prop="bjlxmingzi" />
        <el-table-column align="center" label="部件参数" prop="bjcsmingzi" width="200" />
        <el-table-column align="center" label="部件名称" prop="devName" />
        <el-table-column label="异常级别" align="center" prop="warnLevel">
          <template #default="scope">
            <dict-tag :options="dictData.warn_level" :value="scope.row.warnLevel" />
          </template>
        </el-table-column>
        <el-table-column label="异常类型" align="center" prop="warnType">
          <template #default="scope">
            <dict-tag :options="dictData.warn_type" :value="scope.row.warnType" />
          </template>
        </el-table-column>

        <el-table-column align="center" label="所属模块" prop="systemType" width="180">
          <template #default="scope">
            <dict-tag :options="dictData.warn_system_type" :value="scope.row.systemType" />
          </template>
        </el-table-column>

        <el-table-column align="center" label="状态" prop="disabled">
          <template #default="scope">
            <dict-tag :options="dictData.sys_normal_disable" :value="scope.row.disabled" />
          </template>
        </el-table-column>

        <el-table-column label="备注" align="center" prop="memo"></el-table-column>

        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="170px">
          <template #default="scope">
            <el-button v-hasPermi="['dbedit:warnsource:edit']" icon="Edit" size="mini" type="text" @click="handleUpdate(scope.row)">修改 </el-button>
            <el-button v-hasPermi="['dbedit:warnsource:remove']" icon="Delete" size="mini" type="text" @click="handleDelete(scope.row)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改异常配置对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属模块" prop="warnName">
              <el-select v-model="form.systemType" placeholder="请选择所属模块" :disabled="notSelectSystemType">
                <el-option v-for="dict in dictData.warn_system_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="异常名称" prop="warnName">
              <el-input v-model="form.warnName" placeholder="请输入异常名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="处理措施" prop="warnContents">
              <el-input v-model="form.warnContents" placeholder="请输入内容" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="warnSource">
              <template #label>
                <span>
                  <el-tooltip content="告警数据的来源类型，例如物联网数据、业务数据等" placement="top">
                    <el-icon><question-filled /></el-icon>
                  </el-tooltip>
                  数据来源
                </span>
              </template>
              <el-select v-model="form.warnSource" placeholder="请选择">
                <el-option v-for="dict in dictData.warn_source" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="异常类型" prop="warnType">
              <el-select v-model="form.warnType" placeholder="请选择异常类型">
                <el-option v-for="dict in dictData.warn_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部件类型" prop="partsType">
              <el-select v-model="form.partsType" clearable filterable placeholder="请选择部件类型" @change="changeBjlx">
                <el-option
                  v-for="bujianleixing in bujianleixingList"
                  :key="bujianleixing.id"
                  :label="bujianleixing.mingzi"
                  :value="parseInt(bujianleixing.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部件参数" prop="warnId">
              <el-select v-model="form.warnId" placeholder="请选择部件参数" clearable filterable>
                <el-option
                  v-for="bujiancanshu in bujiancanshuList"
                  :key="bujiancanshu.id"
                  :label="bujiancanshu.mingzi"
                  :value="parseInt(bujiancanshu.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部件名称" prop="devId">
              <el-select v-model="form.devId" placeholder="请选择部件名称" clearable>
                <el-option v-for="bujianid in bujianidList" :key="bujianid.id" :label="bujianid.mingzi" :value="parseInt(bujianid.id)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="异常级别" prop="warnLevel">
              <el-select v-model="form.warnLevel" placeholder="请选择异常级别">
                <el-option v-for="dict in dictData.warn_level" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="计算类型" prop="caculateType">
              <el-select v-model="form.caculateType" placeholder="请选择计算类型">
                <el-option
                  v-for="dict in dictData.warn_caculate_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计算周期" prop="machineCycle">
              <el-select v-model="form.machineCycle" placeholder="请选择计算周期">
                <el-option v-for="dict in dictData.warn_machine_cycle" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 动态加载组件 -->
        <el-row :key="form.warnSource">
          <el-col :span="24">
            <warn-biz-rule-form v-if="form.warnSource === 5" v-model="form.warnRule" />
            <warn-rule-form v-else-if="form.warnSource === 1" v-model="form.warnRule" />
            <el-form-item label="异常规则" prop="warnRule" v-else>
              <el-input v-model="form.warnRule" placeholder="请输入异常规则" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider><span style="font-size: 15px">条件规则</span></el-divider>
        <el-row>
          <el-col :span="24">
            <el-form-item label="条件规则" prop="condition_rule">
              <AceInput v-model="form.conditionRule" placeholder="请输入条件规则" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="memo">
              <el-input v-model="form.memo" placeholder="请输入内容" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="跳转地址" prop="memo">
              <el-input v-model="form.jumpUrl" placeholder="请输入跳转地址" type="text" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="登记人" prop="registrar">
              <el-input v-model="form.registrar" placeholder="请输入登记人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登记日期" prop="registDate">
              <el-date-picker v-model="form.registDate" clearable placeholder="选择登记日期" type="date" value-format="yyyy-MM-dd"> </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="disabled">
              <el-select v-model="form.disabled" placeholder="请选择状态">
                <el-option
                  v-for="dict in dictData.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  addWarnsource,
  allListBujianleixing,
  bujiancanshuListByBjlxid,
  bujianidListByBjlxid,
  delWarnsource,
  getWarnsource,
  listWarnsource,
  updateWarnsource
} from '@/api/comm/warn/warnsource/warnsource';
import { getToken } from '@/utils/auth.ts';
import * as proxy from '@/utils/dict.ts';
import { QuestionFilled } from '@element-plus/icons-vue';
import WarnBizRuleForm from './components/WarnBizRuleForm.vue';
import WarnRuleForm from './components/WarnRuleForm.vue';
import AceInput from './components/AceInupt.vue';

export default {
  name: 'Warnsource',
  components: {
    AceInput,
    QuestionFilled,
    WarnBizRuleForm,
    WarnRuleForm
  },
  data() {
    return {
      dictData: undefined,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      notSelectSystemType: false,
      // 异常配置表格数据
      warnsourceList: [],
      bujiancanshuList: [],
      bujianleixingList: [],
      bujianidList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warnId: null,
        bjlxid: null,
        bjcsid: null,
        partsType: null,
        warnName: null,
        warnLevel: null,
        devName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        warnId: [{ required: true, message: '部件参数不能为空', trigger: 'change' }],
        partsType: [{ required: true, message: '部件类型不能为空', trigger: 'change' }],
        warnName: [{ required: true, message: '异常名称不能为空', trigger: 'blur' }],
        warnContents: [{ required: true, message: '异常内容不能为空', trigger: 'blur' }],
        warnSourceId: [{ required: true, message: '异常来源ID不能为空', trigger: 'blur' }],
        warnSource: [{ required: true, message: '数据来源不能为空', trigger: 'blur' }],
        warnType: [{ required: true, message: '异常类型不能为空', trigger: 'blur' }],
        warnLevel: [{ required: true, message: '异常级别不能为空', trigger: 'blur' }],
        machineCycle: [{ required: true, message: '计算周期不能为空', trigger: 'blur' }]
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {
          Authorization: 'Bearer ' + getToken(),
          clientid: import.meta.env.VITE_APP_CLIENT_ID
        },
        // 上传的地址
        url: import.meta.env.VUE_APP_BASE_API + 'dbedit/warnsource/importData'
      }
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        if (route.query.systemType) {
          this.queryParams.systemType = route.query.systemType;
          this.notSelectSystemType = true;
          this.getList();
        }
      },
      immediate: true
    }
  },
  created() {
    this.getList();
    this.getBujianleixingList();
    this.loadDicts();
    if (this.$route.query.systemType) {
      this.queryParams.systemType = parseInt(this.$route.query.systemType);
      this.notSelectSystemType = true;
    }
  },
  methods: {
    loadDicts() {
      this.dictData = proxy?.useDict(
        'warn_level',
        'warn_type',
        'warn_source',
        'warn_caculate_type',
        'warn_machine_cycle',
        'warn_system_type',
        'sys_normal_disable'
      );
    },

    /** 查询异常配置列表 */
    getList() {
      this.loading = true;
      listWarnsource(this.queryParams).then((response) => {
        this.warnsourceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    changeBjlx(bjlxid) {
      this.getBujiancanshuListByBjlxid(bjlxid);
      this.getBujianidListByBjlxid(bjlxid);
    },
    /** 根据bjlxid查询bujiancanshu列表 */
    getBujiancanshuListByBjlxid(bjlxid) {
      if (!this.open) {
        this.bujiancanshuList = null;
        this.queryParams.bjcsid = null;
      }
      if (bjlxid != null && bjlxid != '') {
        bujiancanshuListByBjlxid(bjlxid).then((response) => {
          this.bujiancanshuList = response.data;
        });
      } else {
        this.bujiancanshuList = [];
      }
    },
    /** 根据bjlxid查询bujianid列表 */
    getBujianidListByBjlxid(bjlxid) {
      if (!this.open) {
        this.bujianidList = null;
      }
      if (bjlxid != null && bjlxid != '') {
        bujianidListByBjlxid(bjlxid).then((response) => {
          this.bujianidList = response.data;
        });
      } else {
        this.bujianidList = [];
      }
    },
    /** 查询bujianleixing列表 */
    getBujianleixingList() {
      allListBujianleixing({ pageSize: -1, pageNum: 1, bujianleixing: this.queryParams.bjlxid }).then((response) => {
        this.bujianleixingList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        warnId: null,
        bjlxmingzi: null,
        bjcsmingzi: null,
        partsType: null,
        devId: null,
        warnName: null,
        warnContents: null,
        warnSourceId: null,
        warnSetId: null,
        warnLevel: null,
        caculateType: null,
        machineCycle: null,
        warnRule: null,
        memo: null,
        registDate: null,
        registrar: null,
        disabled: null
      };
      this.form.systemType = this.queryParams.systemType;
      this.resetForm('form');
    },
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      function GetreqData(item) {
        let reqData = {
          'warnSourceId': item.warnSourceId,
          'warnSetId': item.warnSetId
        };
        return reqData;
      }

      this.ids = selection.map((item) => GetreqData(item));
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加异常配置';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getBujiancanshuListByBjlxid(row.partsType);
      this.getBujianidListByBjlxid(row.partsType);
      let reqData = null;
      const warn_source_Ids = [];
      const warn_set_Ids = [];
      for (var i = 0; i < this.ids.length; i++) {
        warn_source_Ids.push(this.ids[i].warnSourceId);
        warn_set_Ids.push(this.ids[i].warnSetId);
      }
      const warnSourceId = row.warnSourceId || warn_source_Ids;
      const warnSetId = row.warnSetId || warn_set_Ids;
      reqData = {
        'warnSourceId': warnSourceId,
        'warnSetId': warnSetId
      };
      getWarnsource(reqData).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = '修改异常配置';
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.warnSourceId != null) {
            updateWarnsource(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addWarnsource(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      console.log(this.ids);
      const warn_source_Ids = [];
      const warn_set_Ids = [];
      for (var i = 0; i < this.ids.length; i++) {
        warn_source_Ids.push(this.ids[i].warnSourceId);
        warn_set_Ids.push(this.ids[i].warnSetId);
      }
      const warnSourceIds = row.warnSourceId || warn_source_Ids;
      const warnSetIds = row.warnSetId || warn_set_Ids;
      this.$modal
        .confirm('是否确认删除异常配置编号为"' + warnSourceIds + '"的数据项？')
        .then(function () {
          return delWarnsource(warnSetIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'dbedit/warnsource/export',
        {
          ...this.queryParams
        },
        `warnsource_${new Date().getTime()}.xlsx`
      );
    }
  }
};
</script>
