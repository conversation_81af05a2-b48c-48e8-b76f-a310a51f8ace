<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.epms.epfy.mapper.EpfyTransportRecordMapper">
    <resultMap id="EpfyTransportRecordResultMap" type="com.biz.epms.epfy.domain.vo.EpfyTransportRecordVo">
        <id column="transport_id" property="transportId"/>
        <result column="application_id" property="applicationId"/>
        <result column="transport_time" property="transportTime"/>
        <result column="medium_category" property="mediumCategory"/>
        <result column="medium_type" property="mediumType"/>
        <result column="departure_point" property="departurePoint"/>
        <result column="number" property="number"/>
        <result column="sender" property="sender"/>
        <result column="transporter" property="transporter"/>
        <result column="license_plate" property="licensePlate"/>
        <result column="arrival_point" property="arrivalPoint"/>
        <result column="receiver" property="receiver"/>
        <result column="remark" property="remark"/>
        <result column="measurement_voucher" property="measurementVoucher"/>
        <result column="photo" property="photo"/>
        <result column="work_area" property="workArea"/>
        <result column="well_name" property="wellName"/>
        <result column="apply_dept" property="applyDept"/>
        <result column="apply_time" property="applyTime"/>
        <result column="end_examine_approve_time" property="endExamineApproveTime"/>
        <result column="unload_location_type" property="unloadLocationType"/>
    </resultMap>

    <select id="selectVoPageWithFlowType" resultMap="EpfyTransportRecordResultMap">
        SELECT tr.*,  ta.work_area_id as work_area, ta.create_dept as apply_dept, ta.application_date as apply_time, ta.end_examine_approve_time as end_examine_approve_time,
        ta.well_name,ta.unload_location_type
        FROM epfy_transport_record tr
        LEFT JOIN epfy_transport_application ta ON tr.application_id = ta.app_id
        LEFT JOIN epnj_well_preparation wp on ta.unload_location_id = wp.prep_id
        <where>
            <if test="bo.applicationId != null and bo.applicationId != ''">
                and tr.application_id = #{bo.applicationId}
            </if>
            <if test="bo.monthQuery != null and bo.monthQuery != ''">
                and tr.transport_time like concat('%', #{bo.monthQuery}, '%')
            </if>
            <if test="bo.mediumCategory != null and bo.mediumCategory != ''">
                and tr.medium_category = #{bo.mediumCategory}
            </if>
            <if test="bo.mediumType != null and bo.mediumType != ''">
                and tr.medium_type = #{bo.mediumType}
            </if>
            <if test="bo.arrivalPoint != null and bo.arrivalPoint != ''">
                and tr.arrival_point like concat('%', #{bo.arrivalPoint}, '%')
            </if>
            <if test="bo.receiver != null and bo.receiver != ''">
                and tr.receiver like concat('%', #{bo.receiver}, '%')
            </if>
            <if test="bo.transporter != null and bo.transporter != ''">
                and tr.transporter = #{bo.transporter}
            </if>
            <if test="bo.sender != null and bo.sender != ''">
                and tr.sender = #{bo.sender}
            </if>
            <if test="bo.flowType != null and bo.flowType != ''">
                AND ta.flow_type = #{bo.flowType}
            </if>
            <if test="bo.queryStartTime != null and bo.queryStartTime != ''">
                and tr.transport_time &gt;= #{bo.queryStartTime}
            </if>
            <if test="bo.queryEndTime != null and bo.queryEndTime != ''">
                and tr.transport_time &lt;= #{bo.queryEndTime}
            </if>
            <if test="bo.wellName != null and bo.wellName != ''">
                AND ta.well_name like concat('%', #{bo.wellName}, '%')
            </if>
            <if test="bo.operationArea != null and bo.operationArea != ''">
                AND ta.work_area_id = #{bo.operationArea}
            </if>
            <if test="bo.unloadLocationType != null ">
                AND ta.unload_location_type = #{bo.unloadLocationType}
            </if>
            <if test="bo.appName != null and  bo.appName != ''">
                AND ta.app_name like concat('%', #{bo.appName}, '%')
            </if>
            <if test="bo.departurePoint != null and  bo.departurePoint != ''">
                AND tr.departure_point like concat('%', #{bo.departurePoint}, '%')
            </if>
            <if test="bo.licensePlate != null and bo.licensePlate != ''">
                AND tr.license_plate like concat('%', #{bo.licensePlate}, '%')
            </if>
            <if test="bo.pumpTime != null">
                AND tr.pump_time &gt;= DATE_FORMAT(#{bo.pumpTime}, '%Y-%m-%d 00:00:00')
                AND tr.pump_time &lt;= DATE_FORMAT(#{bo.pumpTime}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="bo.transportTime != null">
                AND tr.transport_time &gt;= DATE_FORMAT(#{bo.transportTime}, '%Y-%m-%d 00:00:00')
                AND tr.transport_time &lt;= DATE_FORMAT(#{bo.transportTime}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="bo.pumpLocation != null and bo.pumpLocation != ''">
                AND tr.pump_location like concat('%', #{bo.pumpLocation}, '%')
            </if>
            <if test="bo.unloadLocation != null and bo.unloadLocation != ''">
                AND tr.unload_location like concat('%', #{bo.unloadLocation}, '%')
            </if>
            <if test="bo.unloadDirector != null and bo.unloadDirector != ''">
                AND tr.unload_director like concat('%', #{bo.unloadDirector}, '%')
            </if>
        </where>
        ORDER BY tr.transport_time DESC
    </select>

</mapper>
