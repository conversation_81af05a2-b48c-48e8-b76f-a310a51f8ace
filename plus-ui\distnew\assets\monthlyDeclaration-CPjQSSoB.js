import{aC as ee,d as V,h as te,a as oe,r as s,ai as ae,ak as ne,b as ie,aH as le,c as P,o as _,p as e,t as a,w as E,q as re,a7 as se,M as de,e as y,A as ce,B as pe,F as ve,C as ue,x as M,D as me,K as ge,J as D,am as he,aI as fe,ay as be,ax as _e,v as ye}from"./index-D07cMzhp.js";import{E as we}from"./el-row-CikYE3zA.js";import{_ as Ce}from"./index-BWMgqvQ9.js";import{E as Te}from"./el-col-BaG5Rg5z.js";import{E as Se}from"./el-date-picker-HyhB9X9n.js";import{o as xe}from"./index-ChPmfMlc.js";import{u as x,X as Fe}from"./xlsx-BexUIDLF.js";import{m as Ne}from"./dayjs.min-Brw96_N0.js";import"./el-tree-DW6MoFaI.js";import"./index-VIEDZI2D.js";const Ae=I=>ee({url:"/epwf/wasteMonthlyDeclaration/list",method:"get",params:I}),Ee={class:"p-2"},Me={class:"mb-[10px]"},De=V({name:"WasteStorageRecord"}),qe=V({...De,setup(I){const{proxy:L}=te();oe();const k=s([]),F=s(!0),N=s(!0),Y=s([]),$=s(!0),z=s(!0),B=s(null),q=s(["",""]),O=s(["",""]),X=s(["",""]),w=s(Ne().format("YYYY-MM")),Q=s([]),R=s();s();const j=ae({form:{...{recordId:void 0,generateBatchCode:void 0,incomeBatchCode:void 0,outboundBatchCode:void 0,disposalBatchCode:void 0,generateTime:void 0,incomeTime:void 0,outboundTime:void 0,containerCode:void 0,containerType:void 0,containerNumber:void 0,wasteCommonName:void 0,wasteNationalName:void 0,wasteCategory:void 0,wasteCode:void 0,generateQuantity:void 0,incomeQuantity:void 0,outboundQuantity:void 0,disposalQuantity:void 0,unit:void 0,facilityCode:void 0,generateFacilityCode:void 0,facilityType:void 0,generateAgent:void 0,transportAgent:void 0,storageAgent:void 0,outboundAgent:void 0,generateDestination:void 0,outboundDestination:void 0,generateWasteUnitId:void 0,centralizedUnitId:void 0,approvalStatus:1,approvalOpinions:void 0,territorialUnitId:void 0,generatePlace:void 0,generateFile:void 0,weighFile:void 0,storeFile:void 0,labelFile:void 0,transportCondition:void 0,transportApplicationFile:void 0,transportWasteFile:void 0,wasteLabel:void 0,transportMultFile:void 0,poundList:void 0,processPhotos:void 0,disposalWay:void 0,receiveType:void 0,businessLicenseName:void 0,businessLicenseEncode:void 0,wasteComponentName:void 0,wasteShape:void 0,dangerCharacteristics:void 0,address:void 0}},queryParams:{pageNum:1,pageSize:10,generateBatchCode:void 0,incomeBatchCode:void 0,outboundBatchCode:void 0,disposalBatchCode:void 0,containerCode:void 0,containerType:void 0,containerNumber:void 0,wasteCommonName:void 0,wasteNationalName:void 0,wasteCategory:void 0,wasteCode:void 0,generateQuantity:void 0,incomeQuantity:void 0,outboundQuantity:void 0,disposalQuantity:void 0,unit:void 0,facilityCode:void 0,generateFacilityCode:void 0,facilityType:void 0,generateAgent:void 0,transportAgent:void 0,storageAgent:void 0,outboundAgent:void 0,generateDestination:void 0,outboundDestination:void 0,generateWasteUnitId:void 0,centralizedUnitId:void 0,approvalStatus:void 0,approvalOpinions:void 0,territorialUnitId:void 0,generatePlace:void 0,generateFile:void 0,weighFile:void 0,storeFile:void 0,labelFile:void 0,transportCondition:void 0,transportApplicationFile:void 0,transportWasteFile:void 0,wasteLabel:void 0,transportMultFile:void 0,poundList:void 0,processPhotos:void 0,disposalWay:void 0,receiveType:void 0,businessLicenseName:void 0,businessLicenseEncode:void 0,wasteComponentName:void 0,wasteShape:void 0,dangerCharacteristics:void 0,address:void 0,includeSelf:!0,params:{generateTime:void 0,incomeTime:void 0,outboundTime:void 0}},rules:{}}),{queryParams:c,form:Le,rules:ke}=ne(j),A=async()=>{if(F.value=!0,c.value.params={},w.value){c.value.params=typeof c.value.params=="object"&&!Array.isArray(c.value.params)?c.value.params:{};const o=w.value,[v,r]=o.split("-");if(!v||!r)throw new Error("时间格式错误");const i=`${v}-${r}-01 00:00:00`;c.value.params.thisMonthTime=i;let g=parseInt(v,10),p=parseInt(r,10)+1;p>12&&(g+=1,p=1);const h=String(p).padStart(2,"0"),f=`${g}-${h}-01 00:00:00`;c.value.params.nextMonthTime=f}const n=await Ae(c.value);k.value=n,F.value=!1},U=()=>{c.value.pageNum=1,A()},G=()=>{var n;q.value=["",""],O.value=["",""],X.value=["",""],(n=R.value)==null||n.resetFields(),U()},H=n=>{Y.value=n.map(o=>o.recordId),$.value=n.length!=1,z.value=!n.length},J=()=>{var n;try{const o=(n=B.value)==null?void 0:n.$el;let v=o.querySelector(".el-table__fixed");v||(v=o);const r=x.table_to_book(v,{raw:!0}),i=r.Sheets[r.SheetNames[0]],g=[],p=x.decode_range(i["!ref"]);for(let d=p.s.c;d<=p.e.c;d++){let l=0;for(let u=p.s.r;u<=p.e.r;u++){const t=x.encode_cell({r:u,c:d}),b=i[t];if(b&&b.v){const C=String(b.v).split("").reduce((T,S)=>T+(S.charCodeAt(0)>255?2:1),0);C>l&&(l=C)}}g.push({wch:Math.min(l+2,60)})}i["!cols"]=g;const h={alignment:{horizontal:"center",vertical:"center",wrapText:!0},border:{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},font:{sz:11,name:"宋体",color:{rgb:"000000"}}};Object.keys(i).forEach(d=>{if(!d.startsWith("!")){const l=i[d];l.s=l.s?{...l.s,...h}:{...h},typeof l.v=="number"&&(l.z=l.z||"0.00")}}),i["!merges"]&&i["!merges"].forEach(d=>{for(let l=d.s.r;l<=d.e.r;l++)for(let u=d.s.c;u<=d.e.c;u++){const t=x.encode_cell({r:l,c:u});i[t]||(i[t]={t:"s",v:""}),i[t].s={...h}}});const f=Fe.write(r,{bookType:"xlsx",bookSST:!1,type:"binary",cellStyles:!0});saveAs(new Blob([K(f)],{type:"application/octet-stream"}),`危废月度申报_${new Date().getTime()}.xlsx`)}catch(o){typeof console<"u"&&console.error(o)}},K=n=>{const o=new ArrayBuffer(n.length),v=new Uint8Array(o);for(let r=0;r<n.length;r++)v[r]=n.charCodeAt(r)&255;return o},Z=async()=>{const n=await xe(null,3);Q.value=n.data};return ie(()=>{A(),Z()}),(n,o)=>{var T,S;const v=me,r=pe,i=ce,g=Se,p=ge,h=de,f=se,d=Te,l=Ce,u=we,t=_e,b=be,W=le("hasPermi"),C=ye;return _(),P("div",Ee,[e(fe,{"enter-active-class":(T=y(L))==null?void 0:T.animate.searchAnimate.enter,"leave-active-class":(S=y(L))==null?void 0:S.animate.searchAnimate.leave},{default:a(()=>[E(re("div",Me,[e(f,{shadow:"hover"},{default:a(()=>[e(h,{ref_key:"queryFormRef",ref:R,model:y(c),inline:!0,"label-width":"100px"},{default:a(()=>[e(i,{label:"属地单位",style:{width:"308px"}},{default:a(()=>[e(r,{modelValue:y(c).territorialUnitId,"onUpdate:modelValue":o[0]||(o[0]=m=>y(c).territorialUnitId=m),placeholder:"请选择属地单位"},{default:a(()=>[(_(!0),P(ve,null,ue(Q.value,m=>(_(),M(v,{key:m.deptId,label:m.deptName,value:m.deptId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"申报月份",style:{width:"308px"}},{default:a(()=>[e(g,{modelValue:w.value,"onUpdate:modelValue":o[1]||(o[1]=m=>w.value=m),"default-value":new Date,placeholder:"选择月份",type:"month","value-format":"YYYY-MM"},null,8,["modelValue","default-value"])]),_:1}),e(i,null,{default:a(()=>[e(p,{type:"primary",icon:"Search",onClick:U},{default:a(()=>o[3]||(o[3]=[D("搜索")])),_:1}),e(p,{icon:"Refresh",onClick:G},{default:a(()=>o[4]||(o[4]=[D("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[he,N.value]])]),_:1},8,["enter-active-class","leave-active-class"]),e(f,{shadow:"never"},{header:a(()=>[e(u,{gutter:10,class:"mb8"},{default:a(()=>[e(d,{span:1.5},{default:a(()=>[E((_(),M(p,{icon:"Download",plain:"",type:"warning",onClick:J},{default:a(()=>o[5]||(o[5]=[D("导出")])),_:1})),[[W,["epms:wasteMonthlyDeclaration:export"]]])]),_:1}),e(l,{showSearch:N.value,"onUpdate:showSearch":o[2]||(o[2]=m=>N.value=m),onQueryTable:A},null,8,["showSearch"])]),_:1})]),default:a(()=>[E((_(),M(b,{ref_key:"reportTable",ref:B,data:k.value,stripe:"",onSelectionChange:H},{default:a(()=>[e(t,{label:"序号",type:"index",width:"50"}),e(t,{label:"产生情况",align:"center"},{default:a(()=>[e(t,{label:"危险废物名称",align:"center"},{default:a(()=>[e(t,{label:"行业俗称",align:"center",prop:"wasteCommonName"}),e(t,{label:"国家危废名录名称",align:"center",prop:"wasteNationalName"})]),_:1}),e(t,{label:"危险废物类别",align:"center",prop:"wasteCategory"}),e(t,{label:"危险废物代码",align:"center",prop:"wasteCode"}),e(t,{label:"有害成分名称",align:"center",prop:"wasteComponentName"}),e(t,{label:"形态",align:"center",prop:"wasteShape"}),e(t,{label:"危险特性",align:"center",prop:"dangerCharacteristics"}),e(t,{label:"产生量",align:"center",prop:"generateQuantity"}),e(t,{label:"计量单位",align:"center",prop:"unit"})]),_:1}),e(t,{label:"委托外单位利用/处置情况",align:"center"},{default:a(()=>[e(t,{label:"省(区、市)",align:"center",prop:"address"}),e(t,{label:"单位名称",align:"center",prop:"businessLicenseName"}),e(t,{label:"危险废物经营许可证编号",align:"center",prop:"businessLicenseEncode"}),e(t,{label:"利用/处置方式",align:"center",prop:"disposalWay"}),e(t,{label:"利用/处置量",align:"center",prop:"disposalQuantity"}),e(t,{label:"计量单位",align:"center",prop:"unit"})]),_:1}),e(t,{label:"贮存情况",align:"center"},{default:a(()=>[e(t,{label:"上月底剩余贮存量",align:"center",prop:"lastMonthRemaining"}),e(t,{label:"本月底贮存量",align:"center",prop:"thisMonthRemaining"})]),_:1})]),_:1},8,["data"])),[[C,F.value]])]),_:1})])}}});export{qe as default};
