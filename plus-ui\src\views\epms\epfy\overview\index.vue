<template>
  <div class="waterline p-2">
    <el-row style="justify-content: center">
      <div class="header">
        <span>生产返排液流程管理</span>
      </div>
    </el-row>
    <el-row :gutter="14">
      <el-col :span="12">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>生产返排液业务</span>
            </div>
          </template>
          <div class="card-body">
            <el-tabs v-model="activeName" class="demo-tabs" type="card" @tab-click="handleTabClick">
              <el-tab-pane label="业务介绍" name="jianjie">
                <jianjie :attach-source-type="'fyZongLan'" :notice-id="'1930148541401337858'" />
              </el-tab-pane>
              <el-tab-pane class="hmi-div" label="业务流程" name="zonglan">
                <HmiPreview v-model="fileName" style="height: 100%; width: 100%" />
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>拉运统计</span>
            </div>
          </template>
          <div class="card-body" v-loading="loading">
            <el-table stripe ref="table" :data="transportList" border height="100%">
              <el-table-column align="center" fixed="left" label="" prop="rowName"></el-table-column>
              <el-table-column align="center" fixed="left" label="今日" prop="dayData"></el-table-column>
              <el-table-column align="center" label="本月" prop="monthData" fixed="left"></el-table-column>
              <el-table-column align="center" label="本年" prop="yearData"></el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="14" style="margin-top: 7px">
      <el-col :span="12">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>月度介质拉运量对比</span>
              <span> </span>
              <div class="query">
                <el-date-picker
                  v-model="queryParams.dateTime"
                  type="year"
                  placeholder="选择年度"
                  format="YYYY"
                  date-format="YYYY"
                  value-format="YYYY"
                  @change="initEcharts"
                  style="margin: 0px 10px; width: 200px"
                />
                <el-tree-select
                  v-model="queryParams.danwei"
                  :data="operationAreaTreeList"
                  :props="defaultProps"
                  @change="initEcharts"
                  check-strictly
                  placeholder="请选择取水区块"
                  :render-after-expand="false"
                  style="width: 200px"
                />
              </div>
            </div>
          </template>
          <div class="card-body" v-loading="loading">
            <echarts ref="chartRef" :chartData="chartData" style="width: 100%; height: 100%" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>生产返排液标准规范</span>
            </div>
          </template>
          <div class="card-body">
            <guifan :documentCategory="standardDocumentCategory" :documentType="standardDocumentType" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="never" class="has-el-card-title">
          <template #header>
            <div class="card-header">
              <span>生产返排液告警列表</span>
            </div>
          </template>
          <div class="card-body">
            <warn :systemType="systemType" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import Echarts from '@/views/epms/epfy/overview/components/echarts.vue';
import Jianjie from '@/views/epms/components/overview/jianjie.vue';
import Guifan from '@/views/epms/epfy/overview/components/guifan.vue';
import Warn from '@/views/epms/epfy/overview/components/warn.vue';
import { getOperationArea, listOperationArea } from '@/api/epms/epcom/operationArea';
import moment from 'dayjs';
import { OperationAreaQuery } from '@/api/epms/epcom/operationArea/types';
import { TransportRecordQuery } from '@/api/epms/epfy/transportRecord/types';
import { ecahrtsTransportRecordZongLan, statTransportRecordZongLan } from '@/api/epms/epfy/transportRecord';

const activeName = ref('jianjie');
const fileName = ref<String>('huanbao-feiye');
const defaultProps = {
  value: 'operationAreaId',
  label: 'operationAreaName',
  children: 'children'
};
const loading = ref(false);
const chartData = ref([]);
const operationAreaTreeList = ref([]);
const gongsi = ref({
  operationAreaId: '83',
  operationAreaName: '二连分公司',
  children: {}
});
const transportList = ref([]);
const transportQuery = ref<TransportRecordQuery>({});
const operationAreaList = ref([]);
const operaQuery = ref<OperationAreaQuery>({});
const queryParams = ref({
  danwei: undefined,
  operationAreaType: 2,
  dateTime: moment().format('YYYY-12-DD')
});
const systemType = ref(4);
const chartRef = ref(null);
// 标准规范附件参数
const standardDocumentType = ref();
const standardDocumentCategory = ref(4);

const handleTabClick = () => {};
/**
 * 获取所属地、取水区块
 */
const getOperationTreeArea = () => {
  const areaQuery = ref({
    operationAreaType: 0
  });
  listOperationArea(areaQuery.value).then((res) => {
    gongsi.value.children = res.rows;
    operationAreaTreeList.value = [gongsi.value];
    queryParams.value.danwei = '83';
    queryParams.value.operationAreaType = 2;
    initEcharts();
  });
};

const initEcharts = async (node?: any) => {
  if (queryParams.value.danwei != '83') {
    await getOperationArea(queryParams.value.danwei as string).then((res) => {
      queryParams.value.operationAreaType = res.data.operationAreaType;
    });
  } else {
    queryParams.value.operationAreaType = 2;
  }
  loading.value = true;
  queryParams.value.dateTime = moment(queryParams.value.dateTime).format('YYYY');
  queryParams.value.flowType = 1;
  await ecahrtsTransportRecordZongLan(queryParams.value).then((res) => {
    chartData.value = res.data;
    const dateFormat = function (value) {
      const date = new Date(value);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      return year + '年' + month + '月';
    };
    /**
     * 合并数据
     */
    chartData.value.XAxis.forEach((item, index) => {
      chartData.value.XAxis[index] = dateFormat(item);
    });
  });
  chartRef.value.initChart();
  loading.value = false;
};
const getOperationAreaList = () => {
  operaQuery.value.operationAreaType = 0;
  listOperationArea(operaQuery.value).then((res) => {
    operationAreaList.value = res.rows;
  });
};
const getTransportData = () => {
  transportQuery.value.flowType = 1;
  transportQuery.value.transportTime = moment().format('YYYY-MM-DD HH:mm:ss');
  statTransportRecordZongLan(transportQuery.value).then((res) => {
    transportList.value = res.rows;
  });
};

onMounted(() => {
  getTransportData();
  getOperationTreeArea();
  getOperationAreaList();
});
</script>

<style scoped>
.waterline {
  height: inherit;
}
.header {
  font-weight: bold;
  margin-bottom: 10px;
  align-items: center;
  font-size: 35px;
  letter-spacing: 7px;
}
.card-body {
  height: 36.5vh;
  width: 100%;
}

:deep(.el-card__header) {
  padding: 2px 15px 2px !important;
  background-color: #f5f5f5;
}

:deep(.el-card__body) {
  padding: 0 !important;
}

:deep(.el-tabs__content) {
  padding: 5px !important;
}

.card-header {
  font-weight: bold;
  letter-spacing: 2px;
  font-size: 17px;
  line-height: 40px;
  height: 30px;
  display: flex;
  justify-content: space-between;
}

.hmi-div {
  width: 100%;
  height: 35vh;
}

:deep(.el-tabs--top) {
  border: none;
}

.query {
  display: flex;
  justify-content: start;
  align-items: center;
  padding-top: 5px;
}
:deep(.el-tabs__nav-wrap) {
  padding-left: 10px;
  .el-tabs__item {
    padding: 10px;
  }
}
:deep(.el-tabs__nav-wrap::after) {
  position: static !important;
}
</style>
