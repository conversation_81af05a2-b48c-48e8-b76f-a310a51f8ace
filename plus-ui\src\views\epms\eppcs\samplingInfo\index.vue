<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :inline="true" :model="queryParams" label-width="100px">
            <el-form-item label="取样时间" prop="samplingTimeRange">
              <el-date-picker
                v-model="queryParams.samplingTimeRange"
                clearable
                end-placeholder="结束日期"
                range-separator="至"
                start-placeholder="开始日期"
                type="daterange"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="样品编号" prop="sampleNumber">
              <el-input v-model="queryParams.sampleNumber" clearable placeholder="请输入样品编号" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="属地单位" prop="localUnitId">
              <el-select v-model="queryParams.localUnitId" clearable filterable placeholder="请选择属地单位">
                <el-option v-for="dept in localDeptOptions" :key="dept.deptId" :label="dept.deptName" :value="dept.deptId" />
              </el-select>
            </el-form-item>
            <el-form-item label="取样状态" prop="samplingStatus">
              <el-select v-model="queryParams.samplingStatus" clearable placeholder="请选择取样状态">
                <el-option v-for="dict in eppcs_sampling_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:samplingInfo:add']" icon="Plus" plain type="primary" @click="handleAdd">填报取样</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:samplingInfo:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:samplingInfo:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:samplingInfo:export']" icon="Download" plain type="warning" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="samplingInfoList" border stripe @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column
          :index="(index) => (queryParams.pageNum - 1) * queryParams.pageSize + index + 1"
          align="center"
          label="序号"
          type="index"
          width="55"
        />
        <el-table-column align="center" label="检测项目名称" prop="projectName" show-overflow-tooltip />
        <el-table-column align="center" label="样品编号" prop="sampleNumber" show-overflow-tooltip />
        <el-table-column align="center" label="取样点位" prop="samplingPoint" show-overflow-tooltip />
        <el-table-column align="center" label="取样时间" prop="samplingTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.samplingTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="取样类型" prop="samplingType" show-overflow-tooltip />
        <el-table-column align="center" label="取样量" prop="samplingAmount" width="80">
          <template #default="scope">
            <span>{{ scope.row.samplingAmount }}ml</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="取样人" prop="sampler" show-overflow-tooltip />
        <el-table-column align="center" label="取样状态" prop="samplingStatus" width="140">
          <template #default="scope">
            <dict-tag :options="eppcs_sampling_status" :value="scope.row.samplingStatus" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="审批时间" prop="approvalTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.approvalTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="审批意见" prop="approvalRemark" show-overflow-tooltip />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button icon="View" link type="primary" @click="handleView(scope.row)">查看</el-button>
            <el-tooltip v-if="[0, 1].includes(scope.row.samplingStatus)" content="修改" placement="top">
              <el-button v-hasPermi="['eppcs:samplingInfo:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"> 修改 </el-button>
            </el-tooltip>
            <el-tooltip v-if="[0, 1].includes(scope.row.samplingStatus)" content="提交" placement="top">
              <el-button v-hasPermi="['eppcs:samplingInfo:submit']" icon="Upload" link type="success" @click="handleSubmit(scope.row)">
                提交
              </el-button>
            </el-tooltip>

            <el-tooltip v-if="canApprove(scope.row)" content="审批" placement="top">
              <el-button v-hasPermi="['eppcs:samplingInfo:approve']" icon="Check" link type="warning" @click="handleApprove(scope.row)">
                审批
              </el-button>
            </el-tooltip>

            <el-tooltip v-if="[0, 1].includes(scope.row.samplingStatus)" content="删除" placement="top">
              <el-button v-hasPermi="['eppcs:samplingInfo:remove']" icon="Delete" link type="danger" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </el-tooltip>

            <el-button v-if="scope.row.processPhoto || scope.row.sealPhoto" icon="Document" link type="info" @click="handlePreview(scope.row)"
              >附件</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改取样信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" append-to-body width="1000px">
      <el-form ref="samplingInfoFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="取样申请" prop="applicationId">
              <el-select
                v-model="form.applicationId"
                :disabled="isViewMode"
                placeholder="请选择取样申请"
                style="width: 100%"
                @change="handleApplicationChange"
              >
                <el-option
                  v-for="app in applicationOptions"
                  :key="app.applicationId"
                  :label="`${app.projectName} - ${app.samplingPoint}`"
                  :value="app.applicationId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品编号" prop="sampleNumber">
              <el-input v-model="form.sampleNumber" :disabled="isViewMode" placeholder="请输入样品编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="取样点位" prop="samplingPoint">
              <el-input v-model="form.samplingPoint" :disabled="true" placeholder="根据选择的取样申请自动填充" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="取样时间" prop="samplingTime">
              <el-date-picker
                v-model="form.samplingTime"
                :disabled="isViewMode"
                placeholder="请选择取样时间"
                style="width: 100%"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="取样类型" prop="samplingType">
              <el-input v-model="form.samplingType" :disabled="isViewMode" placeholder="请输入取样类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="取样量(ml)" prop="samplingAmount">
              <el-input-number v-model="form.samplingAmount" :disabled="isViewMode" placeholder="请输入取样量" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="取样人" prop="sampler">
              <el-input v-model="form.sampler" :disabled="isViewMode" placeholder="请输入取样人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="取样过程照片" prop="processPhoto">
          <attachFileUpload
            v-model="form.processPhoto"
            :attach-source-id="form.samplingId"
            :disabled="isViewMode"
            :file-size="50"
            :limit="10"
            attach-category="process"
            attach-source-type="samplingInfo"
          />
        </el-form-item>
        <el-form-item label="取样瓶口封条照片" prop="sealPhoto">
          <attachFileUpload
            v-model="form.sealPhoto"
            :attach-source-id="form.samplingId"
            :disabled="isViewMode"
            :file-size="50"
            :limit="10"
            attach-category="seal"
            attach-source-type="samplingInfo"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" :disabled="isViewMode" :rows="3" placeholder="请输入备注信息" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!isViewMode" :loading="buttonLoading" type="primary" @click="submitForm('save')">保存</el-button>
          <el-button
            v-if="!isViewMode && (form.samplingStatus === 0 || form.samplingStatus === 1)"
            :loading="buttonLoading"
            type="success"
            @click="submitForm('submit')"
            >提交</el-button
          >
          <el-button @click="cancel">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog v-model="approvalDialog.visible" append-to-body title="审批取样信息" width="700px">
      <el-form ref="approvalFormRef" :model="form" :rules="approvalRules" label-width="120px">
        <el-card shadow="never" style="margin-bottom: 20px">
          <template #header>
            <span style="font-weight: bold">取样信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="样品编号">
                <span>{{ form.sampleNumber }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="取样点位">
                <span>{{ form.samplingPoint }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="取样时间">
                <span>{{ parseTime(form.samplingTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="取样类型">
                <span>{{ form.samplingType }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="取样量">
                <span>{{ form.samplingAmount }}ml</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="取样人">
                <span>{{ form.sampler }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="当前状态">
                <dict-tag :options="eppcs_sampling_status" :value="form.samplingStatus" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item v-if="form.remark" label="备注信息">
            <span>{{ form.remark }}</span>
          </el-form-item>
          <el-form-item label="取样过程照片" prop="processPhoto">
            <attachFileUpload
              v-model="form.processPhoto"
              :attach-source-id="form.samplingId"
              :disabled="true"
              attach-category="process"
              attach-source-type="samplingInfo"
            />
          </el-form-item>
          <el-form-item label="取样瓶口封条照片" prop="sealPhoto">
            <attachFileUpload
              v-model="form.sealPhoto"
              :attach-source-id="form.samplingId"
              :disabled="true"
              attach-category="seal"
              attach-source-type="samplingInfo"
            />
          </el-form-item>
        </el-card>

        <el-form-item label="审批意见" prop="approvalRemark">
          <el-input v-model="form.approvalRemark" :rows="4" placeholder="请输入审批意见（必填）" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="approvalLoading" type="success" @click="handleApprovalSubmit(1)">通过</el-button>
          <el-button :loading="approvalLoading" type="danger" @click="handleApprovalSubmit(2)">驳回</el-button>
          <el-button @click="approvalDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" name="SamplingInfo" setup>
import {
  addSamplingInfo,
  approveSamplingInfo,
  delSamplingInfo,
  getSamplingInfo,
  listSamplingInfo,
  submitSamplingInfo,
  updateSamplingInfo
} from '@/api/epms/eppcs/samplingInfo';
import { SamplingInfoForm, SamplingInfoQuery, SamplingInfoVO } from '@/api/epms/eppcs/samplingInfo/types';
import { listSamplingApplication } from '@/api/epms/eppcs/samplingApplication';
import { optionselectAll } from '@/api/system/dept';

import dayjs from 'dayjs';
import { useUserStore } from '@/store/modules/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eppcs_sampling_status } = toRefs<any>(proxy?.useDict('eppcs_sampling_status'));

const samplingInfoList = ref<SamplingInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const samplingInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 审批相关
const approvalDialog = reactive({
  visible: false
});
const approvalFormRef = ref<ElFormInstance>();
const approvalLoading = ref(false);
const approvalRules = reactive({
  approvalRemark: [{ required: true, message: '审批意见不能为空', trigger: 'blur' }]
});

// 部门选项
const localDeptOptions = ref<any[]>([]);
// 取样申请选项
const applicationOptions = ref<any[]>([]);

// 表单状态
const isNewForm = computed(() => !form.value.samplingId);
const isViewMode = ref(false);

const initFormData: SamplingInfoForm = {
  samplingId: undefined,
  applicationId: undefined,
  sampleNumber: undefined,
  samplingTime: undefined,
  samplingType: undefined,
  samplingAmount: undefined,
  sampler: undefined,
  processPhoto: undefined,
  sealPhoto: undefined,
  samplingStatus: 0, // 默认待取样状态
  remark: undefined,
  approvalTime: undefined,
  approvalRemark: undefined
};

const data = reactive<PageData<SamplingInfoForm, SamplingInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    sampleNumber: undefined,
    samplingTimeRange: undefined,
    localUnitId: undefined,
    samplingStatus: undefined,
    params: {}
  },
  rules: {
    applicationId: [{ required: true, message: '取样申请不能为空', trigger: 'change' }],
    sampleNumber: [{ required: true, message: '样品编号不能为空', trigger: 'blur' }],
    samplingTime: [{ required: true, message: '取样时间不能为空', trigger: 'change' }],
    samplingType: [{ required: true, message: '取样类型不能为空', trigger: 'blur' }],
    samplingAmount: [{ required: true, message: '取样量不能为空', trigger: 'blur' }],
    sampler: [{ required: true, message: '取样人不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询取样信息列表 */
const getList = async () => {
  loading.value = true;
  try {
    // 处理时间范围查询
    const params = { ...queryParams.value };
    if (params.samplingTimeRange && params.samplingTimeRange.length === 2) {
      params.samplingTimeStart = params.samplingTimeRange[0];
      params.samplingTimeEnd = params.samplingTimeRange[1];
      delete params.samplingTimeRange;
    }

    const res = await listSamplingInfo(params);
    const samplingList = res.rows || [];

    // 收集申请ID并批量查询申请信息
    if (samplingList.length > 0) {
      const applicationIds = [...new Set(samplingList.map((item) => item.applicationId).filter((id) => id))];

      if (applicationIds.length > 0) {
        try {
          // 批量查询取样申请信息
          const applicationRes = await listSamplingApplication({
            applicationIds: applicationIds.join(','),
            pageNum: 1,
            pageSize: applicationIds.length
          });

          const applicationMap = new Map();
          (applicationRes.rows || []).forEach((app) => {
            applicationMap.set(app.applicationId, app);
          });

          // 填充项目名称和取样点位
          samplingList.forEach((item) => {
            const application = applicationMap.get(item.applicationId);
            if (application) {
              item.projectName = application.projectName;
              item.samplingPoint = application.samplingPoint;
            }
          });
        } catch (error) {
          console.warn('查询申请信息失败:', error);
        }
      }
    }

    samplingInfoList.value = samplingList;
    total.value = res.total;
  } catch (error) {
    console.error('查询列表失败:', error);
    proxy?.$modal.msgError('查询列表失败');
  } finally {
    loading.value = false;
  }
};

/** 取消按钮 */
const cancel = () => {
  reset();
  isViewMode.value = false;
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  samplingInfoFormRef.value?.resetFields();
  getApplicationList();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: SamplingInfoVO[]) => {
  ids.value = selection.map((item) => item.samplingId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = async () => {
  reset();
  isViewMode.value = false;

  // 设置默认值
  form.value.samplingTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  form.value.samplingStatus = 0; // 待取样状态

  // 获取当前用户信息作为取样人
  try {
    const userProfile = useUserStore();
    if (userProfile.nickname) {
      form.value.sampler = userProfile.nickname;
    }
  } catch (error) {
    console.warn('获取用户信息失败:', error);
  }

  dialog.visible = true;
  dialog.title = '填报取样信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: SamplingInfoVO) => {
  reset();
  isViewMode.value = false;
  const _samplingId = row?.samplingId || ids.value[0];
  const res = await getSamplingInfo(_samplingId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改取样信息';
};

/** 提交按钮 */
const submitForm = (action: 'save' | 'submit' = 'save') => {
  samplingInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        let samplingId = form.value.samplingId;

        if (samplingId) {
          // 修改
          await updateSamplingInfo(form.value);
        } else {
          // 新增
          const res = await addSamplingInfo(form.value);
          samplingId = res.data; // 直接获取返回的ID
          form.value.samplingId = samplingId;
        }

        // 如果是提交操作，调用提交接口
        if (action === 'submit' && samplingId) {
          await submitSamplingInfo(samplingId, 1);
        }
        proxy?.$modal.msgSuccess('操作成功');

        dialog.visible = false;
        await getList();
      } catch (error) {
        console.error('操作失败:', error);
        proxy?.$modal.msgError('操作失败');
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: SamplingInfoVO) => {
  const _samplingIds = row?.samplingId || ids.value;
  await proxy?.$modal.confirm('是否确认删除取样信息编号为"' + _samplingIds + '"的数据项？').finally(() => (loading.value = false));
  await delSamplingInfo(_samplingIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/eppcs/samplingInfo/export',
    {
      ...queryParams.value
    },
    `samplingInfo_${new Date().getTime()}.xlsx`
  );
};

/** 判断是否可以审批 */
const canApprove = (row: SamplingInfoVO) => {
  // 待属地现场负责人确认、待属地领导确认状态：2,3
  return [2, 3].includes(row.samplingStatus);
};

/** 跳转预览 */
const handlePreview = async (row?: SamplingInfoVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.samplingId,
    attachSourceType: 'samplingInfo',
    attachCategory: 'general'
  });
};

/** 提交取样信息 */
const handleSubmit = async (row: SamplingInfoVO) => {
  try {
    await proxy?.$modal.confirm('确认提交该取样信息吗？提交后将进入审批流程，无法修改。');
    await submitSamplingInfo(row.samplingId, 1);
    proxy?.$modal.msgSuccess('提交成功');
    await getList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error);
      proxy?.$modal.msgError('提交失败');
    }
  }
};

/** 审批取样信息 */
const handleApprove = (row: SamplingInfoVO) => {
  form.value = { ...row };
  approvalDialog.visible = true;
};

/** 提交审批 */
const handleApprovalSubmit = async (action: number) => {
  if (!approvalFormRef.value) return;

  const valid = await approvalFormRef.value.validate();
  if (!valid) return;

  try {
    approvalLoading.value = true;

    // 获取当前用户信息作为审批人
    const user = useUserStore();
    const approver = user?.nickname || user?.name || '当前用户';

    await approveSamplingInfo(
      form.value.samplingId,
      1, // 用户类型，这里默认为1
      action,
      form.value.approvalRemark,
      approver
    );

    const actionText = action === 1 ? '通过' : '驳回';
    proxy?.$modal.msgSuccess(`审批${actionText}成功`);
    approvalDialog.visible = false;
    await getList();
  } catch (error) {
    console.error('审批失败:', error);
    proxy?.$modal.msgError('审批失败');
  } finally {
    approvalLoading.value = false;
  }
};

/** 获取部门列表 */
const getDeptList = async () => {
  try {
    // 获取属地单位（部门类型为3）
    const localRes = await optionselectAll([], 3);
    localDeptOptions.value = localRes.data || [];
  } catch (error) {
    console.error('获取部门列表失败:', error);
  }
};

/** 获取取样申请列表 */
const getApplicationList = async () => {
  try {
    // 获取阶段为1（取样填报）的申请
    const res = await listSamplingApplication({ currentPhase: 1, pageSize: -1 });
    applicationOptions.value = res.rows || [];
  } catch (error) {
    console.error('获取取样申请列表失败:', error);
  }
};

/** 查看详情 */
const handleView = (row: SamplingInfoVO) => {
  getSamplingInfo(row.samplingId).then((response) => {
    form.value = response.data;
    isViewMode.value = true;
    dialog.visible = true;
    dialog.title = '查看取样信息详情';
  });
};

/** 处理取样申请选择变化 */
const handleApplicationChange = (applicationId: string | number) => {
  const selectedApp = applicationOptions.value.find((app) => app.applicationId === applicationId);
  if (selectedApp) {
    // 自动填充取样点位
    form.value.samplingPoint = selectedApp.samplingPoint;
    form.value.projectName = selectedApp.projectName;
  }
};

onMounted(async () => {
  await getDeptList();
  await getApplicationList();
  await getList();
});
</script>
