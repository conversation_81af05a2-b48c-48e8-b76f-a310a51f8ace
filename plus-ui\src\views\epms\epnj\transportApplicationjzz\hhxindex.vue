<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :inline="true" :model="queryParams" label-width="80px">
            <el-form-item label="申请名称" prop="appName">
              <el-input v-model="queryParams.appName" placeholder="请输入申请名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!--<el-form-item label="申请分类" prop="mediumCategory">
              <el-select v-model="queryParams.mediumCategory" placeholder="请选择申请分类" disabled>
                <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>-->
            <el-form-item label="所属作业区" prop="workAreaId">
              <el-select v-model="queryParams.workAreaId" class="searchDate" clearable filterable placeholder="选择所属作业区">
                <el-option
                  v-for="operationArea in operationAreaList"
                  :key="operationArea.operationAreaId"
                  :label="operationArea.operationAreaName"
                  :value="operationArea.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="单井名称" prop="wellName">
              <el-input v-model="queryParams.wellName" placeholder="请输入单井名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请日期" prop="params">
              <el-date-picker
                v-model="queryParams.params"
                clearable
                end-placeholder="结束时间"
                range-separator="至"
                start-placeholder="开始时间"
                type="datetimerange"
                value-format="YYYY-MM-DD HH:mm:ss"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="装车地点" prop="loadingLocation">
              <el-input v-model="queryParams.loadingLocation" placeholder="请输入装车地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卸车地点" prop="unloadLocation">
              <el-input v-model="queryParams.unloadLocation" placeholder="请输入卸车地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="applicationStatus">
              <el-select v-model="queryParams.applicationStatus" clearable placeholder="请选择状态">
                <el-option v-for="dict in epfy_application_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epfy:transportApplication:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epfy:transportApplication:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['epfy:transportApplication:remove']"
              :disabled="multiple"
              icon="Delete"
              plain
              type="danger"
              @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epfy:transportApplication:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="transportApplicationList"
        :row-class-name="tableRowClassName"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="申请名称" prop="appName" width="200" />
        <el-table-column label="申请分类" align="center" prop="mediumCategory">
          <template #default="scope">
            <dict-tag :options="epfy_medium_category" :value="scope.row.mediumCategory" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="所属作业区" prop="workAreaId" width="135">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.workAreaId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="单井名称" prop="wellName" width="100" />
        <el-table-column label="申请日期" align="center" prop="applicationDate" min-width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="装车地点" prop="loadingLocation" width="100" />
        <!-- <el-table-column label="卸车地点类型" align="center" width="120" prop="unloadLocationType">
          <template #default="scope">
            <dict-tag :options="epfy_unload_location_type" :value="scope.row.unloadLocationType"/>
          </template>
        </el-table-column>-->
        <el-table-column align="center" label="卸车地点" prop="unloadLocation" width="130" />
        <el-table-column label="状态" align="center" prop="applicationStatus">
          <template #default="scope">
            <dict-tag :options="epfy_application_status" :value="scope.row.applicationStatus" />
          </template>
        </el-table-column>
        <el-table-column label="审核意见" align="center" prop="rejectSuggestion" />
        <el-table-column label="审核时间" align="center" prop="examineApproveTime" min-width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.examineApproveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="拉运状态" prop="transportStatus">
          <template #default="scope">
            <dict-tag :options="transport_application_status" :value="scope.row.transportStatus" />
          </template>
        </el-table-column>
        <el-table-column label="备注信息" align="center" prop="remark" />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" min-width="210">
          <template #default="scope">
            <!--     <el-tooltip content="报告预览" placement="top">
              <el-button link type="primary" icon="Document" @click="handlePreview(scope.row)" v-hasPermi="['epfy:transportApplication:preview']">报告预览</el-button>
            </el-tooltip>-->
            <el-tooltip content="审核" placement="top">
              <el-button
                v-hasPermi="['epfy:transportApplication:coordinate']"
                icon="Coordinate"
                link
                type="primary"
                @click="handleCoordinate(scope.row)"
                >审核</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改预警时间" placement="top">
              <el-button
                v-hasPermi="['epfy:transportApplication:coordinate']"
                icon="Edit"
                link
                type="primary"
                @click="handleUpdateWarnTime(scope.row)"
                >修改预警时间</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epfy:transportApplication:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"
                >修改</el-button
              >
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epfy:transportApplication:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改拉运申请对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="transportApplicationFormRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="申请名称" prop="appName">
          <el-input v-model="form.appName" placeholder="请输入申请名称" />
        </el-form-item>
        <el-form-item label="申请分类" prop="mediumCategory">
          <el-select v-model="form.mediumCategory" placeholder="请选择申请分类" disabled>
            <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属作业区" prop="workAreaId">
          <el-select filterable v-model="form.workAreaId" placeholder="选择所属作业区" clearable @change="changeSuoShuDi">
            <el-option
              v-for="operationArea in operationAreaList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="单井名称" prop="relatedId">
          <el-select v-model="form.relatedId" placeholder="请选择单井" @change="changeWellPreparation">
            <el-option v-for="item in wellPreparationList" :key="item.prepId" :label="item.wellName" :value="item.prepId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请日期" prop="applicationDate">
          <el-date-picker v-model="form.applicationDate" clearable placeholder="请选择申请日期" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="装车地点" prop="loadingLocation">
          <el-input v-model="form.loadingLocation" disabled placeholder="请输入装车地点" />
        </el-form-item>
        <el-form-item label="卸车地点" prop="unloadLocation">
          <el-select filterable v-model="form.unloadLocation" placeholder="选择卸车地点" clearable allow-create>
            <el-option
              v-for="operationArea in jizhongzhanList"
              :key="operationArea.operationAreaName"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaName"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="suggestion" label="审核意见" prop="rejectSuggestion">
          <el-input v-model="form.rejectSuggestion" type="textarea" disabled placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核申请对话框 -->
    <el-dialog :title="cooDialog.title" v-model="cooDialog.visible" width="500px" append-to-body>
      <el-form ref="transportApplicationFormRef" :model="form" :rules="coorules" label-width="110px">
        <el-form-item label="申请名称" prop="appName">
          <el-input disabled v-model="form.appName" placeholder="请输入申请名称" />
        </el-form-item>
        <el-form-item label="申请分类" prop="mediumCategory">
          <el-select v-model="form.mediumCategory" placeholder="请选择申请分类" disabled>
            <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属作业区" prop="workAreaId">
          <el-select v-model="form.workAreaId" class="searchDate" clearable disabled filterable placeholder="选择所属作业区">
            <el-option
              v-for="operationArea in operationAreaList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="单井名称" prop="wellName">
          <el-input v-model="form.wellName" disabled placeholder="请输入单井名称" />
        </el-form-item>
        <el-form-item label="申请日期" prop="applicationDate">
          <el-date-picker
            v-model="form.applicationDate"
            clearable
            disabled
            placeholder="请选择申请日期"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="装车地点" prop="loadingLocation">
          <el-input disabled v-model="form.loadingLocation" placeholder="请输入装车地点" />
        </el-form-item>
        <el-form-item label="卸车地点" prop="unloadLocation">
          <el-input v-model="form.unloadLocation" disabled placeholder="请输入卸车地点" />
        </el-form-item>
        <el-form-item label="状态" prop="applicationStatus">
          <el-radio-group v-model="form.applicationStatus">
            <el-radio v-for="dict in epfy_application_status" :key="dict.value" :value="parseInt(dict.value)">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="预警时间" prop="warnTime" v-hasPermi="['epfy:transportApplication:coordinate']">
          <el-input v-model="form.warnTime">
            <template #append>小时</template>
          </el-input>
        </el-form-item>
        <el-form-item label="审核意见" prop="rejectSuggestion">
          <el-input v-model="form.rejectSuggestion" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" type="textarea" disabled placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="cooSubmitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改预警时间 -->
    <el-dialog v-model="warnTimeDialog.visible" :title="warnTimeDialog.title" append-to-body width="500px">
      <el-form ref="transportApplicationFormRef" :model="form" label-width="110px">
        <el-form-item label="预警时间" prop="warnTime">
          <el-input v-model="form.warnTime">
            <template #append>小时</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="warnTimeSubmitForm">确 定</el-button>
          <el-button @click="warnTimeDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TransportApplication" lang="ts">
import {
  addTransportApplication,
  delTransportApplication,
  getTransportApplication,
  listTransportApplication,
  updateTransportApplication,
  updateTransportApplicationCoo
} from '@/api/epms/epfy/transportApplication';
import { TransportApplicationForm, TransportApplicationQuery, TransportApplicationVO } from '@/api/epms/epfy/transportApplication/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { WellPreparationQuery, WellPreparationVO } from '@/api/epms/epnj/wellPreparation/types';
import { listWellPreparation } from '@/api/epms/epnj/wellPreparation';
import dayjs from 'dayjs';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epfy_medium_category, epfy_application_status, epfy_unload_location_type, transport_application_status } = toRefs<any>(
  proxy?.useDict('epfy_medium_category', 'epfy_application_status', 'epfy_unload_location_type', 'transport_application_status')
);

const transportApplicationList = ref<TransportApplicationVO[]>([]);
const operationAreaQuery = ref<OperationAreaQuery>({});
const operationAreaList = ref<OperationAreaVO[]>([]); // 所属作业区列表
const jizhongzhanList = ref<OperationAreaVO[]>([]); // 卸车地点集中站列表
const allJizhongzhanList = ref<OperationAreaVO[]>([]); // 卸车地点集中站列表
const wellPreparationList = ref<WellPreparationVO[]>([]); //井场准备
const allWellPreparationList = ref<WellPreparationVO[]>([]); //井场准备
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const suggestion = ref(false); //拉运申请审核意见是否显示

const queryFormRef = ref<ElFormInstance>();
const transportApplicationFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const cooDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: TransportApplicationForm = {
  appId: undefined,
  relatedId: undefined,
  appName: undefined,
  workAreaId: undefined,
  wellName: undefined,
  // 默认当前年月日 小时分钟
  applicationDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  loadingLocation: undefined,
  unloadLocationType: 2,
  unloadLocation: undefined,
  file: undefined,
  applicationStatus: 1,
  rejectSuggestion: undefined,
  flowType: 2, //泥浆不落地拉运申请
  mediumCategory: 3, //分类: 1固相, 2液相, 3混合相
  remark: undefined
};
const data = reactive<PageData<TransportApplicationForm, TransportApplicationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    relatedId: undefined,
    appName: undefined,
    workAreaId: undefined,
    wellName: undefined,
    loadingLocation: undefined,
    unloadLocationType: 2,
    unloadLocation: undefined,
    file: undefined,
    applicationStatus: undefined,
    rejectSuggestion: undefined,
    flowType: 2,
    mediumCategory: 3, //分类: 混合相
    params: {}
  },
  rules: {
    appId: [{ required: true, message: '申请ID不能为空', trigger: 'blur' }],
    appName: [{ required: true, message: '申请名称不能为空', trigger: 'blur' }],
    mediumCategory: [{ required: true, message: '申请分类不能为空', trigger: 'change' }],
    workAreaId: [{ required: true, message: '所属作业区不能为空', trigger: 'blur' }],
    relatedId: [{ required: true, message: '单井名称不能为空', trigger: 'change' }],
    applicationDate: [{ required: true, message: '申请日期不能为空', trigger: 'blur' }],
    loadingLocation: [{ required: true, message: '装车地点不能为空', trigger: 'blur' }],
    unloadLocationType: [{ required: true, message: '卸车地点类型不能为空', trigger: 'change' }],
    unloadLocation: [{ required: true, message: '卸车地点不能为空', trigger: 'blur' }]
  }
});
const { queryParams, form, rules } = toRefs(data);

const coorules = reactive<ElFormRules>({
  appId: [{ required: true, message: '申请ID不能为空', trigger: 'blur' }],
  appName: [{ required: true, message: '申请名称不能为空', trigger: 'blur' }],
  workAreaId: [{ required: true, message: '所属作业区不能为空', trigger: 'blur' }],
  relatedId: [{ required: true, message: '单井名称不能为空', trigger: 'change' }],
  wellName: [{ required: true, message: '单井名称不能为空', trigger: 'blur' }],
  applicationDate: [{ required: true, message: '申请日期不能为空', trigger: 'blur' }],
  loadingLocation: [{ required: true, message: '装车地点不能为空', trigger: 'blur' }],
  unloadLocationType: [{ required: true, message: '卸车地点类型不能为空', trigger: 'change' }],
  unloadLocation: [{ required: true, message: '卸车地点不能为空', trigger: 'blur' }],
  applicationStatus: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  rejectSuggestion: [{ required: true, message: '审核意见不能为空', trigger: 'blur' }]
});

const wellQuery = reactive<WellPreparationQuery>(<WellPreparationQuery>{
  pageSize: -1,
  handlingType: 1,
  workArea: undefined
});
/** 查询拉运申请列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTransportApplication(queryParams.value);
  transportApplicationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  cooDialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  transportApplicationFormRef.value?.resetFields();
  changeSuoShuDi(null);
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TransportApplicationVO[]) => {
  ids.value = selection.map((item) => item.appId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  getoperationAreaList();
  reset();
  suggestion.value = false;
  dialog.visible = true;
  dialog.title = '添加拉运申请';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TransportApplicationVO) => {
  getoperationAreaList();
  reset();
  const _appId = row?.appId || ids.value[0];
  const res = await getTransportApplication(_appId);
  Object.assign(form.value, res.data);
  await changeSuoShuDi(res.data.workAreaId);
  suggestion.value = true;
  dialog.visible = true;
  dialog.title = '修改拉运申请';
};

/** 提交按钮 */
const submitForm = () => {
  transportApplicationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.appId) {
        await updateTransportApplication(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTransportApplication(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      cooDialog.visible = false;
      await getList();
    }
  });
};

/** 提交按钮 */
const cooSubmitForm = () => {
  transportApplicationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.appId) {
        await updateTransportApplicationCoo(
          form.value.appId,
          form.value.applicationStatus,
          form.value.rejectSuggestion,
          form.value.warnTime,
          1
        ).finally(() => (buttonLoading.value = false));
      } else {
        await addTransportApplication(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      cooDialog.visible = false;
      await getList();
    }
  });
};

/** 审核按钮操作 */
const handleCoordinate = async (row?: TransportApplicationVO) => {
  getoperationAreaList();
  reset();
  const _appId = row?.appId || ids.value[0];
  const res = await getTransportApplication(_appId);
  Object.assign(form.value, res.data);
  await changeSuoShuDi(res.data.workAreaId);
  suggestion.value = true;
  cooDialog.visible = true;
  cooDialog.title = '审核拉运申请';
};

/** 删除按钮操作 */
const handleDelete = async (row?: TransportApplicationVO) => {
  const _appIds = row?.appId || ids.value;
  await proxy?.$modal.confirm('是否确认删除拉运申请编号为"' + _appIds + '"的数据项？').finally(() => (loading.value = false));
  await delTransportApplication(_appIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epfy/transportApplication/export',
    {
      ...queryParams.value
    },
    `transportApplication_${new Date().getTime()}.xlsx`
  );
};

/**
 * 报告预览
 * @param row
 */
const handlePreview = async (row?: TransportApplicationVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.appId,
    attachSourceType: 'njTransportApplication',
    attachCategory: 'transportApplication'
  });
};
/**
 * 文件上传成功处理
 * @param fielInfo
 */
const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
};

const changeWellPreparation = (prepId: number | string) => {
  const wellPreparation = wellPreparationList.value.find((item) => item.prepId === form.value.relatedId);
  if (wellPreparation) {
    form.value.wellName = wellPreparation.wellName;
  }
  form.value.loadingLocation = form.value.wellName;
};

/** 查询所属作业区、单井、集中站列表 */
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaParentId = 0;
  operationAreaQuery.value.operationAreaType = 0;
  const res = await listOperationArea(operationAreaQuery.value); //查所属地
  operationAreaList.value = res.rows;

  operationAreaQuery.value.operationAreaParentId = null;
  operationAreaQuery.value.operationAreaType = 2;
  const jizhongzhanRes = await listOperationArea(operationAreaQuery.value); //查询集中站
  allJizhongzhanList.value = jizhongzhanRes.rows;
  jizhongzhanList.value = jizhongzhanRes.rows;

  const wellRes = await listWellPreparation(wellQuery); //查询单井
  allWellPreparationList.value = wellRes.rows;
  wellPreparationList.value = wellRes.rows;
};
const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationAreaItem = operationAreaList.value.find((item) => item.operationAreaId === operationAreaId);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};
/** 修改所属地 */
const changeSuoShuDi = async (workAreaId: number | string) => {
  if (workAreaId != null) {
    jizhongzhanList.value = allJizhongzhanList.value.filter((item) => item.operationAreaParentId === workAreaId);
    wellPreparationList.value = allWellPreparationList.value.filter((item) => item.workArea === workAreaId);
  } else {
    jizhongzhanList.value = allJizhongzhanList.value;
    wellPreparationList.value = allWellPreparationList.value;
  }
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};

const tableRowClassName = ({ row, rowIndex }) => {
  const warn =
    row.transportRecordCount == 0 && row.examineApproveTime != null && new Date(row.examineApproveTime) < new Date(Date.now() - 24 * 60 * 60 * 1000);
  if (warn) {
    return 'warn-row';
  }
  return '';
};

const warnTimeDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
/** 修改预警时间按钮操作 */
const handleUpdateWarnTime = async (row?: TransportApplicationVO) => {
  reset();
  const _appId = row?.appId || ids.value[0];
  const res = await getTransportApplication(_appId);
  Object.assign(form.value, res.data);

  warnTimeDialog.visible = true;
  warnTimeDialog.title = '修改预警时间';
};

/** 提交预警时间 */
const warnTimeSubmitForm = async () => {
  buttonLoading.value = true;
  if (form.value.appId) {
    await updateTransportApplication(form.value).finally(() => (buttonLoading.value = false));
  }
  proxy?.$modal.msgSuccess('操作成功');
  dialog.visible = false;
  warnTimeDialog.visible = false;
  await getList();
};
onMounted(() => {
  getoperationAreaList();
  getList();
});
</script>
