<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="96px">
            <el-form-item label="申请名称" prop="appName">
              <el-input v-model="queryParams.appName" placeholder="请输入申请名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="属地单位" prop="workAreaId">
              <el-select v-model="queryParams.workAreaId" class="searchDate" clearable filterable placeholder="选择属地单位">
                <el-option
                  v-for="operationArea in operationAreaList"
                  :key="operationArea.operationAreaId"
                  :label="operationArea.operationAreaName"
                  :value="operationArea.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="项目名称" prop="wellName">
              <el-input v-model="queryParams.wellName" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请日期" prop="params">
              <el-date-picker
                v-model="queryParams.params"
                clearable
                end-placeholder="结束时间"
                range-separator="至"
                start-placeholder="开始时间"
                type="datetimerange"
                value-format="YYYY-MM-DD HH:mm:ss"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="装液地点" prop="loadingLocation">
              <el-input v-model="queryParams.loadingLocation" placeholder="请输入装液地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卸液地点" prop="unloadLocation">
              <el-input v-model="queryParams.unloadLocation" placeholder="请输入卸液地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="归口部门状态" prop="endApplicationStatus">
              <el-select v-model="queryParams.endApplicationStatus" clearable placeholder="请选择状态">
                <el-option v-for="dict in epfy_end_application_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="作业区状态" prop="endOperationAreaApproveStatus">
              <el-select v-model="queryParams.endOperationAreaApproveStatus" clearable placeholder="请选择状态">
                <el-option v-for="dict in epfy_end_application_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epfy:transportApplication:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="transportApplicationList"
        :row-class-name="tableRowClassName"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="申请名称" prop="appName" width="300" />
        <el-table-column align="center" label="项目名称" prop="wellName" width="120" />
        <el-table-column align="center" label="属地单位" min-width="135" prop="workAreaId">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.workAreaId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="装液地点" min-width="110px" prop="loadingLocation" />
        <el-table-column align="center" label="卸液地点" min-width="110px" prop="unloadLocation" />
        <el-table-column align="center" label="拉运液量(方)" min-width="100px" prop="transportNumber" />
        <el-table-column label="申请日期" align="center" prop="applicationDate" min-width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.applicationDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束日期" align="center" min-width="110">
          <template #default="scope">
            <span v-if="[2].includes(scope.row.endApplicationStatus)">{{
              parseTime(scope.row.endExamineApproveTime, '{y}-{m}-{d} {h}:{i}:{s}')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="审核信息" min-width="220px">
          <template #default="scope">
            归口部门:
            <span v-if="[2].includes(scope.row.endApplicationStatus)">
              {{ getApproverName(transport_app_regulatory_authorities_approver, scope.row.endSuperviseApprover) }}
              {{ parseTime(scope.row.endExamineApproveTime, '{y}-{m}-{d}') }}
            </span>
            <span v-else style="display: inline-flex">
              <dict-tag :options="epfy_end_application_status" :value="scope.row.endApplicationStatus" /> </span
            ><br />
            作业区:
            <span v-if="[2].includes(scope.row.endOperationAreaApproveStatus)">
              {{ getApproverName(transport_app_operationarea_examine, scope.row.endOperationAreaReviewer) }}
              {{ parseTime(scope.row.endOperationAreaReviewedTime, '{y}-{m}-{d}') }}
            </span>
            <span v-else style="display: inline-flex; margin-top: 5px">
              <dict-tag :options="epfy_end_application_status" :value="scope.row.endOperationAreaApproveStatus" />
            </span>
          </template>
        </el-table-column>
        <el-table-column label="审核意见" align="center" prop="endRejectSuggestion" />
        <el-table-column label="备注信息" align="center" prop="remark" />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-tooltip
              v-if="[2].includes(scope.row.endOperationAreaApproveStatus) && [2].includes(scope.row.endApplicationStatus)"
              content="生成表格"
              placement="top"
            >
              <el-button link type="primary" icon="Download" @click="handleDownload(scope.row)">生成表格</el-button>
            </el-tooltip>
            <el-tooltip
              v-if="[0, 3].includes(scope.row.endApplicationStatus) || [0, 3].includes(scope.row.endOperationAreaApproveStatus)"
              content="拉运结束申请"
              placement="top"
            >
              <el-button v-hasPermi="['epfy:transportApplication:endApplication']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"
                >拉运结束申请</el-button
              >
            </el-tooltip>
            <el-tooltip v-if="[1].includes(scope.row.endApplicationStatus)" content="归口部门审核" placement="top">
              <el-button
                v-hasPermi="['epfy:transportApplication:guikou']"
                icon="Coordinate"
                link
                type="primary"
                @click="handleCoordinate(scope.row, 3)"
                >归口部门审核</el-button
              >
            </el-tooltip>
            <el-tooltip v-if="[1].includes(scope.row.endOperationAreaApproveStatus)" content="作业区审核" placement="top">
              <el-button
                v-hasPermi="['epfy:transportApplication:zuoyequ']"
                icon="Coordinate"
                link
                type="primary"
                @click="handleCoordinate(scope.row, 4)"
                >作业区审核</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改拉运申请对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="transportApplicationFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="申请名称" prop="appName">
          <el-input v-model="form.appName" disabled placeholder="请输入申请名称" />
        </el-form-item>
        <el-form-item label="申请分类" prop="mediumCategory">
          <el-select v-model="form.mediumCategory" placeholder="请选择申请分类" disabled>
            <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="属地单位" prop="workAreaId">
          <el-select filterable v-model="form.workAreaId" placeholder="选择属地单位" clearable disabled @change="changeSuoShuDi">
            <el-option
              v-for="operationArea in operationAreaList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称" prop="wellName">
          <el-input v-model="form.wellName" placeholder="请输入项目名称" disabled />
        </el-form-item>
        <el-form-item label="申请日期" prop="applicationDate">
          <el-date-picker
            v-model="form.applicationDate"
            clearable
            disabled
            placeholder="请选择申请日期"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="装液地点" prop="loadingLocation">
          <el-input v-model="form.loadingLocation" placeholder="请输入装液地点" disabled />
        </el-form-item>
        <el-form-item label="卸液地点" prop="unloadLocation">
          <el-select filterable v-model="form.unloadLocation" placeholder="选择卸液地点" clearable disabled allow-create>
            <el-option
              v-for="operationArea in pianquList"
              :key="operationArea.operationAreaName"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="介质类型" prop="mediumType">
          <el-select v-model="form.mediumType" disabled placeholder="请选择介质类型">
            <el-option v-for="dict in epfy_yexiang_medium_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="拉运液量">
          <el-input v-model="form.transportNumber" disabled>
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <el-form-item label="申请人" prop="createBy">
          <el-select filterable v-model="form.createBy" placeholder="选择申请人" disabled>
            <el-option v-for="user in userList" :key="user.userId" :label="user.nickName" :value="user.userId" />
          </el-select>
        </el-form-item>

        <el-form-item label="申请单位" prop="createDept">
          <el-select filterable v-model="form.createDept" placeholder="选择申请单位" disabled>
            <el-option v-for="dept in deptList" :key="dept.deptId" :label="dept.deptName" :value="dept.deptId" />
          </el-select>
        </el-form-item>
        <el-form-item label="归口部门审核人" prop="endSuperviseApprover">
          <el-select v-model="form.endSuperviseApprover" placeholder="请选择归口部门批准人">
            <el-option v-for="item in filteredJianGuan" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="作业区审核人" prop="endOperationAreaReviewer">
          <el-select v-model="form.endOperationAreaReviewer" placeholder="请选择作业区审核人">
            <el-option v-for="item in filteredZuoYeQu" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" disabled placeholder="请输入内容" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">提交申核</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核申请对话框 -->
    <el-dialog :title="cooDialog.title" v-model="cooDialog.visible" width="600px" append-to-body>
      <el-form ref="transportApplicationFormRef" :model="form" :rules="coorules" label-width="140px">
        <el-form-item label="申请名称" prop="appName">
          <el-input disabled v-model="form.appName" placeholder="请输入申请名称" />
        </el-form-item>
        <el-form-item label="申请分类" prop="mediumCategory">
          <el-select v-model="form.mediumCategory" placeholder="请选择申请分类" disabled>
            <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="属地单位" prop="workAreaId">
          <el-select v-model="form.workAreaId" class="searchDate" clearable disabled filterable placeholder="选择属地单位">
            <el-option
              v-for="operationArea in operationAreaList"
              :key="operationArea.operationAreaId"
              :label="operationArea.operationAreaName"
              :value="operationArea.operationAreaId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称" prop="wellName">
          <el-input disabled v-model="form.wellName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="申请日期" prop="applicationDate">
          <el-date-picker
            v-model="form.applicationDate"
            clearable
            disabled
            placeholder="请选择申请日期"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="装液地点" prop="loadingLocation">
          <el-input disabled v-model="form.loadingLocation" placeholder="请输入装液地点" />
        </el-form-item>
        <el-form-item label="卸液地点" prop="unloadLocation">
          <el-input v-model="form.unloadLocation" disabled placeholder="请输入卸液地点" />
        </el-form-item>
        <el-form-item label="介质类型" prop="mediumType">
          <el-select v-model="form.mediumType" placeholder="请选择介质类型" disabled>
            <el-option v-for="dict in epfy_yexiang_medium_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="拉运液量">
          <el-input v-model="form.transportNumber" disabled>
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <el-form-item label="归口部门审核人" prop="endSuperviseApprover" disabled>
          <el-select v-model="form.endSuperviseApprover" disabled placeholder="请选择批准人">
            <el-option v-for="item in filteredJianGuan" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="归口部门审核状态" prop="endApplicationStatus">
          <el-radio-group v-model="form.endApplicationStatus" :disabled="approveType != 3">
            <el-radio v-for="dict in epfy_end_application_status.slice(1)" :key="dict.value" :value="parseInt(dict.value)">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="作业区审核人" prop="endOperationAreaReviewer" disabled>
          <el-select v-model="form.endOperationAreaReviewer" placeholder="请选择审核人" disabled>
            <el-option v-for="item in filteredZuoYeQu" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="作业区审核状态" prop="endOperationAreaApproveStatus">
          <el-radio-group v-model="form.endOperationAreaApproveStatus" :disabled="approveType != 4">
            <el-radio v-for="dict in epfy_end_application_status.slice(1)" :key="dict.value" :value="parseInt(dict.value)">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="endRejectSuggestion">
          <el-input v-model="form.endRejectSuggestion" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" type="textarea" disabled placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="cooSubmitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TransportEnd" lang="ts">
import {
  addTransportApplication,
  getTransportApplication,
  listTransportApplication,
  updateTransportApplication,
  updateTransportApplicationCoo
} from '@/api/epms/epfy/transportApplication';
import { TransportApplicationForm, TransportApplicationQuery, TransportApplicationVO } from '@/api/epms/epfy/transportApplication/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { useUserStore } from '@/store/modules/user';
import { optionselectAll } from '@/api/system/dept';
import { userOptionSelectAll } from '@/api/system/user';
import { UserVO } from '@/api/system/user/types';
import dayjs from 'dayjs';
import { DeptVO } from '@/api/system/dept/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  epfy_medium_category,
  epfy_yexiang_medium_type,
  epfy_end_application_status,
  epfy_unload_location_type,
  transport_app_regulatory_authorities_approver,
  transport_app_operationarea_examine
} = toRefs<any>(
  proxy?.useDict(
    'epfy_medium_category',
    'epfy_yexiang_medium_type',
    'epfy_end_application_status',
    'epfy_unload_location_type',
    'transport_app_regulatory_authorities_approver',
    'transport_app_operationarea_examine'
  )
);
const userStore = useUserStore();
const transportApplicationList = ref<TransportApplicationVO[]>([]);
const operationAreaQuery = ref<OperationAreaQuery>({});
const operationAreaList = ref<OperationAreaVO[]>([]); // 属地单位列表
const allPianquList = ref<OperationAreaVO[]>([]); // 片区列表
const pianquList = ref<OperationAreaVO[]>([]); // 卸液地点列表
const userList = ref<UserVO[]>([]);
const deptList = ref<DeptVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const approveType = ref<number>(3);
const suggestion = ref(false); //拉运申请审核意见是否显示

const queryFormRef = ref<ElFormInstance>();
const transportApplicationFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const cooDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: TransportApplicationForm = {
  appId: undefined,
  relatedId: undefined,
  appName: undefined,
  workAreaId: undefined,
  wellName: undefined,
  // 默认当前年月日 小时分钟
  applicationDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  loadingLocation: undefined,
  unloadLocationType: 1,
  unloadLocation: undefined,
  file: undefined,
  applicationStatus: 1,
  rejectSuggestion: undefined,
  flowType: 1, //废液拉运申请
  mediumCategory: 2, //分类: 液相
  remark: undefined,
  superviseApprover: undefined,
  operationAreaReviewer: undefined,
  examineApproveTime: undefined,
  operationAreaReviewedTime: undefined,
  operationAreaApproveStatus: 1,
  createBy: userStore.userId,
  createDept: userStore.deptId,
  transportNumber: undefined,
  endSuperviseApprover: undefined,
  endOperationAreaReviewer: undefined,
  endApplicationStatus: 0, //结束申请待申请
  endOperationAreaApproveStatus: 0, //结束申请待申请
  endExamineApproveTime: undefined,
  endOperationAreaReviewedTime: undefined,
  endRejectSuggestion: undefined
};
const data = reactive<PageData<TransportApplicationForm, TransportApplicationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    relatedId: undefined,
    appName: undefined,
    workAreaId: undefined,
    wellName: undefined,
    loadingLocation: undefined,
    unloadLocationType: 1,
    unloadLocation: undefined,
    file: undefined,
    applicationStatus: undefined,
    endApplicationStatus: undefined,
    endOperationAreaApproveStatus: undefined,
    rejectSuggestion: undefined,
    flowType: 1,
    mediumCategory: 2, //分类: 液相
    params: {}
  },
  rules: {
    appId: [{ required: true, message: '申请ID，自动递增不能为空', trigger: 'blur' }],
    appName: [{ required: true, message: '申请名称不能为空', trigger: 'blur' }],
    mediumCategory: [{ required: true, message: '申请分类不能为空', trigger: 'change' }],
    workAreaId: [{ required: true, message: '属地单位不能为空', trigger: 'blur' }],
    wellName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
    applicationDate: [{ required: true, message: '申请日期不能为空', trigger: 'blur' }],
    loadingLocation: [{ required: true, message: '装液地点不能为空', trigger: 'blur' }],
    unloadLocationType: [{ required: true, message: '卸液地点类型不能为空', trigger: 'change' }],
    unloadLocation: [{ required: true, message: '卸液地点不能为空', trigger: 'blur' }],
    endSuperviseApprover: [{ required: true, message: '审核人不能为空', trigger: 'blur' }],
    endOperationAreaReviewer: [{ required: true, message: '审核人不能为空', trigger: 'blur' }]
    // file: [
    //   { required: true, message: "检测报告不能为空", trigger: "blur" }
    // ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const coorules = reactive<ElFormRules>({
  appId: [{ required: true, message: '申请ID，自动递增不能为空', trigger: 'blur' }],
  appName: [{ required: true, message: '申请名称不能为空', trigger: 'blur' }],
  workAreaId: [{ required: true, message: '属地单位不能为空', trigger: 'blur' }],
  wellName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
  applicationDate: [{ required: true, message: '申请日期不能为空', trigger: 'blur' }],
  loadingLocation: [{ required: true, message: '装液地点不能为空', trigger: 'blur' }],
  unloadLocationType: [{ required: true, message: '卸液地点类型不能为空', trigger: 'change' }],
  unloadLocation: [{ required: true, message: '卸液地点不能为空', trigger: 'blur' }],
  applicationStatus: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  operationAreaApproveStatus: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  endRejectSuggestion: [{ required: true, message: '审核意见不能为空', trigger: 'blur' }]
  // file: [
  //   { required: true, message: "检测报告不能为空", trigger: "blur" }
  // ],
});

//监管部门批准人字典筛选条件
const filterJianGuan = ref({
  mediumType: 'fy' //根据流程区分
});
//作业区审核人字典筛选条件
const filterZuoYeQu = ref({
  operationAreaId: undefined
});
const getApproverName = (dict, key) => {
  return dict?.find((item) => {
    return item.value == key;
  })?.label;
};

const getDeptName = (dict, key) => {
  return dict?.find((item) => {
    return item.deptId == key;
  })?.deptName;
};
// 判断是否超期
const isOverdue = (row) => {
  if (row.transportRecordCount != 0) {
    return false;
  }
  if (row.examineApproveTime != null && row.operationAreaReviewedTime != null) {
    const lastDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const examineApproveTime = new Date(row.examineApproveTime);
    const operationAreaReviewedTime = new Date(row.operationAreaReviewedTime);
    if (examineApproveTime < lastDate && operationAreaReviewedTime < lastDate) {
      return true;
    }
  }
  return false;
};
//过滤作业区审核人
const filteredZuoYeQu = computed(() => {
  filterZuoYeQu.value.operationAreaId = form.value.workAreaId;

  const result = transport_app_operationarea_examine.value?.filter((item) => {
    const matchStatus =
      filterZuoYeQu.value.operationAreaId !== undefined && filterZuoYeQu.value.operationAreaId !== null
        ? item.value.includes(filterZuoYeQu.value.operationAreaId)
        : true;
    return matchStatus;
  });
  return result;
});

//过滤直线监管部门批准人
const filteredJianGuan = computed(() => {
  const result = transport_app_regulatory_authorities_approver.value?.filter((item) => {
    return filterJianGuan.value.mediumType ? item.value.includes(filterJianGuan.value.mediumType) : true;
  });
  return result;
});

/** 查询拉运申请列表 */
const getList = async () => {
  loading.value = true;
  // 提取 epfy_end_application_status 的 value 值并赋值给 pageDisplayStatus
  if (epfy_end_application_status.value && Array.isArray(epfy_end_application_status.value)) {
    queryParams.value.params['pageDisplayStatus'] = 2; //查询拉运申请的审批状态为2
  }
  const res = await listTransportApplication(queryParams.value);
  transportApplicationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 查询user列表 */
const getUserList = async () => {
  loading.value = true;
  const res = await userOptionSelectAll([]);
  userList.value = res.data;
};

/** 查询user列表 */
const getDeptList = async () => {
  loading.value = true;
  const res = await optionselectAll([]);
  deptList.value = res.data;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  cooDialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  transportApplicationFormRef.value?.resetFields();
  changeSuoShuDi(null);
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TransportApplicationVO[]) => {
  ids.value = selection.map((item) => item.appId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 拉运结束申请按钮操作 */
const handleUpdate = async (row?: TransportApplicationVO) => {
  reset();
  const _appId = row?.appId || ids.value[0];
  const res = await getTransportApplication(_appId);
  Object.assign(form.value, res.data);
  await changeSuoShuDi(res.data.workAreaId);
  suggestion.value = true;
  dialog.visible = true;
  dialog.title = '拉运结束申请';
};

/** 拉运结束申请按钮操作 */
const handleDownload = async (row?: TransportApplicationVO) => {
  proxy?.download(
    'epfy/transportApplication/downloadTransportEndTable',
    {
      appId: row.appId
    },
    `拉运结束申请_${new Date().getTime()}.xlsx`
  );
};

/** 提交按钮 */
const submitForm = () => {
  transportApplicationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.appId) {
        form.value.reApprove = undefined;
        //提交拉运结束申请，设置审核状态等信息
        form.value.endApplicationStatus = 1;
        form.value.endOperationAreaApproveStatus = 1;
        form.value.endRejectSuggestion = '';
        form.value.endExamineApproveTime = '';
        form.value.endOperationAreaReviewedTime = '';
        await updateTransportApplication(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTransportApplication(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      cooDialog.visible = false;
      await getList();
    }
  });
};

/** 提交按钮 */
const cooSubmitForm = () => {
  transportApplicationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.appId) {
        const appId = form.value.appId;
        const endRejectSuggestion = form.value.endRejectSuggestion;
        const status = approveType.value == 3 ? form.value.endApplicationStatus : form.value.endOperationAreaApproveStatus;

        await updateTransportApplicationCoo(appId, status, endRejectSuggestion, null, approveType.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      cooDialog.visible = false;
      await getList();
    }
  });
};

/** 审核按钮操作 */
const handleCoordinate = async (row?: TransportApplicationVO, type?: number) => {
  reset();
  const _appId = row?.appId || ids.value[0];
  const res = await getTransportApplication(_appId);
  Object.assign(form.value, res.data);
  await changeSuoShuDi(res.data.workAreaId);
  approveType.value = type;
  suggestion.value = true;
  cooDialog.visible = true;
  cooDialog.title = '审核拉运申请';
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epfy/transportApplication/export',
    {
      ...queryParams.value
    },
    `transportApplication_${new Date().getTime()}.xlsx`
  );
};
/** 查询属地单位、单井、片区列表 */
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaParentId = 0;
  operationAreaQuery.value.operationAreaType = 0;
  const res = await listOperationArea(operationAreaQuery.value);
  operationAreaList.value = res.rows;

  operationAreaQuery.value.operationAreaParentId = null;
  operationAreaQuery.value.operationAreaType = 3;
  const pianquRes = await listOperationArea(operationAreaQuery.value); //查询片区
  allPianquList.value = pianquRes.rows;
  pianquList.value = pianquRes.rows;
};
const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationAreaItem = operationAreaList.value.find((item) => item.operationAreaId === operationAreaId);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};
/** 卸液地点 */
const changeSuoShuDi = async (workAreaId: number | string) => {
  if (workAreaId != null) {
    pianquList.value = allPianquList.value.filter((item) => item.operationAreaParentId === workAreaId);
  } else {
    pianquList.value = allPianquList.value;
  }
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};

const tableRowClassName = ({ row, rowIndex }) => {
  // const warn = isOverdue(row)
  // if (warn){
  //   return "warn-row"
  // }
  return '';
};
onMounted(() => {
  getoperationAreaList();
  getList();
  getUserList();
  getDeptList();
});
</script>
