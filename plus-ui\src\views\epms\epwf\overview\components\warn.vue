<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="全部" name="all">
        <el-table
          stripe
          class="table_style"
          v-loading="loading"
          :rowStyle="rowStyle"
          :data="warnList"
          @cell-click="handleCellClick"
          border
          style="width: 100%"
        >
          <el-table-column :index="indexMethod" align="center" label="序号" type="index" width="60px" />
          <el-table-column align="center" label="告警名称" prop="warnName">
            <template #default="scope">
              <el-tooltip :content="scope.row.warnName" placement="top">
                <span class="descStyle">{{ scope.row.warnName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          size="small"
          background
          layout="prev, pager, next"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          class="mt-4 page"
          @size-change="getList"
          @current-change="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { listWarnRecord } from '@/api/comm/warn/warnRecord/warnRecord';
import { TabsPaneContext } from 'element-plus';

const activeName = ref('all');
const loading = ref(false);
const Props = defineProps({
  systemType: {
    type: Number,
    default: 0
  }
});
const total = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 5,
  systemType: Props.systemType,
  warnLevel: undefined,
  warnStatus: 1
});
const warnList = ref([]);

const handleTabClick = (tab: TabsPaneContext, event: Event) => {
  if (tab.props.name == 'all') {
    queryParams.value.warnLevel = undefined;
  } else {
    queryParams.value.warnLevel = tab.props.name;
  }
  getList();
};
const getList = async () => {
  loading.value = true;
  const response = await listWarnRecord(queryParams.value);
  warnList.value = response.rows;
  total.value = response.total;
  loading.value = false;
};

/**
 * 单元格点击事件
 * @param row
 * @param column
 * @param cell
 * @param event
 */
const handleCellClick = (row, column, cell, event) => {
  // 跳转预览界面
};
const rowStyle = (row, index) => {
  return {
    cursor: 'pointer'
  };
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.descStyle {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.2;
}

.table_style {
  height: 240px;
}

.page {
  margin-top: 12px;
  height: 10px;
}
</style>
