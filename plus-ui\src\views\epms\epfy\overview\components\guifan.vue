<template>
  <div>
    <el-tabs type="card" @tab-click="handleTabClick">
      <el-tab-pane label="标准规范">
        <el-table
          v-loading="loading"
          class="table_style"
          :data="attachList"
          :rowStyle="rowStyle"
          border
          show-overflow-tooltip
          stripe
          style="width: 100%"
          @cell-click="handleCellClick"
        >
          <el-table-column :index="indexMethod" align="center" label="序号" type="index" width="60px" />
          <el-table-column align="center" label="文件名称" prop="documentName">
            <template #default="scope">
              <el-tooltip :content="scope.row.documentName" placement="top">
                <span class="descStyle">{{ scope.row.documentName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total1"
          background
          class="mt-4 page"
          layout="prev, pager, next"
          size="small"
          @size-change="getList"
          @current-change="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { listStandardDocument } from '@/api/epms/epcom/standardDocument';
import { listPublicityActivity } from '@/api/epms/eptw/publicityActivity';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const loading = ref(false);
const Props = defineProps({
  documentType: {
    type: Number
  },
  documentCategory: {
    type: Number
  }
});
const total1 = ref(0);
const total2 = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 5,
  documentType: Props.documentType,
  category: Props.documentCategory
});
const activityQueryParams = ref({
  pageNum: 1,
  pageSize: 5
});

const attachList = ref([]);
const activityList = ref([]);
const tab = ref(false);

const handleTabClick = () => {
  tab.value = !tab.value;
  if (!tab) {
    getList();
  } else {
    getActivityList();
  }
};
const getList = async () => {
  loading.value = true;
  const res = await listStandardDocument(queryParams.value);
  attachList.value = res.rows;
  total1.value = res.total;
  loading.value = false;
};

const getActivityList = async () => {
  loading.value = true;
  const res = await listPublicityActivity(activityQueryParams.value);
  activityList.value = res.rows;
  total2.value = res.total;
  loading.value = false;
};
/**
 * 单元格点击事件
 * @param row
 * @param column
 * @param cell
 * @param event
 */
const handleCellClick = (row, column, cell, event) => {
  proxy.showAttachPreview({
    attachSourceId: row.documentId
  });
};
const handleClick = (row, column, cell, event) => {
  proxy.showAttachPreview({
    attachSourceId: row.activityId
  });
};
const rowStyle = (row, index) => {
  return {
    cursor: 'pointer'
  };
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.descStyle {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.2;
}
.table_style {
  height: 240px;
}
.page {
  margin-top: 12px;
  height: 10px;
}
:deep(.el-table__cell) {
  padding: 5px 0 5px 0;
}
</style>
