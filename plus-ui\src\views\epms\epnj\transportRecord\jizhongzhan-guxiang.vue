<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="装车时间" prop="monthQuery">
              <el-date-picker v-model="queryParams.monthQuery" clearable placeholder="请选择装车时间" type="date" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="分类" prop="mediumCategory">
              <el-select v-model="queryParams.mediumCategory" disabled placeholder="请选择分类">
                <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="起运点" prop="departurePoint">
              <el-input v-model="queryParams.departurePoint" placeholder="请输入起运点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="车牌号" prop="licensePlate">
              <el-input v-model="queryParams.licensePlate" placeholder="请输入车牌号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="接收点" prop="arrivalPoint">
              <el-input v-model="queryParams.arrivalPoint" placeholder="请输入接收点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="接收人" prop="receiver">
              <el-input v-model="queryParams.receiver" placeholder="请输入接收人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="拉运申请名称" prop="appName">
              <el-input v-model="queryParams.appName" placeholder="请输入拉运申请名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epfy:transportRecord:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epfy:transportRecord:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epfy:transportRecord:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epfy:transportRecord:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="transportRecordList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column align="center" label="拉运申请" min-width="400" prop="applicationId">
          <template #default="scope">
            {{ toApplicationName[scope.row.applicationId] || '未知' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="装车时间" prop="transportTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.transportTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="拉运时间" prop="transportEndTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.transportEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>

        <el-table-column label="分类" align="center" prop="mediumCategory">
          <template #default="scope">
            <dict-tag :options="epfy_medium_category" :value="scope.row.mediumCategory" />
          </template>
        </el-table-column>
        <el-table-column label="介质类型" align="center" prop="mediumType">
          <template #default="scope">
            <dict-tag :options="epnj_suizuan_guxiang_medium_type" :value="scope.row.mediumType" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="起运点" min-width="130" prop="departurePoint" />
        <el-table-column label="数量(方)" align="center" prop="number" />
        <!--<el-table-column label="发送人" align="center" prop="sender" />-->
        <el-table-column label="拉运人" align="center" prop="transporter" />
        <el-table-column align="center" label="车牌号" prop="licensePlate" width="100" />
        <el-table-column align="center" label="接收点" min-width="130" prop="arrivalPoint" />
        <el-table-column label="接收人" align="center" prop="receiver" />
        <el-table-column align="center" label="卸车时间" min-width="105" prop="unloadTime" />
        <el-table-column label="备注信息" align="center" prop="remark" />
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="240">
          <template #default="scope">
            <el-tooltip content="拉运材料" placement="top">
              <el-button v-hasPermi="['epfy:transportRecord:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >拉运材料</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epfy:transportRecord:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epfy:transportRecord:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改拉运记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="transportRecordFormRef" :model="form" :rules="rules" label-width="130px">
        <el-form-item v-if="isUpdate == false" label="拉运申请" prop="applicationId">
          <el-select v-model="form.applicationId" filterable placeholder="请选择拉运申请" @change="setApplication">
            <el-option v-for="dict in unSportList" :key="dict.appId" :label="dict.appName" :value="dict.appId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-else label="拉运申请" prop="applicationId">
          <el-select v-model="form.applicationId" disabled filterable placeholder="请选择拉运申请">
            <el-option v-for="dict in applicationList" :key="dict.appId" :label="dict.appName" :value="dict.appId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="装车时间" prop="transportTime">
          <el-date-picker v-model="form.transportTime" clearable placeholder="请选择装车时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="拉运时间" prop="transportEndTime">
          <el-date-picker v-model="form.transportEndTime" clearable placeholder="请选择拉运时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="分类" prop="mediumCategory">
          <el-select v-model="form.mediumCategory" placeholder="请选择分类" disabled>
            <el-option v-for="dict in epfy_medium_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <!--<el-form-item label="介质类型" prop="mediumType">
          <el-select v-model="form.mediumType" placeholder="请选择介质类型">
            <el-option
              v-for="dict in epnj_suizuan_guxiang_medium_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>-->
        <el-form-item label="起运点" prop="departurePoint">
          <el-input v-model="form.departurePoint" disabled placeholder="请输入起运点" />
        </el-form-item>
        <el-form-item label="数量" prop="number">
          <el-input v-model="form.number" placeholder="请输入数量">
            <template #append>方</template>
          </el-input>
        </el-form-item>
        <!--        <el-form-item label="发送人" prop="sender">
          <el-input v-model="form.sender" placeholder="请输入发送人" />
        </el-form-item>-->
        <el-form-item label="拉运人" prop="transporter">
          <el-input v-model="form.transporter" placeholder="请输入拉运人" />
        </el-form-item>
        <el-form-item label="车牌号" prop="licensePlate">
          <el-select v-model="form.licensePlate" placeholder="请选择车牌号">
            <el-option v-for="dict in vehicleInfoList" :key="dict.carNumber" :label="dict.carNumber" :value="dict.carNumber"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接收点" prop="arrivalPoint">
          <el-input v-model="form.arrivalPoint" disabled placeholder="请输入接收点" />
        </el-form-item>
        <el-form-item label="接收人" prop="receiver">
          <el-input v-model="form.receiver" placeholder="请输入接收人" />
        </el-form-item>
        <el-form-item label="卸车时间" prop="unloadTime">
          <el-date-picker v-model="form.unloadTime" clearable placeholder="请选择卸车时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注信息" />
        </el-form-item>
        <el-form-item label="计量凭证或转运单" prop="measurementVoucher">
          <attachFileUpload
            v-model="form.measurementVoucher"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="transportMeasurement"
            attach-source-type="gxTransportRecord"
          />
        </el-form-item>
        <el-form-item label="装卸照片" prop="photo">
          <attachFileUpload
            v-model="form.photo"
            :attach-source-id="form.transportId"
            :disabled="false"
            attach-category="transportPhoto"
            attach-source-type="gxTransportRecord"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TransportRecord" lang="ts">
import {
  addTransportRecord,
  delTransportRecord,
  getTransportRecord,
  listTransportRecord,
  updateTransportRecord
} from '@/api/epms/epfy/transportRecord';
import { listVehicleInfo } from '@/api/epms/epcom/vehicleInfo';
import { VehicleInfoVO } from '@/api/epms/epcom/vehicleInfo/types';
import { TransportRecordForm, TransportRecordQuery, TransportRecordVO } from '@/api/epms/epfy/transportRecord/types';
import { listAllTransportApplication } from '@/api/epms/epfy/transportApplication';
import { TransportApplicationQuery, TransportApplicationVO } from '@/api/epms/epfy/transportApplication/types';
import dayjs from 'dayjs';

const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epfy_medium_category, epnj_suizuan_guxiang_medium_type } = toRefs<any>(
  proxy?.useDict('epfy_medium_category', 'epnj_suizuan_guxiang_medium_type')
);
const vehicleInfoList = ref<VehicleInfoVO[]>([]);
const transportRecordList = ref<TransportRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const tranQuery = reactive<TransportApplicationQuery>({});
const total = ref(0);
const applicationList = ref<TransportApplicationVO[]>([]);
// 未拉运的拉运申请
const unSportList = ref<TransportApplicationVO[]>([]);
const queryFormRef = ref<ElFormInstance>();
const transportRecordFormRef = ref<ElFormInstance>();
const isUpdate = ref(false);
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: TransportRecordForm = {
  transportId: undefined,
  applicationId: undefined,
  // 默认当前年月日 小时分钟
  transportTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  mediumCategory: 1,
  mediumType: 9, // 固定为泥饼
  departurePoint: undefined,
  number: undefined,
  sender: '-',
  transporter: undefined,
  licensePlate: undefined,
  arrivalPoint: undefined,
  receiver: undefined,
  remark: undefined,
  measurementVoucher: undefined,
  photo: undefined,
  flowType: 2
};
const data = reactive<PageData<TransportRecordForm, TransportRecordQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    applicationId: undefined,
    transportTime: undefined,
    mediumCategory: 1,
    mediumType: undefined,
    departurePoint: undefined,
    number: undefined,
    sender: undefined,
    transporter: undefined,
    licensePlate: undefined,
    arrivalPoint: undefined,
    receiver: undefined,
    measurementVoucher: undefined,
    photo: undefined,
    flowType: 2, // 泥浆流程
    unloadLocationType: 2, // 卸车地点 集中站
    appName: undefined,
    params: {}
  },
  rules: {
    applicationId: [{ required: true, message: '拉运申请不能为空', trigger: 'change' }],
    transportTime: [{ required: true, message: '装车时间不能为空', trigger: 'blur' }],
    transportEndTime: [{ required: true, message: '拉运时间不能为空', trigger: 'blur' }],
    mediumCategory: [{ required: true, message: '分类不能为空', trigger: 'change' }],
    mediumType: [{ required: true, message: '介质类型不能为空', trigger: 'change' }],
    departurePoint: [{ required: true, message: '起运点不能为空', trigger: 'blur' }],
    number: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
    /*    sender: [
      { required: true, message: "发送人不能为空", trigger: "blur" }
    ],*/
    transporter: [{ required: true, message: '拉运人不能为空', trigger: 'blur' }],
    licensePlate: [{ required: true, message: '车牌号不能为空', trigger: 'blur' }],
    arrivalPoint: [{ required: true, message: '接收点不能为空', trigger: 'blur' }],
    receiver: [{ required: true, message: '接收人不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询拉运记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTransportRecord(queryParams.value);
  transportRecordList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/**
 * 获取全部取水区块列表
 */
const getApplicationList = () => {
  tranQuery.flowType = 2;
  tranQuery.mediumCategory = 1;
  tranQuery.unloadLocationType = 2;
  applicationList.value = [];
  listAllTransportApplication(tranQuery).then((res) => {
    res.rows.forEach((item) => {
      item.appName = `${item.appName}-${item.wellName}-${item.applicationDate}`; // 使用模板字符串拼接
    });
    applicationList.value = res.rows;
  });
  let tranQuery2 = { ...tranQuery };
  tranQuery2.transportStatus = 0;
  listAllTransportApplication(tranQuery2).then((res) => {
    res.rows.forEach((item) => {
      item.appName = `${item.appName}-${item.wellName}-${item.applicationDate}`; // 使用模板字符串拼接
    });
    unSportList.value = res.rows;
  });
};
/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

const toApplicationName = computed(() => {
  return applicationList.value.reduce((map, item) => {
    map[item.appId] = item.appName;
    return map;
  }, {});
});

const getVehicleInfoList = async () => {
  const res = await listVehicleInfo();
  vehicleInfoList.value = res.rows;
  console.log(vehicleInfoList);
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  transportRecordFormRef.value?.resetFields();
  getVehicleInfoList();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TransportRecordVO[]) => {
  ids.value = selection.map((item) => item.transportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  isUpdate.value = false;
  dialog.visible = true;
  dialog.title = '添加拉运记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TransportRecordVO) => {
  reset();
  isUpdate.value = true;
  const _transportId = row?.transportId || ids.value[0];
  const res = await getTransportRecord(_transportId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改拉运记录';
};

const handlePreview = async (row?: TransportRecordVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.transportId,
    attachSourceType: 'gxTransportRecord'
  });
};

/** 提交按钮 */
const submitForm = () => {
  transportRecordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.transportId) {
        await updateTransportRecord(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTransportRecord(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: TransportRecordVO) => {
  const _transportIds = row?.transportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除拉运记录编号为"' + _transportIds + '"的数据项？').finally(() => (loading.value = false));
  await delTransportRecord(_transportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epfy/transportRecord/export',
    {
      ...queryParams.value
    },
    `拉运记录_${new Date().getTime()}.xlsx`
  );
};
const setApplication = () => {
  let app = applicationList.value.find((item) => {
    return item.appId === form.value.applicationId;
  });
  form.value.departurePoint = app.loadingLocation;
  form.value.arrivalPoint = app.unloadLocation;
};
onMounted(() => {
  getList();
  getApplicationList();
});
</script>
