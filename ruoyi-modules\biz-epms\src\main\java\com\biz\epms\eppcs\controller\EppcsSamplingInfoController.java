package com.biz.epms.eppcs.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.biz.comm.attach.annotation.AttachDelete;
import com.biz.comm.attach.annotation.AttachUpload;
import com.biz.epms.eppcs.domain.bo.EppcsSamplingInfoBo;
import com.biz.epms.eppcs.domain.vo.EppcsSamplingInfoVo;
import com.biz.epms.eppcs.service.IEppcsSamplingInfoService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 取样信息
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/epms/eppcs/samplingInfo")
public class EppcsSamplingInfoController extends BaseController {

    private final IEppcsSamplingInfoService eppcsSamplingInfoService;

    /**
     * 查询取样信息列表
     */
    @SaCheckPermission("eppcs:samplingInfo:list")
    @GetMapping("/list")
    public TableDataInfo<EppcsSamplingInfoVo> list(EppcsSamplingInfoBo bo, PageQuery pageQuery) {
        return eppcsSamplingInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出取样信息列表
     */
    @SaCheckPermission("eppcs:samplingInfo:export")
    @Log(title = "取样信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EppcsSamplingInfoBo bo, HttpServletResponse response) {
        List<EppcsSamplingInfoVo> list = eppcsSamplingInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "取样信息", EppcsSamplingInfoVo.class, response);
    }

    /**
     * 获取取样信息详细信息
     *
     * @param samplingId 主键
     */
    @SaCheckPermission("eppcs:samplingInfo:query")
    @GetMapping("/{samplingId}")
    public R<EppcsSamplingInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long samplingId) {
        return R.ok(eppcsSamplingInfoService.queryById(samplingId));
    }

    /**
     * 新增取样信息
     */
    @SaCheckPermission("eppcs:samplingInfo:add")
    @Log(title = "取样信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    @AttachUpload(
        sourceIdExpression = "#bo.samplingId",
        attachIdExpression = {"#bo.processPhoto", "#bo.sealPhoto"}
    )
    public R<Long> add(@Validated(AddGroup.class) @RequestBody EppcsSamplingInfoBo bo) {
        boolean result = eppcsSamplingInfoService.insertByBo(bo);
        if (result) {
            return R.ok("新增成功", bo.getSamplingId());
        } else {
            return R.fail("新增失败");
        }
    }

    /**
     * 修改取样信息
     */
    @SaCheckPermission("eppcs:samplingInfo:edit")
    @Log(title = "取样信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EppcsSamplingInfoBo bo) {
        return toAjax(eppcsSamplingInfoService.updateByBo(bo));
    }

    /**
     * 删除取样信息
     *
     * @param samplingIds 主键串
     */
    @SaCheckPermission("eppcs:samplingInfo:remove")
    @Log(title = "取样信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{samplingIds}")
    @AttachDelete(
        sourceIdExpression = "#samplingIds"
    )
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] samplingIds) {
        return toAjax(eppcsSamplingInfoService.deleteWithValidByIds(List.of(samplingIds), true));
    }

    /**
     * 提交取样信息
     *
     * @param samplingId 取样信息ID
     * @param userType 用户类型
     */
    @SaCheckPermission("eppcs:samplingInfo:submit")
    @Log(title = "提交取样信息", businessType = BusinessType.UPDATE)
    @PostMapping("/submit/{samplingId}")
    public R<Void> submitSamplingInfo(@NotNull(message = "取样信息ID不能为空") @PathVariable Long samplingId,
                                     @RequestParam(defaultValue = "1") Integer userType) {
        return toAjax(eppcsSamplingInfoService.submitSamplingInfo(samplingId, userType));
    }

    /**
     * 审批取样信息
     *
     * @param samplingId 取样信息ID
     * @param userType 用户类型
     * @param action 动作 (1通过, 2驳回)
     * @param approvalRemark 审批意见
     */
    @SaCheckPermission("eppcs:samplingInfo:approve")
    @Log(title = "审批取样信息", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{samplingId}")
    public R<Void> approveSamplingInfo(@NotNull(message = "取样信息ID不能为空") @PathVariable Long samplingId,
                                      @RequestParam Integer userType,
                                      @RequestParam Integer action,
                                      @RequestParam(required = false) String approvalRemark) {
        return toAjax(eppcsSamplingInfoService.approveSamplingInfo(
            samplingId, userType, action, approvalRemark,  LoginHelper.getLoginUser().getNickname()));
    }
}
