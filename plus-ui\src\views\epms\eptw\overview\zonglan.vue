<template>
  <div class="waterline p-2">
    <el-row :gutter="14">
      <el-col :span="12">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>取水业务流程图</span>
            </div>
          </template>
          <HmiPreview v-model="fileName" class="hmi-div" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>取水流向图</span>
              <div class="query">
                <el-date-picker
                  v-model="hmiDataTime"
                  date-format="YYYY-MM-DD HH:mm:ss"
                  format="YYYY-MM-DD"
                  placeholder="选择时间"
                  style="margin: 0px 10px; width: 200px"
                  type="date"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
                <el-tree-select
                  v-model="operationAreaId"
                  :data="operationAreaTreeList"
                  :props="defaultProps"
                  @change="getFileName"
                  placeholder="请选择取水区块"
                  :render-after-expand="false"
                  style="width: 200px"
                />
              </div>

              <!--              <el-select v-model="operationAreaId" placeholder="请选择所属地" @change="getFileName"-->
              <!--                         style="width: 200px">-->
              <!--                <el-option-->
              <!--                  v-for="item in operationAreaList"-->
              <!--                  :key="item.operationAreaId"-->
              <!--                  :label="item.operationAreaName"-->
              <!--                  :value="item.operationAreaId"-->
              <!--                >-->
              <!--                </el-option>-->
              <!--              </el-select>-->
            </div>
          </template>
          <HmiPreview :key="hmiFileName + hmiDataTime" ref="HmiQuRef" v-model="hmiFileName" :hmiDataTime="hmiDataTime" class="hmi-div" />
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="14" style="margin-top: 10px">
      <el-col :span="12">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>月度取水对比图</span>
              <div class="query">
                <el-date-picker
                  v-model="queryParams.dateTime"
                  type="year"
                  placeholder="选择年度"
                  format="YYYY"
                  date-format="YYYY"
                  value-format="YYYY-MM-DD"
                  @change="initEcharts"
                  style="margin: 0px 10px; width: 200px"
                />
                <el-tree-select
                  v-model="queryParams.danwei"
                  :data="operationAreaTreeList"
                  :props="defaultProps"
                  @change="initEcharts"
                  check-strictly
                  placeholder="请选择取水区块"
                  :render-after-expand="false"
                  style="width: 200px"
                />
              </div>
            </div>
          </template>
          <div class="card-body" v-loading="loading">
            <echarts ref="chartRef" :chartData="chartData" style="width: 100%; height: 100%" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>取水标准规范</span>
            </div>
          </template>
          <div class="card-body">
            <guifan :documentCategory="standardDocumentCategory" :documentType="standardDocumentType" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>取水告警列表</span>
            </div>
          </template>
          <div class="card-body">
            <warn :systemType="systemType" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import Echarts from '@/views/epms/eptw/overview/echarts.vue';
import Warn from '@/views/epms/components/overview/warn.vue';
import Guifan from '@/views/epms/components/overview/guifan.vue';
import { getOperationArea, listOperationArea, listTreeOperationArea } from '@/api/epms/epcom/operationArea';
import { getData } from '@/api/epms/eptw/waterLine';
import moment from 'dayjs';
import { OperationAreaQuery } from '@/api/epms/epcom/operationArea/types';

const fileName = ref<String>('huanbao-qushui');
const hmiFileName = ref<String>('qushui-1906906389068820489');
const defaultProps = {
  value: 'id',
  label: 'name',
  children: 'children'
};
const loading = ref(false);
const chartData = ref([]);
const operationAreaTreeList = ref([]);
const gongsi = ref({
  id: 83,
  label: '二连分公司',
  name: '二连分公司',
  children: {}
});
const systemType = ref(1);
const operationAreaList = ref([]);
const operationAreaId = ref('1906906389068820489');
const operaQuery = ref<OperationAreaQuery>({});
const queryParams = ref({
  danwei: undefined,
  operationAreaType: 2,
  dateTime: moment().format('YYYY-12-DD')
});
const chartRef = ref(null);
// 标准规范附件参数
const standardDocumentType = ref();
const hmiDataTime = ref();
const standardDocumentCategory = ref(1);
// 是否暗黑模式
const isDark = useDark({
  storageKey: 'useDarkKey',
  valueDark: 'dark',
  valueLight: 'light'
});

/**
 * 获取所属地、取水区块
 */

const getOperationTreeArea = () => {
  const ancestors = '0,1';
  listTreeOperationArea(ancestors).then((res) => {
    // '1906889578986254337','1906882101376888833'
    const excludeIds = [];
    gongsi.value.children = res.data.filter((item) => !excludeIds.includes(item.id));
    // gongsi.value.children = res.data
    operationAreaTreeList.value = [gongsi.value];
    queryParams.value.danwei = 83;
    queryParams.value.operationAreaType = 2;
    if (res.data[0].name) {
      defaultProps.label = 'name';
    } else if (res.data[0].label) {
      defaultProps.label = 'label';
    }
    initEcharts();
  });
};
// 获取组态图名称
const getFileName = () => {
  if (isDark.value) {
    hmiFileName.value = 'lanse-qushui-' + operationAreaId.value;
  } else {
    hmiFileName.value = 'qushui-' + operationAreaId.value;
  }
};
watch(isDark, () => {
  getFileName();
});
const initEcharts = async (node?: any) => {
  if (queryParams.value.danwei != '83') {
    await getOperationArea(queryParams.value.danwei as string).then((res) => {
      queryParams.value.operationAreaType = res.data.operationAreaType;
    });
  } else {
    queryParams.value.operationAreaType = 2;
  }
  loading.value = true;
  queryParams.value.dateTime = moment(queryParams.value.dateTime).format('YYYY-12-DD');
  await getData(queryParams.value).then((res) => {
    chartData.value = res.data;
    const dateFormat = function (value) {
      const date = new Date(value);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      return year + '年' + month + '月';
    };
    /**
     * 合并数据
     */
    chartData.value.XAxis.forEach((item, index) => {
      chartData.value.XAxis[index] = dateFormat(item);
    });
  });
  chartRef.value.initChart();
  loading.value = false;
};
const getOperationAreaList = () => {
  operaQuery.value.operationAreaType = 0;
  listOperationArea(operaQuery.value).then((res) => {
    operationAreaList.value = res.rows;
  });
};
const resizeChart = () => {
  if (chartRef.value) {
    chartRef.value.resizeChart();
  }
};
onMounted(() => {
  getOperationTreeArea();
  getOperationAreaList();
  getFileName();
});
defineExpose({ resizeChart });
</script>

<style scoped>
.card-body {
  height: 35vh;
  width: 100%;
}

:deep(.el-card__header) {
  padding: 2px 15px 2px !important;
  background-color: #f5f5f5;
}

:deep(.el-card__body) {
  padding: 0 !important;
}

:deep(.el-tabs__content) {
  padding: 5px !important;
}

.header {
  font-weight: bold;
  margin-bottom: 10px;
  align-items: center;
  font-size: 35px;
  letter-spacing: 7px;
}

.card-header {
  font-weight: bold;
  letter-spacing: 2px;
  font-size: 17px;
  line-height: 40px;
  height: 30px;
  display: flex;
  justify-content: space-between;
}

.el-select {
  font-weight: normal;
}

.hmi-div {
  width: 100%;
  height: 35vh;
}

:deep(.el-tabs--top) {
  border: none;
}

.query {
  display: flex;
  justify-content: start;
  align-items: center;
  padding-top: 5px;
}
</style>
