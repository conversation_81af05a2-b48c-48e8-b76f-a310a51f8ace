<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="水源井名称" prop="waterWellName">
              <el-input v-model="queryParams.waterWellName" placeholder="请输入水源井名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="行政区域" prop="administrativeArea">
              <el-select v-model="queryParams.administrativeArea" clearable placeholder="请选择行政区域" @keyup.enter="handleQuery">
                <el-option v-for="dict in eptw_administrative_area" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="所属地" prop="operationAreaParentId">
              <el-select
                v-model="queryParams.operationAreaParentId"
                clearable
                placeholder="请选择所属地"
                @change="queryByParentId(queryParams.operationAreaParentId as number)"
                @keyup.enter="handleQuery"
              >
                <el-option
                  v-for="dict in operationAreaList"
                  :key="dict.operationAreaId"
                  :label="dict.operationAreaName"
                  :value="dict.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="取水区块" prop="blockId">
              <el-select v-model="queryParams.operationAreaId" clearable placeholder="请选择取水区块" @keyup.enter="handleQuery">
                <el-option v-for="dict in queryList" :key="dict.operationAreaId" :label="dict.operationAreaName" :value="dict.operationAreaId" />
              </el-select>
            </el-form-item>
            <el-form-item label="座落位置" prop="waterWellLocation">
              <el-input v-model="queryParams.waterWellLocation" placeholder="请输入座落位置" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="有无远程计量" prop="remoteMetering" label-width="96px">
              <el-select v-model="queryParams.remoteMetering" clearable placeholder="请选择有无远程计量">
                <el-option v-for="dict in sys_hava_or_not" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="使用状态" prop="activeStatus">
              <el-select v-model="queryParams.activeStatus" clearable placeholder="请选择水源井使用状态">
                <el-option v-for="dict in eptw_well_active_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epms:well:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['epms:well:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:well:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:well:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="wellList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="水源井名称" prop="waterWellName" width="120" />
        <el-table-column align="center" label="行政区域" prop="administrativeArea" width="110">
          <template #default="scope">
            {{ getAdministrativeAreaText(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="所属地" prop="operationAreaName" width="135">
          <template #default="scope">
            {{ getAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="取水区块" align="center" prop="blockName">
          <template #default="scope">
            {{ getBlockName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="取水许可证编号" min-width="135" prop="waterDrawingLicenseCode" />
        <el-table-column label="座落位置" align="center" prop="waterWellLocation" />
        <el-table-column label="坐标" align="center" prop="waterWellLocation" width="220">
          <template #default="scope"> ({{ scope.row.longitude }},{{ scope.row.latitude }}) </template>
        </el-table-column>
        <el-table-column align="center" label="深度" prop="wellDepth" width="80" />

        <el-table-column align="center" label="流量(m³/h)" prop="flowRate" width="95" />
        <el-table-column label="泵型号" align="center" prop="pumpModel" />

        <el-table-column align="center" label="远程计量" prop="remoteMetering">
          <template #default="scope">
            <dict-tag :options="sys_hava_or_not" :value="scope.row.remoteMetering" />
          </template>
        </el-table-column>
        <el-table-column label="使用状态" align="center" prop="activeStatus">
          <template #default="scope">
            <dict-tag :options="eptw_well_active_status" :value="scope.row.activeStatus" />
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-tooltip content="查看附件" placement="top">
              <el-button link type="primary" icon="Document" @click="handlePreview(scope.row)" v-hasPermi="['epms:well:preview']">查看附件</el-button>
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="Postcard" @click="handleDetail(scope.row)" v-hasPermi="['epms:well:detail']">详情</el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epms:well:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['epms:well:remove']">删除</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改水源井对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="800px" append-to-body>
      <el-form ref="wellFormRef" :model="form" :rules="rules" label-width="210px">
        <el-form-item label="水源井名称" prop="waterWellName">
          <el-input v-model="form.waterWellName" placeholder="请输入水源井名称" />
        </el-form-item>
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" filterable placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaParentId">
          <el-select
            v-model="form.operationAreaParentId"
            filterable
            placeholder="请选择所属地"
            @change="getOperationAreaList(form.operationAreaParentId as number, 'operationAreaParentId')"
          >
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水区块" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择取水区块">
            <el-option
              v-for="dict in blockQueryList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="座落位置" prop="waterWellLocation">
          <el-input v-model="form.waterWellLocation" placeholder="请输入座落位置" />
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="井径" prop="wellCaliper">
          <el-input v-model="form.wellCaliper" placeholder="请输入井径">
            <template #append>mm</template>
          </el-input>
        </el-form-item>
        <el-form-item label="井深度" prop="wellDepth">
          <el-input v-model="form.wellDepth" placeholder="请输入井深度">
            <template #append>m</template>
          </el-input>
        </el-form-item>
        <el-form-item label="有无远程计量" prop="remoteMetering">
          <el-select v-model="form.remoteMetering" placeholder="请选择有无远程计量">
            <el-option v-for="dict in sys_hava_or_not" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态" prop="activeStatus">
          <el-select v-model="form.activeStatus" placeholder="请选择使用状态">
            <el-option v-for="dict in eptw_well_active_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="投产日期" prop="productionDate">
          <el-date-picker v-model="form.productionDate" clearable placeholder="请选择投产日期" type="date" value-format="x"> </el-date-picker>
        </el-form-item>
        <el-form-item label="下泵深度" prop="pumpDepth">
          <el-input v-model="form.pumpDepth" placeholder="请输入下泵深度">
            <template #append>m</template>
          </el-input>
        </el-form-item>

        <el-form-item label="流量(m³/h)" prop="flowRate">
          <el-input v-model="form.flowRate" placeholder="请输入流量">
            <template #append>m³/h</template>
          </el-input>
        </el-form-item>
        <el-form-item label="出水井段(米-米)" prop="outletSection">
          <el-input v-model="form.outletSection" placeholder="请输入出水井段" />
        </el-form-item>
        <el-form-item label="泵型号" prop="pumpModel">
          <el-input v-model="form.pumpModel" placeholder="请输入泵型号" />
        </el-form-item>
        <el-form-item label="电机功率" prop="motorPower">
          <el-input v-model="form.motorPower" placeholder="请输入电机功率">
            <template #append>kW</template>
          </el-input>
        </el-form-item>
        <el-form-item label="分公司计量水表" prop="branchMeteringWaterMeter">
          <el-input v-model="form.branchMeteringWaterMeter" placeholder="请输入分公司计量水表" />
        </el-form-item>
        <el-form-item label="地方水务部门计量水表" prop="waterDepartmentWaterMeter">
          <el-input v-model="form.waterDepartmentWaterMeter" placeholder="请输入地方水务部门计量水表" />
        </el-form-item>
        <el-form-item label="地方水务部门计量水表是否远传" prop="waterDepartmentRemoteMeter">
          <el-input v-model="form.waterDepartmentRemoteMeter" placeholder="请输入地方水务部门计量水表是否远传" />
        </el-form-item>
        <el-form-item label="供水量（立方米/小时）" prop="waterSupply">
          <el-input v-model="form.waterSupply" placeholder="请输入供水量">
            <template #append>m³/h</template>
          </el-input>
        </el-form-item>
        <el-form-item label="水井房情况" prop="roomSituation">
          <el-input v-model="form.roomSituation" placeholder="请输入水井房情况" />
        </el-form-item>
        <el-form-item label="水源井配套设施情况" prop="supportingFacilities">
          <el-input v-model="form.supportingFacilities" placeholder="请输入水源井配套设施情况" />
        </el-form-item>
        <el-form-item label="报废停用情况" prop="scrapDisuseStatus">
          <el-input v-model="form.scrapDisuseStatus" placeholder="请输入报废停用情况" />
        </el-form-item>
        <el-form-item label="基础资料情况" prop="basicInfor">
          <el-input v-model="form.basicInfor" placeholder="请输入基础资料情况" />
        </el-form-item>
        <el-form-item label="地方水务部门计量水表安装位置" prop="waterDepartmentMeterLocation">
          <el-input v-model="form.waterDepartmentMeterLocation" placeholder="请输入地方水务部门计量水表安装位置" />
        </el-form-item>
        <el-form-item label="封井原因" prop="wellCloseReason">
          <el-input v-model="form.wellCloseReason" placeholder="请输入封井原因" />
        </el-form-item>
        <el-form-item label="封井时间" prop="wellCloseTime">
          <el-date-picker v-model="form.wellCloseTime" clearable placeholder="请选择封井时间" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="封井方式" prop="wellCloseMethod">
          <el-input v-model="form.wellCloseMethod" placeholder="请输入封井方式" />
        </el-form-item>
        <el-form-item label="地方水利局备案验收情况" prop="recordAcceptanceStatus">
          <el-input v-model="form.recordAcceptanceStatus" placeholder="请输入地方水利局备案验收情况" />
        </el-form-item>
        <el-form-item label="属地单位联系人电话" prop="localPhoneNumber">
          <el-input v-model="form.localPhoneNumber" placeholder="请输入属地单位联系人电话" />
        </el-form-item>

        <el-form-item label="水源井房照片（或恢复地貌照片）" prop="wellRoomImg">
          <attachFileUpload
            v-model="form.wellRoomImg"
            :attach-source-id="form.waterWellId"
            :disabled="false"
            attach-category="well"
            attach-source-type="waterWell"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="井口照片" prop="wellheadImg">
          <attachFileUpload
            v-model="form.wellheadImg"
            :attach-source-id="form.waterWellId"
            :disabled="false"
            attach-category="well"
            attach-source-type="waterWell"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="水源井房内照片" prop="wellInRoomImg">
          <attachFileUpload
            v-model="form.wellInRoomImg"
            :attach-source-id="form.waterWellId"
            :disabled="false"
            attach-category="well"
            attach-source-type="waterWell"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="封井附件" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.waterWellId"
            :disabled="false"
            attach-category="well"
            attach-source-type="waterWell"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="wellFormRef" :model="form" :rules="rules" label-width="110px" disabled>
        <el-form-item label="水源井名称" prop="waterWellName">
          <el-input v-model="form.waterWellName" placeholder="请输入水源井名称" />
        </el-form-item>
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" filterable placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaParentId">
          <el-select
            v-model="form.operationAreaParentId"
            filterable
            placeholder="请选择所属地"
            @change="getOperationAreaList(form.operationAreaParentId as number, 'operationAreaParentId')"
          >
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="取水区块" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择取水区块">
            <el-option
              v-for="dict in blockList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="座落位置" prop="waterWellLocation">
          <el-input v-model="form.waterWellLocation" placeholder="请输入座落位置" />
        </el-form-item>

        <el-form-item label="取水许可证编号" prop="waterDrawingLicenseCode">
          <el-input v-model="form.waterDrawingLicenseCode" filterable />
        </el-form-item>
        <el-form-item label="取水地点" prop="intakeLocation">
          <el-input v-model="form.intakeLocation" filterable />
        </el-form-item>
        <el-form-item label="取证(续证)时间" prop="startTime">
          <el-date-picker v-model="form.startTime" clearable type="date" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="证件状态" prop="status">
          <el-select v-model="form.status">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="井径" prop="wellCaliper">
          <el-input v-model="form.wellCaliper" placeholder="请输入井径">
            <template #append>mm</template>
          </el-input>
        </el-form-item>
        <el-form-item label="井深度" prop="wellDepth">
          <el-input v-model="form.wellDepth" placeholder="请输入井深度">
            <template #append>m</template>
          </el-input>
        </el-form-item>
        <el-form-item label="有无远程计量" prop="remoteMetering">
          <el-select v-model="form.remoteMetering" placeholder="请选择有无远程计量">
            <el-option v-for="dict in sys_hava_or_not" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态" prop="activeStatus">
          <el-select v-model="form.activeStatus" placeholder="请选择使用状态">
            <el-option v-for="dict in eptw_well_active_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Well" lang="ts">
import { addWell, delWell, getWell, listWell, updateWell } from '@/api/epms/eptw/well';
import { WellForm, WellQuery, WellVO } from '@/api/epms/eptw/well/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_well_active_status, sys_hava_or_not, eptw_administrative_area, eptw_file_status } = toRefs<any>(
  proxy?.useDict('eptw_well_active_status', 'sys_hava_or_not', 'eptw_administrative_area', 'eptw_file_status')
);

const wellList = ref<WellVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const router = useRouter();
const queryFormRef = ref<ElFormInstance>();
const wellFormRef = ref<ElFormInstance>();

const operationAreaQuery = reactive<OperationAreaQuery>({});
// 渲染列表
const operationAreaList = ref([]);
const blockList = ref<OperationAreaVO[]>([]);
const blockQueryList = ref([]);
const queryList = ref([]);
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WellForm = {
  waterWellId: undefined,
  waterWellName: undefined,
  waterWellLocation: undefined,
  operationAreaId: undefined,
  longitude: undefined,
  latitude: undefined,
  wellDepth: undefined,
  wellCaliper: undefined,
  remoteMetering: undefined,
  activeStatus: undefined,
  iotId: undefined,
  uploadTime: undefined,
  file: undefined,
  wellType: 0,
  waterDrawingLicenseId: undefined,
  waterDrawingLicenseCode: undefined
};
const data = reactive<PageData<WellForm, WellQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    waterWellName: undefined,
    waterWellLocation: undefined,
    operationAreaId: undefined,
    administrativeArea: undefined,
    operationAreaParentId: undefined,
    remoteMetering: undefined,
    activeStatus: undefined,
    wellType: 0,
    params: {}
  },
  rules: {
    waterWellId: [{ required: true, message: '水源井id不能为空', trigger: 'blur' }],
    waterWellName: [{ required: true, message: '水源井名称不能为空', trigger: 'blur' }],
    waterWellLocation: [{ required: true, message: '座落位置不能为空', trigger: 'blur' }],
    blockId: [{ required: true, message: '取水区块不能为空', trigger: 'change' }],
    longitude: [{ required: true, message: '经度不能为空', trigger: 'blur' }],
    latitude: [{ required: true, message: '纬度不能为空', trigger: 'blur' }],
    wellDepth: [{ required: true, message: '井深度(m)不能为空', trigger: 'blur' }],
    wellCaliper: [{ required: true, message: '井径(mm)不能为空', trigger: 'blur' }],
    remoteMetering: [{ required: true, message: '有无远程计量不能为空', trigger: 'change' }],
    activeStatus: [{ required: true, message: '使用状态不能为空', trigger: 'change' }],
    pumpDepth: [{ required: true, message: '下泵深度不能为空', trigger: 'change' }],
    flowRate: [{ required: true, message: '流量不能为空', trigger: 'change' }]
    // motorPower: [
    //   { required: true, message: "电机功率不能为空", trigger: "change" }
    // ],
    // waterSupply: [
    //   { required: true, message: "供水量不能为空", trigger: "change" }
    // ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询水源井列表 */
const getList = async () => {
  loading.value = true;
  const res = await listWell(queryParams.value);
  wellList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  wellFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.operationAreaId = null;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WellVO[]) => {
  ids.value = selection.map((item) => item.waterWellId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  operationAreaQuery.operationAreaParentId = null;
  getOperationAreaList();
  reset();
  dialog.visible = true;
  dialog.title = '添加水源井';
};

const handlePreview = async (row?: WellVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.waterWellId,
    attachSourceType: 'waterWell',
    attachCategory: 'well'
  });
};
/** 详细按钮操作 */
const handleDetail = async (row?: WellVO) => {
  reset();
  const _waterWellId = row?.waterWellId || ids.value[0];
  const res = await getWell(_waterWellId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '水源井详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: WellVO) => {
  reset();
  const _waterWellId = row?.waterWellId || ids.value[0];
  const res = await getWell(_waterWellId);
  Object.assign(form.value, res.data);
  await getOperationAreaList(form.value.operationAreaParentId, 'operationAreaParentId');
  dialog.visible = true;
  dialog.title = '修改水源井';
};

/** 提交按钮 */
const submitForm = () => {
  wellFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.waterWellId) {
        await updateWell(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWell(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WellVO) => {
  const _waterWellIds = row?.waterWellId || ids.value;
  await proxy?.$modal.confirm('是否确认删除水源井编号为"' + _waterWellIds + '"的数据项？').finally(() => (loading.value = false));
  await delWell(_waterWellIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/well/export',
    {
      ...queryParams.value
    },
    `水源井_${new Date().getTime()}.xlsx`
  );
};

const getAdministrativeAreaText = (blockId: number) => {
  if (!blockId) {
    return '未知';
  }
  const block = blockList.value.find((item) => item.operationAreaId === blockId);
  if (block) {
    const administrativeAreaItem = eptw_administrative_area.value.find((item) => item.value == block.administrativeArea);
    return administrativeAreaItem ? administrativeAreaItem.label : '未知';
  } else {
    return '未知';
  }
};

const getBlockName = (blockId: number) => {
  if (!blockId) {
    return '未知';
  }
  const block = blockList.value.find((item) => item.operationAreaId === blockId);
  return block ? block.operationAreaName : '未知';
};

const getAreaName = (blockId: number) => {
  if (!blockId) {
    return '未知';
  }
  const block = blockList.value.find((item) => item.operationAreaId === blockId);
  if (block) {
    const area = blockList.value.find((item) => item.operationAreaId === block.operationAreaParentId);
    return area ? area.operationAreaName : '未知';
  } else {
    return '未知';
  }
};

/**
 * 获取所属地下取水区块列表
 */
const getOperationAreaList = async (operationAreaId?: number, type?: string) => {
  if (type === 'operationAreaParentId') {
    queryParams.value.operationAreaId = null;
    operationAreaQuery.operationAreaParentId = operationAreaId;
  }
  blockQueryList.value = [];
  operationAreaQuery.operationAreaType = 1;
  listOperationArea(operationAreaQuery).then((res) => {
    blockQueryList.value = res.rows;
  });
};

const queryByParentId = async (operationAreaId?: number) => {
  operationAreaQuery.operationAreaParentId = operationAreaId;
  operationAreaQuery.operationAreaType = 1;
  listOperationArea(operationAreaQuery).then((res) => {
    queryList.value = res.rows;
  });
};

/**
 * 获取所属地列表
 */
const getAreaList = async () => {
  operationAreaList.value = [];
  operationAreaQuery.operationAreaType = 0;
  const resOperationArea = await listOperationArea(operationAreaQuery);
  operationAreaList.value = resOperationArea.rows;
};

/**
 * 获取全部取水区块列表
 */
const getBlockList = () => {
  blockList.value = [];
  listOperationArea().then((res) => {
    blockList.value = res.rows;
  });
};
const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
  getAreaList();
  getBlockList();
  queryByParentId();
  getOperationAreaList();
});
</script>
