<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="附件名称" prop="reportName">
              <el-input v-model="queryParams.reportName" clearable placeholder="请输入附件名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="单位" prop="operationAreaId">
              <el-tree-select
                v-model="queryParams.operationAreaId"
                :data="operationAreaTreeList"
                :props="defaultProps"
                check-strictly
                placeholder="请选择取水区块"
                :render-after-expand="false"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['epcom:inspectionReport:add']" icon="Plus" plain type="primary" @click="handleAdd"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epcom:inspectionReport:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epcom:inspectionReport:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epcom:inspectionReport:export']" icon="Download" plain type="warning" @click="handleExport">导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="inspectionReportList" stripe @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column align="center" label="附件名称" prop="reportName" />
        <el-table-column label="单位" align="center" prop="operationAreaId" min-width="100px">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="上传时间" align="center" prop="uploadTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="附件预览" placement="top">
              <el-button v-hasPermi="['epcom:inspectionReport:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >附件预览
              </el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epcom:inspectionReport:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                v-hasPermi="['epcom:inspectionReport:remove']"
                icon="Delete"
                link
                type="primary"
                @click="handleDelete(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改第三方监测附件对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="inspectionReportFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="单位" prop="operationAreaId">
          <el-tree-select
            v-model="form.operationAreaId"
            :data="operationAreaTreeList"
            :props="defaultProps"
            check-strictly
            placeholder="请选择取水区块"
            :render-after-expand="false"
          />
        </el-form-item>
        <el-form-item label="附件" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-category="'waterReport' + reportType"
            :attach-source-id="form.reportId"
            :attach-source-type="'waterReport' + reportType"
            :disabled="false"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="InspectionReport" lang="ts">
import {
  addInspectionReport,
  delInspectionReport,
  getInspectionReport,
  listInspectionReport,
  updateInspectionReport
} from '@/api/epms/epcom/inspectionReport/index';
import { InspectionReportForm, InspectionReportQuery, InspectionReportVO } from '@/api/epms/epcom/inspectionReport/types';
import { listOperationArea, listTreeOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

const route = useRoute();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epcom_inspection_report_type, eptw_quarter } = toRefs<any>(proxy?.useDict('epcom_inspection_report_type', 'eptw_quarter'));

const inspectionReportList = ref<InspectionReportVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeReportTime = ref<[DateModelType, DateModelType]>(['', '']);
const reportType = ref('');
const operationAreaQuery = reactive<OperationAreaQuery>({});
const operationAreaList = ref<OperationAreaVO[]>([]);
const queryFormRef = ref<ElFormInstance>();
const inspectionReportFormRef = ref<ElFormInstance>();
const operationAreaTreeList = ref([]);
const menuName = ref('');
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const defaultProps = {
  value: 'id',
  label: 'name',
  children: 'children'
};
const initFormData: InspectionReportForm = {
  reportId: undefined,
  reportName: undefined,
  reportTime: undefined,
  quarter: undefined,
  operationAreaId: undefined,
  monitorParty: undefined,
  reportType: reportType.value,
  file: undefined
};
const data = reactive<PageData<InspectionReportForm, InspectionReportQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    reportName: undefined,
    quarter: undefined,
    operationAreaId: undefined,
    monitorParty: undefined,
    reportType: reportType.value,
    file: undefined,
    params: {
      reportTime: undefined
    }
  },
  rules: {
    reportId: [{ required: true, message: '监测报告id不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '单位不能为空', trigger: 'blur' }],
    monitorParty: [{ required: true, message: '检测单位不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const getDialog = () => {
  epcom_inspection_report_type.value.forEach((item) => {
    if (item.value == reportType.value) {
      menuName.value = item.label;
    }
  });
};
/** 查询第三方监测报告列表 */
const getList = async () => {
  queryParams.value.reportType = reportType.value;
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeReportTime.value, 'ReportTime');
  const res = await listInspectionReport(queryParams.value);
  inspectionReportList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const getOperationTreeArea = () => {
  const ancestors = '0,1';
  listTreeOperationArea(ancestors).then((res) => {
    operationAreaTreeList.value = res.data;
    if (res.data[0].name) {
      defaultProps.label = 'name';
    } else if (res.data[0].label) {
      defaultProps.label = 'label';
    }
  });
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  inspectionReportFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeReportTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: InspectionReportVO[]) => {
  ids.value = selection.map((item) => item.reportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  getDialog();
  dialog.visible = true;
  dialog.title = '添加' + menuName.value;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: InspectionReportVO) => {
  reset();
  getDialog();
  const _reportId = row?.reportId || ids.value[0];
  const res = await getInspectionReport(_reportId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改' + menuName.value;
};

/** 文件预览操作 */
const handlePreview = async (row?: InspectionReportVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.reportId,
    attachSourceType: 'waterReport' + reportType.value,
    attachCategory: 'waterReport' + reportType.value
  });
};

/** 提交按钮 */
const submitForm = () => {
  form.value.reportType = reportType.value;
  inspectionReportFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.reportId) {
        await updateInspectionReport(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addInspectionReport(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: InspectionReportVO) => {
  const _reportIds = row?.reportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除报告编号为"' + _reportIds + '"的数据项？').finally(() => (loading.value = false));
  await delInspectionReport(_reportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
  form.value.reportName = fielInfo.name;
};

/**
 * 获取所属地列表
 */
const getAreaList = () => {
  listOperationArea(operationAreaQuery).then((res) => {
    operationAreaList.value = res.rows;
  });
};

const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationAreaItem = operationAreaList.value.find((item) => item.operationAreaId === operationAreaId);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};
/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    '/epcom/inspectionReport/export',
    {
      ...queryParams.value
    },
    `inspectionReport_${new Date().getTime()}.xlsx`
  );
};
onMounted(() => {
  if (route.query.reportType) {
    reportType.value = route.query.reportType as string;
  }
  getList();
  getAreaList();
  getOperationTreeArea();
});
</script>
