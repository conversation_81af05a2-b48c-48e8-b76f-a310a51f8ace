package com.biz.epms.eppcs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.epms.eppcs.common.enums.EppcsCurrentPhaseEnums;
import com.biz.epms.eppcs.domain.EppcsSamplingApplication;
import com.biz.epms.eppcs.domain.EppcsSamplingInfo;
import com.biz.epms.eppcs.domain.bo.EppcsSamplingInfoBo;
import com.biz.epms.eppcs.domain.vo.EppcsSamplingInfoVo;
import com.biz.epms.eppcs.mapper.EppcsSamplingApplicationMapper;
import com.biz.epms.eppcs.mapper.EppcsSamplingInfoMapper;
import com.biz.epms.eppcs.service.IEppcsSamplingInfoService;
import com.biz.epms.eppcs.state.EppcsStateMachineManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 取样信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class EppcsSamplingInfoServiceImpl implements IEppcsSamplingInfoService {

    private final EppcsSamplingInfoMapper baseMapper;
    private final EppcsStateMachineManager stateMachineManager;
    private final EppcsSamplingApplicationMapper samplingApplicationMapper;

    /**
     * 查询取样信息
     *
     * @param samplingId 主键
     * @return 取样信息
     */
    @Override
    public EppcsSamplingInfoVo queryById(Long samplingId){
        return baseMapper.selectVoById(samplingId);
    }

    /**
     * 分页查询取样信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 取样信息分页列表
     */
    @Override
    public TableDataInfo<EppcsSamplingInfoVo> queryPageList(EppcsSamplingInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EppcsSamplingInfo> lqw = buildQueryWrapper(bo);
        Page<EppcsSamplingInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的取样信息列表
     *
     * @param bo 查询条件
     * @return 取样信息列表
     */
    @Override
    public List<EppcsSamplingInfoVo> queryList(EppcsSamplingInfoBo bo) {
        LambdaQueryWrapper<EppcsSamplingInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<EppcsSamplingInfo> buildQueryWrapper(EppcsSamplingInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<EppcsSamplingInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(EppcsSamplingInfo::getSamplingId);
        lqw.eq(bo.getApplicationId() != null, EppcsSamplingInfo::getApplicationId, bo.getApplicationId());
        lqw.eq(StringUtils.isNotBlank(bo.getSampleNumber()), EppcsSamplingInfo::getSampleNumber, bo.getSampleNumber());
        lqw.eq(bo.getSamplingTime() != null, EppcsSamplingInfo::getSamplingTime, bo.getSamplingTime());
        lqw.eq(StringUtils.isNotBlank(bo.getSamplingType()), EppcsSamplingInfo::getSamplingType, bo.getSamplingType());
        lqw.eq(bo.getSamplingAmount() != null, EppcsSamplingInfo::getSamplingAmount, bo.getSamplingAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getSampler()), EppcsSamplingInfo::getSampler, bo.getSampler());
        lqw.eq(StringUtils.isNotBlank(bo.getProcessPhoto()), EppcsSamplingInfo::getProcessPhoto, bo.getProcessPhoto());
        lqw.eq(StringUtils.isNotBlank(bo.getSealPhoto()), EppcsSamplingInfo::getSealPhoto, bo.getSealPhoto());
        lqw.eq(bo.getSamplingStatus() != null, EppcsSamplingInfo::getSamplingStatus, bo.getSamplingStatus());
        lqw.eq(bo.getApprovalTime() != null, EppcsSamplingInfo::getApprovalTime, bo.getApprovalTime());
        lqw.eq(StringUtils.isNotBlank(bo.getApprovalRemark()), EppcsSamplingInfo::getApprovalRemark, bo.getApprovalRemark());
        return lqw;
    }

    /**
     * 新增取样信息
     *
     * @param bo 取样信息
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(EppcsSamplingInfoBo bo) {
        EppcsSamplingInfo add = MapstructUtils.convert(bo, EppcsSamplingInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setSamplingId(add.getSamplingId());

            // 更新取样申请的当前阶段为1（取样填报）
            if (add.getApplicationId() != null) {
                EppcsSamplingApplication application = samplingApplicationMapper.selectById(add.getApplicationId());
                if (application != null && application.getCurrentPhase() != EppcsCurrentPhaseEnums.SAMPLING_FILLING.getValue()) {
                    application.setCurrentPhase(EppcsCurrentPhaseEnums.SAMPLING_FILLING.getValue());
                    samplingApplicationMapper.updateById(application);
                }
            }
        }
        return flag;
    }

    /**
     * 修改取样信息
     *
     * @param bo 取样信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(EppcsSamplingInfoBo bo) {
        EppcsSamplingInfo update = MapstructUtils.convert(bo, EppcsSamplingInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(EppcsSamplingInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除取样信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 提交取样信息
     *
     * @param samplingId 取样信息ID
     * @param userType 用户类型
     * @return 是否成功
     */
    @Override
    public Boolean submitSamplingInfo(Long samplingId, Integer userType) {
        EppcsSamplingInfo samplingInfo = baseMapper.selectById(samplingId);
        if (samplingInfo == null) {
            throw new ServiceException("取样信息不存在");
        }

        try {
            // 使用状态机获取下一状态
            int nextStatus = stateMachineManager.getNextSamplingStatus(userType, samplingInfo.getSamplingStatus(), EppcsStateMachineManager.ACTION_SUBMIT);
            // 更新状态
            samplingInfo.setSamplingStatus(nextStatus);

            return baseMapper.updateById(samplingInfo) > 0;
        } catch (Exception e) {
            log.error("提交取样信息失败: {}", e.getMessage());
            throw new ServiceException("提交取样信息失败: " + e.getMessage());
        }
    }

    /**
     * 审批取样信息
     *
     * @param samplingId 取样信息ID
     * @param userType 用户类型
     * @param action 动作 (1通过, 2驳回)
     * @param approvalRemark 审批意见
     * @param approver 审批人
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean approveSamplingInfo(Long samplingId, Integer userType, Integer action,
                                      String approvalRemark, String approver) {
        EppcsSamplingInfo samplingInfo = baseMapper.selectById(samplingId);
        if (samplingInfo == null) {
            throw new ServiceException("取样信息不存在");
        }

        try {
            // 使用状态机获取下一状态
            int nextStatus = stateMachineManager.getNextSamplingStatus(userType, samplingInfo.getSamplingStatus(), action);

            // 更新状态和审批信息
            samplingInfo.setSamplingStatus(nextStatus);
            samplingInfo.setApprovalRemark(approvalRemark);
            samplingInfo.setApprovalTime(new java.util.Date());
            samplingInfo.setApprover(StringUtils.isNotBlank(samplingInfo.getApprover()) ? samplingInfo.getApprover() + "," + approver : approver);


            if (nextStatus == EppcsStateMachineManager.SAMPLING_COMPLETED){
                EppcsSamplingApplication application = new EppcsSamplingApplication();
                application.setApplicationId(samplingInfo.getApplicationId());
                application.setCurrentPhase(EppcsCurrentPhaseEnums.SAMPLING_COMPLETED.getValue());
                samplingApplicationMapper.updateById(application);

            }

            return baseMapper.updateById(samplingInfo) > 0;
        } catch (Exception e) {
            log.error("审批取样信息失败: {}", e.getMessage());
            throw new ServiceException("审批取样信息失败: " + e.getMessage());
        }
    }
}
