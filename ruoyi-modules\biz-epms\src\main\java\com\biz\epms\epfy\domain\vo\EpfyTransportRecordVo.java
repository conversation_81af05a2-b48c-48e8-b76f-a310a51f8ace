package com.biz.epms.epfy.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.biz.epms.epfy.domain.EpfyTransportRecord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 拉运记录视图对象 epfy_transport_record
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = EpfyTransportRecord.class)
public class EpfyTransportRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 拉运ID
     */
//    @ExcelProperty(value = "拉运ID")
    private Long transportId;

    /**
     * 拉运申请ID
     */
//    @ExcelProperty(value = "拉运申请ID")
    private Long applicationId;

    /**
     * 拉运时间
     */
    @ExcelProperty(value = "拉运时间")
    private Date transportTime;

    /**
     * 分类: 1固相, 2液相, 3混合相
     */
    @ExcelProperty(value = "分类", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "epfy_medium_category")
    private Integer mediumCategory;

    /**
     * 介质类型
     */
    @ExcelProperty(value = "介质类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "epfy_medium_type")
    private Integer mediumType;

    /**
     * 起运点
     */
    @ExcelProperty(value = "起运点")
    private String departurePoint;

    /**
     * 数量（方）
     */
    @ExcelProperty(value = "数量")
    private BigDecimal number;

    /**
     * 发送人
     */
    @ExcelProperty(value = "发送人")
    private String sender;

    /**
     * 拉运人
     */
    @ExcelProperty(value = "拉运人")
    private String transporter;

    /**
     * 车牌号
     */
    @ExcelProperty(value = "车牌号")
    private String licensePlate;

    /**
     * 接收点
     */
    @ExcelProperty(value = "接收点")
    private String arrivalPoint;

    /**
     * 接收人
     */
    @ExcelProperty(value = "接收人")
    private String receiver;

    /**
     * 备注信息
     */
    @ExcelProperty(value = "备注信息")
    private String remark;

    /**
     * 计量凭证或转运单
     */
//    @ExcelProperty(value = "计量凭证或转运单")
    private String measurementVoucher;

    /**
     * 装卸照片
     */
//    @ExcelProperty(value = "装卸照片")
    private String photo;

    private Long workArea;

    private String wellName;

    /**
     * 拉运时间
     */
    @NotNull(message = "结束装液时间不能为空", groups = {AddGroup.class, EditGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date transportEndTime;

    /**
     * 施工进度
     */
    private String progress;

    /**
     * 监督人
     */
    private String superintendent;

    /**
     * 量尺照片
     */
    private String measuringPhoto;

    /**
     * 罐车方量表
     */
    private String tankerSquare;

    /**
     * 铅封
     */
    private String accessories;


    /**
     * 重车吨数
     */
    private String heavyVehicleTonnage;

    /**
     * 空车吨数
     */
    private String emptyTonnageVehicles;

    /**
     * 卸液口铅封
     */
    private String leadSealDischargePort;
    /**
     * 空车尺量
     */
    private String emptyVehicleMeasurement;

    @ExcelProperty(value = "过泵时间")
    private Date pumpTime;

    @ExcelProperty(value = "过泵地点")
    private String pumpLocation;

    @ExcelProperty(value = "卸液时间")
    private Date unloadTime;

    @ExcelProperty(value = "卸液地点")
    private String unloadLocation;


    @ExcelProperty(value = "卸液时间")
    private String unloadDirector;
    /**
     * 卸液量
     */
    private BigDecimal unloadNumber;

    /**
     * 铅封号
     */
    private String sealNo;

    private Long applyDept;

    private Date applyTime;

    /**
     * approval_status: 1待审核, 2审核通过, 3审核不通过
     */
// 监督人审核
    @ExcelProperty(value = "监督人审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "epfy_application_status")
    private Integer superintendentApproval;
    // 施工负责人审核
    @ExcelProperty(value = "施工负责人审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "epfy_application_status")
    private Integer senderApproval;

    private Date superintendentApprovalTime;

    private Date senderApprovalTime;

    private String unloadProcess;

    @ExcelProperty(value = "卸液点负责人审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "epfy_application_status")
    private Integer unloadApproval;

    private Date unloadApprovalTime;

    /**
     * 重车吨数
     */
    @ExcelProperty(value = "重车吨数")
    private BigDecimal heavyVehicleTonnageNum;

    /**
     * 空车吨数
     */
    @ExcelProperty(value = "空车吨数")
    private BigDecimal emptyTonnageVehiclesNum;

    //    结束时间
    private Date endExamineApproveTime;

    /**
     * 是否预警（卸液时间-装运时间是否大于预警时间）
     */
    private Boolean isTimeOut;
    /**
     * 类型
     */
    private Integer unloadLocationType;
}
