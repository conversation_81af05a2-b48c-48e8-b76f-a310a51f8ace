import{d as q,h as H,r as d,ai as Y,ak as ee,b as te,c as B,o as b,p as s,t as u,w as k,q as oe,a7 as ae,M as se,e as p,x as D,y as le,A as ne,B as re,F as ie,C as ue,D as ce,K as de,J as S,am as M,aI as pe,ay as me,ax as fe,z as ge,v as ve,az as _e,Q as ye}from"./index-D07cMzhp.js";import{s as he}from"./index-D1htDV9m.js";import{l as be}from"./index-DmNU79vT.js";import{u as x,X as we}from"./xlsx-BexUIDLF.js";const Ae={class:"p-2"},Se={class:"mb-[10px]"},xe=q({name:"MonthlyStatisticsSuiZuan"}),Te=q({...xe,setup(Le){const{proxy:I}=H(),w=d([]),T=d(!0),R=d(!0),C=d(0),V=d(["",""]),E=d(null),N=d([]),P=d();d();const A=d([]),W=Y({form:{...{id:void 0,date:void 0,disposeType:2,disposeId:void 0,slurryDisposeAmount:void 0,mudPullingAmount:void 0,mudStagingAmount:void 0,waterPullingAmount:void 0,waterStagingAmount:void 0,loss:void 0}},queryParams:{pageNum:1,pageSize:10,disposeType:2,disposeId:void 0,slurryDisposeAmount:void 0,mudPullingAmount:void 0,mudStagingAmount:void 0,waterPullingAmount:void 0,waterStagingAmount:void 0,loss:void 0,params:{date:void 0}},rules:{id:[{required:!0,message:"主键ID不能为空",trigger:"blur"}]}}),{queryParams:c,form:De,rules:Ie}=ee(W),U=async()=>{const e=await be({handlingType:2});N.value=e.rows,N.value.forEach(o=>{o.id=o.prepId,o.label=o.wellName,A.value.push(o)})},$=()=>{var t;try{const e=(t=E.value)==null?void 0:t.$el;let o=e.querySelector(".el-table__fixed");o||(o=e);const a=x.table_to_book(o,{raw:!0}),n=a.Sheets[a.SheetNames[0]],v=[],_=x.decode_range(n["!ref"]);for(let r=_.s.c;r<=_.e.c;r++){let l=0;for(let f=_.s.r;f<=_.e.r;f++){const g=x.encode_cell({r:f,c:r}),h=n[g];if(h&&h.v){const z=String(h.v).split("").reduce((J,G)=>J+(G.charCodeAt(0)>255?2:1),0);z>l&&(l=z)}}v.push({wch:Math.min(l+2,60)})}n["!cols"]=v;const y={alignment:{horizontal:"center",vertical:"center",wrapText:!0},border:{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},font:{sz:11,name:"宋体",color:{rgb:"000000"}}};Object.keys(n).forEach(r=>{if(!r.startsWith("!")){const l=n[r];l.s=l.s?{...l.s,...y}:{...y},typeof l.v=="number"&&(l.z=l.z||"0.00")}}),n["!merges"]&&n["!merges"].forEach(r=>{for(let l=r.s.r;l<=r.e.r;l++)for(let f=r.s.c;f<=r.e.c;f++){const g=x.encode_cell({r:l,c:f});n[g]||(n[g]={t:"s",v:""}),n[g].s={...y}}});const m=we.write(a,{bookType:"xlsx",bookSST:!1,type:"binary",cellStyles:!0});saveAs(new Blob([Q(m)],{type:"application/octet-stream"}),`随钻_月度统计_${new Date().getTime()}.xlsx`)}catch(e){typeof console<"u"&&console.error(e)}},Q=t=>{const e=new ArrayBuffer(t.length),o=new Uint8Array(e);for(let a=0;a<t.length;a++)o[a]=t.charCodeAt(a)&255;return e},X=t=>t.isLoss||t.isTotal?t.disposeId:A.value.find(e=>e.id==t.disposeId)?A.value.find(e=>e.id==t.disposeId).label:"未知",L=async()=>{T.value=!0;const t=await he(c.value);if(w.value=t.data,t.total){const e={isTotal:!0,disposeId:t.total.wellNumber,slurryDisposeAmount:t.total.mudTotal,mudPullingAmount:t.total.cakeTotal,waterPullingAmount:t.total.filtrateTotal};w.value.push(e)}if(t.loss){const e={isLoss:!0,disposeId:"损耗≤10%",slurryDisposeAmount:t.loss};w.value.push(e)}T.value=!1},F=()=>{c.value.pageNum=1,L()},j=t=>{try{const e=t.match(/当前损耗[:：]\s*([+-]?\d+(\.\d+)?)/),o=t.match(/损耗最大值[:：]\s*(\d+(\.\d+)?)/);if(e&&o){const a=parseFloat(e[1]),n=parseFloat(o[1]);return a>n}else console.log("error")}catch(e){console.error("解析损耗数值失败:",e)}return!1},K=({row:t,columnIndex:e})=>t.isLoss&&e===1&&(console.log("jinru"),j(t.slurryDisposeAmount))?"text-red":"",O=({row:t,column:e,rowIndex:o,columnIndex:a})=>{if(t.isTotal){if(a===0)return{rowspan:1,colspan:1};if(a%2===1)return{rowspan:1,colspan:2};if(a%2===0)return{rowspan:0,colspan:0}}return t.isLoss&&a===1?{rowspan:1,colspan:6}:{rowspan:1,colspan:1}},Z=()=>{var t;V.value=["",""],(t=P.value)==null||t.resetFields(),F()};return te(()=>{U(),L()}),(t,e)=>{var g,h;const o=ce,a=re,n=ne,v=de,_=se,y=ae,m=fe,r=me,l=_e,f=ve;return b(),B("div",Ae,[s(pe,{"enter-active-class":(g=p(I))==null?void 0:g.animate.searchAnimate.enter,"leave-active-class":(h=p(I))==null?void 0:h.animate.searchAnimate.leave},{default:u(()=>[k(oe("div",Se,[s(y,{shadow:"hover"},{default:u(()=>[s(_,{ref_key:"queryFormRef",ref:P,model:p(c),inline:!0},{default:u(()=>[p(c).disposeType==2?(b(),D(n,{key:0,"label-width":"90",label:"单井名称",prop:"disposeId"},{default:u(()=>[s(a,{modelValue:p(c).disposeId,"onUpdate:modelValue":e[0]||(e[0]=i=>p(c).disposeId=i),clearable:"",filterable:"",placeholder:"请选择单井",style:{width:"240px"}},{default:u(()=>[(b(!0),B(ie,null,ue(A.value,i=>(b(),D(o,{key:i.id,label:i.label,value:i.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):le("",!0),s(n,null,{default:u(()=>[s(v,{type:"primary",icon:"Search",onClick:F},{default:u(()=>e[3]||(e[3]=[S("搜索")])),_:1}),s(v,{icon:"Refresh",onClick:Z},{default:u(()=>e[4]||(e[4]=[S("重置")])),_:1}),s(v,{type:"warning",plain:"",icon:"Download",onClick:$},{default:u(()=>e[5]||(e[5]=[S("导出")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[M,R.value]])]),_:1},8,["enter-active-class","leave-active-class"]),s(y,{shadow:"never"},{default:u(()=>[k((b(),D(r,{ref_key:"reportTable",ref:E,data:w.value,"cell-class-name":K,"span-method":O,border:"",stripe:""},{default:u(()=>[s(m,{label:"单井井号",align:"center",prop:"disposeId"},{default:u(i=>[S(ge(X(i.row)),1)]),_:1}),s(m,{align:"center",label:"泥浆处理量（方）",prop:"slurryDisposeAmount"}),s(m,{align:"center",label:"泥浆暂存量（方）",prop:"slurryStagingAmount"}),s(m,{align:"center",label:"泥饼拉运量（方）",prop:"mudPullingAmount"}),s(m,{align:"center",label:"泥饼暂存量（方）",prop:"mudStagingAmount"}),s(m,{align:"center",label:"滤水拉运量（方）",prop:"waterPullingAmount"}),s(m,{align:"center",label:"滤水暂存量（方）",prop:"waterStagingAmount"})]),_:1},8,["data"])),[[f,T.value]]),k(s(l,{limit:p(c).pageSize,"onUpdate:limit":e[1]||(e[1]=i=>p(c).pageSize=i),page:p(c).pageNum,"onUpdate:page":e[2]||(e[2]=i=>p(c).pageNum=i),total:C.value,onPagination:L},null,8,["limit","page","total"]),[[M,C.value>0]])]),_:1})])}}}),Fe=ye(Te,[["__scopeId","data-v-ba84a663"]]);export{Fe as default};
