export interface QualityTrilateralVO {
  /**
   * 监测报告id
   */
  qualityReportId: string | number;

  qualityReportName: string;

  year: string;

  monitorParty: string;
  /**
   * 检测时间
   */
  detectionTime: Date;

  uploadTime: Date;

  status: number;

  iotId: string;
  /**
   * 附件
   */
  file: string;
}

export interface QualityTrilateralForm extends BaseEntity {
  /**
   * 监测报告id
   */
  qualityReportId?: string | number;

  qualityReportName: string;

  year: string;

  monitorParty: string;
  /**
   * 检测时间
   */
  detectionTime: Date;

  uploadTime: Date;

  status: number;

  /**
   * 附件
   */
  file?: string;
}

export interface QualityTrilateralQuery extends PageQuery {
  qualityReportName?: string;
  /**
   * 检测时间
   */
  year?: string[];

  startYear?: string;
  endYear?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
