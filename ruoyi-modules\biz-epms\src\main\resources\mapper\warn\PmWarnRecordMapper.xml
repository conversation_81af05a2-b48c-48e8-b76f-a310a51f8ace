<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.biz.warn.mapper.PmWarnRecordMapper">

    <resultMap type="com.biz.warn.domain.PmWarnRecord" id="PmWarnRecordResult">
        <result property="warnRecordId" column="WARN_RECORD_ID"/>
        <result property="equipmentId" column="EQUIPMENT_ID"/>
        <result property="assetId" column="ASSET_ID"/>
        <result property="realWarnId" column="REAL_WARN_ID"/>
        <result property="warnName" column="WARN_NAME"/>
        <result property="warnSetId" column="WARN_SET_ID"/>
        <result property="warnLevel" column="WARN_LEVEL"/>
        <result property="warnType" column="WARN_TYPE"/>
        <result property="stationId" column="STATION_ID"/>
        <result property="occurTime" column="OCCUR_TIME"/>
        <result property="occurRecover" column="OCCUR_RECOVER"/>
        <result property="warnStatus" column="WARN_STATUS"/>
        <result property="stationName" column="STATION_NAME"/>
        <result property="equipmentName" column="EQUIPMENT_NAME"/>
        <result property="warnSource" column="WARN_SOURCE"/>
        <result property="equipmentCode" column="EQUIPMENT_CODE"/>
        <result property="sendState" column="SEND_STATE"/>
        <result property="voltage" column="VOLTAGE"/>
        <result property="circuit" column="CIRCUIT"/>
        <result property="recoverWay" column="RECOVER_WAY"/>
        <result property="projectId" column="projectId"/>
        <result property="projectName" column="projectName"/>
        <result property="project_category" column="PROJECT_CATEGORY"/>
        <result property="project_subclass" column="PROJECT_SUBCLASS"/>
        <result property="partsType" column="BJLXID"/>
        <result property="warnCount" column="WARN_COUNT"/>
        <result property="calculationType" column="CALCULATION_TYPE"/>
        <result property="processedNote" column="processed_note"/>
        <result property="processedBy" column="processed_by"/>
        <result property="processedTime" column="processed_time"/>
        <result property="systemType" column="system_type"/>
        <result property="jumpUrl" column="jump_url"/>
    </resultMap>

    <sql id="selectPmWarnRecordVo">
        SELECT
            pm_warn_record.WARN_RECORD_ID,
            pm_warn_record.WARN_NAME,
            pm_warn_record.WARN_SET_ID,
            pm_warn_record.STATION_ID,
            pm_warn_record.WARN_TYPE,
            subnet_ems.MingZi projectName,
            pm_warn_record.OCCUR_TIME,
            pm_warn_record.OCCUR_RECOVER,
            pm_warn_record.WARN_STATUS,
            subnet_ems.id projectId,
            pm_warn_record.EQUIPMENT_ID,
            pm_warn_record.WARN_LEVEL,
            pm_warn_record.WARN_SOURCE,
            pm_warn_record.STATION_NAME,
            pm_warn_record.SEND_STATE,
            pm_warn_record.RECOVER_WAY,
            pm_warn_record.WARN_COUNT,
            pm_warn_set.CALCULATION_TYPE,
            pm_warn_set.system_type,
            pm_warn_record.REAL_WARN_ID,
            pm_warn_detail.processed_note,
            pm_warn_detail.processed_by,
            pm_warn_detail.processed_time,
            pm_warn_set.jump_url
        FROM
            pm_warn_record
            left join pm_warn_set ON pm_warn_set.WARN_SET_ID = pm_warn_record.WARN_SET_ID
            left join pm_warn_relation on pm_warn_set.WARN_SET_ID = pm_warn_relation.WARN_SET_ID
            left join pm_warn_source on pm_warn_relation.WARN_SET_ID = pm_warn_source.WARN_SOURCE_ID
            left join pm_warn_detail on pm_warn_detail.warn_record_id = pm_warn_record.WARN_RECORD_ID
            left join commdev on pm_warn_record.EQUIPMENT_ID = commdev.ID  and pm_warn_source.PARTS_TYPE = commdev.BJLXID
            left join changzhan on pm_warn_record.STATION_ID = changzhan.ID
            left join subnet_ems on changzhan.SUBNETID = subnet_ems.ID
    </sql>

    <select id="selectPmWarnRecordList" parameterType="com.biz.warn.domain.PmWarnRecord"
            resultMap="PmWarnRecordResult">
        <include refid="selectPmWarnRecordVo"/>
        <where>

            <if test="pmWarnRecord.idsList != null  and pmWarnRecord.idsList.size()!=0">and pm_warn_record.EQUIPMENT_ID in
                <foreach collection="pmWarnRecord.idsList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="pmWarnRecord.equipmentId != null ">and pm_warn_record.EQUIPMENT_ID = #{pmWarnRecord.equipmentId}</if>
            <if test="pmWarnRecord.assetId != null ">and pm_warn_record.ASSET_ID = #{pmWarnRecord.assetId}</if>
            <if test="pmWarnRecord.realWarnId != null ">and pm_warn_record.REAL_WARN_ID = #{pmWarnRecord.realWarnId}</if>
            <if test="pmWarnRecord.warnName != null  and pmWarnRecord.warnName != ''">and pm_warn_record.WARN_NAME like concat('%', #{pmWarnRecord.warnName}, '%')</if>
            <if test="pmWarnRecord.warnSetId != null ">and pm_warn_record.WARN_SET_ID = #{pmWarnRecord.warnSetId}</if>
            <if test="pmWarnRecord.warnLevel != null ">and pm_warn_record.WARN_LEVEL = #{pmWarnRecord.warnLevel}</if>
            <if test="pmWarnRecord.warnType != null ">and pm_warn_record.WARN_TYPE = #{pmWarnRecord.warnType}</if>
            <if test="pmWarnRecord.stationId != null ">and pm_warn_record.STATION_ID = #{pmWarnRecord.stationId}</if>
            <if test="pmWarnRecord.occurTimeStart != null ">and pm_warn_record.OCCUR_TIME &gt;= #{pmWarnRecord.occurTimeStart}</if>
            <if test="pmWarnRecord.occurTimeEnd != null ">and pm_warn_record.OCCUR_TIME &lt;= #{pmWarnRecord.occurTimeEnd}</if>
            <if test="pmWarnRecord.recoveryTimeStart != null ">and pm_warn_record.OCCUR_RECOVER &gt;= #{rpmWarnRecord.ecoveryTimeStart}</if>
            <if test="pmWarnRecord.recoveryTimeEnd != null ">and pm_warn_record.OCCUR_RECOVER &lt;= #{pmWarnRecord.recoveryTimeEnd}</if>
            <!--            <if test="occurTime != null "> and pm_warn_record.OCCUR_TIME &gt;= #{occurTime}</if>-->
            <!--            <if test="occurRecover != null "> and pm_warn_record.OCCUR_RECOVER &lt;= #{occurRecover}</if>-->
            <if test="pmWarnRecord.warnStatus != null ">and pm_warn_record.WARN_STATUS = #{pmWarnRecord.warnStatus}</if>
            <if test="pmWarnRecord.stationName != null  and pmWarnRecord.stationName != ''">and STATION_NAME like concat('%', #{pmWarnRecord.stationName},
                '%')
            </if>
            <if test="pmWarnRecord.equipmentName != null  and pmWarnRecord.equipmentName != ''">and EQUIPMENT_NAME like concat('%',
                #{pmWarnRecord.equipmentName}, '%')
            </if>
            <if test="pmWarnRecord.warnSource != null ">and pm_warn_record.WARN_SOURCE = #{pmWarnRecord.warnSource}</if>
            <if test="pmWarnRecord.equipmentCode != null  and pmWarnRecord.equipmentCode != ''">and pm_warn_record.EQUIPMENT_CODE =
                #{pmWarnRecord.equipmentCode}
            </if>
            <if test="pmWarnRecord.sendState != null ">and pm_warn_record.SEND_STATE = #{pmWarnRecord.sendState}</if>
            <if test="pmWarnRecord.voltage != null ">and pm_warn_record.VOLTAGE = #{pmWarnRecord.voltage}</if>
            <if test="pmWarnRecord.circuit != null ">and pm_warn_record.CIRCUIT = #{pmWarnRecord.circuit}</if>
            <if test="pmWarnRecord.recoverWay != null ">and pm_warn_record.RECOVER_WAY = #{pmWarnRecord.recoverWay}</if>
            <if test="pmWarnRecord.projectId != null ">and subnet_ems.id = #{pmWarnRecord.projectId}</if>
            <if test="pmWarnRecord.projectName != null ">and subnet_ems.MingZi like concat('%', #{pmWarnRecord.projectName}, '%')</if>
            <if test="pmWarnRecord.partsType != null ">and commdev.BJLXID = #{pmWarnRecord.partsType}</if>
            <if test="pmWarnRecord.calculationType !=null ">and pm_warn_set.CALCULATION_TYPE = #{pmWarnRecord.calculationType}</if>
            <if test="pmWarnRecord.systemType != null ">and pm_warn_set.system_type = #{pmWarnRecord.systemType}</if>

        </where>
        order by pm_warn_record.OCCUR_TIME desc
    </select>



    <select id="selectPmWarnRecordListByIDs" parameterType="com.biz.warn.domain.PmWarnRecord"
            resultMap="PmWarnRecordResult">
        <include refid="selectPmWarnRecordVo"/>
        <where>
            <if test="idsList != null  and idsList.size()!=0">and pm_warn_record.EQUIPMENT_ID in
                <foreach collection="idsList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="assetId != null ">and pm_warn_record.ASSET_ID = #{assetId}</if>
            <if test="realWarnId != null ">and pm_warn_record.REAL_WARN_ID = #{realWarnId}</if>
            <if test="warnName != null  and warnName != ''">and pm_warn_record.WARN_NAME like concat('%', #{warnName}, '%')</if>
            <if test="warnSetId != null ">and pm_warn_record.WARN_SET_ID = #{warnSetId}</if>
            <if test="warnLevel != null ">and pm_warn_record.WARN_LEVEL = #{warnLevel}</if>
            <if test="warnType != null ">and pm_warn_record.WARN_TYPE = #{warnType}</if>
            <if test="stationId != null ">and pm_warn_record.STATION_ID = #{stationId}</if>
            <if test="occurTimeStart != null ">and pm_warn_record.OCCUR_TIME &gt;= #{occurTimeStart}</if>
            <if test="occurTimeEnd != null ">and pm_warn_record.OCCUR_TIME &lt;= #{occurTimeEnd}</if>
            <!--            <if test="occurTime != null "> and pm_warn_record.OCCUR_TIME &gt;= #{occurTime}</if>-->
            <!--            <if test="occurRecover != null "> and pm_warn_record.OCCUR_RECOVER &lt;= #{occurRecover}</if>-->
            <if test="warnStatus != null ">and pm_warn_record.WARN_STATUS = #{warnStatus}</if>
            <if test="stationName != null  and stationName != ''">and STATION_NAME like concat('%', #{stationName},
                '%')
            </if>
            <if test="equipmentName != null  and equipmentName != ''">and EQUIPMENT_NAME like concat('%',
                #{equipmentName}, '%')
            </if>
            <if test="warnSource != null ">and pm_warn_record.WARN_SOURCE = #{warnSource}</if>

            <if test="equipmentCode != null  and equipmentCode != ''">and pm_warn_record.EQUIPMENT_CODE =
                #{equipmentCode}
            </if>
            <if test="sendState != null ">and pm_warn_record.SEND_STATE = #{sendState}</if>
            <if test="voltage != null ">and pm_warn_record.VOLTAGE = #{voltage}</if>
            <if test="circuit != null ">and pm_warn_record.CIRCUIT = #{circuit}</if>
            <if test="recoverWay != null ">and pm_warn_record.RECOVER_WAY = #{recoverWay}</if>
            <if test="projectId != null ">and subnet_ems.id = #{projectId}</if>
            <if test="projectName != null ">and subnet_ems.MingZi like concat('%', #{projectName}, '%')</if>
            <if test="partsType != null ">and commdev.BJLXID = #{partsType}</if>
            <if test="calculationType !=null ">and pm_warn_set.CALCULATION_TYPE = #{calculationType}</if>
        </where>
        order by pm_warn_record.OCCUR_TIME desc
    </select>

    <select id="selectPmWarnRecordByWarnRecordId" parameterType="Integer" resultMap="PmWarnRecordResult">
        <include refid="selectPmWarnRecordVo"/>
        where pm_warn_record.WARN_RECORD_ID = #{warnRecordId}
    </select>
    <select id="getWarnInfoListByOrgan" resultMap="PmWarnRecordResult">
        SELECT pm_warn_record.WARN_RECORD_ID,
        pm_warn_record.WARN_NAME,
        commdev.MingZi EQUIPMENT_NAME,
        subnet_ems.MingZi projectName,
        pm_warn_record.OCCUR_TIME,
        pm_warn_record.OCCUR_RECOVER,
        pm_warn_record.WARN_STATUS,
        subnet_ems.id projectId,
        commdev.BJLXID,
        pm_warn_record.EQUIPMENT_ID,
        pm_warn_record.WARN_LEVEL,
        pm_warn_record.WARN_SOURCE,
        pm_warn_record.STATION_NAME,
        pm_warn_record.WARN_TYPE,
        commdev.ID EQUIPMENT_CODE,
        commdev.BJLXID partsType,
        pm_warn_record.SEND_STATE,
        pm_warn_record.RECOVER_WAY
        from
        pm_warn_record
        inner JOIN pm_warn_set on pm_warn_set.WARN_SET_ID = pm_warn_record.WARN_SET_ID
        inner JOIN pm_warn_relation on pm_warn_relation.WARN_SET_ID = pm_warn_set.WARN_SET_ID
        inner JOIN pm_warn_source on pm_warn_source.WARN_SOURCE_ID = pm_warn_relation.warn_source_id
        inner JOIN commdev ON pm_warn_record.EQUIPMENT_ID = commdev.ID AND pm_warn_source.PARTS_TYPE = commdev.BJLXID
        inner JOIN changzhan on changzhan.ID = commdev.ChangZhanID
        inner JOIN subnet_ems on subnet_ems.ID = changzhan.SUBNETID
        INNER JOIN ( SELECT ID, MINGZI, parentId FROM subnet_ems WHERE parentId = 0 ) toplevel
        INNER JOIN ( SELECT ID, MINGZI, parentId FROM subnet_ems ) twolevel ON toplevel.ID = twolevel.parentId
        INNER JOIN ( SELECT ID, MINGZI, parentId FROM subnet_ems ) threelevel ON twolevel.ID = threelevel.parentId AND threelevel.ID = subnet_ems.ID
        <if test="regionType == 2">
            AND toplevel.id IN
            <foreach collection="regionIds.split(',')" item="regionId" open="(" separator="," close=")">
                #{regionId}
            </foreach>
        </if>
        <if test="regionType == 3">
            AND twolevel.id IN
            <foreach collection="regionIds.split(',')" item="regionId" open="(" separator="," close=")">
                #{regionId}
            </foreach>
        </if>
        <if test="regionType == 4">
            AND threelevel.id IN
            <foreach collection="regionIds.split(',')" item="regionId" open="(" separator="," close=")">
                #{regionId}
            </foreach>
        </if>
        <if test="regionType == 5">
            AND changzhan.id IN
            <foreach collection="regionIds.split(',')" item="regionId" open="(" separator="," close=")">
                #{regionId}
            </foreach>
        </if>
        <if test="regionType == 6">
            AND commdev.id IN
            <foreach collection="regionIds.split(',')" item="regionId" open="(" separator="," close=")">
                #{regionId}
            </foreach>
        </if>
        <if test="pmWarnRecord.equipmentId != null ">and pm_warn_record.EQUIPMENT_ID = #{pmWarnRecord.equipmentId}</if>
        <if test="pmWarnRecord.assetId != null ">and pm_warn_record.ASSET_ID = #{pmWarnRecord.assetId}</if>
        <if test="pmWarnRecord.realWarnId != null ">and pm_warn_record.REAL_WARN_ID = #{pmWarnRecord.realWarnId}</if>
        <if test="pmWarnRecord.warnName != null  and pmWarnRecord.warnName != ''">and pm_warn_record.WARN_NAME like concat('%',
            #{pmWarnRecord.warnName}, '%')
        </if>
        <if test="pmWarnRecord.warnSetId != null ">and pm_warn_record.WARN_SET_ID = #{pmWarnRecord.warnSetId}</if>
        <if test="pmWarnRecord.warnLevel != null ">and pm_warn_record.WARN_LEVEL = #{pmWarnRecord.warnLevel}</if>
        <if test="pmWarnRecord.warnType != null ">and pm_warn_record.WARN_TYPE = #{pmWarnRecord.warnType}</if>
        <if test="pmWarnRecord.stationId != null ">and pm_warn_record.STATION_ID = #{pmWarnRecord.stationId}</if>
        <if test="occurTimeStart != null ">and pm_warn_record.OCCUR_TIME &gt;= #{occurTimeStart}</if>
        <if test="occurTimeEnd != null ">and pm_warn_record.OCCUR_TIME &lt;= #{occurTimeEnd}</if>
        <!--            <if test="occurTime != null "> and pm_warn_record.OCCUR_TIME &gt;= #{occurTime}</if>-->
        <!--            <if test="occurRecover != null "> and pm_warn_record.OCCUR_RECOVER &lt;= #{occurRecover}</if>-->
        <if test="pmWarnRecord.warnStatus != null ">and pm_warn_record.WARN_STATUS = #{pmWarnRecord.warnStatus}</if>
        <if test="pmWarnRecord.stationName != null  and pmWarnRecord.stationName != ''">and STATION_NAME like
            concat('%', #{pmWarnRecord.stationName},
            '%')
        </if>
        <if test="pmWarnRecord.equipmentName != null  and pmWarnRecord.equipmentName != ''">and commdev.MingZi like
            concat('%',
            #{pmWarnRecord.equipmentName}, '%')
        </if>
        <if test="pmWarnRecord.warnSource != null ">and pm_warn_record.WARN_SOURCE = #{pmWarnRecord.warnSource}</if>
        <if test="pmWarnRecord.equipmentCode != null  and pmWarnRecord.equipmentCode != ''">and
            pm_warn_record.EQUIPMENT_CODE =
            #{pmWarnRecord.equipmentCode}
        </if>
        <if test="pmWarnRecord.sendState != null ">and pm_warn_record.SEND_STATE = #{pmWarnRecord.sendState}</if>
        <if test="pmWarnRecord.voltage != null ">and pm_warn_record.VOLTAGE = #{pmWarnRecord.voltage}</if>
        <if test="pmWarnRecord.circuit != null ">and pm_warn_record.CIRCUIT = #{pmWarnRecord.circuit}</if>
        <if test="pmWarnRecord.recoverWay != null ">and pm_warn_record.RECOVER_WAY = #{pmWarnRecord.recoverWay}</if>
        <if test="pmWarnRecord.projectId != null ">and subnet_ems.id = #{pmWarnRecord.projectId}</if>
        <if test="pmWarnRecord.projectName != null ">and subnet_ems.MingZi like concat('%', #{pmWarnRecord.projectName},
            '%')
        </if>
        <if test="pmWarnRecord.partsType != null ">and commdev.BJLXID = #{pmWarnRecord.partsType}</if>
        order by pm_warn_record.OCCUR_TIME desc
    </select>
    <select id="selectWarnInfoById" resultType="com.biz.warn.domain.PmWarnRecord" parameterType="Integer">
        select * from pm_warn_record
        where WARN_RECORD_ID = #{warnRecordId}

    </select>

    <insert id="insertPmWarnRecord" parameterType="com.biz.warn.domain.PmWarnRecord" useGeneratedKeys="true"
            keyProperty="warnRecordId">
        insert into pm_warn_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipmentId != null">EQUIPMENT_ID,</if>
            <if test="assetId != null">ASSET_ID,</if>
            <if test="realWarnId != null">REAL_WARN_ID,</if>
            <if test="warnName != null">WARN_NAME,</if>
            <if test="warnSetId != null">WARN_SET_ID,</if>
            <if test="warnLevel != null">WARN_LEVEL,</if>
            <if test="warnType != null">WARN_TYPE,</if>
            <if test="stationId != null">STATION_ID,</if>
            <if test="occurTime != null">OCCUR_TIME,</if>
            <if test="occurRecover != null">OCCUR_RECOVER,</if>
            <if test="warnStatus != null">WARN_STATUS,</if>
            <if test="stationName != null">STATION_NAME,</if>
            <if test="equipmentName != null">EQUIPMENT_NAME,</if>
            <if test="warnSource != null">WARN_SOURCE,</if>
            <if test="equipmentCode != null">EQUIPMENT_CODE,</if>
            <if test="sendState != null">SEND_STATE,</if>
            <if test="voltage != null">VOLTAGE,</if>
            <if test="circuit != null">CIRCUIT,</if>
            <if test="recoverWay != null">RECOVER_WAY,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipmentId != null">#{equipmentId},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="realWarnId != null">#{realWarnId},</if>
            <if test="warnName != null">#{warnName},</if>
            <if test="warnSetId != null">#{warnSetId},</if>
            <if test="warnLevel != null">#{warnLevel},</if>
            <if test="warnType != null">#{warnType},</if>
            <if test="stationId != null">#{stationId},</if>
            <if test="occurTime != null">#{occurTime},</if>
            <if test="occurRecover != null">#{occurRecover},</if>
            <if test="warnStatus != null">#{warnStatus},</if>
            <if test="stationName != null">#{stationName},</if>
            <if test="equipmentName != null">#{equipmentName},</if>
            <if test="warnSource != null">#{warnSource},</if>
            <if test="equipmentCode != null">#{equipmentCode},</if>
            <if test="sendState != null">#{sendState},</if>
            <if test="voltage != null">#{voltage},</if>
            <if test="circuit != null">#{circuit},</if>
            <if test="recoverWay != null">#{recoverWay},</if>
        </trim>
    </insert>

    <update id="updatePmWarnRecord" parameterType="com.biz.warn.domain.PmWarnRecord">
        update pm_warn_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="equipmentId != null">EQUIPMENT_ID = #{equipmentId},</if>
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="realWarnId != null">REAL_WARN_ID = #{realWarnId},</if>
            <if test="warnName != null">WARN_NAME = #{warnName},</if>
            <if test="warnSetId != null">WARN_SET_ID = #{warnSetId},</if>
            <if test="warnLevel != null">WARN_LEVEL = #{warnLevel},</if>
            <if test="warnType != null">WARN_TYPE = #{warnType},</if>
            <if test="stationId != null">STATION_ID = #{stationId},</if>
            <if test="occurTime != null">OCCUR_TIME = #{occurTime},</if>
            <if test="occurRecover != null">OCCUR_RECOVER = #{occurRecover},</if>
            <if test="warnStatus != null">WARN_STATUS = #{warnStatus},</if>
            <if test="stationName != null">STATION_NAME = #{stationName},</if>
            <if test="equipmentName != null">EQUIPMENT_NAME = #{equipmentName},</if>
            <if test="warnSource != null">WARN_SOURCE = #{warnSource},</if>
            <if test="equipmentCode != null">EQUIPMENT_CODE = #{equipmentCode},</if>
            <if test="sendState != null">SEND_STATE = #{sendState},</if>
            <if test="voltage != null">VOLTAGE = #{voltage},</if>
            <if test="circuit != null">CIRCUIT = #{circuit},</if>
            <if test="recoverWay != null">RECOVER_WAY = #{recoverWay},</if>
            <if test="warnCount != null">WARN_COUNT = #{warnCount},</if>
        </trim>
        where WARN_RECORD_ID = #{warnRecordId}
    </update>
    <update id="updateSendStateLimitState" >
        update pm_warn_record
        set SEND_STATE = #{sendState}
        where WARN_RECORD_ID = #{warnRecordId} and SEND_STATE = 2
    </update>

    <delete id="deletePmWarnRecordByWarnRecordId" parameterType="Integer">
        delete
        from pm_warn_record
        where WARN_RECORD_ID = #{warnRecordId}
    </delete>

    <delete id="deletePmWarnRecordByWarnRecordIds" parameterType="String">
        delete from pm_warn_record where WARN_RECORD_ID in
        <foreach item="warnRecordId" collection="array" open="(" separator="," close=")">
            #{warnRecordId}
        </foreach>
    </delete>

</mapper>
