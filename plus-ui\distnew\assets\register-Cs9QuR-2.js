import{d as N,R as O,j as R,x as I,o as u,t as o,q as c,p as s,_ as T,S as j,T as G,e,J as $,U as J,E as Q,Q as B,h as W,a as X,r as p,b as Y,m as Z,c as E,y as z,z as V,A as ee,B as se,F as te,C as re,D as oe,G as ae,H as S,K as ne,L as le,M as ie,N as de,V as ue,W as ce,O as pe,P as ge}from"./index-Bm6k27Yz.js";const _e={class:"lang-select--style"},me=N({__name:"index",setup(M){const n=O(),{locale:C}=R(),k={zh_CN:"切换语言成功！",en_US:"Switch Language Successful!"},l=t=>{C.value=t,n.changeLanguage(t),Q.success(k[t]||"切换语言成功！")};return(t,d)=>{const w=G,q=j,v=J;return u(),I(v,{trigger:"click",onCommand:l},{dropdown:o(()=>[s(q,null,{default:o(()=>[s(w,{disabled:e(n).language==="zh_CN",command:"zh_CN"},{default:o(()=>d[0]||(d[0]=[$(" 中文 ")])),_:1},8,["disabled"]),s(w,{disabled:e(n).language==="en_US",command:"en_US"},{default:o(()=>d[1]||(d[1]=[$(" English ")])),_:1},8,["disabled"])]),_:1})]),default:o(()=>[c("div",_e,[s(T,{"icon-class":"language"})])]),_:1})}}}),fe=B(me,[["__scopeId","data-v-d380d173"]]),we={class:"register"},ve={class:"title-box"},he={class:"title"},ye={class:"register-code"},be=["src"],xe={key:0},Ee={key:1},Ve={style:{float:"right"}},Ie=N({__name:"register",setup(M){const{proxy:n}=W(),C="环保信息化平台",k=X(),{t:l}=R(),t=p({tenantId:"",username:"",password:"",confirmPassword:"",code:"",uuid:"",userType:"sys_user"}),d=p(!0),w=(i,r,g)=>{t.value.password!==r?g(new Error(l("register.rule.confirmPassword.equalToPassword"))):g()},q={tenantId:[{required:!0,trigger:"blur",message:l("register.rule.tenantId.required")}],username:[{required:!0,trigger:"blur",message:l("register.rule.username.required")},{min:2,max:20,message:l("register.rule.username.length",{min:2,max:20}),trigger:"blur"}],password:[{required:!0,trigger:"blur",message:l("register.rule.password.required")},{min:5,max:20,message:l("register.rule.password.length",{min:5,max:20}),trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:l("register.rule.password.pattern",{strings:`< > " ' \\ |`}),trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:l("register.rule.confirmPassword.required")},{required:!0,validator:w,trigger:"blur"}],code:[{required:!0,trigger:"change",message:l("register.rule.code.required")}]},v=p(""),h=p(!1),y=p(!0),U=p(),m=p([]),b=()=>{var i;(i=U.value)==null||i.validate(async r=>{if(r){h.value=!0;const[g]=await de(ue(t.value));if(g)h.value=!1,y.value&&P();else{const L=t.value.username;await ce.alert('<span style="color: red; ">'+l("register.registerSuccess",{username:L})+"</font>","系统提示",{app:void 0,dangerouslyUseHTMLString:!0,type:"success"}),await k.push("/login")}}})},P=async()=>{const i=await pe(),{data:r}=i;y.value=r.captchaEnabled===void 0?!0:r.captchaEnabled,y.value&&(v.value="data:image/gif;base64,"+r.img,t.value.uuid=r.uuid)},D=async()=>{const{data:i}=await ge(!1);d.value=i.tenantEnabled===void 0?!0:i.tenantEnabled,d.value&&(m.value=i.voList,m.value!=null&&m.value.length!==0&&(t.value.tenantId=m.value[0].tenantId))};return Y(()=>{P(),D()}),(i,r)=>{const g=fe,L=oe,f=T,F=se,_=ee,x=ae,K=ne,A=Z("router-link"),H=ie;return u(),E("div",we,[s(H,{ref_key:"registerRef",ref:U,model:e(t),rules:q,class:"register-form"},{default:o(()=>[c("div",ve,[c("h3",he,V(e(C)),1),s(g)]),e(d)?(u(),I(_,{key:0,prop:"tenantId"},{default:o(()=>[s(F,{modelValue:e(t).tenantId,"onUpdate:modelValue":r[0]||(r[0]=a=>e(t).tenantId=a),filterable:"",placeholder:e(n).$t("register.selectPlaceholder"),style:{width:"100%"}},{prefix:o(()=>[s(f,{"icon-class":"company",class:"el-input__icon input-icon"})]),default:o(()=>[(u(!0),E(te,null,re(e(m),a=>(u(),I(L,{key:a.tenantId,label:a.companyName,value:a.tenantId},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1})):z("",!0),s(_,{prop:"username"},{default:o(()=>[s(x,{modelValue:e(t).username,"onUpdate:modelValue":r[1]||(r[1]=a=>e(t).username=a),type:"text",size:"large","auto-complete":"off",placeholder:e(n).$t("register.username")},{prefix:o(()=>[s(f,{"icon-class":"user",class:"el-input__icon input-icon"})]),_:1},8,["modelValue","placeholder"])]),_:1}),s(_,{prop:"password"},{default:o(()=>[s(x,{modelValue:e(t).password,"onUpdate:modelValue":r[2]||(r[2]=a=>e(t).password=a),type:"password",size:"large","auto-complete":"off",placeholder:e(n).$t("register.password"),onKeyup:S(b,["enter"])},{prefix:o(()=>[s(f,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue","placeholder"])]),_:1}),s(_,{prop:"confirmPassword"},{default:o(()=>[s(x,{modelValue:e(t).confirmPassword,"onUpdate:modelValue":r[3]||(r[3]=a=>e(t).confirmPassword=a),type:"password",size:"large","auto-complete":"off",placeholder:e(n).$t("register.confirmPassword"),onKeyup:S(b,["enter"])},{prefix:o(()=>[s(f,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue","placeholder"])]),_:1}),e(y)?(u(),I(_,{key:1,prop:"code"},{default:o(()=>[s(x,{modelValue:e(t).code,"onUpdate:modelValue":r[4]||(r[4]=a=>e(t).code=a),size:"large","auto-complete":"off",placeholder:e(n).$t("register.code"),style:{width:"63%"},onKeyup:S(b,["enter"])},{prefix:o(()=>[s(f,{"icon-class":"validCode",class:"el-input__icon input-icon"})]),_:1},8,["modelValue","placeholder"]),c("div",ye,[c("img",{src:e(v),class:"register-code-img",onClick:P},null,8,be)])]),_:1})):z("",!0),s(_,{style:{width:"100%"}},{default:o(()=>[s(K,{loading:e(h),size:"large",type:"primary",style:{width:"100%"},onClick:le(b,["prevent"])},{default:o(()=>[e(h)?(u(),E("span",Ee,V(e(n).$t("register.registering")),1)):(u(),E("span",xe,V(e(n).$t("register.register")),1))]),_:1},8,["loading"]),c("div",Ve,[s(A,{class:"link-type",to:"/login"},{default:o(()=>[$(V(e(n).$t("register.switchLoginPage")),1)]),_:1})])]),_:1})]),_:1},8,["model"]),r[5]||(r[5]=c("div",{class:"el-register-footer"},[c("span",null,"Copyright © 2018-2025 疯狂的狮子Li All Rights Reserved.")],-1))])}}}),ke=B(Ie,[["__scopeId","data-v-e8f9f395"]]);export{ke as default};
