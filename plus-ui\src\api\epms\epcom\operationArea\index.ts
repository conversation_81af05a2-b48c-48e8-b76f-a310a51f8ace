import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OperationAreaForm, OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

/**
 * 查询作业区列表
 * @param query
 * @returns {*}
 */

export const listOperationArea = (query?: OperationAreaQuery): AxiosPromise<OperationAreaVO[]> => {
  return request({
    url: '/epms/operationArea/list',
    method: 'get',
    params: query
  });
};
export const listOperationAreaByDataScope = (query?: OperationAreaQuery): AxiosPromise<OperationAreaVO[]> => {
  return request({
    url: '/epms/operationArea/listByDataScope',
    method: 'get',
    params: query
  });
};

export const listTreeOperationArea = (ancestors: string) => {
  return request({
    url: '/epms/operationArea/listTree',
    method: 'get',
    params: {
      ancestors: ancestors
    }
  });
};

/**
 * 查询作业区详细
 * @param operationAreaId
 */
export const getOperationArea = (operationAreaId: string | number): AxiosPromise<OperationAreaVO> => {
  return request({
    url: '/epms/operationArea/' + operationAreaId,
    method: 'get'
  });
};

/**
 * 新增作业区
 * @param data
 */
export const addOperationArea = (data: OperationAreaForm) => {
  return request({
    url: '/epms/operationArea',
    method: 'post',
    data: data
  });
};

/**
 * 修改作业区
 * @param data
 */
export const updateOperationArea = (data: OperationAreaForm) => {
  return request({
    url: '/epms/operationArea',
    method: 'put',
    data: data
  });
};

/**
 * 删除作业区
 * @param operationAreaId
 * @param oitId
 */
export const delOperationArea = (operationAreaId: string | number | Array<string | number>, oitId: string | number | Array<string | number>) => {
  return request({
    url: '/epms/operationArea',
    method: 'delete',
    params: {
      operationAreaIds: Array.isArray(operationAreaId) ? operationAreaId.join(',') : operationAreaId,
      oitIds: Array.isArray(oitId) ? oitId.join(',') : oitId
    }
  });
};
