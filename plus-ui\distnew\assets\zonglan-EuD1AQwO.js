import{aC as D,d as X,r as s,b as G,c as N,o as A,p as a,t,a7 as H,q as n,a8 as K,e as r,ay as O,ax as W,w as Z,v as J,Q as $}from"./index-D07cMzhp.js";import"./el-tree-DW6MoFaI.js";import{E as ee}from"./el-tree-select-mjERCflS.js";import{E as ae}from"./el-row-CikYE3zA.js";import{E as te}from"./el-date-picker-HyhB9X9n.js";import{E as oe}from"./el-col-BaG5Rg5z.js";import{_ as le}from"./HmiPreview-CGaT42aW.js";import ne from"./echarts-Dj4jYDvP.js";import re from"./warn-BWnJQS3u.js";import se from"./guifan-B4521Vic.js";import{m as x}from"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";import"./index-C0_-menq.js";import"./el-tab-pane-B0KEvacl.js";import"./index-BYLYBMy7.js";import"./index-DIX2TuGt.js";const de=f=>D({url:"/epiw/overview/ecahrtsTongji",method:"get",params:f}),ie=f=>D({url:"/epiw/overview/getTableData",method:"get",params:f}),ue=()=>D({url:"/epiw/overview/listTree",method:"get"}),ce={class:"waterline p-2"},me={class:"hmi-div"},pe={class:"card-header"},fe={class:"query"},he={class:"hmi-div"},ve={class:"card-header"},_e={class:"query"},ye={class:"card-body"},ge={class:"card-body"},Ye={class:"card-body"},we=X({__name:"zonglan",setup(f,{expose:P}){const y=s("huanbao-zhushui"),g=s(!1),q=s([1]),h=s([]),E=s([]);s([]);const Y=s([]),w={value:"id",label:"name",children:"children"},C=s({id:1,name:"二连分公司",label:"二连分公司",children:{}}),d=s({danwei:1,dateTime:x().format("YYYY-12-DD"),operationAreaType:2}),T=s({month:x().format("YYYY-MM")}),z=s(2),v=s(null),B=s(),F=s(2),_=async()=>{g.value=!0;const o=R(d.value.danwei);o===void 0?d.value.operationAreaType=0:o===1?d.value.operationAreaType=1:d.value.operationAreaType=2,d.value.dateTime=x(d.value.dateTime).format("YYYY"),await de(d.value).then(e=>{h.value=e.data;const c=function(i){const l=new Date(i),m=l.getFullYear(),p=l.getMonth()+1;return m+"年"+p+"月"};h.value.XAxis.forEach((i,l)=>{h.value.XAxis[l]=c(i)})}),v.value.initChart(),g.value=!1},V=o=>{if(o==null)return"0.0000";const e=Number(o);return isNaN(e)?"0.0000":e.toFixed(4)},I=()=>{v.value&&v.value.resizeChart()},R=o=>{if(!o)return null;const e=i=>{for(const l of i){if(String(l.id)===String(o))return l;if(l.children&&l.children.length>0){const m=e(l.children);if(m)return m}}return null},c=e(Y.value);return c?c.parentId:null},U=()=>{ue().then(o=>{const e=[6,8];C.value.children=o.data.filter(c=>!e.includes(c.id)),Y.value=[C.value],d.value.danwei=1,d.value.operationAreaType=2,o.data[0].name?w.label="name":o.data[0].label&&(w.label="label"),_()})},k=()=>{ie(T.value).then(o=>{E.value=o.data})};return G(()=>{U(),k(),_()}),P({resizeChart:I}),(o,e)=>{const c=le,i=H,l=oe,m=te,p=W,L=O,M=ae,S=ee,j=J;return A(),N("div",ce,[a(M,{gutter:14},{default:t(()=>[a(l,{span:12},{default:t(()=>[a(i,{class:"has-el-card-title",shadow:"never"},{header:t(()=>e[4]||(e[4]=[n("div",{class:"card-header"},[n("span",null,"注水水质监测三级管理流程")],-1)])),default:t(()=>[n("div",me,[a(c,{modelValue:r(y),"onUpdate:modelValue":e[0]||(e[0]=u=>K(y)?y.value=u:null),style:{height:"100%",width:"100%"}},null,8,["modelValue"])])]),_:1})]),_:1}),a(l,{span:12},{default:t(()=>[a(i,{class:"has-el-card-title",shadow:"never"},{header:t(()=>[n("div",pe,[e[5]||(e[5]=n("span",null,"注水站月度注水量统计",-1)),n("div",fe,[a(m,{modelValue:r(T).month,"onUpdate:modelValue":e[1]||(e[1]=u=>r(T).month=u),type:"month",placeholder:"选择时间",format:"YYYY-MM","date-format":"YYYY-MM","value-format":"YYYY-MM",onChange:k,style:{margin:"0px 10px",width:"200px"}},null,8,["modelValue"])])])]),default:t(()=>[n("div",he,[a(L,{stripe:"",ref:"table",data:r(E),height:"100%"},{default:t(()=>[a(p,{align:"center",label:"作业区",prop:"blockName",fixed:"left"}),a(p,{align:"center",label:"注水站",prop:"bengName",fixed:"left"}),a(p,{formatter:(u,Q,b)=>V(b),align:"center",fixed:"left",label:"本月注水量（万方）",prop:"monthData"},null,8,["formatter"]),a(p,{formatter:(u,Q,b)=>V(b),align:"center",label:"本年累计量（万方）",prop:"yearData"},null,8,["formatter"])]),_:1},8,["data"])])]),_:1})]),_:1})]),_:1}),a(M,{gutter:14,style:{"margin-top":"10px"}},{default:t(()=>[a(l,{span:12},{default:t(()=>[a(i,{class:"has-el-card-title",shadow:"never"},{header:t(()=>[n("div",ve,[e[6]||(e[6]=n("span",null,"全油田月度注水量统计",-1)),n("div",_e,[a(m,{modelValue:r(d).dateTime,"onUpdate:modelValue":e[2]||(e[2]=u=>r(d).dateTime=u),type:"year",placeholder:"选择年度",format:"YYYY","date-format":"YYYY","value-format":"YYYY",onChange:_,style:{margin:"0px 10px",width:"200px"}},null,8,["modelValue"]),a(S,{modelValue:r(d).danwei,"onUpdate:modelValue":e[3]||(e[3]=u=>r(d).danwei=u),data:r(Y),props:w,onChange:_,"check-strictly":"","node-key":"id","default-expanded-keys":r(q),placeholder:"请选择取水区块","render-after-expand":!1,style:{width:"200px"}},null,8,["modelValue","data","default-expanded-keys"])])])]),default:t(()=>[Z((A(),N("div",ye,[a(ne,{ref_key:"chartRef",ref:v,chartData:r(h),style:{width:"100%",height:"100%"}},null,8,["chartData"])])),[[j,r(g)]])]),_:1})]),_:1}),a(l,{span:6},{default:t(()=>[a(i,{class:"has-el-card-title",shadow:"never"},{header:t(()=>e[7]||(e[7]=[n("div",{class:"card-header"},[n("span",null,"注水标准规范")],-1)])),default:t(()=>[n("div",ge,[a(se,{documentCategory:r(F),documentType:r(B),"show-activity":!1},null,8,["documentCategory","documentType"])])]),_:1})]),_:1}),a(l,{span:6},{default:t(()=>[a(i,{class:"has-el-card-title",shadow:"never"},{header:t(()=>e[8]||(e[8]=[n("div",{class:"card-header"},[n("span",null,"注水告警列表")],-1)])),default:t(()=>[n("div",Ye,[a(re,{systemType:r(z)},null,8,["systemType"])])]),_:1})]),_:1})]),_:1})])}}}),Ie=$(we,[["__scopeId","data-v-7268c0cd"]]);export{Ie as default};
