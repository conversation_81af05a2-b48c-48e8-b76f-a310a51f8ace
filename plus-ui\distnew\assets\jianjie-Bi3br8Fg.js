import{d as I,h as q,ai as p,ak as A,r as _,bj as L,b as M,c as R,o as k,F as $,q as r,p as o,aL as H,t as n,K,e as t,aA as O,M as Q,A as G,J as j,x as W,y as X,cA as Y,aD as Z,bc as ee,Q as te}from"./index-D07cMzhp.js";import{_ as oe}from"./index-DVHplxfU.js";import{_ as ae}from"./index.vue_vue_type_style_index_0_lang-BhNJbPz4.js";import{g as F,u as ne}from"./index-ByKt8xWV.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./dayjs.min-Brw96_N0.js";const se={class:"waterline p-2"},le={class:"bianji"},ie={style:{float:"right"}},re=["innerHTML"],ce={class:"dialog-footer"},de={style:{height:"68vh"}},me=I({name:"Jianjie"}),ue=I({...me,props:{attachSourceType:{},noticeId:{}},setup(T){const c=T,f=async()=>{const a=await F(c.noticeId);h.value=a.data.noticeContent},{proxy:d}=q(),v={noticeId:void 0,noticeTitle:"",noticeType:"",noticeContent:"",status:"",remark:"",createByName:""},E=p({form:{...v},queryParams:{pageNum:1,pageSize:10},rules:{noticeContent:[{required:!0,message:"内容不能为空",trigger:"blur"}]}}),{queryParams:pe,form:s,rules:N}=A(E),u=_(),i=p({visible:!1,title:""}),g=L(),x=_({}),b=p({visible:!1,url:""}),y=()=>{var a;s.value={...v},(a=u.value)==null||a.resetFields()},h=_(),P=async()=>{y();const a=await F(c.noticeId);Object.assign(s.value,a.data),i.visible=!0,i.title="编辑简介"},B=()=>{var a;(a=u.value)==null||a.validate(async e=>{e&&(await ne(s.value),d==null||d.$modal.msgSuccess("操作成功"),i.visible=!1,await f())})},D=()=>{i.visible=!1,y()},J=async a=>{d.showAttachPreview({attachSourceId:c.noticeId,attachSourceType:c.attachSourceType,attachCategory:a})};return M(()=>{f()}),(a,e)=>{const m=K,w=H,S=ae,C=G,z=oe,U=Q,V=O;return k(),R($,null,[r("div",se,[r("div",le,[r("div",ie,[o(w,{class:"item",placement:"bottom",content:"编辑"},{default:n(()=>[o(m,{circle:"",icon:"EditPen",onClick:P})]),_:1}),o(w,{class:"item",placement:"bottom-end",content:"下载"},{default:n(()=>[o(m,{circle:"",icon:"Download",onClick:e[0]||(e[0]=l=>J("zonglanJianjie"))})]),_:1})])]),r("div",{class:"jianjie",innerHTML:t(h)},null,8,re)]),o(V,{modelValue:t(i).visible,"onUpdate:modelValue":e[3]||(e[3]=l=>t(i).visible=l),title:t(i).title,width:"600px","append-to-body":""},{footer:n(()=>[r("div",ce,[o(m,{type:"primary",onClick:B},{default:n(()=>e[5]||(e[5]=[j("确 定")])),_:1}),o(m,{onClick:D},{default:n(()=>e[6]||(e[6]=[j("取 消")])),_:1})])]),default:n(()=>[o(U,{ref_key:"configFormRef",ref:u,model:t(s),rules:t(N),"label-width":"80px"},{default:n(()=>[o(C,{label:"页面简介",prop:"noticeContent"},{default:n(()=>[o(S,{modelValue:t(s).noticeContent,"onUpdate:modelValue":e[1]||(e[1]=l=>t(s).noticeContent=l),"min-height":100,placeholder:"请输入页面简介内容",type:"base64"},null,8,["modelValue"])]),_:1}),o(C,{label:"简介附件",prop:"remark"},{default:n(()=>[o(z,{modelValue:t(s).remark,"onUpdate:modelValue":e[2]||(e[2]=l=>t(s).remark=l),"attach-source-id":t(s).noticeId,disabled:!1,"attach-category":"zonglanJianjie","attach-source-type":"zongLan"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),o(V,{title:"附件预览",modelValue:t(b).visible,"onUpdate:modelValue":e[4]||(e[4]=l=>t(b).visible=l),width:"80%",he:"","append-to-body":""},{default:n(()=>[r("div",de,[g.value?(k(),W(ee(g.value),Y(Z({key:0},t(x))),null,16)):X("",!0)])]),_:1},8,["modelValue"])],64)}}}),Ce=te(ue,[["__scopeId","data-v-16b8c40b"]]);export{Ce as default};
