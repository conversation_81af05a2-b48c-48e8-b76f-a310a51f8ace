export interface MeteringDeviceVO {
  /**
   * 计量器具id
   */
  meteringDeviceId: string | number;

  /**
   * 器具名称
   */
  meteringDeviceName: string;

  /**
   * 器具类型
   */
  meteringDeviceType: number;

  /**
   * 设备型号
   */
  deviceModel: string;

  /**
   * 厂家
   */
  manufacturer: string;

  /**
   * 安装日期
   */
  installDate: string;

  /**
   * 标定时间
   */
  calibrationTime: string;

  /**
   * 状态
   */
  status: number;

  /**
   * 当前使用者id
   */
  currentUserId: string | number;

  /**
   * 当前使用者类型
   */
  currentUserType: number;

  /**
   * 所在位置
   */
  location: string;

  /**
   * 物联网id
   */
  iotId: string | number;

  operationAreaId: number;

  operationAreaName: String;
}

export interface MeteringDeviceForm extends BaseEntity {
  /**
   * 计量器具id
   */
  meteringDeviceId?: string | number;

  /**
   * 器具名称
   */
  meteringDeviceName?: string;

  /**
   * 器具类型
   */
  meteringDeviceType?: number;

  /**
   * 设备型号
   */
  deviceModel?: string;

  /**
   * 厂家
   */
  manufacturer?: string;

  /**
   * 安装日期
   */
  installDate?: string;

  /**
   * 标定时间
   */
  calibrationTime?: string;

  /**
   * 状态
   */
  status?: number;

  /**
   * 当前使用者id
   */
  currentUserId?: string | number;

  /**
   * 当前使用者类型
   */
  currentUserType?: number;

  /**
   * 所在位置
   */
  location?: string;

  /**
   * 物联网id
   */
  iotId?: string | number;

  operationAreaId?: number;

  operationAreaName?: String;
}

export interface MeteringDeviceQuery extends PageQuery {
  /**
   * 器具名称
   */
  meteringDeviceName?: string;

  /**
   * 器具类型
   */
  meteringDeviceType?: number;

  /**
   * 设备型号
   */
  deviceModel?: string;

  /**
   * 厂家
   */
  manufacturer?: string;

  /**
   * 安装日期
   */
  installDate?: string[];

  installDateStart?: string;
  installDateEnd?: string;

  /**
   * 标定时间
   */
  calibrationTime?: string[];
  calibrationTimeStart?: string;
  calibrationTimeEnd?: string;
  /**
   * 状态
   */
  status?: number;

  /**
   * 当前使用者id
   */
  currentUserId?: string | number;

  /**
   * 当前使用者类型
   */
  currentUserType?: number;

  /**
   * 所在位置
   */
  location?: string;

  /**
   * 物联网id
   */
  iotId?: string | number;

  operationAreaId?: number;

  operationAreaName?: String;
  /**
   * 日期范围参数
   */
  params?: any;
}
