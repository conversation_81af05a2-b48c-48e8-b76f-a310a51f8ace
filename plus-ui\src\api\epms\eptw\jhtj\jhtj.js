import request from '@/utils/request.js';

export function queryNYJHIndicatorData() {
  return request({
    url: '/epms/plan/queryNYJHIndicator',
    method: 'get'
  });
}

export function queryNYJHIndicatorByDate(query) {
  return request({
    url: '/epms/plan/queryNYJHIndicatorByDate',
    method: 'get',
    params: query
  });
}

export function EnergyConsumptionStatistics(query) {
  return request({
    url: '/epms/PlanningStatistics/EnergyConsumptionStatistics',
    method: 'get',
    params: query
  });
}

export function areaEnergyConsumptionStatistical(query) {
  return request({
    url: '/epms/PlanningStatistics/areaEnergyConsumptionStatistical',
    method: 'get',
    params: query
  });
}

export function totalEnergyConsumption(query) {
  return request({
    url: '/epms/PlanningStatistics/totalEnergyConsumption',
    method: 'get',
    params: query
  });
}

// 查询能源计划指标
export function queryQuShuiPlanIndicator() {
  return request({
    url: '/epms/indicatortemplate/queryQuShuiPlanIndicator',
    method: 'get'
  });
}
