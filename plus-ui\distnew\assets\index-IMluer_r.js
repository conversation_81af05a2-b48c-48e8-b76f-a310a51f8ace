import{aC as W,d as de,h as Ce,ak as ie,r as y,ai as $,b as Ne,aH as De,c as E,o as d,p as l,t as o,w as v,q as k,a7 as Te,M as Ue,e as t,A as he,G as Ee,H as L,B as Se,F as z,C as B,x as f,D as Fe,K as Me,J as p,am as ne,aI as Pe,ay as xe,ax as $e,z as K,aL as Le,v as ze,az as Be,a8 as Ke,aA as Oe}from"./index-D07cMzhp.js";import{_ as je}from"./index-DVHplxfU.js";import{E as He}from"./el-row-CikYE3zA.js";import{_ as Ge}from"./index-BWMgqvQ9.js";import{E as Je}from"./el-col-BaG5Rg5z.js";import{E as Xe}from"./el-date-picker-HyhB9X9n.js";import{l as Ze}from"./index-BhIIZXqy.js";import"./el-link-Ar98HWTJ.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./dayjs.min-Brw96_N0.js";import"./el-tree-DW6MoFaI.js";import"./index-VIEDZI2D.js";const ea=c=>W({url:"/epms/waterWellQuality/list",method:"get",params:c}),ue=c=>W({url:"/epms/waterWellQuality/"+c,method:"get"}),aa=c=>W({url:"/epms/waterWellQuality",method:"post",data:c}),la=c=>W({url:"/epms/waterWellQuality",method:"put",data:c}),ta=c=>W({url:"/epms/waterWellQuality/"+c,method:"delete"}),oa={class:"p-2"},ra={class:"mb-[10px]"},ia={class:"dialog-footer"},na={class:"dialog-footer"},ua=de({name:"WaterWellQuality"}),Ra=de({...ua,setup(c){const{proxy:n}=Ce(),{eptw_administrative_area:da}=ie(n==null?void 0:n.useDict("eptw_administrative_area")),O=y([]),A=y([]),Q=y(!1),C=y(!0),N=y(!0),D=y([]),j=y(!0),H=y(!0),S=y(0),G=y({}),J=y(),T=y(),g=$({visible:!1,title:""}),V=$({visible:!1,title:""}),X={qualityReportId:void 0,qualityReportName:void 0,administrativeArea:void 0,operationAreaId:void 0,file:void 0,year:void 0,status:0},se=$({form:{...X},queryParams:{pageNum:1,pageSize:10,qualityReportName:void 0,administrativeArea:void 0,operationAreaId:void 0,file:void 0,year:void 0,params:{}},rules:{qualityReportId:[{required:!0,message:"水质监测报告id不能为空",trigger:"blur"}],year:[{required:!0,message:"监测年度不能为空",trigger:"blur"}],qualityReportName:[{required:!0,message:"水质监测报告名称不能为空",trigger:"blur"}],operationAreaId:[{required:!0,message:"所属地不能为空",trigger:"blur"}],qualityReportTime:[{required:!0,message:"报告日期不能为空",trigger:"blur"}]}}),{queryParams:u,form:i,rules:Z}=ie(se),q=async()=>{C.value=!0;const r=await ea(u.value);O.value=r.rows,S.value=r.total,C.value=!1},pe=async()=>{G.value.operationAreaParentId=0;const r=await Ze(G.value);A.value=r.rows},me=()=>{I(),g.visible=!1},ye=()=>{I(),V.visible=!1},I=()=>{var r;i.value={...X},(r=T.value)==null||r.resetFields()},Y=()=>{u.value.pageNum=1,q()},fe=()=>{var r;(r=J.value)==null||r.resetFields(),Y()},ve=r=>{D.value=r.map(e=>e.qualityReportId),j.value=r.length!=1,H.value=!r.length},ce=()=>{I(),g.visible=!0,g.title="添加水源井水质监测报告"},_e=async r=>{n.showAttachPreview({attachSourceId:r.qualityReportId,attachSourceType:"operationArea",attachCategory:"waterWellQuality"})},ge=async r=>{I();const e=(r==null?void 0:r.qualityReportId)||D.value[0],_=await ue(e);Object.assign(i.value,_.data),V.visible=!0,V.title="水质监测报告详情"},ee=async r=>{I();const e=(r==null?void 0:r.qualityReportId)||D.value[0],_=await ue(e);Object.assign(i.value,_.data),g.visible=!0,g.title="修改水质监测报告"},be=()=>{var r;(r=T.value)==null||r.validate(async e=>{e&&(Q.value=!0,i.value.qualityReportId?await la(i.value).finally(()=>Q.value=!1):await aa(i.value).finally(()=>Q.value=!1),n==null||n.$modal.msgSuccess("操作成功"),g.visible=!1,await q())})},ae=async r=>{const e=(r==null?void 0:r.qualityReportId)||D.value;await(n==null?void 0:n.$modal.confirm('是否确认删除水质监测报告编号为"'+e+'"的数据项？').finally(()=>C.value=!1)),await ta(e),n==null||n.$modal.msgSuccess("删除成功"),await q()},we=()=>{n==null||n.download("epms/waterWellQuality/export",{...u.value},`水质监测报告_${new Date().getTime()}.xlsx`)},Ve=r=>{i.value.uploadTime=r.uploadTime,i.value.balanceReportName=r.name},qe=r=>{if(!r)return"未知";const e=A.value.find(_=>_.operationAreaId===r&&_.operationAreaParentId===0);return e?e.operationAreaName:"未知"},Re=r=>(u.value.pageNum-1)*u.value.pageSize+r+1;return Ne(()=>{q(),pe()}),(r,e)=>{var oe,re;const _=Ee,s=he,F=Fe,M=Se,R=Xe,m=Me,P=Ue,le=Te,U=Je,Ae=Ge,Ie=He,b=$e,h=Le,Ye=xe,ke=Be,We=je,te=Oe,w=De("hasPermi"),Qe=ze;return d(),E("div",oa,[l(Pe,{"enter-active-class":(oe=t(n))==null?void 0:oe.animate.searchAnimate.enter,"leave-active-class":(re=t(n))==null?void 0:re.animate.searchAnimate.leave},{default:o(()=>[v(k("div",ra,[l(le,{shadow:"hover"},{default:o(()=>[l(P,{ref_key:"queryFormRef",ref:J,model:t(u),inline:!0,"label-width":"85px"},{default:o(()=>[l(s,{label:"报告名称",prop:"qualityReportName"},{default:o(()=>[l(_,{modelValue:t(u).qualityReportName,"onUpdate:modelValue":e[0]||(e[0]=a=>t(u).qualityReportName=a),clearable:"",placeholder:"请输入水质监测报告名称",onKeyup:L(Y,["enter"])},null,8,["modelValue"])]),_:1}),l(s,{label:"所属地",prop:"operationAreaId"},{default:o(()=>[l(M,{modelValue:t(u).operationAreaId,"onUpdate:modelValue":e[1]||(e[1]=a=>t(u).operationAreaId=a),clearable:"",placeholder:"请选择所属地",onKeyup:L(Y,["enter"])},{default:o(()=>[(d(!0),E(z,null,B(t(A),a=>(d(),f(F,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"监测年度",prop:"year"},{default:o(()=>[l(R,{modelValue:t(u).params,"onUpdate:modelValue":e[2]||(e[2]=a=>t(u).params=a),type:"yearrange","value-format":"YYYY-MM-DD","range-separator":"至","start-placeholder":"开始年度","end-placeholder":"结束年度",clearable:"",onKeyup:L(Y,["enter"])},null,8,["modelValue"])]),_:1}),l(s,null,{default:o(()=>[l(m,{type:"primary",icon:"Search",onClick:Y},{default:o(()=>e[20]||(e[20]=[p("搜索")])),_:1}),l(m,{icon:"Refresh",onClick:fe},{default:o(()=>e[21]||(e[21]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[ne,t(N)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(le,{shadow:"never"},{header:o(()=>[l(Ie,{gutter:10,class:"mb8"},{default:o(()=>[l(U,{span:1.5},{default:o(()=>[v((d(),f(m,{icon:"Plus",plain:"",type:"primary",onClick:ce},{default:o(()=>e[22]||(e[22]=[p(" 新增 ")])),_:1})),[[w,["epms:waterWellQuality:add"]]])]),_:1}),l(U,{span:1.5},{default:o(()=>[v((d(),f(m,{disabled:t(j),icon:"Edit",plain:"",type:"success",onClick:e[3]||(e[3]=a=>ee())},{default:o(()=>e[23]||(e[23]=[p("修改 ")])),_:1},8,["disabled"])),[[w,["epms:waterWellQuality:edit"]]])]),_:1}),l(U,{span:1.5},{default:o(()=>[v((d(),f(m,{disabled:t(H),icon:"Delete",plain:"",type:"danger",onClick:e[4]||(e[4]=a=>ae())},{default:o(()=>e[24]||(e[24]=[p("删除 ")])),_:1},8,["disabled"])),[[w,["epms:waterWellQuality:remove"]]])]),_:1}),l(U,{span:1.5},{default:o(()=>[v((d(),f(m,{icon:"Download",plain:"",type:"warning",onClick:we},{default:o(()=>e[25]||(e[25]=[p("导出 ")])),_:1})),[[w,["epms:waterWellQuality:export"]]])]),_:1}),l(Ae,{showSearch:t(N),"onUpdate:showSearch":e[5]||(e[5]=a=>Ke(N)?N.value=a:null),onQueryTable:q},null,8,["showSearch"])]),_:1})]),default:o(()=>[v((d(),f(Ye,{stripe:"",data:t(O),onSelectionChange:ve},{default:o(()=>[l(b,{align:"center",type:"selection",width:"55"}),l(b,{index:Re,label:"序号",type:"index",width:"50"}),l(b,{align:"center",label:"报告名称","min-width":"400",prop:"qualityReportName","show-overflow-tooltip":""}),l(b,{label:"所属地",align:"center",prop:"operationAreaName","min-width":"120"},{default:o(a=>[p(K(qe(a.row.operationAreaId)),1)]),_:1}),l(b,{label:"报告日期",align:"center",prop:"qualityReportTime","min-width":"110"},{default:o(a=>[k("span",null,K(r.parseTime(a.row.qualityReportTime,"{y}-{m}-{d}")),1)]),_:1}),l(b,{align:"center",label:"监测年度",prop:"year"}),l(b,{align:"center",label:"剩余时间(天)",prop:"effectiveDay"}),l(b,{label:"上传时间",align:"center",prop:"uploadTime",width:"130"},{default:o(a=>[k("span",null,K(r.parseTime(a.row.uploadTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(b,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作",width:"305"},{default:o(a=>[l(h,{content:"水质监测报告",placement:"top"},{default:o(()=>[v((d(),f(m,{icon:"Document",link:"",type:"primary",onClick:x=>_e(a.row)},{default:o(()=>e[26]||(e[26]=[p("报告预览 ")])),_:2},1032,["onClick"])),[[w,["epms:waterWellQuality:preview"]]])]),_:2},1024),l(h,{content:"详情",placement:"top"},{default:o(()=>[v((d(),f(m,{icon:"Postcard",link:"",type:"primary",onClick:x=>ge(a.row)},{default:o(()=>e[27]||(e[27]=[p("详情 ")])),_:2},1032,["onClick"])),[[w,["epms:waterWellQuality:detail"]]])]),_:2},1024),l(h,{content:"修改",placement:"top"},{default:o(()=>[v((d(),f(m,{icon:"Edit",link:"",type:"primary",onClick:x=>ee(a.row)},{default:o(()=>e[28]||(e[28]=[p("修改 ")])),_:2},1032,["onClick"])),[[w,["epms:waterWellQuality:edit"]]])]),_:2},1024),l(h,{content:"删除",placement:"top"},{default:o(()=>[v((d(),f(m,{icon:"Delete",link:"",type:"primary",onClick:x=>ae(a.row)},{default:o(()=>e[29]||(e[29]=[p("删除 ")])),_:2},1032,["onClick"])),[[w,["epms:waterWellQuality:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Qe,t(C)]]),v(l(ke,{limit:t(u).pageSize,"onUpdate:limit":e[6]||(e[6]=a=>t(u).pageSize=a),page:t(u).pageNum,"onUpdate:page":e[7]||(e[7]=a=>t(u).pageNum=a),total:t(S),onPagination:q},null,8,["limit","page","total"]),[[ne,t(S)>0]])]),_:1}),l(te,{title:t(g).title,modelValue:t(g).visible,"onUpdate:modelValue":e[13]||(e[13]=a=>t(g).visible=a),width:"550px","append-to-body":""},{footer:o(()=>[k("div",ia,[l(m,{loading:t(Q),type:"primary",onClick:be},{default:o(()=>e[30]||(e[30]=[p("确 定")])),_:1},8,["loading"]),l(m,{onClick:me},{default:o(()=>e[31]||(e[31]=[p("取 消")])),_:1})])]),default:o(()=>[l(P,{ref_key:"waterWellQualityFormRef",ref:T,model:t(i),rules:t(Z),"label-width":"100px"},{default:o(()=>[l(s,{label:"报告名称",prop:"qualityReportName"},{default:o(()=>[l(_,{modelValue:t(i).qualityReportName,"onUpdate:modelValue":e[8]||(e[8]=a=>t(i).qualityReportName=a),placeholder:"请输入水质监测报告"},null,8,["modelValue"])]),_:1}),l(s,{label:"所属地",prop:"operationAreaId"},{default:o(()=>[l(M,{modelValue:t(i).operationAreaId,"onUpdate:modelValue":e[9]||(e[9]=a=>t(i).operationAreaId=a),placeholder:"请选择所属地"},{default:o(()=>[(d(!0),E(z,null,B(t(A),a=>(d(),f(F,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"报告日期",prop:"qualityReportTime"},{default:o(()=>[l(R,{modelValue:t(i).qualityReportTime,"onUpdate:modelValue":e[10]||(e[10]=a=>t(i).qualityReportTime=a),clearable:"",format:"YYYY-MM-DD",placeholder:"报告日期",type:"data","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),l(s,{label:"监测年度",prop:"year"},{default:o(()=>[l(R,{modelValue:t(i).year,"onUpdate:modelValue":e[11]||(e[11]=a=>t(i).year=a),format:"YYYY",placeholder:"请选择监测年度",type:"year","value-format":"YYYY"},null,8,["modelValue"])]),_:1}),l(s,{label:"文件列表",prop:"file"},{default:o(()=>[l(We,{modelValue:t(i).file,"onUpdate:modelValue":e[12]||(e[12]=a=>t(i).file=a),"attach-source-id":t(i).qualityReportId,disabled:!1,"attach-category":"waterWellQuality","attach-source-type":"operationArea",onUploadSuccess:Ve},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(te,{title:t(V).title,modelValue:t(V).visible,"onUpdate:modelValue":e[19]||(e[19]=a=>t(V).visible=a),width:"550px","append-to-body":""},{footer:o(()=>[k("div",na,[l(m,{onClick:ye},{default:o(()=>e[32]||(e[32]=[p("取 消")])),_:1})])]),default:o(()=>[l(P,{ref_key:"waterWellQualityFormRef",ref:T,model:t(i),rules:t(Z),"label-width":"100px",disabled:""},{default:o(()=>[l(s,{label:"报告名称",prop:"qualityReportName"},{default:o(()=>[l(_,{modelValue:t(i).qualityReportName,"onUpdate:modelValue":e[14]||(e[14]=a=>t(i).qualityReportName=a),placeholder:"请输入水质监测报告"},null,8,["modelValue"])]),_:1}),l(s,{label:"所属地",prop:"operationAreaId"},{default:o(()=>[l(M,{modelValue:t(i).operationAreaId,"onUpdate:modelValue":e[15]||(e[15]=a=>t(i).operationAreaId=a),placeholder:"请选择所属地"},{default:o(()=>[(d(!0),E(z,null,B(t(A),a=>(d(),f(F,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"报告日期",prop:"qualityReportTime"},{default:o(()=>[l(R,{modelValue:t(i).qualityReportTime,"onUpdate:modelValue":e[16]||(e[16]=a=>t(i).qualityReportTime=a),clearable:"",format:"YYYY-MM-DD",placeholder:"报告日期",type:"data","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),l(s,{label:"监测年度",prop:"year"},{default:o(()=>[l(R,{modelValue:t(i).year,"onUpdate:modelValue":e[17]||(e[17]=a=>t(i).year=a),format:"YYYY",placeholder:"请选择监测年度",type:"year","value-format":"YYYY"},null,8,["modelValue"])]),_:1}),l(s,{label:"上传时间",prop:"uploadTime"},{default:o(()=>[l(R,{modelValue:t(i).uploadTime,"onUpdate:modelValue":e[18]||(e[18]=a=>t(i).uploadTime=a),clearable:"",placeholder:"上传时间",type:"datetime"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Ra as default};
