package com.biz.epms.eppcs.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.comm.attach.annotation.AttachField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 检测结果对象 eppcs_detection_result
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("eppcs_detection_result")
public class EppcsDetectionResult extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 检测结果id
     */
    @TableId(value = "result_id")
    private Long resultId;

    /**
     * 申请id
     */
    private Long applicationId;

    /**
     * 样品编号
     */
    private String sampleNumber;

    /**
     * 检测报告附件
     */
    @AttachField
    private Long reportFile;

    /**
     * 检测结果数据 JSON格式
     */
    private String resultData;

    /**
     * 检测结果 (0达标,1不达标)
     */
    private Integer resultStatus;

    /**
     * 检测状态 (0草稿,1驳回,2待属地单位确认,3待煤层气管理中心确认,4检测完成)
     */
    private Integer detectionStatus;

    /**
     * 检测日期
     */
    private Date detectionDate;

    /**
     * 报送时间
     */
    private Date submitTime;


    /**
     * 审批时间
     */
    private Date approvalTime;

    /**
     * 审批人
     */
    private String approver;

    /**
     * 审批建议
     */
    private String approvalRemark;



    /**
     * 备注
     */
    private String remark;


}
