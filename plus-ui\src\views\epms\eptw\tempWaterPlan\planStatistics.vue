<template>
  <div class="p-2">
    <div class="jhtjFull">
      <div>
        <el-form ref="queryParams" :inline="true" label-width="100" :model="form" class="mt10 mb10">
          <el-form-item label="用水批复函" prop="applyId">
            <el-select v-model="form.org" placeholder="选择用水批复函">
              <el-option
                v-for="deptment in getParkData"
                :key="deptment.waterDrawingLicenseId"
                :label="deptment.waterDrawingLicenseCode"
                :value="deptment.waterDrawingLicenseId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              v-hasPermi="['emcs:plan:quanBuNengHaoTongJi']"
              v-loading="dropDownLoading || nengYuanZongHaoLoading || leiJiLoading || qbLoading"
              :disabled="dropDownLoading || nengYuanZongHaoLoading || leiJiLoading || qbLoading"
              icon="Search"
              type="primary"
              @click="search"
              >查询
            </el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" style="height: 200px">
          <el-col v-loading="leiJiLoading" :span="24" style="height: 100%">
            <el-card class="leftDetailBox" style="height: 100%">
              <div>
                <el-row :gutter="20">
                  <el-col :span="9">
                    <div class="totalBox">
                      <div class="totalBoxHeader" style="background: #e6a23c">
                        <span>{{ currentNengYuanName }}</span>
                      </div>
                      <div class="totalBoxCont">{{ leiJiData['nianJiHua'] || 0 }} 万方</div>
                    </div>
                  </el-col>
                  <el-col :span="15" class="totalDetail">
                    <div>
                      <span class="tit">取水指标:</span>
                      <span class="num">{{ leiJiData['nianJiHua'] || 0 }} 万方</span>
                    </div>
                    <div class="titProgress">
                      <span class="tit">累计取水:</span>
                      <el-progress
                        :percentage="
                          leiJiData['nianJiHua'] && leiJiData['nianJiHua'] !== 0
                            ? Number(((leiJiData['nianLeiJi'] * 100) / leiJiData['nianJiHua']).toFixed(0))
                            : 0
                        "
                        :stroke-width="10"
                      ></el-progress>
                    </div>
                    <div></div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-card class="mt10">
          <el-table stripe :data="nengYuanZongHaoData">
            <el-table-column prop="nianJiHua" align="center" label="总取水指标(万方)"></el-table-column>
            <el-table-column prop="nianLeiJi" align="center" label="累计取水量(万方)"></el-table-column>
            <el-table-column prop="shengYu" align="center" label="剩余取水量(万方)"></el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import { queryQuShuiPlanIndicator, totalEnergyConsumption } from '@/api/epms/eptw/jhtj/jhtj.js';
import { listLicense } from '@/api/epms/eptw/license/index';
import moment from 'dayjs';

export default {
  name: 'jhtj',

  computed: {
    //年计划
    cpuNianHuanBi: function () {
      if (this.leiJiData['nianHuanBi'] && this.leiJiData['nianHuanBi'] === -1) {
        return '--';
      } else {
        return this.leiJiData['nianHuanBi'] || 0;
      }
    },
    //月计划
    cpuYueHuanBi: function () {
      if (this.leiJiData['yueJiHua'] && this.leiJiData['yueJiHua'] === -1) {
        return '--';
      } else {
        return this.leiJiData['yueJiHua'] || 0;
      }
    }
  },
  data() {
    let that = this;
    return {
      // 累计用的能源显示名（不能用计算属性，由查询触发）
      currentNengYuanName: '取水指标',
      leiJiLoading: false,
      // 所有下拉菜单基础数据的loading状态开关
      dropDownLoading: false,
      // 能源总耗loading状态开关
      nengYuanZongHaoLoading: false,
      // 子组件
      qbLoading: false,
      // 搜索条件
      form: {
        org: 0,
        nengYuanId: 1, //取水指标
        indicatorTypeId: 103,
        dateTime: moment().format('YYYY-MM-DD'),
        indicatorPlanType: 2
      },
      // 累计（左上角）所用的数据
      leiJiData: {},
      // 能源类型下拉的数据
      nengYuanList: [],
      // 能源总耗echart实例
      echartItem: null,
      // 能源总耗的数据
      nengYuanZongHaoData: [],
      // 取水区域下拉框列表
      getParkData: [],
      licenseQuery: {
        licenseType: 1
      }
    };
  },
  mounted() {
    // 加载下拉
    this.initData();
    // 监听尺寸变化，刷新图表
    let that = this;
    window.addEventListener('resize', function () {
      if (that.echartItem) {
        that.echartItem.resize();
      }
    });
  },
  methods: {
    async initData() {
      await this.getParkList();
      await this.loadNengYuanType();
      await this.loadLeiJi();
    },
    setCurrentNengYuanName() {
      if (this.nengYuanList && this.nengYuanList.length > 0) {
        this.currentNengYuanName = this.nengYuanList.filter((item) => item['value'] === this.form.nengYuanId)[0]['label'];
      } else {
        this.currentNengYuanName = '';
      }
    },

    /** 查询 */
    search() {
      this.loadLeiJi();
      this.setCurrentNengYuanName();
    },
    /** 获取取水区域下拉框列表 */
    async getParkList() {
      await listLicense(this.licenseQuery).then((res) => {
        if (res.code === 200) {
          this.getParkData = res.rows;
          this.form.org = this.getParkData[0].waterDrawingLicenseId;
        } else {
          this.$modal.msgError('获取数据失败');
        }
      });
    },
    /** 读取能源类型下拉数据 */
    async loadNengYuanType() {
      try {
        this.dropDownLoading = true;
        await queryQuShuiPlanIndicator().then((data) => {
          if (data) {
            this.nengYuanList = [];
            for (const item of data['data']) {
              this.nengYuanList.push({
                label: item['aliasName'],
                value: item['indicatorId']
              });
            }
          }
        });
        this.dropDownLoading = false;
      } catch (e) {
        ElMessage.error('获取能源类型的下拉信息失败');
        this.dropDownLoading = false;
      }
    },
    /** 左上角的累计 */
    async loadLeiJi() {
      try {
        this.leiJiLoading = true;
        this.nengYuanZongHaoData = [];
        await totalEnergyConsumption({
          danwei: this.form.org,
          indicatorId: this.form.nengYuanId,
          indicatorTypeId: this.form.indicatorTypeId,
          dateTime: this.form.dateTime,
          indicatorPlanType: this.form.indicatorPlanType
        }).then((res) => {
          if (res['code'] === 200) {
            this.leiJiData = res['data'];
            this.leiJiData['shengYu'] = parseFloat((this.leiJiData['nianJiHua'] - this.leiJiData['nianLeiJi']).toFixed(4));
            this.nengYuanZongHaoData.push(this.leiJiData);
          }
        });
      } catch (e) {
        this.$modal.msgError(`左上角的累计数据获取异常,${e}`);
      } finally {
        this.leiJiLoading = false;
      }
    }
  }
};
</script>
<style scoped lang="scss">
.jhtjFull {
  height: 100%;

  & > .el-card__body {
    height: calc(100% - 50px);
    overflow-y: auto;
  }

  .totalBox {
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;

    .totalBoxHeader {
      padding: 20px 0;
      background: #67c23a;
      color: #fff;
      text-align: center;
    }

    .totalBoxCont {
      padding: 20px 0;
      text-align: center;
      font-size: 18px;
    }
  }

  .totalDetail {
    & > div {
      padding: 10px 5px;
    }

    .tit {
      width: 4.6em;
      //color: #606266;
      display: inline-block;
      text-align: justify;
      text-align-last: justify;
      font-size: 14px;
    }

    .titProgress {
      display: flex;

      .el-progress {
        width: calc(100% - 7em);
        margin-left: 5px;
      }
    }

    .num {
      padding: 0 10px;
      color: #409eff;
    }
  }
}
</style>
<style lang="scss">
.jhtjFull {
  height: 100%;

  & > .el-card__body {
    height: calc(100% - 50px);
    overflow-y: auto;
  }
}

.leftDetailBox {
  height: 100%;

  .el-card__body {
    display: flex;
    flex-direction: column;
    height: 100%;

    & > div {
      flex: auto;
      display: flex;
      align-items: center;

      & > div {
        flex: 1;
      }
    }

    & > div:first-child {
      border-bottom: 1px solid #eee;
    }
  }
}

.showEchartBox {
  .el-card__body {
    height: 100%;
  }
}
</style>
