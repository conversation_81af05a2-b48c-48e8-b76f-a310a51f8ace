<template>
  <div class="common-layout p-2">
    <div class="mb-[10px]">
      <el-card shadow="hover">
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="时间" prop="month">
            <el-date-picker
              v-model="queryParams.month"
              :append-to-body="false"
              class="searchDate"
              format="YYYY-MM"
              type="month"
              value-format="YYYY-MM"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item label="所属地" prop="operationAreaId">
            <el-select v-model="queryParams.operationAreaId" :popper-append-to-body="false" class="searchDate" filterable placeholder="选择所属地">
              <el-option
                v-for="operationArea in operationAreaList"
                :key="operationArea.iotId"
                :label="operationArea.operationAreaName"
                :value="operationArea.iotId"
              />
            </el-select>
          </el-form-item>
          <el-form-item style="padding-left: 20px">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    <el-card shadow="never" class="grid-container">
      <vxe-grid ref="xTable" :data="tableData" height="auto" v-bind="gridOptions" @edit-closed="editClosedEvent">
        <template #val_default="obj">
          {{ showValue(obj) }}
        </template>
        <template #val_edit="obj">
          <el-input v-model="obj.row[obj.column.property]"></el-input>
        </template>
      </vxe-grid>
    </el-card>
  </div>
</template>
<script>
import moment from 'dayjs';
import { getDataList, updateData } from '@/api/epms/eptw/tempwellsjlr/index.js';
import { listOperationArea } from '@/api/epms/epcom/operationArea/index';

export default {
  name: 'zqssjlr',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      operationAreaList: [], //所属地列表
      tableData: [], //数据列表
      //查询参数
      queryParams: {
        month: moment().format('YYYY-MM'),
        operationAreaId: null
      },
      gridOptions: {
        border: true,
        stripe: true,
        resizable: true,
        showOverflow: true,
        keepSource: true,
        height: 600,
        editConfig: {
          trigger: 'dblclick',
          mode: 'cell',
          showStatus: true
        },
        columns: []
      }
    };
  },
  async created() {
    this.getAreaList();
  },
  methods: {
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    //获取所属地下拉框
    async getAreaList() {
      const query = {
        operationAreaType: 0
      };
      const res = await listOperationArea(query);
      this.operationAreaList = res.rows;
      this.queryParams.operationAreaId = res.rows[0].iotId;
      this.getList();
    },
    handleQuery() {
      this.getList();
    },
    async getList() {
      this.loading = true;
      const response = await getDataList(this.queryParams);
      this.tableData = response.rows;
      this.initColumn(response.rows);
      this.loading = false;
    },
    /** 展示数据 */
    showValue(obj) {
      return obj.row[obj.column.property];
    },
    initColumn(tab) {
      const cols = [
        { title: '序号', type: 'seq', width: 50, align: 'center', fixed: 'left' },
        { field: 'wellName', title: '临时水源井', width: 180, align: 'center', fixed: 'left' }
      ];
      const col = {
        field: '日取水量',
        title: '日取水量',
        width: 150,
        align: 'center',
        children: []
      };
      const col2 = {
        field: 'monthSum',
        title: '月取水合计',
        width: 150,
        align: 'center'
      };
      for (const time in tab[0].riqi) {
        col.children.push({
          field: tab[0].riqi[time],
          title: tab[0].riqi[time],
          width: 150,
          align: 'center',
          editRender: {},
          slots: { default: 'val_default', edit: 'val_edit' }
        });
      }
      cols.push(col);
      cols.push(col2);
      this.gridOptions.columns = cols;
      this.tableData = tab.map((row) => {
        let sum = 0;
        for (const time in tab[0].riqi) {
          sum += Number(row[tab[0].riqi[time]]);
        }
        row.monthSum = sum;
        return row;
      });
    },

    editClosedEvent(obj) {
      let row = obj.row;
      let column = obj.column;
      let field = column.property;
      const cellValue = row[field];
      const $table = this.$refs.xTable;

      row.monthSum = 0;
      row.riqi.forEach((date) => {
        row.monthSum += Number(row[date] || 0);
      });

      updateData({
        monthDate: field,
        bujianId: row.wellId,
        value: cellValue,
        monthSum: row.monthSum
      }).then((response) => {
        if (response.code === 200) {
          $table.reloadRow(row, null, field);
          this.$message({
            message: '设置成功',
            type: 'success'
          });
        }
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('epms/wellDataReportController/exportData', { ...this.queryParams }, `数据报表_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>
<style lang="scss" scoped>
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100%; // 父容器高度
}
.grid-container {
  flex: 1; // 表格容器占满剩余空间
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  height: 100%;
}
vxe-grid {
  height: 100%;
}
.el-date-editor {
  --el-date-editor-width: 100%;
}
</style>
