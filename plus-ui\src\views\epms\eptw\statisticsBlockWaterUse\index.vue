<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="年份" prop="year">
              <el-date-picker v-model="queryParams.year" format="YYYY" placeholder="选择日期" type="year" value-format="YYYY" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:license:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table ref="reportTable" v-loading="loading" :data="licenseList" :span-method="handleSpan" border stripe>
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column prop="administrativeArea" min-width="80" label="所属地域" align="center">
          <template #default="scope">
            {{ getAdministrativeAreaText(scope.row.administrativeArea) }}
          </template>
        </el-table-column>
        <el-table-column prop="operationAreaName" min-width="110" label="单位" align="center" />
        <el-table-column align="center" label="取水批复水量(吨/年)" min-width="100" prop="waterDrawingAmount" />
        <el-table-column prop="value1" label="1月" min-width="80" align="center" />
        <el-table-column prop="value2" label="2月" min-width="80" align="center" />
        <el-table-column prop="value3" label="3月" min-width="80" align="center" />
        <el-table-column prop="value4" label="4月" min-width="80" align="center" />
        <el-table-column prop="value5" label="5月" min-width="80" align="center" />
        <el-table-column prop="value6" label="6月" min-width="80" align="center" />
        <el-table-column prop="value7" label="7月" min-width="80" align="center" />
        <el-table-column prop="value8" label="8月" min-width="80" align="center" />
        <el-table-column prop="value9" label="9月" min-width="80" align="center" />
        <el-table-column prop="value10" label="10月" min-width="80" align="center" />
        <el-table-column prop="value11" label="11月" min-width="80" align="center" />
        <el-table-column prop="value12" label="12月" min-width="80" align="center" />
        <el-table-column align="center" label="全年合计" min-width="80" prop="yearQuShui" />

        <el-table-column prop="useRate" label="已用水占比%" min-width="110" align="center">
          <template #default="scope">
            <span :style="{ color: getColor(scope.row.useRate) }">
              {{ scope.row.useRate }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="许可证编号" min-width="130" prop="licenseCode" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="License" lang="ts">
import { listBlockWaterUse } from '@/api/epms/eptw/statisticsWaterUse';
import { WaterUseFrom, WaterUseQuery, WaterUseVO } from '@/api/epms/eptw/statisticsWaterUse/types';
import { OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import type { TableColumnCtx } from 'element-plus';
import * as XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style-vite';
import { saveAs } from 'file-saver';

import { ref } from 'vue';
// 定义 span 方法参数类型
type SpanMethodProps<T> = {
  row: T;
  column: TableColumnCtx<T>;
  rowIndex: number;
  columnIndex: number;
};
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_hava_or_not, eptw_administrative_area, eptw_file_status } = toRefs<any>(
  proxy?.useDict('sys_hava_or_not', 'eptw_administrative_area', 'eptw_file_status')
);

const licenseList = ref<WaterUseVO[]>([]);
const blockList = ref<OperationAreaVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const reportTable = ref(null);
const queryFormRef = ref<ElFormInstance>();

const initFormData: WaterUseFrom = {
  licenseId: undefined,
  waterDrawingLicenseCode: undefined,
  yearWaterIntake: undefined,
  waterDrawingAmount: undefined,
  wellVoList: [],
  _group: {
    waterDrawingLicenseCode: undefined,
    waterDrawingAmount: undefined,
    yearWaterIntake: undefined,
    isFirstRow: undefined,
    groupSize: undefined
  }
};

const data = reactive<PageData<WaterUseVO, WaterUseQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    year: String(new Date().getFullYear())
  },
  rules: {
    waterDrawingLicenseId: [{ required: true, message: '取水证id不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '取水区块不能为空', trigger: 'change' }],
    waterDrawingLicenseCode: [{ required: true, message: '取水证编号不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询取水证列表 */
const getList = async () => {
  loading.value = true;
  const res = await listBlockWaterUse(queryParams.value);
  licenseList.value = res.rows;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

const getColor = (rate) => {
  if (rate >= 90) return '#ef1a1a';
  else if (rate >= 70) return '#e6c62c';
  else if (rate >= 50) return '#33c10f';
};
/**
 * 单元格合并规则
 */
const handleSpan = ({ row, column, rowIndex, columnIndex }: SpanMethodProps<WaterUseVO>) => {
  const property = column.property;
  if (property === 'administrativeArea') {
    // 获取当前行数据
    const currentValue = row.administrativeArea;
    // 查找合并起始行
    let startRow = rowIndex;
    while (startRow > 0 && licenseList.value[startRow - 1].administrativeArea === currentValue) {
      startRow--;
    }
    // 仅当是连续区间的第一行时计算合并行数
    if (startRow === rowIndex) {
      let mergeRows = 1;
      while (rowIndex + mergeRows < licenseList.value.length && licenseList.value[rowIndex + mergeRows].administrativeArea === currentValue) {
        mergeRows++;
      }
      return { rowspan: mergeRows, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
  return { rowspan: 1, colspan: 1 };
};

/**
 * 获取全部列表
 */
const getAllList = () => {
  listOperationArea().then((res) => {
    blockList.value = res.rows;
  });
};
const getAdministrativeAreaText = (administrativeArea: number) => {
  if (!administrativeArea) {
    return '';
  }
  const administrativeAreaItem = eptw_administrative_area.value.find((item) => item.value === administrativeArea.toString());
  return administrativeAreaItem ? administrativeAreaItem.label : '未知';
};

const s2ab = (s) => {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xff;
  return buf;
};

const handleExport = () => {
  try {
    // 使用 ref 值需要加上 .value
    const $e = reportTable.value?.$el;
    let $table = $e.querySelector('.el-table__fixed');
    if (!$table) {
      $table = $e;
    }

    const wb = XLSX.utils.table_to_book($table, { raw: true });
    const ws = wb.Sheets[wb.SheetNames[0]];
    // 1. 设置列宽自适应
    const colWidths = [];
    // 遍历所有单元格计算最大列宽
    const range = XLSX.utils.decode_range(ws['!ref']);
    for (let col = range.s.c; col <= range.e.c; col++) {
      let maxWidth = 0;
      for (let row = range.s.r; row <= range.e.r; row++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = ws[cellAddress];
        if (cell && cell.v) {
          const cellText = String(cell.v);
          // 粗略计算宽度：中文占2字符，英文占1字符
          const width = cellText.split('').reduce((acc, char) => acc + (char.charCodeAt(0) > 255 ? 2 : 1), 0);
          if (width > maxWidth) maxWidth = width;
        }
      }
      // 设置列宽（加缓冲值）
      colWidths.push({ wch: Math.min(maxWidth + 2, 60) }); // 限制最大宽度
    }
    ws['!cols'] = colWidths;

    // 2. 设置单元格样式（居中+边框）
    const style = {
      alignment: {
        horizontal: 'center',
        vertical: 'center',
        wrapText: true // 增加自动换行
      },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      },
      font: {
        // 必须包含基础字体设置
        sz: 11,
        name: '宋体',
        color: { rgb: '000000' }
      }
    };

    // 遍历所有单元格应用样式
    Object.keys(ws).forEach((cellAddress) => {
      if (!cellAddress.startsWith('!')) {
        const cell = ws[cellAddress];
        // 保留原始单元格样式（如果有）
        cell.s = cell.s ? { ...cell.s, ...style } : { ...style };
        // 处理数字格式（保留两位小数）
        if (typeof cell.v === 'number') {
          cell.z = cell.z || '0.00';
        }
      }
    });
    if (ws['!merges']) {
      ws['!merges'].forEach((merge) => {
        for (let r = merge.s.r; r <= merge.e.r; r++) {
          for (let c = merge.s.c; c <= merge.e.c; c++) {
            const cellAddr = XLSX.utils.encode_cell({ r, c });
            if (!ws[cellAddr]) ws[cellAddr] = { t: 's', v: '' }; // 填充空单元格
            ws[cellAddr].s = { ...style };
          }
        }
      });
    }
    const wbout = XLSXStyle.write(wb, {
      bookType: 'xlsx',
      bookSST: false,
      type: 'binary',
      cellStyles: true // 必须启用样式支持
    });
    saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), `清水用量统计报表_${new Date().getTime()}.xlsx`);
  } catch (e) {
    if (typeof console !== 'undefined') console.error(e);
  }
};

onMounted(() => {
  getList();
  getAllList();
});
</script>
