<template>
  <div class="waterline p-2">
    <el-row :gutter="14">
      <el-col :span="12">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>钻井日报</span>
              <div class="query">
                <el-date-picker
                  v-model="drillingQuery.date"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  date-format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="getDrillingtData"
                  style="margin: 0px 10px; width: 200px"
                />
              </div>
            </div>
          </template>
          <div class="hmi-div">
            <el-table stripe ref="table" :data="drillingList" :row-style="rowStyle" height="100%" @row-click="toDrill">
              <el-table-column align="center" label="日期" prop="date" min-width="110px" fixed="left">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.date, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="钻井公司" prop="drillingCompany" fixed="left"></el-table-column>
              <el-table-column align="center" label="钻井井号" prop="wellNumber" min-width="130px"></el-table-column>
              <el-table-column align="center" label="井别" prop="wellType">
                <template #default="scope">
                  <dict-tag :options="drilling_well_type" :value="scope.row.wellType" />
                </template>
              </el-table-column>
              <el-table-column align="center" label="开钻日期" prop="spudDate" min-width="110px">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.spudDate, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column>
              <!--              <el-table-column align="center" label="泥浆处理类型" prop="mudHandlingType" min-width="150px">-->
              <!--                <template #default="scope">-->
              <!--                  <dict-tag :options="epnj_handling_type" :value="scope.row.mudHandlingType"/>-->
              <!--                </template>-->
              <!--              </el-table-column>-->
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>泥浆拉运统计</span>
            </div>
          </template>
          <div class="hmi-div">
            <el-table stripe ref="table" :data="transportList" :row-style="rowStyle" height="100%">
              <el-table-column align="center" label="" prop="rowName" fixed="left"></el-table-column>
              <el-table-column align="center" label="今日" prop="dayData" fixed="left"></el-table-column>
              <el-table-column align="center" label="本月" prop="monthData" fixed="left"></el-table-column>
              <el-table-column align="center" label="本年" prop="yearData"></el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="14" style="margin-top: 10px">
      <el-col :span="12">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>随钻处理、集中处理对比图</span>
              <div class="query">
                <el-date-picker
                  v-model="queryParams.dateTime"
                  type="year"
                  placeholder="选择年度"
                  format="YYYY"
                  date-format="YYYY"
                  value-format="YYYY"
                  @change="initEcharts"
                  style="margin: 0px 10px; width: 200px"
                />
                <el-select v-model="queryParams.danwei" placeholder="请选择取水区块" style="width: 200px" @change="initEcharts">
                  <el-option value="83" label="全部"></el-option>
                  <el-option
                    v-for="item in operationAreaList"
                    :key="item.operationAreaId"
                    :label="item.operationAreaName"
                    :value="item.operationAreaId"
                  />
                </el-select>
              </div>
            </div>
          </template>
          <div class="card-body" v-loading="loading">
            <echarts ref="chartRef" :chartData="chartData" style="width: 100%; height: 100%" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>泥浆标准规范</span>
            </div>
          </template>
          <div class="card-body">
            <guifan :documentCategory="standardDocumentCategory" :documentType="standardDocumentType" :show-activity="false" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="has-el-card-title" shadow="never">
          <template #header>
            <div class="card-header">
              <span>泥浆告警列表</span>
            </div>
          </template>
          <div class="card-body">
            <warn :systemType="warnSystemType" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import Echarts from './components/echarts.vue';
import Warn from './components/warn.vue';
import Guifan from '@/views/epms/components/overview/guifan.vue';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import moment from 'dayjs';
import { OperationAreaQuery } from '@/api/epms/epcom/operationArea/types';
import { TransportRecordQuery } from '@/api/epms/epfy/transportRecord/types';
import { ecahrtsNiJiangZongLan, statNiJiangZongLan } from '@/api/epms/epfy/transportRecord';
import { listDrillingDaily } from '@/api/epms/epnj/drillingDaily';
import { DrillingDailyVO } from '@/api/epms/epnj/drillingDaily/types';
import router from '@/router';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epnj_handling_type, drilling_well_type } = toRefs<any>(proxy?.useDict('epnj_handling_type', 'drilling_well_type'));
const fileName = ref<String>('huanbao-nijiang');
const hmiFileName = ref<String>('qushui-1906906389068820489');
const loading = ref(false);
const chartData = ref([]);
const transportList = ref([]);
const drillingList = ref([]);
const transportQuery = ref<TransportRecordQuery>({
  operationAreaId: '83'
});
const operationAreaList = ref([]);
const operaQuery = ref<OperationAreaQuery>({});
const queryParams = ref({
  danwei: '83',
  flowType: 2,
  dateTime: moment().format('YYYY-12-DD')
});
const drillingQuery = ref({
  date: moment().format('YYYY-MM-DD')
});
const rowStyle = ref({});
const chartRef = ref(null);
// 标准规范附件参数
const standardDocumentType = ref();
const standardDocumentCategory = ref(3);
const warnSystemType = ref(3);
const initEcharts = async () => {
  loading.value = true;
  queryParams.value.dateTime = moment(queryParams.value.dateTime).format('YYYY');
  await ecahrtsNiJiangZongLan(queryParams.value).then((res) => {
    chartData.value = res.data;
    const dateFormat = function (value) {
      const date = new Date(value);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      return year + '年' + month + '月';
    };
    chartData.value.XAxis.forEach((item, index) => {
      chartData.value.XAxis[index] = dateFormat(item);
    });
  });
  chartRef.value.initChart();
  loading.value = false;
};
const getOperationAreaList = () => {
  operaQuery.value.operationAreaType = 0;
  listOperationArea(operaQuery.value).then((res) => {
    operationAreaList.value = res.rows;
  });
};
const resizeChart = () => {
  if (chartRef.value) {
    chartRef.value.resizeChart();
  }
};
const getTransportData = () => {
  transportQuery.value.flowType = 2;
  transportQuery.value.transportTime = moment().format('YYYY-MM-DD HH:mm:ss');
  statNiJiangZongLan(transportQuery.value).then((res) => {
    transportList.value = res.rows;
  });
};

const toDrill = (row: DrillingDailyVO) => {
  router.push({
    path: '/epnj/drillingDaily',
    query: {
      date: row.date,
      wellNumber: row.wellNumber
    }
  });
};

const getDrillingtData = () => {
  listDrillingDaily(drillingQuery.value).then((res) => {
    drillingList.value = res.rows;
  });
};

onMounted(() => {
  getTransportData();
  getOperationAreaList();
  initEcharts();
  getDrillingtData();
});
defineExpose({ resizeChart });
</script>

<style scoped>
.card-body {
  height: 35vh;
  width: 100%;
}

:deep(.el-card__header) {
  padding: 2px 15px 2px !important;
  background-color: #f5f5f5;
}

:deep(.el-card__body) {
  padding: 0 !important;
}

:deep(.el-tabs__content) {
  padding: 5px !important;
}

.header {
  font-weight: bold;
  margin-bottom: 10px;
  align-items: center;
  font-size: 35px;
  letter-spacing: 7px;
}

.card-header {
  font-weight: bold;
  letter-spacing: 2px;
  font-size: 17px;
  line-height: 40px;
  height: 30px;
  display: flex;
  justify-content: space-between;
}

.el-select {
  font-weight: normal;
}

.hmi-div {
  width: 100%;
  height: 35vh;
}

:deep(.el-tabs--top) {
  border: none;
}

.query {
  display: flex;
  justify-content: start;
  align-items: center;
  padding-top: 5px;
}
</style>
