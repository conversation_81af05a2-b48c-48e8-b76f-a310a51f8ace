package com.biz.epms.eppcs.service;

import com.biz.epms.eppcs.domain.bo.EppcsDetectionResultBo;
import com.biz.epms.eppcs.domain.vo.EppcsDetectionResultVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 检测结果Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IEppcsDetectionResultService {

    /**
     * 查询检测结果
     *
     * @param resultId 主键
     * @return 检测结果
     */
    EppcsDetectionResultVo queryById(Long resultId);

    /**
     * 分页查询检测结果列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 检测结果分页列表
     */
    TableDataInfo<EppcsDetectionResultVo> queryPageList(EppcsDetectionResultBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的检测结果列表
     *
     * @param bo 查询条件
     * @return 检测结果列表
     */
    List<EppcsDetectionResultVo> queryList(EppcsDetectionResultBo bo);

    /**
     * 新增检测结果
     *
     * @param bo 检测结果
     * @return 是否新增成功
     */
    Boolean insertByBo(EppcsDetectionResultBo bo);

    /**
     * 修改检测结果
     *
     * @param bo 检测结果
     * @return 是否修改成功
     */
    Boolean updateByBo(EppcsDetectionResultBo bo);

    /**
     * 校验并批量删除检测结果信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 提交检测结果
     *
     * @param resultId 检测结果ID
     * @param userType 用户类型
     * @return 是否成功
     */
    Boolean submitDetectionResult(Long resultId, Integer userType);

    /**
     * 属地确认检测结果
     *
     * @param resultId 检测结果ID
     * @param userType 用户类型
     * @param action 动作 (1达标确认, 2不达标报告)
     * @param approvalRemark 审批意见
     * @param approver 审批人
     * @return 是否成功
     */
    Boolean localConfirmDetectionResult(Long resultId, Integer userType, Integer action,
                                       String approvalRemark, String approver);

    /**
     * 管理中心确认检测结果
     *
     * @param resultId 检测结果ID
     * @param userType 用户类型
     * @param action 动作 (1结束, 2驳回)
     * @param approvalRemark 审批意见
     * @param approver 审批人
     * @return 是否成功
     */
    Boolean centerConfirmDetectionResult(Long resultId, Integer userType, Integer action,
                                        String approvalRemark, String approver);
}
