import{Q as r,c as o,o as a,p as c,y as p,t as d,F as h,C as m,x as _,D as f,B as z,a as V}from"./index-D07cMzhp.js";import{l as g}from"./index-Csd-Gsnl.js";const v={name:"HmiRun",data(){return{zutaiOptions:[],zutaiValue:void 0,zutaiSrc:void 0,publicPath:"/"}},created(){this.getZutaiOptions(),window.top.router=V()},methods:{getZutaiOptions(){g().then(n=>{this.zutaiOptions=n})},handleChange(){this.zutaiValue!=null&&(this.zutaiSrc=this.publicPath+"hmi/hmiRun.html?filename="+this.zutaiValue)}}},x={class:"app-container"},C=["src"];function O(n,t,b,k,i,l){const s=f,u=z;return a(),o("div",x,[c(u,{modelValue:i.zutaiValue,"onUpdate:modelValue":t[0]||(t[0]=e=>i.zutaiValue=e),placeholder:"请选择组态文件",style:{margin:"20px"},onChange:t[1]||(t[1]=e=>l.handleChange())},{default:d(()=>[(a(!0),o(h,null,m(i.zutaiOptions,e=>(a(),_(s,{key:e.id,label:e.filename,value:e.filename},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),this.zutaiValue!=null?(a(),o("iframe",{key:0,src:this.zutaiSrc,frameborder:"0",height:"800px",scrolling:"no",width:"100%"},null,8,C)):p("",!0)])}const B=r(v,[["render",O]]);export{B as default};
