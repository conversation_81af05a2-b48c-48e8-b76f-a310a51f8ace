import{Q as d,m as c,c as _,o as f,p as e,t,q as h,b7 as w,A as R,M as V}from"./index-D07cMzhp.js";import{E as g}from"./el-row-CikYE3zA.js";import{E}from"./el-col-BaG5Rg5z.js";import b from"./AceInupt-BbFCPjvh.js";const v={name:"WarnBizRuleForm",components:{AceInput:b},props:{modelValue:String},data(){return{opt:{useWorker:!0,enableBasicAutocompletion:!0,enableLiveAutocompletion:!0,enableSnippets:!0,showPrintMargin:!1,highlightActiveLine:!0,highlightSelectedWord:!0,tabSize:4,fontSize:14,wrap:!1,readonly:!1},warnRule:""}},watch:{warnRule:{deep:!0,handler(o){this.warnRule!==this.modelValue&&this.$emit("update:modelValue",o)}},modelValue:{immediate:!0,deep:!0,handler(o){this.warnRule=o}}},methods:{}};function x(o,l,A,B,n,S){const r=w,a=c("ace-input"),u=R,s=E,i=g,m=V;return f(),_("div",null,[e(r,null,{default:t(()=>l[1]||(l[1]=[h("span",{style:{"font-size":"15px"}},"告警规则",-1)])),_:1}),e(m,{ref:"form","label-width":"auto"},{default:t(()=>[e(i,null,{default:t(()=>[e(s,{span:24},{default:t(()=>[e(u,{label:"告警规则"},{default:t(()=>[e(a,{ref:"aceInput",modelValue:n.warnRule,"onUpdate:modelValue":l[0]||(l[0]=p=>n.warnRule=p),format:!0,lang:"groovy",placeholder:"请输入告警规则"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},512)])}const y=d(v,[["render",x]]);export{y as default};
