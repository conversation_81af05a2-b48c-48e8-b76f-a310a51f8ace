<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="年份" prop="year">
              <el-date-picker v-model="queryParams.year" format="YYYY" placeholder="选择日期" type="year" value-format="YYYY" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button icon="Download" plain type="warning" @click="handleExport"> 导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table ref="reportTable" v-loading="loading" :data="dataList" border height="600" stripe :cell-style="cellStyleHandler">
        <el-table-column align="center" fixed="left" label="序号" type="index" width="55" />
        <el-table-column align="center" fixed="left" label="取用水单位" min-width="110" prop="bujianName" />
        <el-table-column label="1月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs01" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs01" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz01" />
        </el-table-column>
        <el-table-column label="2月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs02" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs02" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz02" />
        </el-table-column>
        <el-table-column label="3月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs03" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs03" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz03" />
        </el-table-column>
        <el-table-column label="4月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs04" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs04" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz04" />
        </el-table-column>
        <el-table-column label="5月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs05" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs05" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz05" />
        </el-table-column>
        <el-table-column label="6月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs06" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs06" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz06" />
        </el-table-column>
        <el-table-column label="7月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs07" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs07" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz07" />
        </el-table-column>
        <el-table-column label="8月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs08" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs08" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz08" />
        </el-table-column>
        <el-table-column label="9月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs09" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs09" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz09" />
        </el-table-column>
        <el-table-column label="10月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs10" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs10" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz10" />
        </el-table-column>
        <el-table-column label="11月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs11" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs11" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz11" />
        </el-table-column>
        <el-table-column label="12月份" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="valueqs12" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="valuezs12" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="valuecz12" />
        </el-table-column>
        <el-table-column label="年合计" align="center">
          <el-table-column align="center" label="取用水量（方）" min-width="90" prop="qsTotal" />
          <el-table-column align="center" label="注清水量（方）" min-width="90" prop="zsTotal" />
          <el-table-column align="center" label="取用水平衡分析（方）" min-width="90" prop="czTotal" />
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup name="QuYongShui" lang="ts">
import { WaterUseFrom, WaterUseQuery, WaterUseVO } from '@/api/epms/eptw/statisticsWaterUse/types';
import { OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import * as XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style-vite';
import { saveAs } from 'file-saver';

import { ref } from 'vue';
import { listPingHeng } from '@/api/epms/eptw/quYongShuiPingHeng';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const dataList = ref<WaterUseVO[]>([]);
const blockList = ref<OperationAreaVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const reportTable = ref(null);
const queryFormRef = ref<ElFormInstance>();

const initFormData: WaterUseFrom = {
  licenseId: undefined,
  waterDrawingLicenseCode: undefined,
  yearWaterIntake: undefined,
  waterDrawingAmount: undefined,
  wellVoList: [],
  _group: {
    waterDrawingLicenseCode: undefined,
    waterDrawingAmount: undefined,
    yearWaterIntake: undefined,
    isFirstRow: undefined,
    groupSize: undefined
  }
};

const data = reactive<PageData<WaterUseVO, WaterUseQuery>>({
  form: { ...initFormData },
  queryParams: {
    year: String(new Date().getFullYear())
  },
  rules: {
    waterDrawingLicenseId: [{ required: true, message: '取水证id不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '取水区块不能为空', trigger: 'change' }],
    waterDrawingLicenseCode: [{ required: true, message: '取水证编号不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询取水证列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPingHeng(queryParams.value);
  dataList.value = res.data.map((item) => {
    for (let i = 1; i <= 12; i++) {
      const month = i < 10 ? `0${i}` : `${i}`;
      item[`valueqs${month}`] = parseFloat((item[`valueqs${month}`] ?? 0).toFixed(2));
      item[`valuezs${month}`] = parseFloat((item[`valuezs${month}`] ?? 0).toFixed(2));
      item[`valuecz-${month}`] = item[`valueqs${month}`] - item[`valuezs${month}`];
      item[`valuecz${month}`] = parseFloat((item[`valuecz-${month}`] ?? 0).toFixed(2));
      item[`czTotal`] = item[`qsTotal`] - item[`zsTotal`];
      item[`czTotal`] = parseFloat((item[`czTotal`] ?? 0).toFixed(2));
    }
    return item;
  });

  loading.value = false;
};

const cellStyleHandler = ({ row, column }) => {
  const ignoreProps = ['', 'bujianName'];
  if (ignoreProps.includes(column.property)) {
    return {};
  }
  const cellValue = row[column.property];
  if (typeof cellValue === 'number' && cellValue < 0) {
    return {
      color: 'red' // 负值显示为红色
    };
  }
  return {};
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

const getColor = (rate) => {
  if (rate >= 90) return '#ef1a1a';
  else if (rate >= 70) return '#e6c62c';
  else if (rate >= 50) return '#33c10f';
};

/**
 * 获取全部列表
 */
const getAllList = () => {
  listOperationArea().then((res) => {
    blockList.value = res.rows;
  });
};

const s2ab = (s) => {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < s.length; i++) view[i] = s.charCodeAt(i) & 0xff;
  return buf;
};

const handleExport = () => {
  try {
    // 使用 ref 值需要加上 .value
    const $e = reportTable.value?.$el;
    let $table = $e.querySelector('.el-table__fixed');
    if (!$table) {
      $table = $e;
    }

    const wb = XLSX.utils.table_to_book($table, { raw: true });
    const ws = wb.Sheets[wb.SheetNames[0]];
    // 1. 设置列宽自适应
    const colWidths = [];
    // 遍历所有单元格计算最大列宽
    const range = XLSX.utils.decode_range(ws['!ref']);
    for (let col = range.s.c; col <= range.e.c; col++) {
      let maxWidth = 0;
      for (let row = range.s.r; row <= range.e.r; row++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = ws[cellAddress];
        if (cell && cell.v) {
          const cellText = String(cell.v);
          // 粗略计算宽度：中文占2字符，英文占1字符
          const width = cellText.split('').reduce((acc, char) => acc + (char.charCodeAt(0) > 255 ? 2 : 1), 0);
          if (width > maxWidth) maxWidth = width;
        }
      }
      // 设置列宽（加缓冲值）
      colWidths.push({ wch: Math.min(maxWidth + 2, 60) }); // 限制最大宽度
    }
    ws['!cols'] = colWidths;

    // 2. 设置单元格样式（居中+边框）
    const style = {
      alignment: {
        horizontal: 'center',
        vertical: 'center',
        wrapText: true // 增加自动换行
      },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      },
      font: {
        // 必须包含基础字体设置
        sz: 11,
        name: '宋体',
        color: { rgb: '000000' }
      }
    };

    // 遍历所有单元格应用样式
    Object.keys(ws).forEach((cellAddress) => {
      if (!cellAddress.startsWith('!')) {
        const cell = ws[cellAddress];
        // 保留原始单元格样式（如果有）
        cell.s = cell.s ? { ...cell.s, ...style } : { ...style };
        // 处理数字格式（保留两位小数）
        if (typeof cell.v === 'number') {
          cell.z = cell.z || '0.00';
        }
      }
    });
    if (ws['!merges']) {
      ws['!merges'].forEach((merge) => {
        for (let r = merge.s.r; r <= merge.e.r; r++) {
          for (let c = merge.s.c; c <= merge.e.c; c++) {
            const cellAddr = XLSX.utils.encode_cell({ r, c });
            if (!ws[cellAddr]) ws[cellAddr] = { t: 's', v: '' }; // 填充空单元格
            ws[cellAddr].s = { ...style };
          }
        }
      });
    }
    const wbout = XLSXStyle.write(wb, {
      bookType: 'xlsx',
      bookSST: false,
      type: 'binary',
      cellStyles: true // 必须启用样式支持
    });
    saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), `取用水平衡分析报表_${new Date().getTime()}.xlsx`);
  } catch (e) {
    if (typeof console !== 'undefined') console.error(e);
  }
};

onMounted(() => {
  getList();
  getAllList();
});
</script>
