package com.biz.epms.eppcs.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.biz.epms.eppcs.domain.EppcsSamplingApplication;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 取样检测申请视图对象 eppcs_sampling_application
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = EppcsSamplingApplication.class)
public class EppcsSamplingApplicationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 申请id
     */
    @ExcelProperty(value = "申请id")
    private Long applicationId;

    /**
     * 检测项目名称
     */
    @ExcelProperty(value = "检测项目名称")
    private String projectName;

    /**
     * 取样点位
     */
    @ExcelProperty(value = "取样点位")
    private String samplingPoint;

    /**
     * 检测标准
     */
    @ExcelProperty(value = "检测标准")
    private Integer detectionStandard;

    /**
     * 申请单位ID
     */
    @ExcelProperty(value = "申请单位ID")
    private Long applicationUnitId;

    /**
     * 属地单位ID
     */
    @ExcelProperty(value = "属地单位ID")
    private Long localUnitId;



    /**
     * 申请时间
     */
    @ExcelProperty(value = "申请时间")
    private Date applyTime;

    /**
     * 申请状态 (0草稿,1驳回,2待属地组室审批,...)
     */
    @ExcelProperty(value = "申请状态")
    private Integer applicationStatus;

    /**
     * 申请状态描述
     */
    @ExcelProperty(value = "申请状态描述")
    private String applicationStatusDesc;

    /**
     * 当前阶段 (0取样申请,1取样填报,...)
     */
    @ExcelProperty(value = "当前阶段")
    private Integer currentPhase;

    /**
     * 当前阶段描述
     */
    @ExcelProperty(value = "当前阶段描述")
    private String currentPhaseDesc;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 附件IDs
     */
    @ExcelProperty(value = "附件IDs")
    private String file;

    /**
     * 审批时间
     */
    @ExcelProperty(value = "审批时间")
    private Date approvalTime;

    /**
     * 审批人
     */
    @ExcelProperty(value = "审批人")
    private String approver;

    /**
     * 审批建议
     */
    @ExcelProperty(value = "审批建议")
    private String approvalRemark;




}
