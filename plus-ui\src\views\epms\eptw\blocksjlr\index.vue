<template>
  <div class="common-layout p-2">
    <div class="mb-[10px]">
      <el-card shadow="hover">
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="年份" prop="year">
            <el-date-picker v-model="queryParams.year" :append-to-body="false" class="searchDate" format="YYYY" type="year" value-format="YYYY">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="所属地" prop="operationAreaId">
            <el-select v-model="queryParams.operationAreaId" :popper-append-to-body="false" class="searchDate" filterable placeholder="选择所属地">
              <el-option
                v-for="operationArea in operationAreaList"
                :key="operationArea.iotId"
                :label="operationArea.operationAreaName"
                :value="operationArea.iotId"
              />
            </el-select>
          </el-form-item>
          <el-form-item style="padding-left: 20px">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" icon="Upload" @click="handleImport">导入</el-button>
            <el-button type="warning" icon="Download" @click="handleExport">导出</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    <el-card shadow="never" class="grid-container">
      <vxe-grid ref="xTable" :data="tableData" height="auto" v-bind="gridOptions" @edit-closed="editClosedEvent">
        <template #val_default="obj">
          {{ showValue(obj) }}
        </template>
        <template #val_edit="obj">
          <el-input v-model="obj.row[obj.column.property]"></el-input>
        </template>
      </vxe-grid>
    </el-card>
    <el-dialog v-dialog-drag :title="uploadConfig.title" v-model="uploadConfig.open" :append-to-body="true" width="400px">
      <el-form ref="upload" :model="upload" :rules="rules" :inline="true" label-width="68px">
        <el-form-item label="年份" prop="year">
          <el-date-picker v-model="upload.year" class="searchDate" format="YYYY" type="year" value-format="YYYY"> </el-date-picker>
        </el-form-item>

        <!--        <el-form-item label="所属地" prop="operationAreaId">-->
        <!--          <el-select filterable v-model="upload.operationAreaId" class="searchDate" placeholder="选择设备" :popper-append-to-body="false" >-->
        <!--            <el-option v-for="operationArea in operationAreaList"-->
        <!--                       :key="operationArea.iotId"-->
        <!--                       :label="operationArea.operationAreaName"-->
        <!--                       :value="operationArea.iotId" />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <el-button type="primary" plain @click="downloadTemp">生成模板</el-button>
        </el-form-item>
      </el-form>
      <el-upload
        ref="upload"
        :limit="1"
        drag
        accept=".xlsx, .xls"
        :headers="uploadConfig.headers"
        :action="uploadConfig.url + '?updateSupport=' + uploadConfig.updateSupport"
        :disabled="uploadConfig.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或 <em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
        </template>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm" class="sure">确 定</el-button>
        <el-button @click="uploadConfig.open = false" class="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import moment from 'dayjs';
import { getDataList, updateData } from '@/api/epms/eptw/blocksjlr/index.js';
import { getToken } from '@/utils/auth.ts';
import { listOperationAreaByDataScope } from '@/api/epms/epcom/operationArea/index';

export default {
  name: 'blocksjlr',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      operationAreaList: [], //所属地列表
      tableData: [], //数据列表
      //查询参数
      queryParams: {
        year: moment().format('YYYY'),
        operationAreaId: null
      },
      gridOptions: {
        border: true,
        stripe: true,
        resizable: true,
        showOverflow: true,
        keepSource: true,
        height: 600,
        editConfig: {
          trigger: 'dblclick',
          mode: 'cell',
          showStatus: true
        },
        columns: []
      },
      upload: {
        year: moment().format('YYYY'),
        operationAreaId: null
      },
      uploadConfig: {
        // 是否展示弹出层
        open: false,
        //弹出层标题
        title: '',
        //是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头
        headers: {
          Authorization: 'Bearer ' + getToken(),
          clientid: import.meta.env.VITE_APP_CLIENT_ID
        },
        // 上传的地址
        url: import.meta.env.VITE_APP_BASE_API + '/epms/blockDataReport/importData',
        loading: null
      },
      rules: {
        year: [{ required: true, message: '请选择年份', trigger: 'change' }],
        operationAreaId: [{ required: true, message: '请选择所属地', trigger: 'change' }]
      }
    };
  },
  async created() {
    this.getAreaList();
  },
  methods: {
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    //获取所属地下拉框
    async getAreaList() {
      const query = {
        operationAreaType: 0
      };
      const res = await listOperationAreaByDataScope(query);
      this.operationAreaList = res.rows;
      this.queryParams.operationAreaId = res.rows[0].iotId;
      this.getList();
    },
    handleQuery() {
      this.getList();
    },
    async getList() {
      this.loading = true;
      const response = await getDataList(this.queryParams);
      this.tableData = response.rows;
      this.initColumn(response.rows);
      this.loading = false;
    },
    /** 展示数据 */
    showValue(obj) {
      return obj.row[obj.column.property];
    },
    initColumn(tab) {
      const cols = [
        { title: '序号', type: 'seq', width: 50, align: 'center', fixed: 'left' },
        { field: 'operationAreaName', title: '所属地', width: 150, align: 'center', fixed: 'left' },
        { field: 'blockName', title: '区块名称', width: 180, align: 'center', fixed: 'left' }
      ];
      const col = {
        field: '月取水量',
        title: '月取水量(m³)',
        width: 150,
        align: 'center',
        children: []
      };
      for (const time in tab[0].riqi) {
        col.children.push({
          field: tab[0].riqi[time],
          title: tab[0].riqi[time],
          width: 150,
          align: 'center',
          editRender: {},
          slots: { default: 'val_default', edit: 'val_edit' }
        });
      }
      cols.push(col);
      this.gridOptions.columns = cols;
    },
    editClosedEvent(obj) {
      let row = obj.row;
      let column = obj.column;
      let field = column.property;
      const cellValue = row[field];

      const $table = this.$refs.xTable;

      updateData({
        monthDate: field,
        bujianId: row.blockId,
        value: cellValue
      }).then((response) => {
        if (response.code === 200) {
          $table.reloadRow(row, null, field);
          this.$message({
            message: '设置成功',
            type: 'success'
          });
        }
      });
    },
    /** 导入按钮操作*/
    async handleImport() {
      this.uploadConfig.title = '数据导入';
      this.uploadConfig.open = true;
      this.upload.operationAreaId = this.operationAreaList[0].iotId;
    },
    downloadTemp() {
      this.loading = true;
      this.download('epms/blockDataReport/downloadTemplate', { ...this.upload }, `导入模板_${new Date().getTime()}.xlsx`);
      this.loading = false;
    },
    handleFileUploadProgress(event, file, fileList) {
      this.uploadConfig.isUploading = true;
      this.uploadConfig.loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.uploadConfig.open = false;
      this.uploadConfig.isUploading = false;
      this.$refs.upload.clearFiles();
      if (this.uploadConfig.loading) {
        this.uploadConfig.loading.close(); // 确保 loading 存在
      }
      ElMessageBox.alert(response.msg, {
        title: '导入结果',
        dangerouslyUseHtml: true // 注意这里属性名也变化了
      });
      this.getList();
    },
    // 上传文件后提交
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('epms/blockDataReport/exportData', { ...this.queryParams }, `取水区块数据_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>
<style lang="scss" scoped>
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100%; // 父容器高度
}
.grid-container {
  flex: 1; // 表格容器占满剩余空间
  display: flex;
  flex-direction: column;
}
:deep(.el-card__body) {
  height: 100%;
}
vxe-grid {
  height: 100%;
}
.el-date-editor {
  --el-date-editor-width: 100%;
}
</style>
