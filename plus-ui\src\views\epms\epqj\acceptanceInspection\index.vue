<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="96px">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属地" prop="workAreaId">
              <el-select v-model="queryParams.workAreaId" placeholder="请选择作业区" clearable>
                <el-option
                  v-for="item in operationAreaList"
                  :key="item.operationAreaId"
                  :label="item.operationAreaName"
                  :value="item.operationAreaId"
                  @keyup.enter="handleQuery"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="施工单位名称" prop="constructionUnit">
              <el-input v-model="queryParams.constructionUnit" placeholder="请输入施工单位名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目状态" prop="status">
              <el-select v-model="queryParams.status" clearable placeholder="请选择项目状态">
                <el-option v-for="dict in epqj_clean_operation_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="项目开始时间" style="width: 336px">
              <el-date-picker
                v-model="dateRangeStartTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="项目验收时间" style="width: 336px">
              <el-date-picker
                v-model="dateRangeEndTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epqj:cleanOperation:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="cleanOperationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" type="index" width="60" :index="indexMethod" />
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column align="center" label="所属地" prop="workAreaId">
          <template #default="scope">
            {{ operationAreaList.find((item) => item.operationAreaId === scope.row.workAreaId)?.operationAreaName || '未知' }}
          </template>
        </el-table-column>
        <el-table-column label="施工单位名称" align="center" prop="constructionUnit" />
        <el-table-column label="项目状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="epqj_clean_operation_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="项目开始时间" align="center" prop="startTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目结束时间" align="center" prop="endTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="文件查看" align="center" class-name="small-padding fixed-width" min-width="200">
          <template #default="scope">
            <el-tooltip content="文件查看" placement="top">
              <el-button v-hasPermi="['epqj:cleanOperation:preview']" icon="Document" link type="primary" @click="previewALLFile(scope.row.projectId)"
                >文件查看</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epqj:cleanOperation:edit']">验收</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改清洁作业项目信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="cleanOperationFormRef" :model="form" :rules="rules" label-width="96px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" disabled placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="所属地" prop="workAreaId">
          <el-select v-model="form.workAreaId" placeholder="请选择作业区" clearable disabled>
            <el-option v-for="item in operationAreaList" :key="item.operationAreaId" :label="item.operationAreaName" :value="item.operationAreaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="施工单位名称" prop="constructionUnit">
          <el-input v-model="form.constructionUnit" disabled placeholder="请输入施工单位名称" />
        </el-form-item>
        <el-form-item label="项目状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择项目状态" disabled>
            <el-option v-for="dict in epqj_clean_operation_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目开始时间" prop="startTime">
          <el-date-picker
            clearable
            disabled
            v-model="form.startTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择项目开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="验收时间" prop="endTime">
          <el-date-picker v-model="form.endTime" clearable placeholder="请选择验收时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="清洁作业附件" prop="endTime">
          <el-button @click="previewALLFile(form.projectId)">附件查看</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 附件预览 -->
    <el-dialog title="附件预览" v-model="previewDialog.visible" width="80%" he append-to-body>
      <div style="height: 68vh">
        <component :is="previewComponent" v-if="previewComponent" v-bind="previewProps" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="CleanOperation" lang="ts">
import { addCleanOperation, delCleanOperation, getCleanOperation, listCleanOperation, updateCleanOperation } from '@/api/epms/epqj/cleanOperation';
import { CleanOperationForm, CleanOperationQuery, CleanOperationVO } from '@/api/epms/epqj/cleanOperation/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { shallowRef } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epqj_clean_operation_status } = toRefs<any>(proxy?.useDict('epqj_clean_operation_status'));

const cleanOperationList = ref<CleanOperationVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const cleanOperationFormRef = ref<ElFormInstance>();
const operationAreaQuery = ref<OperationAreaQuery>({}); // 所属地片区查询条件
const operationAreaList = ref<OperationAreaVO[]>([]); // 所属地列表
const dateRangeStartTime = ref<[DateModelType, DateModelType]>(['', '']); //项目日期查询时间段
const dateRangeEndTime = ref<[DateModelType, DateModelType]>(['', '']); //项目日期查询时间段
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const previewComponent = shallowRef();
const previewProps = ref({} as Record<string, any>);
const previewDialog = reactive({
  visible: false,
  url: ''
});

const initFormData: CleanOperationForm = {
  projectId: undefined,
  projectName: undefined,
  workAreaId: undefined,
  constructionUnit: undefined,
  status: 0,
  startTime: undefined,
  endTime: undefined,
  geologicalDesign: undefined,
  engineeringDesign: undefined,
  constructionDesign: undefined,
  environmentalPermit: undefined,
  hsePlan: undefined,
  wellHandoverDocument: undefined,
  emergencyResponsePlan: undefined,
  dailyBriefingRecord: undefined,
  siteCommencementApproval: undefined,
  operationDailyReport: undefined,
  supervisionRecords: undefined,
  wellControlDrill: undefined,
  workoverSummary: undefined
};
const data = reactive<PageData<CleanOperationForm, CleanOperationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    workAreaId: undefined,
    constructionUnit: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    projectId: [{ required: true, message: '项目id不能为空', trigger: 'blur' }],
    projectName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
    endTime: [{ required: true, message: '验收时间不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
/** 查询清洁作业项目信息列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeStartTime.value, 'StartTime');
  proxy?.addDateRange(queryParams.value, dateRangeEndTime.value, 'EndTime');
  const res = await listCleanOperation(queryParams.value);
  cleanOperationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  cleanOperationFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CleanOperationVO[]) => {
  ids.value = selection.map((item) => item.projectId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CleanOperationVO) => {
  reset();
  const _projectId = row?.projectId || ids.value[0];
  const res = await getCleanOperation(_projectId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改清洁作业项目信息';
};

/** 提交按钮 */
const submitForm = () => {
  cleanOperationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.projectId) {
        form.value.status = 2;
        await updateCleanOperation(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCleanOperation(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CleanOperationVO) => {
  const _projectIds = row?.projectId || ids.value;
  await proxy?.$modal.confirm('是否确认删除清洁作业项目信息编号为"' + _projectIds + '"的数据项？').finally(() => (loading.value = false));
  await delCleanOperation(_projectIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epqj/cleanOperation/export',
    {
      ...queryParams.value
    },
    `cleanOperation_${new Date().getTime()}.xlsx`
  );
};

/** 查询所属地、片区列表 */
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaParentId = 0;
  operationAreaQuery.value.operationAreaType = 0;
  const res = await listOperationArea(operationAreaQuery.value);
  operationAreaList.value = res.rows;
};

/** 审批弹窗附件预览 */
const previewFile = async (applicationId?: string | number, category?: string) => {
  if (!applicationId) return;

  const props = {
    attachSourceId: applicationId,
    attachSourceType: 'cleanOperationProcess',
    attachCategory: category
  };

  // 异步加载 preview.vue 组件
  previewComponent.value = defineAsyncComponent(() => import('@/views/comm/attach/preview.vue'));

  // 传递参数给 preview.vue
  previewProps.value = props;
  previewDialog.visible = true;
};

/** 审批弹窗附件预览 */
const previewALLFile = async (applicationId?: string) => {
  if (!applicationId) return;
  proxy.showAttachPreview({
    attachSourceId: applicationId,
    attachSourceType: 'cleanOperationProcess'
  });
  // const props = {
  //   attachSourceId: applicationId,
  //   attachSourceType: "cleanOperationProcess",
  // };
  //
  // // 异步加载 preview.vue 组件
  // previewComponent.value = defineAsyncComponent(() =>
  //   import('@/views/comm/attach/preview.vue')
  // );
  //
  // // 传递参数给 preview.vue
  // previewProps.value = props;
  // previewDialog.visible = true;
};

onMounted(() => {
  getoperationAreaList();
  getList();
});
</script>
<style lang="scss" scoped>
:deep(.el-form-item__label) {
  line-height: 18px;
  align-items: center;
}
:deep(.el-date-editor) {
  --el-date-editor-width: 100%;
}
</style>
