export interface DetectionResultVO {
  /**
   * 检测结果id
   */
  resultId: string | number;

  /**
   * 申请id
   */
  applicationId: string | number;

  /**
   * 样品编号
   */
  sampleNumber: string;

  /**
   * 检测项目名称
   */
  projectName: string;

  /**
   * 检测标准
   */
  detectionStandard: number;

  /**
   * 检测日期
   */
  detectionDate: string;

  /**
   * 报送时间
   */
  submitTime: string;

  /**
   * 检测状态 (0草稿,1驳回,2待属地单位确认,3待煤层气管理中心确认,4检测完成)
   */
  detectionStatus: number;

  /**
   * 检测报告附件
   */
  reportFile: number;

  /**
   * 检测结果数据 JSON格式
   */
  resultData: string;

  /**
   * 检测结果 (0达标,1不达标)
   */
  resultStatus: number;

  /**
   * 审批时间
   */
  approvalTime: string;

  /**
   * 审批建议
   */
  approvalRemark: string;

  /**
   * 审批人
   */
  approver: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 预警标记 (0无预警,1有预警)
   */
  warningFlag: number;

}

export interface DetectionResultForm extends BaseEntity {
  /**
   * 检测结果id
   */
  resultId?: string | number;

  /**
   * 申请id
   */
  applicationId?: string | number;

  /**
   * 样品编号
   */
  sampleNumber?: string;

  /**
   * 检测项目名称
   */
  projectName?: string;

  /**
   * 检测标准
   */
  detectionStandard?: number;

  /**
   * 检测日期
   */
  detectionDate?: string;

  /**
   * 报送时间
   */
  submitTime?: string;

  /**
   * 检测状态 (0草稿,1驳回,2待属地单位确认,3待煤层气管理中心确认,4检测完成)
   */
  detectionStatus?: number;

  /**
   * 检测报告附件
   */
  reportFile?: number;

  /**
   * 检测结果数据 JSON格式
   */
  resultData?: string;

  /**
   * 检测结果 (0达标,1不达标)
   */
  resultStatus?: number;

  /**
   * 审批时间
   */
  approvalTime?: string;

  /**
   * 审批建议
   */
  approvalRemark?: string;

  /**
   * 审批人
   */
  approver?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 预警标记 (0无预警,1有预警)
   */
  warningFlag?: number;

}

export interface DetectionResultQuery extends PageQuery {

  /**
   * 申请id
   */
  applicationId?: string | number;

  /**
   * 检测项目名称
   */
  projectName?: string;

  /**
   * 样品编号
   */
  sampleNumber?: string;

  /**
   * 检测日期范围
   */
  detectionDateRange?: string[];

  /**
   * 检测日期开始
   */
  detectionDateStart?: string;

  /**
   * 检测日期结束
   */
  detectionDateEnd?: string;

  /**
   * 检测状态 (0草稿,1驳回,2待属地单位确认,3待煤层气管理中心确认,4检测完成)
   */
  detectionStatus?: number;

  /**
   * 检测结果 (0达标,1不达标)
   */
  resultStatus?: number;

  /**
   * 申请ID列表（逗号分隔）
   */
  applicationIds?: string;

  /**
   * 审批时间
   */
  approvalTime?: string;

  /**
   * 审批建议
   */
  approvalRemark?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}

export interface DetectionResultQuery extends PageQuery {

  /**
   * 申请id
   */
  applicationId?: string | number;

  /**
   * 检测结果数据 JSON格式
   */
  resultData?: string;

  /**
   * 检测结果 (0达标,1不达标)
   */
  resultStatus?: number;

  /**
   * 审批时间
   */
  approvalTime?: string;

  /**
   * 审批建议
   */
  approvalRemark?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



