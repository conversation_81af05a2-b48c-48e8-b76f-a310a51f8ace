import{Q as s,c as t,o,q as c}from"./index-D07cMzhp.js";const a={name:"Hmi",data(){return{publicPath:"/"}},created(){},methods:{}},r={class:"p-2"},n=["src"];function i(d,_,l,m,e,p){return o(),t("div",r,[c("iframe",{src:`${e.publicPath}hmi/hmiDesign.html`,class:"configuration-editor",frameborder:"0",scrolling:"no"},null,8,n)])}const h=s(a,[["render",i],["__scopeId","data-v-ead9f450"]]);export{h as default};
