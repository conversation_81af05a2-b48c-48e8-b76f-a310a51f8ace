import{aC as V,d as ce,a as Ke,r as u,h as Me,ak as ie,ai as U,bj as Te,b as ze,aH as He,c as j,o as s,p as l,t as o,w as g,q as Y,a7 as We,M as qe,e as t,A as xe,G as Be,H as se,B as Qe,F as q,C as x,x as m,D as Oe,a8 as de,K as Je,J as c,am as ue,aI as Ge,ay as Xe,ax as Ze,z as me,aJ as ea,aL as aa,y as B,v as ta,az as la,aA as oa,bc as na,cA as ra,aD as ia,Q as sa}from"./index-D07cMzhp.js";import{_ as da}from"./index-CtxYKwMQ.js";import{_ as ua}from"./index-DVHplxfU.js";import{E as ma}from"./el-row-CikYE3zA.js";import{_ as pa}from"./index-BWMgqvQ9.js";import{E as ca}from"./el-col-BaG5Rg5z.js";import{E as fa}from"./el-date-picker-HyhB9X9n.js";import{l as va}from"./index-DjjjlEwF.js";import{f as $}from"./types-DSjC7ANO.js";import{l as _a}from"./index-BhIIZXqy.js";import{m as ga}from"./dayjs.min-Brw96_N0.js";import"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import"./index-BA1WtQgJ.js";import"./el-link-Ar98HWTJ.js";import"./el-tree-DW6MoFaI.js";import"./index-VIEDZI2D.js";const ba=f=>V({url:"/epfj/landReclamation/list",method:"get",params:f}),wa=f=>V({url:"/epfj/landReclamation/"+f,method:"get"}),Ra=f=>V({url:"/epfj/landReclamation",method:"post",data:f}),pe=f=>V({url:"/epfj/landReclamation",method:"put",data:f}),ya=f=>V({url:"/epfj/landReclamation/"+f,method:"delete"}),ha=(f,k,i)=>V({url:"/epfj/landReclamation/submitStatus",method:"post",params:{recordId:f,approvalStatus:k,action:i}}),Ia={class:"p-2"},Va={class:"mb-[10px]"},ka={class:"dialog-footer"},Da={style:{height:"68vh"}},Sa=ce({name:"landReclamation"}),Aa=ce({...Sa,setup(f){Ke();const k=u({}),{proxy:i}=Me(),{epfj_approval_status:fe}=ie(i==null?void 0:i.useDict("epfj_approval_status")),S=u([]),Q=u([]),_=U({visible:!1,title:""}),w=u(!1),A=u(!0),P=u(!0),O=u([]),ve=u(!0),_e=u(!0),K=u(0),C=u(["",""]),J=u(),N=u(),G=Te(),ge=u({}),X=U({visible:!1,url:""}),Z={recordId:void 0,recordName:void 0,recordDate:void 0,operationAreaId:void 0,sealIds:void 0,sealIdList:void 0,reclamationAcceptanceReport:void 0,preReclamationPhotos:void 0,underReclamationPhotos:void 0,afterReclamationPhotos:void 0,approvalStatus:$.tuDiFuKen.value,approvalOpinions:void 0},be=U({form:{...Z},queryParams:{pageNum:1,pageSize:10,operationAreaId:void 0,recordName:void 0,wellName:void 0,params:{}},rules:{recordId:[{required:!0,message:"记录id不能为空",trigger:"blur"}],recordName:[{required:!0,message:"土地复垦名称不能为空",trigger:"blur"}]}}),{queryParams:d,form:r,rules:we}=ie(be),ee=u([]),M=U({pageSize:-1,pageNum:1,workAreaId:void 0,approvalStatus:$.yiFengJing.value}),E=async n=>{n?M.workAreaId=n:M.workAreaId=void 0;const e=await va(M);ee.value=e.rows},R=async()=>{A.value=!0,d.value.params={},i==null||i.addDateRange(d.value,C.value,"RecordDate");const n=await ba(d.value);Q.value=n.rows,K.value=n.total,A.value=!1},Re=()=>{T(),_.visible=!1},ye=n=>(d.value.pageNum-1)*d.value.pageSize+n+1,he=()=>{var n;(n=N.value)==null||n.validate(async e=>{e&&(w.value=!0,r.value.recordId?(await pe(r.value).finally(()=>w.value=!1),await ha(r.value.recordId,r.value.approvalStatus,0)):i==null||i.$modal.msgError("数据异常"),i==null||i.$modal.msgSuccess("操作成功"),_.visible=!1,await R())})},Ie=async n=>{const e=(n==null?void 0:n.recordId)||O.value;await(i==null?void 0:i.$modal.confirm('是否确认删除土地复垦编号为"'+e+'"的数据项？').finally(()=>A.value=!1)),await ya(e),i==null||i.$modal.msgSuccess("删除成功"),await R()},Ve=async n=>{T();const e=n==null?void 0:n.recordId,y=await wa(e);await E(null),Object.assign(r.value,y.data),_.visible=!0,_.title="复垦验收报告"},F=async(n,e)=>{n&&i.showAttachPreview({attachSourceId:n,attachSourceType:"wasteWellSealProcess",attachCategory:e})},ke=async()=>{k.value.operationAreaParentId=0,k.value.operationAreaType=0;const n=await _a(k.value);S.value=n.rows},De=n=>{if(!n)return"未知";const e=S.value.find(y=>y.operationAreaId===n);return e?e.operationAreaName:"未知"},T=()=>{var n;r.value={...Z},(n=N.value)==null||n.resetFields()},L=()=>{d.value.pageNum=1,R()},Se=()=>{var n;(n=J.value)==null||n.resetFields(),L()},Ae=n=>{O.value=n.map(e=>e.recordId),ve.value=n.length!=1,_e.value=!n.length},Pe=()=>{T(),E(null),r.value.recordDate=ga(new Date).format("YYYY-MM-DD HH:mm:ss"),_.visible=!0,_.title="添加土地复垦"},Ce=()=>{var n;(n=N.value)==null||n.validate(async e=>{e&&(w.value=!0,r.value.recordId?await pe(r.value).finally(()=>w.value=!1):await Ra(r.value).finally(()=>w.value=!1),i==null||i.$modal.msgSuccess("操作成功"),_.visible=!1,await R())})};return ze(()=>{ke(),E(null),R()}),(n,e)=>{var ne,re;const y=Be,p=xe,z=Oe,H=Qe,ae=fa,v=Je,te=qe,le=We,Ne=ca,Ee=pa,Fe=ma,b=Ze,Le=ea,I=aa,Ue=Xe,je=la,Ye=ua,W=da,oe=oa,h=He("hasPermi"),$e=ta;return s(),j("div",Ia,[l(Ge,{"enter-active-class":(ne=t(i))==null?void 0:ne.animate.searchAnimate.enter,"leave-active-class":(re=t(i))==null?void 0:re.animate.searchAnimate.leave},{default:o(()=>[g(Y("div",Va,[l(le,{shadow:"hover"},{default:o(()=>[l(te,{ref_key:"queryFormRef",ref:J,model:t(d),inline:!0,"label-width":"100px"},{default:o(()=>[l(p,{label:"土地复垦名称",prop:"recordName"},{default:o(()=>[l(y,{modelValue:t(d).recordName,"onUpdate:modelValue":e[0]||(e[0]=a=>t(d).recordName=a),placeholder:"请输入土地复垦名称",clearable:"",onKeyup:se(L,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"井名称",prop:"wellName"},{default:o(()=>[l(y,{modelValue:t(d).wellName,"onUpdate:modelValue":e[1]||(e[1]=a=>t(d).wellName=a),placeholder:"请输入井名称",clearable:"",onKeyup:se(L,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"所属地",prop:"workAreaId"},{default:o(()=>[l(H,{modelValue:t(d).operationAreaId,"onUpdate:modelValue":e[2]||(e[2]=a=>t(d).operationAreaId=a),class:"searchDate",clearable:"",filterable:"",placeholder:"选择所属地"},{default:o(()=>[(s(!0),j(q,null,x(t(S),a=>(s(),m(z,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"土地复垦时间",style:{width:"400px"}},{default:o(()=>[l(ae,{modelValue:t(C),"onUpdate:modelValue":e[3]||(e[3]=a=>de(C)?C.value=a:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]},null,8,["modelValue","default-time"])]),_:1}),l(p,null,{default:o(()=>[l(v,{type:"primary",icon:"Search",onClick:L},{default:o(()=>e[17]||(e[17]=[c("搜索")])),_:1}),l(v,{icon:"Refresh",onClick:Se},{default:o(()=>e[18]||(e[18]=[c("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[ue,t(P)]])]),_:1},8,["enter-active-class","leave-active-class"]),l(le,{shadow:"never"},{header:o(()=>[l(Fe,{gutter:10,class:"mb8"},{default:o(()=>[l(Ne,{span:1.5},{default:o(()=>[g((s(),m(v,{type:"primary",plain:"",icon:"Plus",onClick:Pe},{default:o(()=>e[19]||(e[19]=[c("新增")])),_:1})),[[h,["epfj:landReclamation:add"]]])]),_:1}),l(Ee,{showSearch:t(P),"onUpdate:showSearch":e[4]||(e[4]=a=>de(P)?P.value=a:null),onQueryTable:R},null,8,["showSearch"])]),_:1})]),default:o(()=>[g((s(),m(Ue,{data:t(Q),stripe:"",onSelectionChange:Ae},{default:o(()=>[l(b,{type:"selection",width:"55",align:"center"}),l(b,{label:"序号",align:"center",type:"index",width:"55",index:ye}),l(b,{align:"center",label:"土地复垦名称",prop:"recordName"}),l(b,{label:"土地复垦时间",align:"center",prop:"recordDate",width:"105"},{default:o(a=>[Y("span",null,me(n.parseTime(a.row.recordDate,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),l(b,{align:"center",label:"所属地",prop:"operationAreaId"},{default:o(a=>[c(me(De(a.row.operationAreaId)),1)]),_:1}),l(b,{align:"center",label:"井名称",prop:"sealNames"}),l(b,{label:"审批状态",align:"center",prop:"approvalStatus"},{default:o(a=>[l(Le,{options:t(fe),value:a.row.approvalStatus},null,8,["options","value"])]),_:1}),l(b,{label:"文件查看",align:"center","class-name":"small-padding fixed-width",fixed:"right","min-width":"120"},{default:o(a=>[l(I,{content:"查看复垦验收报告",placement:"top"},{default:o(()=>[g((s(),m(v,{disabled:!a.row.reclamationAcceptanceReport,icon:"Document",link:"",type:"primary",onClick:D=>F(a.row.recordId,"reclamationAcceptanceReport")},{default:o(()=>e[20]||(e[20]=[c("复垦验收报告")])),_:2},1032,["disabled","onClick"])),[[h,["epfj:landReclamation:preview"]]])]),_:2},1024),l(I,{content:"查看复垦前照片",placement:"top"},{default:o(()=>[g((s(),m(v,{disabled:!a.row.preReclamationPhotos,icon:"Document",link:"",type:"primary",onClick:D=>F(a.row.recordId,"preReclamationPhotos")},{default:o(()=>e[21]||(e[21]=[c("复垦前照片")])),_:2},1032,["disabled","onClick"])),[[h,["epfj:landReclamation:preview"]]])]),_:2},1024),l(I,{content:"查看复垦中照片",placement:"top"},{default:o(()=>[g((s(),m(v,{disabled:!a.row.underReclamationPhotos,icon:"Document",link:"",type:"primary",onClick:D=>F(a.row.recordId,"underReclamationPhotos")},{default:o(()=>e[22]||(e[22]=[c("复垦中照片")])),_:2},1032,["disabled","onClick"])),[[h,["epfj:landReclamation:preview"]]])]),_:2},1024),l(I,{content:"查看复垦后照片",placement:"top"},{default:o(()=>[g((s(),m(v,{disabled:!a.row.afterReclamationPhotos,icon:"Document",link:"",type:"primary",onClick:D=>F(a.row.recordId,"afterReclamationPhotos")},{default:o(()=>e[23]||(e[23]=[c("复垦后照片")])),_:2},1032,["disabled","onClick"])),[[h,["epfj:landReclamation:preview"]]])]),_:2},1024)]),_:1}),l(b,{align:"center","class-name":"small-padding fixed-width",fixed:"right",label:"操作","min-width":"130"},{default:o(a=>[a.row.approvalStatus==t($).tuDiFuKen.value?(s(),m(I,{key:0,content:"复垦验收",placement:"top"},{default:o(()=>[g((s(),m(v,{icon:"Edit",link:"",type:"primary",onClick:D=>Ve(a.row)},{default:o(()=>e[24]||(e[24]=[c("复垦验收")])),_:2},1032,["onClick"])),[[h,["epfj:landReclamation:fuKen"]]])]),_:2},1024)):B("",!0),a.row.approvalStatus==t($).tuDiFuKen.value?(s(),m(I,{key:1,content:"删除",placement:"top"},{default:o(()=>[g((s(),m(v,{icon:"Delete",link:"",type:"primary",onClick:D=>Ie(a.row)},{default:o(()=>e[25]||(e[25]=[c("删除")])),_:2},1032,["onClick"])),[[h,["epfj:landReclamation:remove"]]])]),_:2},1024)):B("",!0)]),_:1})]),_:1},8,["data"])),[[$e,t(A)]]),g(l(je,{total:t(K),page:t(d).pageNum,"onUpdate:page":e[5]||(e[5]=a=>t(d).pageNum=a),limit:t(d).pageSize,"onUpdate:limit":e[6]||(e[6]=a=>t(d).pageSize=a),onPagination:R},null,8,["total","page","limit"]),[[ue,t(K)>0]])]),_:1}),l(oe,{title:t(_).title,modelValue:t(_).visible,"onUpdate:modelValue":e[15]||(e[15]=a=>t(_).visible=a),width:"550px","append-to-body":""},{footer:o(()=>[Y("div",ka,[l(v,{loading:t(w),type:"warning",onClick:he},{default:o(()=>e[26]||(e[26]=[c("提 交")])),_:1},8,["loading"]),l(v,{loading:t(w),type:"primary",onClick:Ce},{default:o(()=>e[27]||(e[27]=[c("保 存")])),_:1},8,["loading"]),l(v,{onClick:Re},{default:o(()=>e[28]||(e[28]=[c("取 消")])),_:1})])]),default:o(()=>[l(te,{ref_key:"landReclamationFormRef",ref:N,model:t(r),rules:t(we),"label-width":"115px"},{default:o(()=>[l(p,{label:"土地复垦名称",prop:"recordName"},{default:o(()=>[l(y,{modelValue:t(r).recordName,"onUpdate:modelValue":e[7]||(e[7]=a=>t(r).recordName=a),placeholder:"请输入土地复垦名称"},null,8,["modelValue"])]),_:1}),l(p,{label:"土地复垦时间",prop:"recordDate"},{default:o(()=>[l(ae,{modelValue:t(r).recordDate,"onUpdate:modelValue":e[8]||(e[8]=a=>t(r).recordDate=a),clearable:"",placeholder:"请选择土地复垦时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),l(p,{label:"所属地",prop:"operationAreaId"},{default:o(()=>[l(H,{modelValue:t(r).operationAreaId,"onUpdate:modelValue":e[9]||(e[9]=a=>t(r).operationAreaId=a),clearable:"",placeholder:"请选择所属地",onChange:E},{default:o(()=>[(s(!0),j(q,null,x(t(S),a=>(s(),m(z,{key:a.operationAreaId,label:a.operationAreaName,value:a.operationAreaId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"封井选井",prop:"sealIdList"},{default:o(()=>[l(H,{modelValue:t(r).sealIdList,"onUpdate:modelValue":e[10]||(e[10]=a=>t(r).sealIdList=a),clearable:"",filterable:"",multiple:"",placeholder:"请选择封井列表"},{default:o(()=>[(s(!0),j(q,null,x(t(ee),a=>(s(),m(z,{key:a.sealId,label:a.wellName,value:a.sealId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"复垦验收报告",prop:"reclamationAcceptanceReport"},{default:o(()=>[l(Ye,{modelValue:t(r).reclamationAcceptanceReport,"onUpdate:modelValue":e[11]||(e[11]=a=>t(r).reclamationAcceptanceReport=a),"attach-source-id":t(r).recordId,disabled:!1,"attach-category":"reclamationAcceptanceReport","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1}),l(p,{label:"复垦前照片",prop:"preReclamationPhotos"},{default:o(()=>[l(W,{modelValue:t(r).preReclamationPhotos,"onUpdate:modelValue":e[12]||(e[12]=a=>t(r).preReclamationPhotos=a),"attach-source-id":t(r).recordId,disabled:!1,"attach-category":"preReclamationPhotos","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1}),l(p,{label:"复垦中照片",prop:"underReclamationPhotos"},{default:o(()=>[l(W,{modelValue:t(r).underReclamationPhotos,"onUpdate:modelValue":e[13]||(e[13]=a=>t(r).underReclamationPhotos=a),"attach-source-id":t(r).recordId,disabled:!1,"attach-category":"underReclamationPhotos","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1}),l(p,{label:"复垦后照片",prop:"afterReclamationPhotos"},{default:o(()=>[l(W,{modelValue:t(r).afterReclamationPhotos,"onUpdate:modelValue":e[14]||(e[14]=a=>t(r).afterReclamationPhotos=a),"attach-source-id":t(r).recordId,disabled:!1,"attach-category":"afterReclamationPhotos","attach-source-type":"wasteWellSealProcess"},null,8,["modelValue","attach-source-id"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(oe,{title:"附件预览",modelValue:t(X).visible,"onUpdate:modelValue":e[16]||(e[16]=a=>t(X).visible=a),width:"80%",he:"","append-to-body":""},{default:o(()=>[Y("div",Da,[G.value?(s(),m(na(G.value),ra(ia({key:0},t(ge))),null,16)):B("",!0)])]),_:1},8,["modelValue"])])}}}),xa=sa(Aa,[["__scopeId","data-v-53ff1c94"]]);export{xa as default};
