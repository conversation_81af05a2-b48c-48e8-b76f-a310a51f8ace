<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="单井名称" prop="wellName">
              <el-input v-model="queryParams.wellName" placeholder="请输入单井名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="井的位置描述" label-width="100" prop="location">
              <el-input v-model="queryParams.location" placeholder="请输入井的位置描述" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属作业区" label-width="100" prop="workArea">
              <el-select clearable v-model="queryParams.workArea" placeholder="请选择所属作业区">
                <el-option v-for="dict in workAreaList" :key="dict.operationAreaId" :label="dict.operationAreaName" :value="dict.operationAreaId" />
              </el-select>
            </el-form-item>
            <el-form-item label="开工日期" prop="startDateRange">
              <el-date-picker
                clearable
                v-model="queryParams.startDateRange"
                type="daterange"
                end-placeholder="开工结束日期"
                start-placeholder="开工开始日期"
                value-format="YYYY-MM-DD"
                placeholder="请选择开工日期"
              />
            </el-form-item>
            <el-form-item label="验收日期" prop="inspectDateRange">
              <el-date-picker
                clearable
                v-model="queryParams.inspectDateRange"
                end-placeholder="验收结束日期"
                start-placeholder="验收开始日期"
                type="daterange"
                value-format="YYYY-MM-DD"
                placeholder="请选择验收日期"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epnj:wellPreparation:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epnj:wellPreparation:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epnj:wellPreparation:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epnj:wellPreparation:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="wellPreparationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="单井名称" align="center" prop="wellName" />
        <el-table-column align="center" label="所属作业区" prop="workArea">
          <template #default="scope">
            <span>{{ getAreaName(scope.row.workArea) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="井的位置描述" align="center" prop="location" :show-overflow-tooltip="true" />
        <el-table-column label="开工日期" align="center" prop="startDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="验收日期" align="center" prop="inspectDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.inspectDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="验收单上传日期" align="center" prop="uploadDate" width="180">-->
        <!--          <template #default="scope">-->
        <!--            <span>{{ parseTime(scope.row.uploadDate, '{y}-{m}-{d}') }}</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="处理类型" align="center" prop="handlingType">
          <template #default="scope">
            <dict-tag :options="epnj_handling_type" :value="scope.row.handlingType" />
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="305">
          <template #default="scope">
            <el-tooltip content="附件预览" placement="top">
              <el-button v-hasPermi="['epnj:wellPreparation:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >附件预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epnj:wellPreparation:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epnj:wellPreparation:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改井场准备对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" append-to-body width="500px">
      <el-form ref="wellPreparationFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="单井名称" prop="wellName">
          <el-input v-model="form.wellName" placeholder="请输入单井名称" />
        </el-form-item>
        <el-form-item label="所属作业区" prop="workArea">
          <el-select v-model="form.workArea" placeholder="请选择所属作业区">
            <el-option v-for="dict in workAreaList" :key="dict.operationAreaId" :label="dict.operationAreaName" :value="dict.operationAreaId" />
          </el-select>
        </el-form-item>
        <el-form-item label="井的位置描述" prop="location">
          <el-input v-model="form.location" placeholder="请输入井的位置描述" />
        </el-form-item>
        <el-form-item label="开工日期" prop="startDate">
          <el-date-picker v-model="form.startDate" clearable placeholder="请选择开工日期" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="验收日期" prop="inspectDate">
          <el-date-picker v-model="form.inspectDate" clearable placeholder="请选择验收日期" type="date" value-format="YYYY-MM-DD HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="开工验收单" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.prepId"
            :disabled="false"
            attach-category="wellPreparation"
            attach-source-type="wellPreparation"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WellPreparationsz" lang="ts">
import {
  addWellPreparation,
  delWellPreparation,
  getWellPreparation,
  listWellPreparation,
  updateWellPreparation
} from '@/api/epms/epnj/wellPreparation';
import { WellPreparationForm, WellPreparationQuery, WellPreparationVO } from '@/api/epms/epnj/wellPreparation/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { epnj_handling_type } = toRefs<any>(proxy?.useDict('epnj_handling_type'));

const wellPreparationList = ref<WellPreparationVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const wellPreparationFormRef = ref<ElFormInstance>();

const areaQuery = ref<OperationAreaQuery>({
  operationAreaType: 0
});
const workAreaList = ref<OperationAreaVO[]>([]);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WellPreparationForm = {
  prepId: undefined,
  wellName: undefined,
  workArea: undefined,
  location: undefined,
  startDate: undefined,
  inspectDate: undefined,
  uploadDate: undefined,
  file: undefined,
  handlingType: 2
};
const data = reactive<PageData<WellPreparationForm, WellPreparationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    wellName: undefined,
    workArea: undefined,
    location: undefined,
    startDate: undefined,
    inspectDate: undefined,
    uploadDate: undefined,
    file: undefined,
    handlingType: 2,
    params: {}
  },
  rules: {
    prepId: [{ required: true, message: '准备ID，自动递增不能为空', trigger: 'blur' }],
    wellName: [{ required: true, message: '单井名称不能为空', trigger: 'blur' }],
    workArea: [{ required: true, message: '所属作业区不能为空', trigger: 'change' }],
    location: [{ required: true, message: '井的位置描述不能为空', trigger: 'blur' }],
    startDate: [{ required: true, message: '开工日期不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询井场准备列表 */
const getList = async () => {
  loading.value = true;
  const res = await listWellPreparation(queryParams.value);
  wellPreparationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  wellPreparationFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.kgStartDate = queryParams.value.startDateRange?.[0];
  queryParams.value.kgEndDate = queryParams.value.startDateRange?.[1];
  queryParams.value.inspectStartDate = queryParams.value.inspectDateRange?.[0];
  queryParams.value.inspectEndDate = queryParams.value.inspectDateRange?.[1];
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.workArea = undefined;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WellPreparationVO[]) => {
  ids.value = selection.map((item) => item.prepId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加井场准备';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: WellPreparationVO) => {
  reset();
  const _prepId = row?.prepId || ids.value[0];
  const res = await getWellPreparation(_prepId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改井场准备';
};

/** 提交按钮 */
const submitForm = () => {
  wellPreparationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.prepId) {
        await updateWellPreparation(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWellPreparation(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 跳转预览 */
const handlePreview = async (row?: WellPreparationVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.prepId,
    attachSourceType: 'wellPreparation',
    attachCategory: 'wellPreparation'
  });
};
/** 上传成功回调 */
const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadDate = fielInfo.uploadTime;
  // form.value.balanceReportName = fielInfo.name;
};
/** 删除按钮操作 */
const handleDelete = async (row?: WellPreparationVO) => {
  const _prepIds = row?.prepId || ids.value;
  await proxy?.$modal.confirm('是否确认删除井场准备编号为"' + _prepIds + '"的数据项？').finally(() => (loading.value = false));
  await delWellPreparation(_prepIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epnj/wellPreparation/export',
    {
      ...queryParams.value
    },
    `wellPreparation_${new Date().getTime()}.xlsx`
  );
};

const getWorkAreaList = async () => {
  const res = await listOperationArea(areaQuery.value);
  workAreaList.value = res.rows;
};

const getAreaName = (id: string) => {
  const workArea = workAreaList.value.find((item) => item.operationAreaId === id);
  return workArea?.operationAreaName;
};

onMounted(() => {
  getList();
  getWorkAreaList();
});
</script>
