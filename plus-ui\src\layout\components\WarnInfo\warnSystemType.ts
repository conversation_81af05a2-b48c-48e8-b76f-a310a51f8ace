import router from '@/router';

export interface WarnSystemTypeConfig {
  pathTest: RegExp;
  title: string;
  systemType: number;
  toWarnRecordPage: Function;
}

const jumpRoutePath = (url: string) => {
  // 将path中?后面的转为对象
  const path = url.split('?');
  const pathParams = path[1];
  // 转成对象
  const query = pathParams ? JSON.parse('{"' + decodeURI(pathParams).replace(/&/g, '","').replace(/=/g, '":"') + '"}') : {};
  router.push({
    path: path[0],
    query: query
  });
};

export const routePathWarnConfig: WarnSystemTypeConfig[] = [
  { pathTest: /^\/eptw\//, title: '告警列表', systemType: 1, toWarnRecordPage: () => jumpRoutePath('/eptw/warn/warnRecord?systemType=1') },
  { pathTest: /^\/epiw\//, title: '告警列表', systemType: 2, toWarnRecordPage: () => jumpRoutePath('/epiw/warn/warnRecord?systemType=2') },
  { pathTest: /^\/epnj\//, title: '告警列表', systemType: 3, toWarnRecordPage: () => jumpRoutePath('/epnj/warn/warnRecord?systemType=3') },
  { pathTest: /^\/epfy\//, title: '告警列表', systemType: 4, toWarnRecordPage: () => jumpRoutePath('/epfy/warn/warnRecord?systemType=4') },
  { pathTest: /^\/epwf\//, title: '告警列表', systemType: 5, toWarnRecordPage: () => jumpRoutePath('/epwf/warn/warnRecord?systemType=5') }
];

export const getRoutePathWarnConfig = (path: string): WarnSystemTypeConfig | undefined => {
  return routePathWarnConfig.find((item) => item.pathTest.test(path));
};
