import{d as le,bz as G,h as pe,r as c,aS as Ne,X as ue,k as ze,m as De,c as R,o as g,p as a,y as T,a2 as Le,e as l,t as n,ar as Re,J as _,F as de,q as F,z as K,aA as fe,a8 as ee,Q as Fe,a as je,ai as me,ak as Pe,b as Ke,aH as He,w as C,a7 as qe,M as xe,A as Me,G as Qe,H as J,K as Ye,am as ce,aI as Ge,x as V,ay as Je,ax as Xe,aK as We,aL as Ze,v as el,az as ll}from"./index-D07cMzhp.js";import{E as al}from"./el-upload-Z_KZ1p2a.js";import"./el-progress-BtGF5yys.js";import{d as ge,l as tl,_ as ol,a as sl}from"./index-Bd4DOhoC.js";import{i as nl}from"./index-BA1WtQgJ.js";import{E as il}from"./el-row-CikYE3zA.js";import{_ as rl}from"./index-BWMgqvQ9.js";import{E as ul}from"./el-col-BaG5Rg5z.js";import{E as dl}from"./el-date-picker-HyhB9X9n.js";import"./el-link-Ar98HWTJ.js";import"./el-tree-DW6MoFaI.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";const ml={class:"component-upload-image"},cl={key:0,class:"el-upload__tip"},pl={style:{color:"#f56c6c"}},fl={style:{color:"#f56c6c"}},gl=["src"],vl=le({__name:"index",props:{modelValue:{type:[String,Object,Array],default:()=>[]},limit:G.number.def(5),fileSize:G.number.def(5),fileType:G.array.def(["png","jpg","jpeg"]),isShowTip:{type:Boolean,default:!0},compressSupport:{type:Boolean,default:!1},compressTargetSize:G.number.def(300)},emits:["update:modelValue"],setup(E,{emit:X}){const t=E,{proxy:i}=pe(),z=X,k=c(0),b=c([]),A=c(""),B=c(!1),H=c("/prod-api"+"/resource/oss/upload"),j=c(Ne()),p=c([]),I=ue(()=>t.isShowTip&&(t.fileType||t.fileSize)),$=c(),y=ue(()=>t.fileType.map(o=>`.${o}`).join(","));ze(()=>t.modelValue,async o=>{if(o){let u=[];Array.isArray(o)?u=o:u=(await tl(o)).data,p.value=u.map(d=>{let f;return typeof d=="string"?f={name:d,url:d}:f={name:d.ossId,url:d.url,ossId:d.ossId},f})}else return p.value=[],[]},{deep:!0,immediate:!0});const D=o=>{let u=!1;if(t.fileType.length){let d="";o.name.lastIndexOf(".")>-1&&(d=o.name.slice(o.name.lastIndexOf(".")+1)),u=t.fileType.some(f=>!!(o.type.indexOf(f)>-1||d&&d.indexOf(f)>-1))}else u=o.type.indexOf("image")>-1;if(!u)return i==null||i.$modal.msgError(`文件格式不正确, 请上传${t.fileType.join("/")}图片格式文件!`),!1;if(o.name.includes(","))return i==null||i.$modal.msgError("文件名不正确，不能包含英文逗号!"),!1;if(t.fileSize&&!(o.size/1024/1024<t.fileSize))return i==null||i.$modal.msgError(`上传头像图片大小不能超过 ${t.fileSize} MB!`),!1;if(t.compressSupport&&o.size/1024>t.compressTargetSize)return i==null||i.$modal.loading("正在上传图片，请稍候..."),k.value++,nl.compressAccurately(o,t.compressTargetSize);i==null||i.$modal.loading("正在上传图片，请稍候..."),k.value++},q=()=>{i==null||i.$modal.msgError(`上传文件数量不能超过 ${t.limit} 个!`)},x=(o,u)=>{var d;o.code===200?(b.value.push({name:o.data.fileName,url:o.data.url,ossId:o.data.ossId}),Q()):(k.value--,i==null||i.$modal.closeLoading(),i==null||i.$modal.msgError(o.msg),(d=$.value)==null||d.handleRemove(u),Q())},M=o=>{const u=p.value.map(d=>d.name).indexOf(o.name);if(u>-1&&b.value.length===k.value){const d=p.value[u].ossId;return ge(d),p.value.splice(u,1),z("update:modelValue",Y(p.value)),!1}return!0},Q=()=>{k.value>0&&b.value.length===k.value&&(p.value=p.value.filter(o=>o.url!==void 0).concat(b.value),b.value=[],k.value=0,z("update:modelValue",Y(p.value)),i==null||i.$modal.closeLoading())},m=()=>{i==null||i.$modal.msgError("上传图片失败"),i==null||i.$modal.closeLoading()},U=o=>{A.value=o.url,B.value=!0},Y=(o,u)=>{let d="";u=u||",";for(const f in o)o[f].ossId!==void 0&&o[f].url.indexOf("blob:")!==0&&(d+=o[f].ossId+u);return d!=""?d.substring(0,d.length-1):""};return(o,u)=>{const d=De("plus"),f=Re,O=al,W=fe;return g(),R("div",ml,[a(O,{ref_key:"imageUploadRef",ref:$,multiple:"",action:l(H),"list-type":"picture-card","on-success":x,"before-upload":D,limit:E.limit,accept:l(y),"on-error":m,"on-exceed":q,"before-remove":M,"show-file-list":!0,headers:l(j),"file-list":l(p),"on-preview":U,class:Le({hide:l(p).length>=E.limit})},{default:n(()=>[a(f,{class:"avatar-uploader-icon"},{default:n(()=>[a(d)]),_:1})]),_:1},8,["action","limit","accept","headers","file-list","class"]),l(I)?(g(),R("div",cl,[u[3]||(u[3]=_(" 请上传 ")),E.fileSize?(g(),R(de,{key:0},[u[1]||(u[1]=_(" 大小不超过 ")),F("b",pl,K(E.fileSize)+"MB",1)],64)):T("",!0),E.fileType?(g(),R(de,{key:1},[u[2]||(u[2]=_(" 格式为 ")),F("b",fl,K(E.fileType.join("/")),1)],64)):T("",!0),u[4]||(u[4]=_(" 的文件 "))])):T("",!0),a(W,{modelValue:l(B),"onUpdate:modelValue":u[0]||(u[0]=Z=>ee(B)?B.value=Z:null),title:"预览",width:"800px","append-to-body":""},{default:n(()=>[F("img",{src:l(A),style:{display:"block","max-width":"100%",margin:"0 auto"}},null,8,gl)]),_:1},8,["modelValue"])])}}}),_l=Fe(vl,[["__scopeId","data-v-a8c9ea68"]]),bl={class:"p-2"},yl={class:"mb-[10px]"},wl=["textContent"],hl={class:"dialog-footer"},Cl=le({name:"Oss"}),Dl=le({...Cl,setup(E){const X=je(),{proxy:t}=pe(),i=c([]),z=c(!0),k=c(!1),b=c(!0),A=c(!0),B=c([]),ae=c(!0),H=c(!0),j=c(0),p=c(0),I=c(!0),$=c(["",""]),y=me({visible:!1,title:""}),D=c({prop:"createTime",order:"ascending"}),q=c(),x=c(),M={file:void 0},Q=me({form:{...M},queryParams:{pageNum:1,pageSize:10,fileName:"",originalName:"",fileSuffix:"",createTime:"",service:"",orderByColumn:D.value.prop,isAsc:D.value.order},rules:{file:[{required:!0,message:"文件不能为空",trigger:"blur"}]}}),{queryParams:m,form:U,rules:Y}=Pe(Q),o=async()=>{b.value=!0;const s=await(t==null?void 0:t.getConfigKey("sys.oss.previewListResource"));I.value=(s==null?void 0:s.data)===void 0?!0:s.data==="true";const e=await sl(t==null?void 0:t.addDateRange(m.value,$.value,"CreateTime"));i.value=e.rows,j.value=e.total,b.value=!1,z.value=!0};function u(s){const e=[".png",".jpg",".jpeg"];return(Array.isArray(s)?s:[s]).some(v=>e.includes(v.toLowerCase()))}function d(){y.visible=!1,f()}function f(){var s;U.value={...M},(s=q.value)==null||s.resetFields()}function O(){m.value.pageNum=1,o()}function W(){var s;z.value=!1,$.value=["",""],(s=x.value)==null||s.resetFields(),m.value.orderByColumn=D.value.prop,m.value.isAsc=D.value.order,O()}function Z(s){B.value=s.map(e=>e.ossId),ae.value=s.length!=1,H.value=!s.length}const ve=({column:s})=>{s.order=s.multiOrder},_e=s=>{if(s.sortable==="custom"){switch(s.multiOrder){case"descending":s.multiOrder="ascending";break;case"ascending":s.multiOrder="";break;default:s.multiOrder="descending";break}be(s.property,s.multiOrder)}},be=(s,e)=>{const h=m.value.orderByColumn?m.value.orderByColumn.split(","):[],v=m.value.isAsc?m.value.isAsc.split(","):[],L=h.indexOf(s);L!==-1?e?v[L]=e:(v.splice(L,1),h.splice(L,1)):(h.push(s),v.push(e)),m.value.orderByColumn=h.join(","),m.value.isAsc=v.join(","),o()},ye=()=>{X.push("/system/oss-config/index")},we=()=>{f(),p.value=0,y.visible=!0,y.title="上传文件"},he=()=>{f(),p.value=1,y.visible=!0,y.title="上传图片"},Ce=()=>{y.visible=!1,o()},ke=s=>{t==null||t.$download.oss(s.ossId)},Se=async s=>{const e=s?"启用":"停用";try{await(t==null?void 0:t.$modal.confirm('确认要"'+e+'""预览列表图片"配置吗?')),await(t==null?void 0:t.updateConfigByKey("sys.oss.previewListResource",s)),await o(),t==null||t.$modal.msgSuccess(e+"成功")}catch{return}},te=async s=>{const e=(s==null?void 0:s.ossId)||B.value;await(t==null?void 0:t.$modal.confirm('是否确认删除OSS对象存储编号为"'+e+'"的数据项?')),b.value=!0,await ge(e).finally(()=>b.value=!1),await o(),t==null||t.$modal.msgSuccess("删除成功")};return Ke(()=>{o()}),(s,e)=>{var ie,re;const h=Qe,v=Me,L=dl,w=Ye,oe=xe,se=qe,P=ul,Ve=rl,Te=il,S=Xe,ne=Ze,Ee=Je,Ie=ll,$e=ol,Ue=_l,Oe=fe,N=He("hasPermi"),Ae=el;return g(),R("div",bl,[a(Ge,{"enter-active-class":(ie=l(t))==null?void 0:ie.animate.searchAnimate.enter,"leave-active-class":(re=l(t))==null?void 0:re.animate.searchAnimate.leave},{default:n(()=>[C(F("div",yl,[a(se,{shadow:"hover"},{default:n(()=>[a(oe,{ref_key:"queryFormRef",ref:x,model:l(m),inline:!0},{default:n(()=>[a(v,{label:"文件名",prop:"fileName"},{default:n(()=>[a(h,{modelValue:l(m).fileName,"onUpdate:modelValue":e[0]||(e[0]=r=>l(m).fileName=r),placeholder:"请输入文件名",clearable:"",onKeyup:J(O,["enter"])},null,8,["modelValue"])]),_:1}),a(v,{label:"原名",prop:"originalName"},{default:n(()=>[a(h,{modelValue:l(m).originalName,"onUpdate:modelValue":e[1]||(e[1]=r=>l(m).originalName=r),placeholder:"请输入原名",clearable:"",onKeyup:J(O,["enter"])},null,8,["modelValue"])]),_:1}),a(v,{label:"文件后缀",prop:"fileSuffix"},{default:n(()=>[a(h,{modelValue:l(m).fileSuffix,"onUpdate:modelValue":e[2]||(e[2]=r=>l(m).fileSuffix=r),placeholder:"请输入文件后缀",clearable:"",onKeyup:J(O,["enter"])},null,8,["modelValue"])]),_:1}),a(v,{label:"创建时间",style:{width:"308px"}},{default:n(()=>[a(L,{modelValue:l($),"onUpdate:modelValue":e[3]||(e[3]=r=>ee($)?$.value=r:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]},null,8,["modelValue","default-time"])]),_:1}),a(v,{label:"服务商",prop:"service"},{default:n(()=>[a(h,{modelValue:l(m).service,"onUpdate:modelValue":e[4]||(e[4]=r=>l(m).service=r),placeholder:"请输入服务商",clearable:"",onKeyup:J(O,["enter"])},null,8,["modelValue"])]),_:1}),a(v,null,{default:n(()=>[a(w,{type:"primary",icon:"search",onClick:O},{default:n(()=>e[13]||(e[13]=[_("搜索")])),_:1}),a(w,{icon:"Refresh",onClick:W},{default:n(()=>e[14]||(e[14]=[_("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[ce,l(A)]])]),_:1},8,["enter-active-class","leave-active-class"]),a(se,{shadow:"hover"},{header:n(()=>[a(Te,{gutter:10,class:"mb8"},{default:n(()=>[a(P,{span:1.5},{default:n(()=>[C((g(),V(w,{type:"primary",plain:"",icon:"Upload",onClick:we},{default:n(()=>e[15]||(e[15]=[_("上传文件")])),_:1})),[[N,["system:oss:upload"]]])]),_:1}),a(P,{span:1.5},{default:n(()=>[C((g(),V(w,{type:"primary",plain:"",icon:"Upload",onClick:he},{default:n(()=>e[16]||(e[16]=[_("上传图片")])),_:1})),[[N,["system:oss:upload"]]])]),_:1}),a(P,{span:1.5},{default:n(()=>[C((g(),V(w,{type:"danger",plain:"",icon:"Delete",disabled:l(H),onClick:e[5]||(e[5]=r=>te())},{default:n(()=>e[17]||(e[17]=[_(" 删除 ")])),_:1},8,["disabled"])),[[N,["system:oss:remove"]]])]),_:1}),a(P,{span:1.5},{default:n(()=>[C((g(),V(w,{type:l(I)?"danger":"warning",plain:"",onClick:e[6]||(e[6]=r=>Se(!l(I)))},{default:n(()=>[_("预览开关 : "+K(l(I)?"禁用":"启用"),1)]),_:1},8,["type"])),[[N,["system:oss:edit"]]])]),_:1}),a(P,{span:1.5},{default:n(()=>[C((g(),V(w,{type:"info",plain:"",icon:"Operation",onClick:ye},{default:n(()=>e[18]||(e[18]=[_("配置管理")])),_:1})),[[N,["system:ossConfig:list"]]])]),_:1}),a(Ve,{"show-search":l(A),"onUpdate:showSearch":e[7]||(e[7]=r=>ee(A)?A.value=r:null),onQueryTable:o},null,8,["show-search"])]),_:1})]),default:n(()=>[l(z)?C((g(),V(Ee,{key:0,data:l(i),border:"","header-cell-class-name":ve,onSelectionChange:Z,onHeaderClick:_e},{default:n(()=>[a(S,{type:"selection",width:"55",align:"center"}),T("",!0),a(S,{label:"文件名",align:"center",prop:"fileName"}),a(S,{label:"原名",align:"center",prop:"originalName"}),a(S,{label:"文件后缀",align:"center",prop:"fileSuffix"}),a(S,{label:"文件展示",align:"center",prop:"url"},{default:n(r=>[l(I)&&u(r.row.fileSuffix)?(g(),V(We,{key:0,width:100,height:100,src:r.row.url,"preview-src-list":[r.row.url]},null,8,["src","preview-src-list"])):T("",!0),!u(r.row.fileSuffix)||!l(I)?(g(),R("span",{key:1,textContent:K(r.row.url)},null,8,wl)):T("",!0)]),_:1}),a(S,{label:"创建时间",align:"center",prop:"createTime",width:"180",sortable:"custom"},{default:n(r=>[F("span",null,K(l(t).parseTime(r.row.createTime,"{y}-{m}-{d}")),1)]),_:1}),a(S,{label:"上传人",align:"center",prop:"createByName"}),a(S,{label:"服务商",align:"center",prop:"service",sortable:"custom"}),a(S,{align:"center","class-name":"small-padding fixed-width",label:"操作"},{default:n(r=>[a(ne,{content:"下载",placement:"top"},{default:n(()=>[C(a(w,{link:"",type:"primary",icon:"Download",onClick:Be=>ke(r.row)},null,8,["onClick"]),[[N,["system:oss:download"]]])]),_:2},1024),a(ne,{content:"删除",placement:"top"},{default:n(()=>[C(a(w,{link:"",type:"primary",icon:"Delete",onClick:Be=>te(r.row)},null,8,["onClick"]),[[N,["system:oss:remove"]]])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Ae,l(b)]]):T("",!0),C(a(Ie,{page:l(m).pageNum,"onUpdate:page":e[8]||(e[8]=r=>l(m).pageNum=r),limit:l(m).pageSize,"onUpdate:limit":e[9]||(e[9]=r=>l(m).pageSize=r),total:l(j),onPagination:o},null,8,["page","limit","total"]),[[ce,l(j)>0]])]),_:1}),a(Oe,{modelValue:l(y).visible,"onUpdate:modelValue":e[12]||(e[12]=r=>l(y).visible=r),title:l(y).title,width:"500px","append-to-body":""},{footer:n(()=>[F("div",hl,[a(w,{loading:l(k),type:"primary",onClick:Ce},{default:n(()=>e[19]||(e[19]=[_("确 定")])),_:1},8,["loading"]),a(w,{onClick:d},{default:n(()=>e[20]||(e[20]=[_("取 消")])),_:1})])]),default:n(()=>[a(oe,{ref_key:"ossFormRef",ref:q,model:l(U),rules:l(Y),"label-width":"80px"},{default:n(()=>[a(v,{label:"文件名"},{default:n(()=>[l(p)===0?(g(),V($e,{key:0,modelValue:l(U).file,"onUpdate:modelValue":e[10]||(e[10]=r=>l(U).file=r)},null,8,["modelValue"])):T("",!0),l(p)===1?(g(),V(Ue,{key:1,modelValue:l(U).file,"onUpdate:modelValue":e[11]||(e[11]=r=>l(U).file=r)},null,8,["modelValue"])):T("",!0)]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}});export{Dl as default};
