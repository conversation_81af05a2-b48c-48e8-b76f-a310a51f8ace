import{d as j,h as H,ai as T,r as p,b as G,c as B,o as w,p as n,t as u,w as I,q as ee,a7 as te,M as ae,A as oe,B as ne,F as se,C as le,x as V,D as re,K as ie,J as L,am as ue,e as Y,aI as ce,ay as me,ax as de,v as pe,cE as ge,Q as _e}from"./index-D07cMzhp.js";import{E as fe}from"./el-date-picker-HyhB9X9n.js";import{j as he}from"./index-D1htDV9m.js";import{l as ye}from"./index-BhIIZXqy.js";import{u as S,X as ve}from"./xlsx-BexUIDLF.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";const be={class:"p-2"},Ae={class:"mb-[10px]"},we=j({name:"MonthlyStatisticsJiZhong"}),Se=j({...we,setup(Ee){const{proxy:g}=H(),{epnj_handling_type:xe,epfy_medium_category:De,epnj_transport_hunhe_medium_type:Le}=T((g==null?void 0:g.useDict("epnj_handling_type","epfy_medium_category","epnj_transport_hunhe_medium_type"))||{}),y=p([]),E=p(!0),q=p(!0),k=p(0),P=p(null),C=p([]),A=p([]),x=p(new Date().getFullYear().toString()),N=p(),v=T({pageNum:1,pageSize:10,disposeType:1,disposeId:void 0,slurryDisposeAmount:void 0,mudPullingAmount:void 0,mudStagingAmount:void 0,waterPullingAmount:void 0,waterStagingAmount:void 0,loss:void 0,params:{date:void 0}}),D=new Map,K=async()=>{const t={operationAreaType:2};try{const e=await ye(t);C.value=e.rows||[],A.value=C.value.map(s=>({id:s.operationAreaId,label:s.operationAreaName||`未知集中站(${s.operationAreaId})`}))}catch(e){console.error("获取集中站列表失败:",e),A.value=[]}},z=t=>{try{const e=t.match(/当前损耗[:：]\s*([+-]?\d+(\.\d+)?)/),s=t.match(/损耗最大值[:：]\s*(\d+(\.\d+)?)/);if(e&&s){const o=parseFloat(e[1]),h=parseFloat(s[1]);return o>h}}catch(e){console.error("解析损耗数值失败:",e)}return!1},$=({row:t,columnIndex:e})=>t.isLoss&&e===2&&z(t.slurryDisposeAmount)?"text-red":"",Q=t=>{const e=[];return D.clear(),t.forEach(s=>{var _;const{stationName:o,monthlyData:h,lossStr:r}=s,c=((_=A.value.find(l=>l.id===o))==null?void 0:_.label)||o;Array.from({length:12},(l,a)=>(a+1).toString()).forEach(l=>{const a=h[l]||{mudStagingAmount:"0",waterStagingAmount:"0",waterPullingAmount:"0",mudPullingAmount:"0",slurryDisposeAmount:"0",slurryStagingAmount:"0"};e.push({stationName:c,stationKey:o,month:`${l}月份`,slurryDisposeAmount:a.slurryDisposeAmount,slurryStagingAmount:a.slurryStagingAmount,mudPullingAmount:a.mudPullingAmount,mudStagingAmount:a.mudStagingAmount,waterPullingAmount:a.waterPullingAmount,waterStagingAmount:a.waterStagingAmount,isLoss:!1})}),e.push({stationName:c,stationKey:o,month:"损耗≤10%",slurryDisposeAmount:r,slurryStagingAmount:"",mudPullingAmount:"",mudStagingAmount:"",waterPullingAmount:"",waterStagingAmount:"",isLoss:!0}),D.set(o,13)}),e},J=({row:t,rowIndex:e,columnIndex:s})=>s===0?e===0||y.value[e-1].stationKey!==t.stationKey?{rowspan:D.get(t.stationKey)||1,colspan:1}:{rowspan:0,colspan:0}:t.isLoss&&s>=2?{rowspan:1,colspan:6}:{rowspan:1,colspan:1},O=()=>{var t;try{const e=(t=P.value)==null?void 0:t.$el;if(!e)return;let s=e.querySelector(".el-table__fixed");s||(s=e.querySelector(".el-table__body-wrapper table"));const o=S.table_to_book(s,{raw:!0}),h=o.SheetNames[0],r=o.Sheets[h],c=S.decode_range(r["!ref"]||"A1:H1"),b=[];for(let a=c.s.c;a<=c.e.c;a++){let i=0;for(let d=c.s.r;d<=c.e.r;d++){const f=S.encode_cell({r:d,c:a}),m=r[f];if(m&&m.v){const W=String(m.v).split("").reduce((X,Z)=>X+(Z.charCodeAt(0)>255?2:1),0);i=Math.max(i,W)}}b.push({wch:Math.min(i+2,60)})}r["!cols"]=b;const _={alignment:{horizontal:"center",vertical:"center",wrapText:!0},border:{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},font:{sz:11,name:"宋体",color:{rgb:"000000"}}};Object.keys(r).forEach(a=>{if(!a.startsWith("!")){const i=r[a];i.s={..._},typeof i.v=="number"&&(i.z="0.00")}}),r["!merges"]&&r["!merges"].forEach(a=>{for(let i=a.s.r;i<=a.e.r;i++)for(let d=a.s.c;d<=a.e.c;d++){const f=S.encode_cell({r:i,c:d});r[f]||(r[f]={t:"s",v:""}),r[f].s={..._}}});const l=ve.write(o,{bookType:"xlsx",type:"binary",cellStyles:!0});ge.saveAs(new Blob([R(l)],{type:"application/octet-stream"}),`集中站月度统计_${new Date().getTime()}.xlsx`)}catch(e){console.error("导出失败:",e),g==null||g.$message.error("导出失败，请重试")}},R=t=>{const e=new ArrayBuffer(t.length),s=new Uint8Array(e);for(let o=0;o<t.length;o++)s[o]=t.charCodeAt(o)&255;return e},F=async()=>{E.value=!0;try{const t=await he(x.value,v);y.value=Q(t||[]),k.value=y.value.length}catch(t){console.error("获取数据失败:",t),y.value=[],k.value=0}finally{E.value=!1}},M=()=>{v.pageNum=1,F()},U=()=>{var t;(t=N.value)==null||t.resetFields(),M()};return G(()=>{K().then(()=>{F()})}),(t,e)=>{var d,f;const s=fe,o=oe,h=re,r=ne,c=ie,b=ae,_=te,l=de,a=me,i=pe;return w(),B("div",be,[n(ce,{"enter-active-class":(d=Y(g))==null?void 0:d.animate.searchAnimate.enter,"leave-active-class":(f=Y(g))==null?void 0:f.animate.searchAnimate.leave},{default:u(()=>[I(ee("div",Ae,[n(_,{shadow:"hover"},{default:u(()=>[n(b,{ref_key:"queryFormRef",ref:N,model:v,inline:!0},{default:u(()=>[n(o,{label:"年份",prop:"year"},{default:u(()=>[n(s,{modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=m=>x.value=m),format:"YYYY",placeholder:"选择日期",type:"year","value-format":"YYYY"},null,8,["modelValue"])]),_:1}),n(o,{"label-width":"90",label:"集中站名称",prop:"disposeId"},{default:u(()=>[n(r,{modelValue:v.disposeId,"onUpdate:modelValue":e[1]||(e[1]=m=>v.disposeId=m),clearable:"",filterable:"",placeholder:"请选择集中站",style:{width:"240px"}},{default:u(()=>[(w(!0),B(se,null,le(A.value,m=>(w(),V(h,{key:m.id,label:m.label,value:m.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(o,null,{default:u(()=>[n(c,{type:"primary",icon:"Search",onClick:M},{default:u(()=>e[2]||(e[2]=[L("搜索")])),_:1}),n(c,{icon:"Refresh",onClick:U},{default:u(()=>e[3]||(e[3]=[L("重置")])),_:1}),n(c,{type:"warning",plain:"",icon:"Download",onClick:O},{default:u(()=>e[4]||(e[4]=[L("导出")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[ue,q.value]])]),_:1},8,["enter-active-class","leave-active-class"]),n(_,{shadow:"never"},{default:u(()=>[I((w(),V(a,{ref_key:"reportTable",ref:P,data:y.value,"cell-class-name":$,"span-method":J,border:"",stripe:""},{default:u(()=>[n(l,{align:"center",label:"集中站处理",prop:"stationName",width:"150"}),n(l,{align:"center",label:"时间",prop:"month",width:"100"}),n(l,{label:"泥浆处理量（方）",prop:"slurryDisposeAmount",align:"center"}),n(l,{label:"泥浆暂存量（方）",prop:"slurryStagingAmount",align:"center"}),n(l,{label:"泥饼拉运量（方）",prop:"mudPullingAmount",align:"center"}),n(l,{label:"泥饼暂存量（方）",prop:"mudStagingAmount",align:"center"}),n(l,{label:"滤水拉运量（方）",prop:"waterPullingAmount",align:"center"}),n(l,{label:"滤水暂存量（方）",prop:"waterStagingAmount",align:"center"})]),_:1},8,["data"])),[[i,E.value]])]),_:1})])}}}),Ie=_e(Se,[["__scopeId","data-v-47db521a"]]);export{Ie as default};
