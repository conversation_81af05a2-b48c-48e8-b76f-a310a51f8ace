import request from '@/utils/request.js';

// 查询warnRecord列表
export function listWarnRecord(query) {
  return request({
    url: '/warnRecord/pageList',
    method: 'get',
    params: query
  });
}

// 查询warnRecord详细
export function getWarnRecord(warnRecordId) {
  return request({
    url: '/warnRecord/' + warnRecordId,
    method: 'get'
  });
}

// 新增warnRecord
export function addWarnRecord(data) {
  return request({
    url: '/warnRecord',
    method: 'post',
    data: data
  });
}

// 修改warnRecord
export function updateWarnRecord(data) {
  return request({
    url: '/warnRecord',
    method: 'put',
    data: data
  });
}

// 删除warnRecord
export function delWarnRecord(warnRecordId) {
  return request({
    url: '/warnRecord/' + warnRecordId,
    method: 'delete'
  });
}

// 查询warnRecord
export function allListWarnRecord(query) {
  return request({
    url: '/warnRecord/allList',
    method: 'get',
    params: query
  });
}

// 查询province列表
export function allListProvince(query) {
  return request({
    url: '/province/allList',
    method: 'get',
    params: query
  });
}
// 查询city列表
export function allListCity(query) {
  return request({
    url: '/city/allList',
    method: 'get',
    params: query
  });
}
// R
export function cityListByProvinceid(provinceid) {
  return request({
    url: '/subnet_project/city/' + provinceid,
    method: 'get'
  });
}
// 查询town列表
export function allListTown(query) {
  return request({
    url: '/town/allList',
    method: 'get',
    params: query
  });
}
// 根据cityid查询town列表
export function townListByCityid(cityid) {
  return request({
    url: '/subnet_project/town/' + cityid,
    method: 'get'
  });
}
