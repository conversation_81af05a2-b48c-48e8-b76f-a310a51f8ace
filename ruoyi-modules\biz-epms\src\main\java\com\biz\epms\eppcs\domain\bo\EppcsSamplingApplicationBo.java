package com.biz.epms.eppcs.domain.bo;

import com.biz.epms.eppcs.domain.EppcsSamplingApplication;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 取样检测申请业务对象 eppcs_sampling_application
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = EppcsSamplingApplication.class, reverseConvertGenerate = false)
public class EppcsSamplingApplicationBo extends BaseEntity {

    /**
     * 申请id
     */
    private Long applicationId;

    /**
     * 检测项目名称
     */
    private String projectName;

    /**
     * 取样点位
     */
    private String samplingPoint;

    /**
     * 检测标准
     */
    private Integer detectionStandard;

    /**
     * 申请单位ID
     */
    private Long applicationUnitId;

    /**
     * 属地单位ID
     */
    private Long localUnitId;



    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 申请状态 (0草稿,1驳回,2待属地组室审批,...)
     */
    private Integer applicationStatus;

    /**
     * 当前阶段 (0取样申请,1取样填报,...)
     */
    private Integer currentPhase;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件IDs
     */
    private String file;

    /**
     * 审批时间
     */
    private Date approvalTime;
    /**
     * 审批人
     */
    private String approver;

    /**
     * 审批建议
     */
    private String approvalRemark;

    /**
     * 申请ID列表（逗号分隔）
     */
    private String applicationIds;

}
