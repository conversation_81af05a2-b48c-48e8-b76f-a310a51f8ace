<template>
  <div class="p-2">
    <transition :enter-active-class="animate.searchAnimate.enter" :leave-active-class="animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card>
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="预警名称" prop="warnName">
              <el-input v-model="queryParams.warnName" clearable placeholder="请输入预警名称" @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="日期" prop="occurTime">
              <el-date-picker
                v-model="queryParams.occurTimeStart"
                :append-to-body="false"
                clearable
                format="YYYY-MM-DD"
                type="date"
                value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="—" prop="occurRecover" label-width="10px">
              <el-date-picker
                v-model="queryParams.occurTimeEnd"
                :append-to-body="false"
                clearable
                format="YYYY-MM-DD"
                type="date"
                value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="预警状态" prop="warnStatus">
              <el-select v-model="queryParams.warnStatus" clearable placeholder="请选择预警状态">
                <el-option v-for="dict in dictData.warn_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="预警级别" prop="warnLevel">
              <el-select v-model="queryParams.warnLevel" clearable placeholder="请选择预警级别">
                <el-option v-for="dict in dictData.warn_level" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item label="所属模块" prop="systemType">
              <el-select v-model="queryParams.systemType" :disabled="notSelectSystemType" clearable placeholder="请选择所属模块">
                <el-option
                  v-for="dict in dictData.warn_system_type"
                  :key="dict.value"
                  :disabled="notSelectSystemType"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card>
      <!-- <template #header>
         <el-row :gutter="10" class="mb8">
           <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
         </el-row>
       </template>-->
      <el-table stripe v-loading="loading" :data="warnRecordList" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column align="center" header-align="center" label="预警名称" prop="warnName" width="450" />
        <!--<el-table-column label="设备名称" align="center" prop="equipmentName" />-->
        <el-table-column label="预警发生时间" align="center" prop="occurTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.occurTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="预警恢复时间" prop="occurRecover" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.occurRecover, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="预警级别" align="center" prop="warnLevel">
          <template #default="scope">
            <dict-tag :options="dictData.warn_level" :value="scope.row.warnLevel" />
          </template>
        </el-table-column>

        <el-table-column label="所属模块" align="center" prop="systemType" width="180">
          <template #default="scope">
            <dict-tag :options="dictData.warn_system_type" :value="scope.row.systemType" />
          </template>
        </el-table-column>

        <el-table-column label="预警状态" align="center" prop="warnStatus" width="180">
          <template #default="scope">
            <dict-tag :options="dictData.warn_status" :value="scope.row.warnStatus" />
          </template>
        </el-table-column>

        <!--<el-table-column label="处理人" align="center" prop="processedBy" />-->
        <!--<el-table-column label="处理时间" align="center" prop="processedTime"  />-->
        <!--<el-table-column label="处理措施" align="center" prop="processedNote" />-->

        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="180px">
          <template #default="scope">
            <el-button
              v-if="scope.row.warnStatus !== 5"
              v-has-Permi="['dbedit:warnRecord:edit']"
              icon="Refresh"
              type="text"
              @click="handleAction(scope.row)"
              >处理
            </el-button>
            <!--<el-button
                type="text"
                icon="Delete"
                v-has-Permi="['dbedit:warnRecord:edit']"
                @click="handleDelete(scope.row)"
            >删除
            </el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>

    <!-- 处理-->
    <el-dialog v-model="openAction" :title="actionTitle" append-to-body width="600px">
      <el-form ref="submitActionForm" :model="submitForm" :rules="actionFormRules">
        <!--<el-row>
          <el-col :span="24">
            <el-form-item label="处理建议" label-width="200px" class="custom-label">
              <el-input v-model="submitForm.advice" placeholder="" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>-->

        <el-row>
          <el-col :span="24">
            <el-form-item class="custom-label" label="处理时间" label-width="100px">
              <el-date-picker
                v-model="submitForm.actionTime"
                :picker-options="pickerOptions"
                placeholder="处理时间"
                style="width: 460px"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <!--<el-row>
          <el-col :span="24">
            <el-form-item label="接收时间" label-width="100px" class="custom-label">
              <el-date-picker v-model="submitForm.processedReceive" style="width: 460px" type="datetime" disabled
                              value-format="yyyy-MM-dd HH:mm:ss" placeholder="处理时间" :picker-options="pickerOptions">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>-->
        <!--<el-row>
          <el-col :span="24">
            <el-form-item label="处理人" label-width="200px" class="custom-label">
              <el-input v-model="submitForm.actionPerson" type="text" placeholder="" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>-->
        <el-row>
          <el-col :span="24">
            <el-form-item class="custom-label" label="处理措施" label-width="100px">
              <el-input v-model="submitForm.action" type="textarea" placeholder=""></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button v-has-permi="['dbedit:detail:edit']" :loading="loading" type="primary" @click="updateWarnDetail(submitForm)">处理 </el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { delWarnRecord, listWarnRecord, updateWarnRecord } from '@/api/comm/warn/warnRecord/warnRecord.js';
import { allListBujianleixing } from '@/api/comm/warn/warnRecord/warnsource';
import overLimit from '@/views/comm/warn/warnrecord/overLimit';
import fluctuate from '@/views/comm/warn/warnrecord/fluctuate';
import { updateWarnDetail } from '@/api/comm/warn/warnRecord/warnDetail.js';
import * as proxy from '@/utils/dict.ts';
import dayjs from 'dayjs';

export default {
  name: 'WarnRecord',
  //   {type: "biz_startDate", data: "起始时间边界"} ],
  components: {
    overLimit,
    fluctuate
  },
  data() {
    let that = this;
    return {
      notSelectSystemType: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      actionTitle: '',
      openAction: false,
      row: [],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      bujianleixingList: [],
      dictInfo: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // warnRecord表格数据
      warnRecordList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      //当前登录用户
      user: '',
      dictData: undefined,
      //处理过程填写表单
      submitForm: {
        advice: '',
        actionTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        processedReceive: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        action: '',
        actionPerson: ''
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        equipmentId: null,
        project_category: null,
        project_class: null,
        assetId: null,
        partsType: null,
        realWarnId: null,
        calculationType: null,
        project_subclass: null,
        warnName: null,
        projectName: null,
        warnSetId: null,
        warnLevel: null,
        //发生时间搜索
        occurTimeStart: undefined,
        occurTimeEnd: dayjs().format('YYYY-MM-DD'),
        warnType: null,
        stationId: null,
        occurTime: null,
        occurRecover: null,
        occurRecover_cen: null,
        warnStatus: '1',
        stationName: null,
        equipmentName: null,
        warnSource: null,
        equipmentCode: null,
        sendState: null,
        voltage: null,
        circuit: null,
        recoverWay: null,
        projectCategory: null
      },
      buJianCanShu: null,
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 表单校验
      actionFormRules: {
        warnRecordId: [{ max: 38, message: '预警ID长度不能超过38', trigger: 'blur' }]
      }
    };
  },
  created() {
    this.queryParams.equipmentId = this.$route.query.equipmentId || null;
    this.queryParams.calculationType = this.$route.query.calculationType || null;
    this.queryParams.warnType = this.$route.query.warnType || null;
    this.queryParams.realWarnId = this.$route.query.realWarnId || null;
    this.queryParams.systemType = parseInt(this.$route.query.systemType) || null;
    this.buJianCanShu = this.$route.query.buJianCanShu || null;
    this.getList();
    this.getBujianleixingList();
    this.loadDicts();
  },

  watch: {
    $route: {
      handler: function (route) {
        if (route.query.systemType) {
          this.queryParams.systemType = parseInt(route.query.systemType);
          this.notSelectSystemType = true;
          this.getList();
        }
      },
      immediate: true
    }
  },
  methods: {
    loadDicts() {
      this.dictData = proxy?.useDict(
        'warn_system_type',
        'warn_level',
        'warn_system_type',
        'project_category',
        'jienengChild',
        'warn_status',
        'warn_level',
        'warn_source',
        'recoverWay',
        'sendState'
      );
    },
    /** 查询warnRecord列表 */
    getList() {
      this.loading = true;
      // 将 this.queryParams.occurTimeEnd 增加一天

      const params = { ...this.queryParams };
      if (params.occurTimeEnd) {
        params.occurTimeEnd = dayjs(this.queryParams.occurTimeEnd).add(1, 'days').format('YYYY-MM-DD');
      }

      listWarnRecord(params).then((response) => {
        this.warnRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 提交warnDetail，处理过程,设置告警为已接收 */
    updateWarnDetail(submitForm) {
      this.loading = true;
      updateWarnDetail({
        warnRecordId: this.row.warnRecordId,
        processedNote: submitForm.action,
        processedBy: submitForm.actionPerson,
        processedTime: submitForm.actionTime,
        processedReceive: submitForm.processedReceive
      }).then((response) => {
        this.row.warnStatus = 5;
        this.row.warnCount = 0;
        updateWarnRecord(this.row).then((response) => {
          if (response.code === 200) {
            this.$modal.msgSuccess('处理成功');
          }
          this.getList();
        });
        this.loading = false;
        this.openAction = false;
      });
    },

    // 取消按钮
    cancel() {
      this.openAction = false;
      this.resetActionForm();
    },
    /** 查询bujianleixing列表 */
    getBujianleixingList() {
      allListBujianleixing(this.queryParams).then((response) => {
        this.bujianleixingList = response.data;
      });
    },
    // 重置处理表单
    resetActionForm() {
      this.submitForm = {
        actionTime: new Date(),
        action: '',
        processedReceive: new Date(),
        actionPerson: this.submitForm.actionPerson,
        advice: this.submitForm.advice
      };
      this.resetForm('submitActionForm');
    },
    // 表单重置
    reset() {
      this.form = {
        warnRecordId: null,
        partsType: null,
        equipmentId: null,
        assetId: null,
        realWarnId: null,
        warnName: null,
        warnSetId: null,
        warnLevel: null,
        warnType: null,
        stationId: null,
        occurTime: null,
        occurRecover: null,
        warnStatus: 0,
        stationName: null,
        equipmentName: null,
        warnSource: null,
        equipmentCode: null,
        sendState: null,
        voltage: null,
        circuit: null,
        recoverWay: null
      };
      this.resetForm('form');
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.warnRecordId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleAction(row) {
      this.row = row;
      this.openAction = true;
      this.title = '预警处理';
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const warnRecordIds = row.warnRecordId || this.ids;
      this.$modal
        .confirm('是否确认删除warnRecord编号为"' + warnRecordIds + '"的数据项？')
        .then(function () {
          return delWarnRecord(warnRecordIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'dbedit/warnRecord/export',
        {
          ...this.queryParams
        },
        `warnRecord_${new Date().getTime()}.xlsx`
      );
    },

    /**
     * 取消告警
     */
    handleCancel(row) {
      row.warnStatus = 5;
      updateWarnRecord(row).then((res) => {
        if (res.code === 200) {
          this.$modal.msgSuccess('操作成功');
        }
        this.getList();
      });
    }
  }
};
</script>
