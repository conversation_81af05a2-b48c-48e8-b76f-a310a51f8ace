<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="所属地" prop="operationAreaId">
              <el-select v-model="queryParams.operationAreaId" placeholder="请选择所属地" clearable @keyup.enter="handleQuery">
                <el-option
                  v-for="dict in operationAreaList"
                  :key="dict.operationAreaId"
                  :label="dict.operationAreaName"
                  :value="dict.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="监测日期" prop="detectionTime">
              <el-date-picker
                clearable
                v-model="queryParams.params"
                type="daterange"
                value-format="YYYY-MM-DD"
                start-placeholder="起始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['epms:dayQuality:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:dayQuality:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:dayQuality:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['epms:dayQuality:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="dayQualityList" stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <!--        <el-table-column label="行政区域" align="center" prop="administrativeArea">-->
        <!--          <template #default="scope">-->
        <!--            {{ getAdministrativeAreaText(scope.row.administrativeArea) }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column align="center" label="所属地" prop="operationAreaName">
          <template #default="scope">
            <span>{{ getOperationAreaName(scope.row.operationAreaId) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="监测日期" align="center" prop="detectionTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.detectionTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上传时间" align="center" prop="uploadTime" width="">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
          <template #default="scope">
            <el-tooltip content="日度水质监测" placement="top">
              <el-button v-hasPermi="['epms:dayQuality:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >监测预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button link type="primary" icon="Postcard" @click="handleDetail(scope.row)" v-hasPermi="['epms:dayQuality:detail']">详情</el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['epms:dayQuality:edit']">修改</el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['epms:dayQuality:remove']">删除</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改日度水质监测对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="dayQualityFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择所属地">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="监测日期" prop="detectionTime">
          <el-date-picker v-model="form.detectionTime" clearable placeholder="请选择监测日期" type="date" value-format="YYYY-MM-DD"> </el-date-picker>
        </el-form-item>
        <el-form-item label="水质监测附件" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.qualityReportId"
            :disabled="false"
            attach-category="dayQuality"
            attach-source-type="operationArea"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="dayQualityFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" filterable placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" filterable placeholder="请选择所属地">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="监测日期" prop="detectionTime">
          <el-date-picker v-model="form.detectionTime" clearable placeholder="请选择监测日期" type="date" value-format="YYYY-MM-DD"> </el-date-picker>
        </el-form-item>
        <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker v-model="form.uploadTime" clearable placeholder="上传时间" type="datetime"> </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DayQuality" lang="ts">
import { addDayQuality, delDayQuality, getDayQuality, listDayQuality, updateDayQuality } from '@/api/epms/epiw/dayQuality';
import { DayQualityForm, DayQualityQuery, DayQualityVO } from '@/api/epms/epiw/dayQuality/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const dayQualityList = ref<DayQualityVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const dayQualityFormRef = ref<ElFormInstance>();

const operationAreaQuery = reactive<OperationAreaQuery>({});
const operationAreaList = ref<OperationAreaVO[]>([]);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DayQualityForm = {
  qualityReportId: undefined,
  detectionTime: undefined,
  file: undefined
};
const data = reactive<PageData<DayQualityForm, DayQualityQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    detectionTime: undefined,
    file: undefined,
    params: {}
  },
  rules: {
    qualityReportId: [{ required: true, message: '监测报告id不能为空', trigger: 'blur' }],
    detectionTime: [{ required: true, message: '检测时间不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询日度水质监测列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDayQuality(queryParams.value);
  dayQualityList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  dayQualityFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DayQualityVO[]) => {
  ids.value = selection.map((item) => item.qualityReportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加日度水质监测';
};

const handlePreview = async (row?: DayQualityVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.qualityReportId,
    attachSourceType: 'operationArea',
    attachCategory: 'dayQuality'
  });
};
const handleDetail = async (row?: DayQualityVO) => {
  reset();
  const _qualityReportId = row?.qualityReportId || ids.value[0];
  const res = await getDayQuality(_qualityReportId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '日度水质监测详情';
};
/** 修改按钮操作 */
const handleUpdate = async (row?: DayQualityVO) => {
  reset();
  const _qualityReportId = row?.qualityReportId || ids.value[0];
  const res = await getDayQuality(_qualityReportId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改日度水质监测';
};

/** 提交按钮 */
const submitForm = () => {
  dayQualityFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.qualityReportId) {
        await updateDayQuality(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addDayQuality(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DayQualityVO) => {
  const _qualityReportIds = row?.qualityReportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除日度水质监测编号为"' + _qualityReportIds + '"的数据项？').finally(() => (loading.value = false));
  await delDayQuality(_qualityReportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/dayQuality/export',
    {
      ...queryParams.value
    },
    `日度水质监测_${new Date().getTime()}.xlsx`
  );
};

const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
  form.value.balanceReportName = fielInfo.name;
};

const getAdministrativeAreaText = (administrativeArea: number) => {
  if (!administrativeArea) {
    return '未知';
  }
  const administrativeAreaItem = eptw_administrative_area.value.find((item) => item.value === administrativeArea.toString());
  return administrativeAreaItem ? administrativeAreaItem.label : '未知';
};

const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationAreaItem = operationAreaList.value.find((item) => item.operationAreaId === operationAreaId);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};

/**
 * 获取所属地列表
 */
const getAreaList = () => {
  operationAreaQuery.operationAreaParentId = 0;
  listOperationArea(operationAreaQuery).then((res) => {
    operationAreaList.value = res.rows;
  });
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
  getAreaList();
});
</script>
