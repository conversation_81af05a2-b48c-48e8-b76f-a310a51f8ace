<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :inline="true" :model="queryParams" label-width="100px">
            <el-form-item label="检测日期" prop="detectionDateRange">
              <el-date-picker
                v-model="queryParams.detectionDateRange"
                clearable
                end-placeholder="结束日期"
                range-separator="至"
                start-placeholder="开始日期"
                type="daterange"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChange"
              />
            </el-form-item>
            <el-form-item label="检测项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" clearable placeholder="请输入检测项目名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="样品编号" prop="sampleNumber">
              <el-input v-model="queryParams.sampleNumber" clearable placeholder="请输入样品编号" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="检测状态" prop="detectionStatus">
              <el-select v-model="queryParams.detectionStatus" clearable placeholder="请选择检测状态">
                <el-option v-for="dict in eppcs_detection_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:detectionResult:add']" icon="Plus" plain type="primary" @click="handleAdd">填报检测结果</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:detectionResult:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:detectionResult:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['eppcs:detectionResult:export']" icon="Download" plain type="warning" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table v-loading="loading" :data="detectionResultList" border stripe @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column
          :index="(index) => (queryParams.pageNum - 1) * queryParams.pageSize + index + 1"
          align="center"
          label="序号"
          type="index"
          width="55"
        />
        <el-table-column align="center" label="样品编号" prop="sampleNumber" show-overflow-tooltip />
        <el-table-column align="center" label="检测项目名称" prop="projectName" show-overflow-tooltip />
        <el-table-column align="center" label="检测结果" prop="resultStatus" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.resultStatus === 0" type="success">达标</el-tag>
            <el-tag v-else-if="scope.row.resultStatus === 1" type="danger">不达标</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="报送时间" prop="submitTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.submitTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="审批时间" prop="approvalTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.approvalTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="审批意见" prop="approvalRemark" show-overflow-tooltip />
        <el-table-column align="center" label="检测状态" prop="detectionStatus" width="120">
          <template #default="scope">
            <dict-tag :options="eppcs_detection_status" :value="scope.row.detectionStatus" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="预警标记" prop="warningFlag" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.warningFlag === 1" size="small" type="warning">预警</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="240">
          <template #default="scope">
            <el-button icon="View" link type="primary" @click="handleView(scope.row)">查看</el-button>
            <el-tooltip v-if="[0, 1].includes(scope.row.detectionStatus)" content="修改" placement="top">
              <el-button v-hasPermi="['eppcs:detectionResult:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)">
                修改
              </el-button>
            </el-tooltip>
            <el-tooltip v-if="[0, 1].includes(scope.row.detectionStatus)" content="提交" placement="top">
              <el-button v-hasPermi="['eppcs:detectionResult:submit']" icon="Upload" link type="success" @click="handleSubmit(scope.row)">
                提交
              </el-button>
            </el-tooltip>

            <el-tooltip v-if="canLocalConfirm(scope.row)" content="属地确认" placement="top">
              <el-button v-hasPermi="['eppcs:detectionResult:localConfirm']" icon="Check" link type="warning" @click="handleLocalConfirm(scope.row)">
                属地确认
              </el-button>
            </el-tooltip>

            <el-tooltip v-if="canCenterConfirm(scope.row)" content="管理中心确认" placement="top">
              <el-button
                v-hasPermi="['eppcs:detectionResult:centerConfirm']"
                icon="CircleCheck"
                link
                type="info"
                @click="handleCenterConfirm(scope.row)"
              >
                管理中心确认
              </el-button>
            </el-tooltip>

            <el-tooltip v-if="[0, 1].includes(scope.row.detectionStatus)" content="删除" placement="top">
              <el-button v-hasPermi="['eppcs:detectionResult:remove']" icon="Delete" link type="danger" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改检测结果对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" append-to-body width="900px">
      <el-form ref="detectionResultFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="取样申请" prop="applicationId">
              <el-select
                v-model="form.applicationId"
                :disabled="isViewMode"
                placeholder="请选择取样申请"
                style="width: 100%"
                @change="handleApplicationChange"
              >
                <el-option
                  v-for="app in applicationOptions"
                  :key="app.applicationId"
                  :label="`${app.projectName} - ${app.samplingPoint}`"
                  :value="app.applicationId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品编号" prop="sampleNumber">
              <el-input v-model="form.sampleNumber" :disabled="isViewMode" placeholder="请输入样品编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="检测日期" prop="detectionDate">
              <el-date-picker
                v-model="form.detectionDate"
                :disabled="isViewMode"
                placeholder="请选择检测日期"
                style="width: 100%"
                type="date"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测标准" prop="detectionStandard">
              <el-select v-model="form.detectionStandard" :disabled="isViewMode" placeholder="请选择检测标准" style="width: 100%">
                <el-option v-for="dict in eppcs_detection_standard" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 检测结果数据表单 -->
        <el-card v-if="!isViewMode" shadow="never" style="margin-bottom: 20px">
          <template #header>
            <span style="font-weight: bold">检测结果数据</span>
          </template>
          <el-row :gutter="20">
            <el-col v-for="(item, key) in detectionItems" :key="key" :span="8">
              <el-form-item :label="item.label" :prop="`detectionData.${key}`">
                <el-input-number
                  v-model="form.detectionData[key]"
                  :min="0"
                  :placeholder="`请输入${item.label}`"
                  :precision="item.precision || 2"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-form-item label="检测报告附件" prop="reportFile">
          <attachFileUpload
            v-model="form.reportFile"
            :attach-source-id="form.resultId"
            :disabled="isViewMode"
            :file-size="50"
            :limit="10"
            attach-category="report"
            attach-source-type="detectionResult"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" :disabled="isViewMode" :rows="3" placeholder="请输入备注信息" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!isViewMode" :loading="buttonLoading" type="primary" @click="submitForm('save')">保存</el-button>
          <el-button
            v-if="!isViewMode && (form.detectionStatus === 0 || form.detectionStatus === 1)"
            :loading="buttonLoading"
            type="success"
            @click="submitForm('submit')"
            >提交</el-button
          >
          <el-button @click="cancel">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 属地确认对话框 -->
    <el-dialog v-model="localConfirmDialog.visible" append-to-body title="属地确认检测结果" width="700px">
      <el-form ref="localConfirmFormRef" :model="form" :rules="localConfirmRules" label-width="120px">
        <el-card shadow="never" style="margin-bottom: 20px">
          <template #header>
            <span style="font-weight: bold">检测结果信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="样品编号">
                <span>{{ form.sampleNumber }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测项目名称">
                <span>{{ form.projectName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="检测日期">
                <span>{{ parseTime(form.detectionDate, '{y}-{m}-{d}') }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="当前状态">
                <dict-tag :options="eppcs_detection_status" :value="form.detectionStatus" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="检测报告附件" prop="reportFile">
            <attachFileUpload
              v-model="form.reportFile"
              :attach-source-id="form.resultId"
              :disabled="true"
              attach-category="report"
              attach-source-type="detectionResult"
            />
          </el-form-item>
        </el-card>

        <el-form-item label="审批意见" prop="approvalRemark">
          <el-input v-model="form.approvalRemark" :rows="4" placeholder="请输入审批意见（必填）" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="localConfirmLoading" type="success" @click="handleLocalConfirmSubmit(1)">达标确认</el-button>
          <el-button :loading="localConfirmLoading" type="danger" @click="handleLocalConfirmSubmit(2)">不达标报告</el-button>
          <el-button @click="localConfirmDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 管理中心确认对话框 -->
    <el-dialog v-model="centerConfirmDialog.visible" append-to-body title="管理中心确认检测结果" width="700px">
      <el-form ref="centerConfirmFormRef" :model="form" :rules="centerConfirmRules" label-width="120px">
        <el-card shadow="never" style="margin-bottom: 20px">
          <template #header>
            <span style="font-weight: bold">检测结果信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="样品编号">
                <span>{{ form.sampleNumber }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测项目名称">
                <span>{{ form.projectName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="检测结果">
                <el-tag v-if="form.resultStatus === 0" type="success">达标</el-tag>
                <el-tag v-else-if="form.resultStatus === 1" type="danger">不达标</el-tag>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="当前状态">
                <dict-tag :options="eppcs_detection_status" :value="form.detectionStatus" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item v-if="form.approvalRemark" label="属地审批意见">
            <span>{{ form.approvalRemark }}</span>
          </el-form-item>
        </el-card>

        <el-form-item label="最终审批意见" prop="finalApprovalRemark">
          <el-input v-model="form.finalApprovalRemark" :rows="4" placeholder="请输入最终审批意见（必填）" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="centerConfirmLoading" type="success" @click="handleCenterConfirmSubmit(1)">结束</el-button>
          <el-button :loading="centerConfirmLoading" type="danger" @click="handleCenterConfirmSubmit(2)">驳回</el-button>
          <el-button @click="centerConfirmDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" name="DetectionResult" setup>
import {
  addDetectionResult,
  centerConfirmDetectionResult,
  delDetectionResult,
  getDetectionResult,
  listDetectionResult,
  localConfirmDetectionResult,
  submitDetectionResult,
  updateDetectionResult
} from '@/api/epms/eppcs/detectionResult';
import { DetectionResultForm, DetectionResultQuery, DetectionResultVO } from '@/api/epms/eppcs/detectionResult/types';
import { listSamplingApplication } from '@/api/epms/eppcs/samplingApplication';
import dayjs from 'dayjs';
import { useUserStore } from '@/store/modules/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eppcs_detection_status, eppcs_detection_standard } = toRefs<any>(proxy?.useDict('eppcs_detection_status', 'eppcs_detection_standard'));

const detectionResultList = ref<DetectionResultVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const detectionResultFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 确认相关
const localConfirmDialog = reactive({
  visible: false
});
const centerConfirmDialog = reactive({
  visible: false
});
const localConfirmFormRef = ref<ElFormInstance>();
const centerConfirmFormRef = ref<ElFormInstance>();
const localConfirmLoading = ref(false);
const centerConfirmLoading = ref(false);
const localConfirmRules = reactive({
  approvalRemark: [{ required: true, message: '审批意见不能为空', trigger: 'blur' }]
});
const centerConfirmRules = reactive({
  finalApprovalRemark: [{ required: true, message: '最终审批意见不能为空', trigger: 'blur' }]
});

// 取样申请选项
const applicationOptions = ref<any[]>([]);

// 表单状态
const isNewForm = computed(() => !form.value.resultId);
const isViewMode = ref(false);

// 检测项目定义
const detectionItems = {
  ph: { label: 'pH值(无量纲)', precision: 2 },
  waterTemp: { label: '水温(℃)', precision: 1 },
  permanganateIndex: { label: '高锰酸钾指数(mg/L)', precision: 2 },
  cod: { label: '化学需氧量(mg/L)', precision: 2 },
  bod5: { label: '五日生化需氧量(mg/L)', precision: 2 },
  dissolvedOxygen: { label: '溶解氧(mg/L)', precision: 2 },
  totalPhosphorus: { label: '总磷(mg/L)', precision: 3 },
  totalNitrogen: { label: '总氮(mg/L)', precision: 3 },
  ammoniaNitrogen: { label: '氨氮(mg/L)', precision: 3 },
  fluoride: { label: '氟化物(mg/L)', precision: 3 },
  volatilePhenol: { label: '挥发酚(mg/L)', precision: 4 },
  cyanide: { label: '氰化物(mg/L)', precision: 4 },
  sulfide: { label: '硫化物(mg/L)', precision: 3 },
  petroleum: { label: '石油类(mg/L)', precision: 3 },
  hexavalentChromium: { label: '六价铬(mg/L)', precision: 4 },
  anionicSurfactant: { label: '阴离子表面活性剂(mg/L)', precision: 3 },
  copper: { label: '铜(mg/L)', precision: 4 },
  zinc: { label: '锌(mg/L)', precision: 4 },
  lead: { label: '铅(mg/L)', precision: 4 },
  cadmium: { label: '镉(mg/L)', precision: 5 },
  arsenic: { label: '砷(mg/L)', precision: 5 },
  totalMercury: { label: '总汞(mg/L)', precision: 5 },
  selenium: { label: '硒(mg/L)', precision: 5 },
  fecalColiform: { label: '粪大肠菌群(CFU/L)', precision: 0 }
};

const initFormData: DetectionResultForm = {
  resultId: undefined,
  applicationId: undefined,
  sampleNumber: undefined,
  projectName: undefined,
  detectionStandard: undefined,
  detectionDate: undefined,
  submitTime: undefined,
  detectionStatus: 0, // 默认草稿状态
  reportFile: undefined,
  resultData: undefined,
  resultStatus: undefined,
  approvalTime: undefined,
  approvalRemark: undefined,
  approver: undefined,
  remark: undefined,
  warningFlag: 0,
  detectionData: {},
  finalApprovalRemark: undefined
};

const data = reactive<PageData<DetectionResultForm, DetectionResultQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    sampleNumber: undefined,
    detectionDateRange: undefined,
    detectionDateStart: undefined,
    detectionDateEnd: undefined,
    detectionStatus: undefined,
    resultStatus: undefined,
    applicationIds: undefined,
    approvalTime: undefined,
    approvalRemark: undefined,
    params: {}
  },
  rules: {
    applicationId: [{ required: true, message: '取样申请不能为空', trigger: 'change' }],
    sampleNumber: [{ required: true, message: '样品编号不能为空', trigger: 'blur' }],
    detectionDate: [{ required: true, message: '检测日期不能为空', trigger: 'change' }],
    detectionStandard: [{ required: true, message: '检测标准不能为空', trigger: 'change' }],
    reportFile: [{ required: true, message: '检测报告附件不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 处理日期范围变化 */
const handleDateRangeChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    queryParams.value.detectionDateStart = dates[0];
    queryParams.value.detectionDateEnd = dates[1];
  } else {
    queryParams.value.detectionDateStart = undefined;
    queryParams.value.detectionDateEnd = undefined;
  }
};

/** 查询检测结果列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listDetectionResult(queryParams.value);
    const detectionList = res.rows || [];

    // 收集申请ID并批量查询申请信息
    if (detectionList.length > 0) {
      const applicationIds = [...new Set(detectionList.map((item) => item.applicationId).filter((id) => id))];

      if (applicationIds.length > 0) {
        try {
          // 批量查询取样申请信息
          const applicationRes = await listSamplingApplication({
            applicationIds: applicationIds.join(','),
            pageNum: 1,
            pageSize: applicationIds.length
          });

          const applicationMap = new Map();
          (applicationRes.rows || []).forEach((app) => {
            applicationMap.set(app.applicationId, app);
          });

          // 填充项目名称
          detectionList.forEach((item) => {
            const application = applicationMap.get(item.applicationId);
            if (application) {
              item.projectName = application.projectName;
            }
          });
        } catch (error) {
          console.warn('查询申请信息失败:', error);
        }
      }
    }

    detectionResultList.value = detectionList;
    total.value = res.total;
  } catch (error) {
    console.error('查询列表失败:', error);
    proxy?.$modal.msgError('查询列表失败');
  } finally {
    loading.value = false;
  }
};

/** 取消按钮 */
const cancel = () => {
  reset();
  isViewMode.value = false;
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  form.value.detectionData = {};
  detectionResultFormRef.value?.resetFields();
  getApplicationList();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.detectionDateRange = undefined;
  handleDateRangeChange([]);
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DetectionResultVO[]) => {
  ids.value = selection.map((item) => item.resultId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = async () => {
  reset();
  isViewMode.value = false;

  // 设置默认值
  form.value.detectionDate = dayjs().format('YYYY-MM-DD');
  form.value.detectionStatus = 0; // 草稿状态
  form.value.detectionData = {};

  dialog.visible = true;
  dialog.title = '填报检测结果';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: DetectionResultVO) => {
  reset();
  isViewMode.value = false;
  const _resultId = row?.resultId || ids.value[0];
  const res = await getDetectionResult(_resultId);
  Object.assign(form.value, res.data);

  // 解析检测数据
  if (res.data.resultData) {
    try {
      form.value.detectionData = JSON.parse(res.data.resultData);
    } catch (error) {
      console.warn('解析检测数据失败:', error);
      form.value.detectionData = {};
    }
  }

  dialog.visible = true;
  dialog.title = '修改检测结果';
};

/** 提交按钮 */
const submitForm = (action: 'save' | 'submit' = 'save') => {
  detectionResultFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        // 将检测数据转换为JSON字符串
        form.value.resultData = JSON.stringify(form.value.detectionData || {});

        let resultId = form.value.resultId;

        if (resultId) {
          // 修改
          await updateDetectionResult(form.value);
        } else {
          // 新增
          const res = await addDetectionResult(form.value);
          resultId = res.data; // 直接获取返回的ID
          form.value.resultId = resultId;
        }

        // 如果是提交操作，调用提交接口
        if (action === 'submit' && resultId) {
          await submitDetectionResult(resultId, 1);
        }
        proxy?.$modal.msgSuccess('操作成功');

        dialog.visible = false;
        await getList();
      } catch (error) {
        console.error('操作失败:', error);
        proxy?.$modal.msgError('操作失败');
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DetectionResultVO) => {
  const _resultIds = row?.resultId || ids.value;
  await proxy?.$modal.confirm('是否确认删除检测结果编号为"' + _resultIds + '"的数据项？').finally(() => (loading.value = false));
  await delDetectionResult(_resultIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/eppcs/detectionResult/export',
    {
      ...queryParams.value
    },
    `detectionResult_${new Date().getTime()}.xlsx`
  );
};

/** 判断是否可以属地确认 */
const canLocalConfirm = (row: DetectionResultVO) => {
  // 待属地单位确认状态：2
  return row.detectionStatus === 2;
};

/** 判断是否可以管理中心确认 */
const canCenterConfirm = (row: DetectionResultVO) => {
  // 待煤层气管理中心确认状态：3
  return row.detectionStatus === 3;
};

/** 提交检测结果 */
const handleSubmit = async (row: DetectionResultVO) => {
  try {
    await proxy?.$modal.confirm('确认提交该检测结果吗？提交后将进入确认流程，无法修改。');
    await submitDetectionResult(row.resultId, 1);
    proxy?.$modal.msgSuccess('提交成功');
    await getList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error);
      proxy?.$modal.msgError('提交失败');
    }
  }
};

/** 属地确认检测结果 */
const handleLocalConfirm = (row: DetectionResultVO) => {
  form.value = { ...row };
  localConfirmDialog.visible = true;
};

/** 提交属地确认 */
const handleLocalConfirmSubmit = async (action: number) => {
  if (!localConfirmFormRef.value) return;

  const valid = await localConfirmFormRef.value.validate();
  if (!valid) return;

  try {
    localConfirmLoading.value = true;

    // 获取当前用户信息作为审批人
    const user = useUserStore();
    const approver = user?.nickname || user?.name || '当前用户';

    await localConfirmDetectionResult(
      form.value.resultId,
      1, // 用户类型，这里默认为1
      action,
      form.value.approvalRemark,
      approver
    );

    const actionText = action === 1 ? '达标确认' : '不达标报告';
    proxy?.$modal.msgSuccess(`${actionText}成功`);
    localConfirmDialog.visible = false;
    await getList();
  } catch (error) {
    console.error('属地确认失败:', error);
    proxy?.$modal.msgError('属地确认失败');
  } finally {
    localConfirmLoading.value = false;
  }
};

/** 管理中心确认检测结果 */
const handleCenterConfirm = (row: DetectionResultVO) => {
  form.value = { ...row };
  centerConfirmDialog.visible = true;
};

/** 提交管理中心确认 */
const handleCenterConfirmSubmit = async (action: number) => {
  if (!centerConfirmFormRef.value) return;

  const valid = await centerConfirmFormRef.value.validate();
  if (!valid) return;

  try {
    centerConfirmLoading.value = true;

    // 获取当前用户信息作为审批人
    const user = useUserStore();
    const approver = user?.nickname || user?.name || '当前用户';

    await centerConfirmDetectionResult(
      form.value.resultId,
      1, // 用户类型，这里默认为1
      action,
      form.value.finalApprovalRemark,
      approver
    );

    const actionText = action === 1 ? '结束' : '驳回';
    proxy?.$modal.msgSuccess(`${actionText}成功`);
    centerConfirmDialog.visible = false;
    await getList();
  } catch (error) {
    console.error('管理中心确认失败:', error);
    proxy?.$modal.msgError('管理中心确认失败');
  } finally {
    centerConfirmLoading.value = false;
  }
};

/** 获取取样申请列表 */
const getApplicationList = async () => {
  try {
    // 获取阶段为3（上传检测结果）的申请
    const res = await listSamplingApplication({ currentPhase: 5, pageSize: -1 });
    applicationOptions.value = res.rows || [];
  } catch (error) {
    console.error('获取取样申请列表失败:', error);
  }
};

/** 查看详情 */
const handleView = (row: DetectionResultVO) => {
  getDetectionResult(row.resultId).then((response) => {
    form.value = response.data;

    // 解析检测数据
    if (response.data.resultData) {
      try {
        form.value.detectionData = JSON.parse(response.data.resultData);
      } catch (error) {
        console.warn('解析检测数据失败:', error);
        form.value.detectionData = {};
      }
    }

    isViewMode.value = true;
    dialog.visible = true;
    dialog.title = '查看检测结果详情';
  });
};

/** 处理取样申请选择变化 */
const handleApplicationChange = (applicationId: string | number) => {
  const selectedApp = applicationOptions.value.find((app) => app.applicationId === applicationId);
  if (selectedApp) {
    // 自动填充项目名称等信息
    form.value.projectName = selectedApp.projectName;
  }
};

onMounted(async () => {
  await getApplicationList();
  await getList();
});
</script>
