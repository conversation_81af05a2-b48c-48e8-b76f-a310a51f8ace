import{d as c,h as b,r as m,x as V,o as q,t as a,p as o,A as v,G as y,e as r,K as C,J as i,M as E}from"./index-D07cMzhp.js";import{b as k}from"./index-BdvXA74M.js";const T=c({__name:"resetPwd",setup(x){const{proxy:l}=b(),w=m(),s=m({oldPassword:"",newPassword:"",confirmPassword:""}),f=m({oldPassword:[{required:!0,message:"旧密码不能为空",trigger:"blur"}],newPassword:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],confirmPassword:[{required:!0,message:"确认密码不能为空",trigger:"blur"},{required:!0,validator:(n,e,t)=>{s.value.newPassword!==e?t(new Error("两次输入的密码不一致")):t()},trigger:"blur"}]}),P=()=>{var n;(n=w.value)==null||n.validate(async e=>{e&&(await k(s.value.oldPassword,s.value.newPassword),l==null||l.$modal.msgSuccess("修改成功"))})},g=()=>{l==null||l.$tab.closePage()};return(n,e)=>{const t=y,u=v,p=C,_=E;return q(),V(_,{ref_key:"pwdRef",ref:w,model:r(s),rules:r(f),"label-width":"80px"},{default:a(()=>[o(u,{label:"旧密码",prop:"oldPassword"},{default:a(()=>[o(t,{modelValue:r(s).oldPassword,"onUpdate:modelValue":e[0]||(e[0]=d=>r(s).oldPassword=d),placeholder:"请输入旧密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),o(u,{label:"新密码",prop:"newPassword"},{default:a(()=>[o(t,{modelValue:r(s).newPassword,"onUpdate:modelValue":e[1]||(e[1]=d=>r(s).newPassword=d),placeholder:"请输入新密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),o(u,{label:"确认密码",prop:"confirmPassword"},{default:a(()=>[o(t,{modelValue:r(s).confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=d=>r(s).confirmPassword=d),placeholder:"请确认新密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),o(u,null,{default:a(()=>[o(p,{type:"primary",onClick:P},{default:a(()=>e[3]||(e[3]=[i("保存")])),_:1}),o(p,{type:"danger",onClick:g},{default:a(()=>e[4]||(e[4]=[i("关闭")])),_:1})]),_:1})]),_:1},8,["model","rules"])}}});export{T as _};
