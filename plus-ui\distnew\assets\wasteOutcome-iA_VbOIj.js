import{d as $,h as le,ak as O,r as l,ai as q,b as se,c as R,o as F,p as e,t as d,w as x,q as de,a7 as ue,M as ce,e as b,A as pe,B as ve,F as me,C as ge,x as M,D as be,K as fe,J as B,am as P,aI as _e,ay as ye,ax as he,v as we,az as Ce}from"./index-D07cMzhp.js";import{E as Te}from"./el-row-CikYE3zA.js";import{_ as Se}from"./index-BWMgqvQ9.js";import{E as Ne}from"./el-col-BaG5Rg5z.js";import{E as Fe}from"./el-date-picker-HyhB9X9n.js";import{l as Ae}from"./index-Dt50K0j_.js";import{o as V}from"./index-ChPmfMlc.js";import{u as A,X as De}from"./xlsx-BexUIDLF.js";import"./el-tree-DW6MoFaI.js";import"./dayjs.min-Brw96_N0.js";import"./index-VIEDZI2D.js";const Ee={class:"p-2"},Ie={class:"mb-[10px]"},qe=$({name:"WasteOutboundRecord"}),Je=$({...qe,setup(xe){const{proxy:y}=le(),{epwf_transport_condition:Be,epwf_container_type:Le,epwf_approval_status:Ue}=O(y==null?void 0:y.useDict("epwf_transport_condition","epwf_container_type","epwf_approval_status")),L=l([]);l(!1);const D=l(!0),E=l(!0),Y=l([]);l([]);const X=l(!0),h=l(),j=l(!0),U=l([]),G=l([]),I=l(0),J=l(["",""]),K=l(["",""]),H=l(["",""]),k=l(null),Q=l();l(),q({visible:!1,title:""}),q({visible:!1,title:""});const Z=q({form:{...{recordId:void 0,generateBatchCode:void 0,incomeBatchCode:void 0,outboundBatchCode:void 0,disposalBatchCode:void 0,generateTime:void 0,incomeTime:void 0,outboundTime:void 0,containerCode:void 0,containerType:void 0,containerNumber:void 0,wasteCommonName:void 0,wasteNationalName:void 0,wasteCategory:void 0,wasteCode:void 0,generateQuantity:void 0,incomeQuantity:void 0,outboundQuantity:void 0,disposalQuantity:void 0,unit:void 0,facilityCode:void 0,generateFacilityCode:void 0,facilityType:void 0,generateAgent:void 0,transportAgent:void 0,storageAgent:void 0,outboundAgent:void 0,generateDestination:void 0,outboundDestination:void 0,generateWasteUnitId:void 0,centralizedUnitId:void 0,approvalStatus:1,approvalOpinions:void 0,territorialUnitId:void 0,generatePlace:void 0,generateFile:void 0,weighFile:void 0,storeFile:void 0,labelFile:void 0,transportCondition:void 0,transportApplicationFile:void 0,transportWasteFile:void 0,wasteLabel:void 0,transportMultFile:void 0,poundList:void 0,processPhotos:void 0,disposalWay:void 0,receiveType:void 0,businessLicenseName:void 0,businessLicenseEncode:void 0,wasteComponentName:void 0,wasteShape:void 0,dangerCharacteristics:void 0,address:void 0}},queryParams:{pageNum:1,pageSize:10,generateBatchCode:void 0,incomeBatchCode:void 0,outboundBatchCode:void 0,disposalBatchCode:void 0,containerCode:void 0,containerType:void 0,containerNumber:void 0,wasteCommonName:void 0,wasteNationalName:void 0,wasteCategory:void 0,wasteCode:void 0,generateQuantity:void 0,incomeQuantity:void 0,outboundQuantity:void 0,disposalQuantity:void 0,unit:void 0,facilityCode:void 0,generateFacilityCode:void 0,facilityType:void 0,generateAgent:void 0,transportAgent:void 0,storageAgent:void 0,outboundAgent:void 0,generateDestination:void 0,outboundDestination:void 0,generateWasteUnitId:void 0,centralizedUnitId:void 0,approvalStatus:void 0,approvalOpinions:void 0,territorialUnitId:void 0,generatePlace:void 0,generateFile:void 0,weighFile:void 0,storeFile:void 0,labelFile:void 0,transportCondition:void 0,transportApplicationFile:void 0,transportWasteFile:void 0,wasteLabel:void 0,transportMultFile:void 0,poundList:void 0,processPhotos:void 0,disposalWay:void 0,receiveType:void 0,businessLicenseName:void 0,businessLicenseEncode:void 0,wasteComponentName:void 0,wasteShape:void 0,dangerCharacteristics:void 0,address:void 0,includeSelf:!0,params:{generateTime:void 0,incomeTime:void 0,outboundTime:void 0}},rules:{recordId:[{required:!0,message:"记录ID不能为空",trigger:"blur"}],generateWasteUnitId:[{required:!0,message:"产废单位不能为空",trigger:"blur"}],centralizedUnitId:[{required:!0,message:"归口部门不能为空",trigger:"blur"}],territorialUnitId:[{required:!0,message:"属地单位不能为空",trigger:"blur"}],generateBatchCode:[{required:!0,message:"产生批次不能为空",trigger:"blur"}],generateFacilityCode:[{required:!0,message:"产生危险废物设施编码不能为空",trigger:"blur"}],generateQuantity:[{required:!0,message:"产生量不能为空",trigger:"blur"}],unit:[{required:!0,message:"计量单位不能为空",trigger:"blur"}],generateTime:[{required:!0,message:"产生时间不能为空",trigger:"blur"}],generatePlace:[{required:!0,message:"危废产生地点不能为空",trigger:"blur"}],containerCode:[{required:!0,message:"容器/包装编码不能为空",trigger:"blur"}],containerType:[{required:!0,message:"容器/包装类型不能为空",trigger:"blur"}],containerNumber:[{required:!0,message:"容器/包装数量不能为空",trigger:"blur"}],generateAgent:[{required:!0,message:"产生部门经办人不能为空",trigger:"blur"}],generateDestination:[{required:!0,message:"产生去向不能为空",trigger:"blur"}]}}),{queryParams:r,form:Qe,rules:ze}=O(Z),C=async()=>{if(D.value=!0,r.value.params={},h.value){r.value.params=typeof r.value.params=="object"&&!Array.isArray(r.value.params)?r.value.params:{};const t=h.value;r.value.params.beginOutboundTime=`${h.value}-01 00:00:00`;const[c,n]=t.split("-");if(!c||!n)throw new Error("时间格式错误");const i=new Date(c,parseInt(n,10),0).getDate(),v=`${c}-${n}-${i.toString().padStart(2,"0")} 23:59:59`;r.value.params.endOutboundTime=v}const a=await Ae(r.value);L.value=a.rows,I.value=a.total,D.value=!1},z=()=>{r.value.pageNum=1,C()},ee=()=>{var a;J.value=["",""],K.value=["",""],H.value=["",""],(a=Q.value)==null||a.resetFields(),z()},te=a=>(r.value.pageNum-1)*r.value.pageSize+a+1,oe=async()=>{const a=await V(null,2);G.value=a.data;const t=await V(null,3);U.value=t.data},ae=a=>{Y.value=a.map(t=>t.recordId),X.value=a.length!=1,j.value=!a.length},ne=a=>{const{columns:t,data:c}=a,n=[];return t.forEach((i,v)=>{if(v===0){n[v]="合计";return}if(i.property==="outboundQuantity"){const _=c.map(f=>Number(f[i.property])||0).reduce((f,u)=>f+u,0);n[v]=`${_.toFixed(2)} `}else n[v]=""}),n},ie=()=>{var a;try{const t=(a=k.value)==null?void 0:a.$el;let c=t.querySelector(".el-table__fixed");c||(c=t);const n=A.table_to_book(c,{raw:!0}),i=n.Sheets[n.SheetNames[0]],v=[],m=A.decode_range(i["!ref"]);for(let u=m.s.c;u<=m.e.c;u++){let s=0;for(let g=m.s.r;g<=m.e.r;g++){const o=A.encode_cell({r:g,c:u}),w=i[o];if(w&&w.v){const T=String(w.v).split("").reduce((S,N)=>S+(N.charCodeAt(0)>255?2:1),0);T>s&&(s=T)}}v.push({wch:Math.min(s+2,60)})}i["!cols"]=v;const _={alignment:{horizontal:"center",vertical:"center",wrapText:!0},border:{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},font:{sz:11,name:"宋体",color:{rgb:"000000"}}};Object.keys(i).forEach(u=>{if(!u.startsWith("!")){const s=i[u];s.s=s.s?{...s.s,..._}:{..._},typeof s.v=="number"&&(s.z=s.z||"0.00")}}),i["!merges"]&&i["!merges"].forEach(u=>{for(let s=u.s.r;s<=u.e.r;s++)for(let g=u.s.c;g<=u.e.c;g++){const o=A.encode_cell({r:s,c:g});i[o]||(i[o]={t:"s",v:""}),i[o].s={..._}}});const f=De.write(n,{bookType:"xlsx",bookSST:!1,type:"binary",cellStyles:!0});saveAs(new Blob([re(f)],{type:"application/octet-stream"}),`危废出库报表_${new Date().getTime()}.xlsx`)}catch(t){typeof console<"u"&&console.error(t)}},re=a=>{const t=new ArrayBuffer(a.length),c=new Uint8Array(t);for(let n=0;n<a.length;n++)c[n]=a.charCodeAt(n)&255;return t};return se(()=>{C(),oe()}),(a,t)=>{var S,N;const c=be,n=ve,i=pe,v=Fe,m=fe,_=ce,f=ue,u=Ne,s=Se,g=Te,o=he,w=ye,W=Ce,T=we;return F(),R("div",Ee,[e(_e,{"enter-active-class":(S=b(y))==null?void 0:S.animate.searchAnimate.enter,"leave-active-class":(N=b(y))==null?void 0:N.animate.searchAnimate.leave},{default:d(()=>[x(de("div",Ie,[e(f,{shadow:"hover"},{default:d(()=>[e(_,{ref_key:"queryFormRef",ref:Q,model:b(r),inline:!0,"label-width":"100px"},{default:d(()=>[e(i,{label:"属地单位",style:{width:"308px"}},{default:d(()=>[e(n,{modelValue:b(r).territorialUnitId,"onUpdate:modelValue":t[0]||(t[0]=p=>b(r).territorialUnitId=p),placeholder:"请选择属地单位"},{default:d(()=>[(F(!0),R(me,null,ge(U.value,p=>(F(),M(c,{key:p.deptId,label:p.deptName,value:p.deptId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"出库月份",style:{width:"308px"}},{default:d(()=>[e(v,{modelValue:h.value,"onUpdate:modelValue":t[1]||(t[1]=p=>h.value=p),"default-value":new Date,placeholder:"选择月份",type:"month","value-format":"YYYY-MM"},null,8,["modelValue","default-value"])]),_:1}),e(i,null,{default:d(()=>[e(m,{type:"primary",icon:"Search",onClick:z},{default:d(()=>t[5]||(t[5]=[B("搜索")])),_:1}),e(m,{icon:"Refresh",onClick:ee},{default:d(()=>t[6]||(t[6]=[B("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1})],512),[[P,E.value]])]),_:1},8,["enter-active-class","leave-active-class"]),e(f,{shadow:"never"},{header:d(()=>[e(g,{gutter:10,class:"mb8"},{default:d(()=>[e(u,{span:1.5},{default:d(()=>[e(m,{type:"warning",plain:"",icon:"Download",onClick:ie},{default:d(()=>t[7]||(t[7]=[B("导出")])),_:1})]),_:1}),e(s,{showSearch:E.value,"onUpdate:showSearch":t[2]||(t[2]=p=>E.value=p),onQueryTable:C},null,8,["showSearch"])]),_:1})]),default:d(()=>[x((F(),M(w,{ref_key:"reportTable",ref:k,data:L.value,"summary-method":ne,"show-summary":"",stripe:"",onSelectionChange:ae},{default:d(()=>[e(o,{index:te,label:"序号",type:"index",width:"55"}),e(o,{label:"出库批次编码",align:"center",prop:"outboundBatchCode"}),e(o,{label:"出库时间",align:"center",prop:"outboundTime"}),e(o,{label:"容器/包装编码",align:"center",prop:"containerCode"}),e(o,{label:"容器/包装类型",align:"center",prop:"containerType"}),e(o,{label:"容器/包装数量",align:"center",prop:"containerNumber"}),e(o,{label:"危险废物名称",align:"center"},{default:d(()=>[e(o,{label:"行业俗称",align:"center",prop:"wasteCommonName"}),e(o,{label:"国家危废名录名称",align:"center",prop:"wasteNationalName"})]),_:1}),e(o,{label:"危险废物类别",align:"center",prop:"wasteCategory"}),e(o,{label:"危险废物代码",align:"center",prop:"wasteCode"}),e(o,{label:"出库量",align:"center",prop:"outboundQuantity"}),e(o,{label:"计量单位",align:"center",prop:"unit"}),e(o,{label:"贮存设施编码",align:"center",prop:"facilityCode"}),e(o,{label:"贮存设施类型",align:"center",prop:"facilityType"}),e(o,{label:"出库部门经办人",align:"center",prop:"outboundAgent"}),e(o,{label:"运送部门经办人",align:"center",prop:"transportAgent"}),e(o,{label:"去向",align:"center",prop:"outboundDestination"})]),_:1},8,["data"])),[[T,D.value]]),x(e(W,{total:I.value,page:b(r).pageNum,"onUpdate:page":t[3]||(t[3]=p=>b(r).pageNum=p),limit:b(r).pageSize,"onUpdate:limit":t[4]||(t[4]=p=>b(r).pageSize=p),onPagination:C},null,8,["total","page","limit"]),[[P,I.value>0]])]),_:1})])}}});export{Je as default};
