<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="报告名称" prop="balanceReportName">
              <el-input v-model="queryParams.balanceReportName" clearable placeholder="请输入水平衡测试报告名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <!--            <el-form-item label="行政区域" prop="administrativeArea">-->
            <!--              <el-select v-model="queryParams.administrativeArea" placeholder="请选择行政区域" clearable @keyup.enter="handleQuery">-->
            <!--                <el-option v-for="dict in eptw_administrative_area" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"/>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="所属地" prop="operationAreaId">
              <el-select v-model="queryParams.operationAreaId" clearable placeholder="请选择所属地" @keyup.enter="handleQuery">
                <el-option
                  v-for="dict in operationAreaList"
                  :key="dict.operationAreaId"
                  :label="dict.operationAreaName"
                  :value="dict.operationAreaId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="报告状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择报告状态">
                <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterBalanc:add']" icon="Plus" plain type="primary" @click="handleAdd">新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterBalanc:edit']" :disabled="single" icon="Edit" plain type="success" @click="handleUpdate()"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterBalanc:remove']" :disabled="multiple" icon="Delete" plain type="danger" @click="handleDelete()"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['epms:waterBalanc:export']" icon="Download" plain type="warning" @click="handleExport">导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table stripe v-loading="loading" :data="waterBalancList" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column :index="indexMethod" label="序号" type="index" width="50" />
        <el-table-column align="center" label="报告名称" prop="balanceReportName" width="600" />
        <!--        <el-table-column label="行政区域" align="center" prop="administrativeArea">-->
        <!--          <template #default="scope">-->
        <!--            {{ getAdministrativeAreaText(scope.row.administrativeArea) }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="所属地" align="center" prop="operationAreaName" width="160">
          <template #default="scope">
            {{ getOperationAreaName(scope.row.operationAreaId) }}
          </template>
        </el-table-column>
        <el-table-column label="报告状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="eptw_file_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="报告日期" align="center" prop="balanceReportTime" width="140">
          <template #default="scope">
            <span>{{ parseTime(scope.row.balanceReportTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="剩余时间(天)" prop="effectiveDay" />
        <el-table-column align="center" label="上传时间" prop="uploadTime" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="305">
          <template #default="scope">
            <el-tooltip content="水平衡测试报告预览" placement="top">
              <el-button v-hasPermi="['epms:waterBalanc:preview']" icon="Document" link type="primary" @click="handlePreview(scope.row)"
                >报告预览</el-button
              >
            </el-tooltip>
            <el-tooltip content="详情" placement="top">
              <el-button v-hasPermi="['epms:waterBalanc:detail']" icon="Postcard" link type="primary" @click="handleDetail(scope.row)"
                >详情</el-button
              >
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['epms:waterBalanc:edit']" icon="Edit" link type="primary" @click="handleUpdate(scope.row)">修改 </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['epms:waterBalanc:remove']" icon="Delete" link type="primary" @click="handleDelete(scope.row)">删除 </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改水平衡测试报告对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="550px" append-to-body>
      <el-form ref="waterBalancFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="报告名称" prop="balanceReportName">
          <el-input v-model="form.balanceReportName" placeholder="请输入水平衡测试报告名称" />
        </el-form-item>
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--                v-for="dict in eptw_administrative_area"-->
        <!--                :key="dict.value"-->
        <!--                :label="dict.label"-->
        <!--                :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" placeholder="请选择所属地">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择报告状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告日期" prop="balanceReportTime">
          <el-date-picker
            v-model="form.balanceReportTime"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择日期"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="文件列表" prop="file">
          <attachFileUpload
            v-model="form.file"
            :attach-source-id="form.balanceReportId"
            :disabled="false"
            attach-category="waterBalanc"
            attach-source-type="operationArea"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="detaildialog.title" v-model="detaildialog.visible" width="550px" append-to-body>
      <el-form ref="waterBalancFormRef" :model="form" :rules="rules" label-width="100px" disabled>
        <el-form-item label="报告名称" prop="balanceReportName">
          <el-input v-model="form.balanceReportName" placeholder="请输入水平衡测试报告名称" />
        </el-form-item>
        <!--        <el-form-item label="行政区域" prop="administrativeArea">-->
        <!--          <el-select v-model="form.administrativeArea" placeholder="请选择行政区域">-->
        <!--            <el-option-->
        <!--              v-for="dict in eptw_administrative_area"-->
        <!--              :key="dict.value"-->
        <!--              :label="dict.label"-->
        <!--              :value="parseInt(dict.value)"-->
        <!--            ></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="所属地" prop="operationAreaId">
          <el-select v-model="form.operationAreaId" placeholder="请选择所属地">
            <el-option
              v-for="dict in operationAreaList"
              :key="dict.operationAreaId"
              :label="dict.operationAreaName"
              :value="dict.operationAreaId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择报告状态">
            <el-option v-for="dict in eptw_file_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报告日期" prop="balanceReportTime">
          <el-date-picker
            v-model="form.balanceReportTime"
            clearable
            date-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD"
            placeholder="报告日期"
            type="dataTime"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker v-model="form.uploadTime" clearable placeholder="上传时间" type="datetime"> </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailcancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WaterBalanc" lang="ts">
import { addWaterBalanc, delWaterBalanc, getWaterBalanc, listWaterBalanc, updateWaterBalanc } from '@/api/epms/eptw/waterBalanc';
import { WaterBalancForm, WaterBalancQuery, WaterBalancVO } from '@/api/epms/eptw/waterBalanc/types';
import { OperationAreaQuery, OperationAreaVO } from '@/api/epms/epcom/operationArea/types';
import { listOperationArea } from '@/api/epms/epcom/operationArea';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eptw_administrative_area, eptw_file_status } = toRefs<any>(proxy?.useDict('eptw_administrative_area', 'eptw_file_status'));

const waterBalancList = ref<WaterBalancVO[]>([]);
const operationAreaList = ref<OperationAreaVO[]>([]); // 所属地列表
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const operationAreaQuery = ref<OperationAreaQuery>({});
const queryFormRef = ref<ElFormInstance>();
const waterBalancFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const detaildialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WaterBalancForm = {
  balanceReportId: undefined,
  administrativeArea: undefined,
  operationAreaId: undefined,
  balanceReportName: undefined,
  balanceReportTime: undefined,
  status: undefined,
  uploadTime: undefined,
  file: undefined
};
const data = reactive<PageData<WaterBalancForm, WaterBalancQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    administrativeArea: undefined,
    file: undefined,
    params: {}
  },
  rules: {
    balanceReportId: [{ required: true, message: '水平衡报告id不能为空', trigger: 'blur' }],
    operationAreaId: [{ required: true, message: '所属地不能为空', trigger: 'change' }],
    balanceReportName: [{ required: true, message: '水平衡测试报告名称不能为空', trigger: 'blur' }],
    balanceReportTime: [{ required: true, message: '报告日期不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '报告状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询水平衡测试报告列表 */
const getList = async () => {
  loading.value = true;
  const res = await listWaterBalanc(queryParams.value);
  waterBalancList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 查询所属地列表 */
const getoperationAreaList = async () => {
  operationAreaQuery.value.operationAreaParentId = 0;
  const res = await listOperationArea(operationAreaQuery.value);
  operationAreaList.value = res.rows;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const detailcancel = () => {
  reset();
  detaildialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  waterBalancFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WaterBalancVO[]) => {
  ids.value = selection.map((item) => item.balanceReportId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加水平衡测试报告';
};

/** 文件预览操作 */
const handlePreview = async (row?: WaterBalancVO) => {
  proxy.showAttachPreview({
    attachSourceId: row.balanceReportId,
    attachSourceType: 'operationArea',
    attachCategory: 'waterBalanc'
  });
};
const handleDetail = async (row?: WaterBalancVO) => {
  reset();
  const _balanceReportId = row?.balanceReportId || ids.value[0];
  const res = await getWaterBalanc(_balanceReportId);
  Object.assign(form.value, res.data);
  detaildialog.visible = true;
  detaildialog.title = '水平衡测试报告详情';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: WaterBalancVO) => {
  reset();
  const _balanceReportId = row?.balanceReportId || ids.value[0];
  const res = await getWaterBalanc(_balanceReportId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改水平衡测试报告';
};

const handleUploadSuccess = (fielInfo: any) => {
  form.value.uploadTime = fielInfo.uploadTime;
  form.value.balanceReportName = fielInfo.name;
};
/** 提交按钮 */
const submitForm = () => {
  waterBalancFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.balanceReportId) {
        await updateWaterBalanc(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWaterBalanc(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WaterBalancVO) => {
  const _balanceReportIds = row?.balanceReportId || ids.value;
  await proxy?.$modal.confirm('是否确认删除水平衡测试报告编号为"' + _balanceReportIds + '"的数据项？').finally(() => (loading.value = false));
  await delWaterBalanc(_balanceReportIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/waterBalanc/export',
    {
      ...queryParams.value
    },
    `水平衡测试报告_${new Date().getTime()}.xlsx`
  );
};

const getAdministrativeAreaText = (administrativeArea: number) => {
  if (!administrativeArea) {
    return '未知';
  }
  const administrativeAreaItem = eptw_administrative_area.value.find((item) => item.value === administrativeArea.toString());
  return administrativeAreaItem ? administrativeAreaItem.label : '未知';
};

const getOperationAreaName = (operationAreaId: number) => {
  if (!operationAreaId) {
    return '未知';
  }
  const operationAreaItem = operationAreaList.value.find((item) => item.operationAreaId === operationAreaId && item.operationAreaParentId === 0);
  return operationAreaItem ? operationAreaItem.operationAreaName : '未知';
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
  getoperationAreaList();
});
</script>
