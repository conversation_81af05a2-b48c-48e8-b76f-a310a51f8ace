import{d as D,r as i,h as Q,ak as M,b as $,c as G,o as E,p as e,t as l,w as c,q as H,a7 as j,M as O,e as t,A as W,G as X,H as h,K as Y,J as _,am as b,aI as Z,x as ee,ay as oe,ax as te,z as ae,aJ as le,v as ne,az as se,a8 as re,ai as ie}from"./index-D07cMzhp.js";import{E as ue}from"./el-row-CikYE3zA.js";import{_ as me}from"./index-BWMgqvQ9.js";import{i as de}from"./index-3RY37FEX.js";import{w as pe}from"./index-BpaKvpB0.js";import"./el-tree-DW6MoFaI.js";const ce={class:"p-2"},_e={class:"mb-[10px]"},Ne=D({__name:"taskCopyList",setup(fe){const y=i(),{proxy:m}=Q(),{wf_business_status:x}=M(m==null?void 0:m.useDict("wf_business_status")),f=i(!0),z=i([]),I=i(!0),T=i(!0),d=i(!0),w=i(0),C=i([]),s=i({pageNum:1,pageSize:10,nodeName:void 0,flowName:void 0,flowCode:void 0}),u=()=>{N()},B=()=>{var n;(n=y.value)==null||n.resetFields(),s.value.pageNum=1,s.value.pageSize=10,u()},R=n=>{z.value=n.map(o=>o.id),I.value=n.length!==1,T.value=!n.length},N=()=>{f.value=!0,de(s.value).then(n=>{C.value=n.rows,w.value=n.total,f.value=!1})},U=n=>{const o=ie({businessId:n.businessId,taskId:n.id,type:"view",formCustom:n.formCustom,formPath:n.formPath});pe.routerJump(o,m)};return $(()=>{N()}),(n,o)=>{var V,S;const g=X,p=W,v=Y,F=O,k=j,K=me,q=ue,r=te,J=le,L=oe,P=se,A=ne;return E(),G("div",ce,[e(Z,{"enter-active-class":(V=t(m))==null?void 0:V.animate.searchAnimate.enter,"leave-active-class":(S=t(m))==null?void 0:S.animate.searchAnimate.leave},{default:l(()=>[c(H("div",_e,[e(k,{shadow:"hover"},{default:l(()=>[c(e(F,{ref_key:"queryFormRef",ref:y,model:t(s),inline:!0},{default:l(()=>[e(p,{label:"任务名称",prop:"nodeName"},{default:l(()=>[e(g,{modelValue:t(s).nodeName,"onUpdate:modelValue":o[0]||(o[0]=a=>t(s).nodeName=a),placeholder:"请输入任务名称",onKeyup:h(u,["enter"])},null,8,["modelValue"])]),_:1}),e(p,{label:"流程定义名称","label-width":"100",prop:"flowName"},{default:l(()=>[e(g,{modelValue:t(s).flowName,"onUpdate:modelValue":o[1]||(o[1]=a=>t(s).flowName=a),placeholder:"请输入流程定义名称",onKeyup:h(u,["enter"])},null,8,["modelValue"])]),_:1}),e(p,{label:"流程定义编码","label-width":"100",prop:"flowCode"},{default:l(()=>[e(g,{modelValue:t(s).flowCode,"onUpdate:modelValue":o[2]||(o[2]=a=>t(s).flowCode=a),placeholder:"请输入流程定义编码",onKeyup:h(u,["enter"])},null,8,["modelValue"])]),_:1}),e(p,null,{default:l(()=>[e(v,{type:"primary",icon:"Search",onClick:u},{default:l(()=>o[6]||(o[6]=[_("搜索")])),_:1}),e(v,{icon:"Refresh",onClick:B},{default:l(()=>o[7]||(o[7]=[_("重置")])),_:1})]),_:1})]),_:1},8,["model"]),[[b,t(d)]])]),_:1})],512),[[b,t(d)]])]),_:1},8,["enter-active-class","leave-active-class"]),e(k,{shadow:"hover"},{header:l(()=>[e(q,{gutter:10,class:"mb8"},{default:l(()=>[e(K,{"show-search":t(d),"onUpdate:showSearch":o[3]||(o[3]=a=>re(d)?d.value=a:null),onQueryTable:u},null,8,["show-search"])]),_:1})]),default:l(()=>[c((E(),ee(L,{data:t(C),border:"",onSelectionChange:R},{default:l(()=>[e(r,{type:"selection",width:"55",align:"center"}),e(r,{align:"center",type:"index",label:"序号",width:"60"}),e(r,{"show-overflow-tooltip":!0,prop:"flowName",align:"center",label:"流程定义名称"}),e(r,{align:"center",prop:"flowCode",label:"流程定义编码"}),e(r,{align:"center",prop:"categoryName",label:"流程分类"}),e(r,{align:"center",prop:"version",label:"版本号",width:"90"},{default:l(a=>[_(" v"+ae(a.row.version)+".0",1)]),_:1}),e(r,{align:"center",prop:"nodeName",label:"任务名称"}),e(r,{align:"center",label:"流程状态","min-width":"70"},{default:l(a=>[e(J,{options:t(x),value:a.row.flowStatus},null,8,["options","value"])]),_:1}),e(r,{align:"center",label:"操作",width:"200"},{default:l(a=>[e(v,{type:"primary",size:"small",icon:"View",onClick:we=>U(a.row)},{default:l(()=>o[8]||(o[8]=[_("查看")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[A,t(f)]]),c(e(P,{page:t(s).pageNum,"onUpdate:page":o[4]||(o[4]=a=>t(s).pageNum=a),limit:t(s).pageSize,"onUpdate:limit":o[5]||(o[5]=a=>t(s).pageSize=a),total:t(w),onPagination:u},null,8,["page","limit","total"]),[[b,t(w)>0]])]),_:1})])}}});export{Ne as default};
