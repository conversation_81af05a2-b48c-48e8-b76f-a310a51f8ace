import request from '@/utils/request.js';

// 查询指标计划明细列表
export function listPlandetail(query) {
  return request({
    url: '/emcs/plandetail/list',
    method: 'get',
    params: query
  });
}

// 查询指标计划明细详细
export function getPlandetail(indicatorPlanId) {
  return request({
    url: '/emcs/plandetail/' + indicatorPlanId,
    method: 'get'
  });
}

// 新增指标计划明细
export function addPlandetail(data) {
  return request({
    url: '/emcs/plandetail',
    method: 'post',
    data: data
  });
}

// 修改指标计划明细
export function updatePlandetail(data) {
  return request({
    url: '/emcs/plandetail',
    method: 'put',
    data: data
  });
}

// 删除指标计划明细
export function delPlandetail(indicatorPlanId) {
  return request({
    url: '/emcs/plandetail/' + indicatorPlanId,
    method: 'delete'
  });
}
