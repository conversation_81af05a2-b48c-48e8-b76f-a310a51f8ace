<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="85px">
            <el-form-item label="年份" prop="year">
              <el-date-picker v-model="queryParams.year" format="YYYY" placeholder="选择日期" type="year" value-format="YYYY" />
            </el-form-item>
            <el-form-item label="取水区块" prop="operationAreaId">
              <el-tree-select
                v-model="queryParams.operationAreaId"
                :data="operationAreaTreeList"
                clearable
                :props="defaultProps"
                placeholder="请选择取水区块"
                @change="getFileName"
                :render-after-expand="true"
                style="width: 240px"
              />
            </el-form-item>
            <!--            <el-form-item label="时间" prop="date">-->
            <!--              <el-date-picker-->
            <!--                  v-model="queryParams.date"-->
            <!--                  type="date"-->
            <!--                  format="YYYY-MM-DD"-->
            <!--                  value-format="YYYY-MM-DD"-->
            <!--                  placeholder="选择日期"-->
            <!--                  clearable-->
            <!--              >-->
            <!--              </el-date-picker>-->
            <!--            </el-form-item>-->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <!--              <el-button type="warning" plain icon="Download" @click="handleExport"-->
              <!--                         v-hasPermi="['epms:waterBalanc:export']">导出</el-button>-->
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" :data="waterQuotaList" height="600px">
        <el-table-column align="center" fixed="left" label="序号" type="index" width="55" />
        <el-table-column :index="indexMethod" align="center" fixed="left" label="取水区块" min-width="120" prop="operationAreaName" />
        <el-table-column label="一月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan1" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue1" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan1" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue1" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota1" />
          <el-table-column align="center" label="取采比" prop="quotaRatio1" />
        </el-table-column>
        <el-table-column label="二月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan2" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue2" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan2" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue2" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota2" />
          <el-table-column align="center" label="取采比" prop="quotaRatio2" />
        </el-table-column>
        <el-table-column label="三月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan3" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue3" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan3" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue3" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota3" />
          <el-table-column align="center" label="取采比" prop="quotaRatio3" />
        </el-table-column>
        <el-table-column label="四月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan4" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue4" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan4" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue4" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota4" />
          <el-table-column align="center" label="取采比" prop="quotaRatio4" />
        </el-table-column>
        <el-table-column label="五月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan5" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue5" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan5" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue5" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota5" />
          <el-table-column align="center" label="取采比" prop="quotaRatio5" />
        </el-table-column>
        <el-table-column label="六月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan6" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue6" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan6" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue6" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota6" />
          <el-table-column align="center" label="取采比" prop="quotaRatio6" />
        </el-table-column>
        <el-table-column label="七月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan7" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue7" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan7" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue7" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota7" />
          <el-table-column align="center" label="取采比" prop="quotaRatio7" />
        </el-table-column>
        <el-table-column label="八月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan8" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue8" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan8" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue8" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota8" />
          <el-table-column align="center" label="取采比" prop="quotaRatio8" />
        </el-table-column>
        <el-table-column label="九月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan9" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue9" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan9" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue9" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota9" />
          <el-table-column align="center" label="取采比" prop="quotaRatio9" />
        </el-table-column>
        <el-table-column label="十月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan10" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue10" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan10" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue10" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota10" />
          <el-table-column align="center" label="取采比" prop="quotaRatio10" />
        </el-table-column>
        <el-table-column label="十一月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan11" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue11" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan11" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue11" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota11" />
          <el-table-column align="center" label="取采比" prop="quotaRatio11" />
        </el-table-column>
        <el-table-column label="十二月" align="center">
          <el-table-column align="center" label="计划取水量(方)" min-width="110" prop="qushuiPlan12" />
          <el-table-column align="center" label="实际取水量(方)" min-width="110" prop="qushuiValue12" />
          <el-table-column align="center" label="计划采油量(吨)" min-width="110" prop="caiYouPlan12" />
          <el-table-column align="center" label="实际采油量(吨)" min-width="110" prop="caiYouValue12" />
          <el-table-column align="center" label="行业定额" min-width="110" prop="industryQuota12" />
          <el-table-column align="center" label="取采比" prop="quotaRatio12" />
        </el-table-column>
        <el-table-column label="年取水量(方)" align="center" prop="yearQuShui" fixed="right" />
        <el-table-column label="年采油量(吨)" align="center" prop="yearCaiYou" fixed="right" />
        <el-table-column label="年取水配额" align="center" prop="yearRatio" fixed="right" />
      </el-table>
      <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum" :total="total" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="WaterQuota" lang="ts">
import { listTreeOperationArea } from '@/api/epms/epcom/operationArea';
import { getQuShuiData } from '@/api/epms/eptw/waterQuota';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const loading = ref(false);
const total = ref(0);
const showSearch = ref(true);
const waterQuotaList = ref([]);
const queryFormRef = ref<ElFormInstance>();
const operationAreaTreeList = ref([]);
const defaultProps = {
  value: 'id',
  label: 'name',
  children: 'children'
};
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  operationAreaId: undefined,
  year: String(new Date().getFullYear())
});
/** 查询水平衡测试报告列表 */
const getList = async () => {
  loading.value = true;
  const res = await getQuShuiData(queryParams.value);
  waterQuotaList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 查询所属地列表 */
const getoperationAreaList = async () => {
  const ancestors = '0,1';
  listTreeOperationArea(ancestors).then((res) => {
    operationAreaTreeList.value = res.data;
    if (res.data[0].name) {
      defaultProps.label = 'name';
    } else if (res.data[0].label) {
      defaultProps.label = 'label';
    }
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/waterBalanc/export',
    {
      ...queryParams.value
    },
    `waterBalanc_${new Date().getTime()}.xlsx`
  );
};
/** 获取序号 */
const indexMethod = (index: number) => {
  return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1;
};
onMounted(() => {
  getList();
  getoperationAreaList();
});
</script>
