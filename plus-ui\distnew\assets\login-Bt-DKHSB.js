import{d as A,h as O,i as j,a as G,j as H,r as i,k as J,b as Q,m as W,c as f,o as d,p as l,q as m,t as n,x as k,y as x,z as v,e,A as X,B as Y,F as Z,C as ee,D as te,_ as oe,G as ae,H as U,I as le,J as L,K as ne,L as se,M as re,N as ie,O as ue,P as de,Q as ce}from"./index-D07cMzhp.js";const me={class:"login"},pe={class:"title-box"},ge={class:"title"},_e={class:"login-code"},fe=["src"],ve={key:0},Ie={key:1},ye={key:0,style:{float:"right"}},be=A({__name:"login",setup(he){const{proxy:u}=O(),$="环保信息化平台",B=j(),C=G(),{t:I}=H(),t=i({tenantId:"000000",username:"",password:"",rememberMe:!1,code:"",uuid:""}),R={tenantId:[{required:!0,trigger:"blur",message:I("login.rule.tenantId.required")}],username:[{required:!0,trigger:"blur",message:I("login.rule.username.required")}],password:[{required:!0,trigger:"blur",message:I("login.rule.password.required")}],code:[{required:!0,trigger:"change",message:I("login.rule.code.required")}]},E=i(""),p=i(!1),y=i(!0),b=i(!0),N=i(!1),M=i("/"),q=i(),g=i([]);J(()=>C.currentRoute.value,a=>{M.value=a.query&&a.query.redirect&&decodeURIComponent(a.query.redirect)},{immediate:!0});const h=()=>{var a;(a=q.value)==null||a.validate(async(o,c)=>{if(o){p.value=!0,t.value.rememberMe?(localStorage.setItem("tenantId",String(t.value.tenantId)),localStorage.setItem("username",String(t.value.username)),localStorage.setItem("password",String(t.value.password)),localStorage.setItem("rememberMe",String(t.value.rememberMe))):(localStorage.removeItem("tenantId"),localStorage.removeItem("username"),localStorage.removeItem("password"),localStorage.removeItem("rememberMe"));const[r]=await ie(B.login(t.value));if(r)p.value=!1,y.value&&await w();else{const S=M.value||"/";await C.push(S),p.value=!1}}else console.log("error submit!",c)})},w=async()=>{const a=await ue(),{data:o}=a;y.value=o.captchaEnabled===void 0?!0:o.captchaEnabled,y.value&&(E.value="data:image/gif;base64,"+o.img,t.value.uuid=o.uuid)},z=()=>{const a=localStorage.getItem("tenantId"),o=localStorage.getItem("username"),c=localStorage.getItem("password"),r=localStorage.getItem("rememberMe");t.value={tenantId:a===null?String(t.value.tenantId):a,username:o===null?String(t.value.username):o,password:String(c===null?t.value.password:c),rememberMe:r===null?!1:!!r}},F=async()=>{const{data:a}=await de(!1);b.value=a.tenantEnabled===void 0?!0:a.tenantEnabled,b.value&&(g.value=a.voList,g.value!=null&&g.value.length!==0&&(t.value.tenantId=g.value[0].tenantId))};return Q(()=>{w(),F(),z()}),(a,o)=>{const c=te,r=oe,S=Y,_=X,V=ae,K=le,P=ne,D=W("router-link"),T=re;return d(),f("div",me,[l(T,{ref_key:"loginRef",ref:q,model:e(t),rules:R,class:"login-form"},{default:n(()=>[m("div",pe,[m("h3",ge,v(e($)),1)]),e(b)?(d(),k(_,{key:0,prop:"tenantId"},{default:n(()=>[l(S,{modelValue:e(t).tenantId,"onUpdate:modelValue":o[0]||(o[0]=s=>e(t).tenantId=s),filterable:"",placeholder:e(u).$t("login.selectPlaceholder"),style:{width:"100%"}},{prefix:n(()=>[l(r,{"icon-class":"company",class:"el-input__icon input-icon"})]),default:n(()=>[(d(!0),f(Z,null,ee(e(g),s=>(d(),k(c,{key:s.tenantId,label:s.companyName,value:s.tenantId},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1})):x("",!0),l(_,{prop:"username"},{default:n(()=>[l(V,{modelValue:e(t).username,"onUpdate:modelValue":o[1]||(o[1]=s=>e(t).username=s),type:"text",size:"large","auto-complete":"off",placeholder:e(u).$t("login.username")},{prefix:n(()=>[l(r,{"icon-class":"user",class:"el-input__icon input-icon"})]),_:1},8,["modelValue","placeholder"])]),_:1}),l(_,{prop:"password"},{default:n(()=>[l(V,{modelValue:e(t).password,"onUpdate:modelValue":o[2]||(o[2]=s=>e(t).password=s),type:"password",size:"large","auto-complete":"off",placeholder:e(u).$t("login.password"),onKeyup:U(h,["enter"])},{prefix:n(()=>[l(r,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue","placeholder"])]),_:1}),e(y)?(d(),k(_,{key:1,prop:"code"},{default:n(()=>[l(V,{modelValue:e(t).code,"onUpdate:modelValue":o[3]||(o[3]=s=>e(t).code=s),size:"large","auto-complete":"off",placeholder:e(u).$t("login.code"),style:{width:"63%"},onKeyup:U(h,["enter"])},{prefix:n(()=>[l(r,{"icon-class":"validCode",class:"el-input__icon input-icon"})]),_:1},8,["modelValue","placeholder"]),m("div",_e,[m("img",{src:e(E),class:"login-code-img",onClick:w},null,8,fe)])]),_:1})):x("",!0),l(K,{modelValue:e(t).rememberMe,"onUpdate:modelValue":o[4]||(o[4]=s=>e(t).rememberMe=s),style:{margin:"0 0 25px 0"}},{default:n(()=>[L(v(e(u).$t("login.rememberPassword")),1)]),_:1},8,["modelValue"]),l(_,{style:{width:"100%"}},{default:n(()=>[l(P,{loading:e(p),size:"large",type:"primary",style:{width:"100%"},onClick:se(h,["prevent"])},{default:n(()=>[e(p)?(d(),f("span",Ie,v(e(u).$t("login.logging")),1)):(d(),f("span",ve,v(e(u).$t("login.login")),1))]),_:1},8,["loading"]),e(N)?(d(),f("div",ye,[l(D,{class:"link-type",to:"/register"},{default:n(()=>[L(v(e(u).$t("login.switchRegisterPage")),1)]),_:1})])):x("",!0)]),_:1})]),_:1},8,["model"]),o[5]||(o[5]=m("div",{class:"el-login-footer"},[m("span",null,"Copyright © 2025-2025 环保信息化平台 All Rights Reserved.")],-1))])}}}),Se=ce(be,[["__scopeId","data-v-3830cfd6"]]);export{Se as default};
