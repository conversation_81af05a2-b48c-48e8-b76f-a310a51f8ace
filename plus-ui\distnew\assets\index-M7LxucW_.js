import{aC as p}from"./index-D07cMzhp.js";const e=t=>p({url:"/epfy/transportApplication/list",method:"get",params:t}),i=t=>p({url:"/epfy/transportApplication/"+t,method:"get"}),l=t=>p({url:"/epfy/transportApplication",method:"post",data:t}),c=t=>p({url:"/epfy/transportApplication",method:"put",data:t}),u=(t,r,o,a,n)=>p({url:"/epfy/transportApplication/coordinate",method:"put",params:{appId:t,status:r,rejectSuggestion:o,warnTime:a,type:n}}),A=t=>p({url:"/epfy/transportApplication/"+t,method:"delete"}),d=t=>p({url:"/epfy/transportApplication/listAll",method:"get",params:t}),m=t=>p({url:"/epfy/transportApplication/optionselect",method:"get",params:t}),y=t=>p({url:"/epfy/transportApplication/copyTransportApplication",method:"post",data:t});export{l as a,u as b,y as c,A as d,d as e,i as g,e as l,m as o,c as u};
