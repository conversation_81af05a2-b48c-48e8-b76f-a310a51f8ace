<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input v-model="queryParams.equipmentName" clearable placeholder="请输入设备名称" size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="设备类型" prop="partsType">
        <el-select v-model="queryParams.partsType" clearable filterable placeholder="请选择设备类型">
          <el-option
            v-for="bujianleixing in bujianleixingList"
            :key="bujianleixing.id"
            :label="bujianleixing.mingzi"
            :value="parseInt(bujianleixing.id)"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="异常名称" prop="warnName">
        <el-input v-model="queryParams.warnName" clearable placeholder="请输入异常名称" size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="日期" prop="occurTime">
        <el-date-picker v-model="queryParams.occurTimeStart" clearable size="small" type="date" value-format="yyyy-MM-dd"> </el-date-picker>
      </el-form-item>
      —
      <el-form-item label="" prop="occurRecover">
        <el-date-picker v-model="queryParams.occurTimeEnd" clearable size="small" type="date" value-format="yyyy-MM-dd"> </el-date-picker>
      </el-form-item>
      <el-form-item label="异常状态" prop="warnStatus">
        <el-select v-model="queryParams.warnStatus" placeholder="请选择异常状态" clearable size="small">
          <el-option v-for="dict in dictData.warnStatus" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="异常级别" prop="warnLevel">
        <el-select v-model="queryParams.warnLevel" placeholder="请选择异常状态" clearable size="small">
          <el-option v-for="dict in dictData.warnLevel" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
          v-hasPermi="['dbedit:warnRecord:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table stripe v-loading="loading" :data="warnRecordList" @selection-change="handleSelectionChange">
      <el-table-column label="异常记录" align="center" prop="warnRecordId" />
      <el-table-column label="异常名称" align="center" prop="warnName" />
      <el-table-column label="所属设备" align="center" prop="equipmentName" />
      <el-table-column label="所属作业区" align="center" prop="projectName" />
      <el-table-column label="异常发生时间" align="center" prop="occurTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.occurTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="异常结束时间" align="center" prop="occurRecover" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.occurRecover, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="异常次数" prop="warnCount"> </el-table-column>
      <el-table-column align="center" label="异常状态" prop="warnStatus">
        <template slot-scope="scope">
          <dict-tag :options="dictData.warnStatus" :value="scope.row.warnStatus" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="异常级别" prop="warnLevel">
        <template slot-scope="scope">
          <span :style="levelColor(scope.row.warnLevel)">
            <dict-tag :options="dictData.warnLevel" :value="scope.row.warnLevel" />
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作">
        <template slot-scope="scope">
          <el-button v-hasPermi="['dbedit:warnRecord:edit']" icon="el-icon-edit" size="mini" type="text" @click="handleUpdate(scope.row)"
            >详情</el-button
          >
          <el-button v-hasPermi="['dbedit:warnRecord:edit']" icon="el-icon-refresh" size="mini" type="text" @click="resetAlert(scope.row)"
            >恢复</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :limit.sync="queryParams.pageSize" :page.sync="queryParams.pageNum" :total="total" @pagination="getList" />
    <!-- 添加或修改warnRecord对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col span="12">
            <el-form-item label="异常名称" prop="warnName">
              <el-input v-model="form.warnName" disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="异常级别" prop="warnLevel">
              <el-select v-model="form.warnLevel" disabled="disabled">
                <el-option v-for="dict in dictData.warnLevel" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="异常来源" prop="warnSource">
              <el-select v-model="form.warnSource" disabled="disabled">
                <el-option v-for="dict in dictData.warnSource" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="异常状态" prop="warnStatus">
              <el-select v-model="form.warnStatus" clearable disabled="disabled" size="small">
                <el-option v-for="dict in dictData.warnStatus" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="发生时间" prop="occurTime">
              <el-date-picker
                v-model="form.occurTime"
                clearable
                disabled="disabled"
                placeholder="选择发生时间"
                size="small"
                type="date"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="恢复时间" prop="occurRecover">
              <el-date-picker
                v-model="form.occurRecover"
                clearable
                disabled="disabled"
                placeholder="选择恢复时间"
                size="small"
                type="date"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="所属项目" prop="projectName">
              <el-input disabled="disabled" v-model="form.projectName" />
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="电站名称" prop="stationName">
              <el-input v-model="form.stationName" disabled="disabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="电站维度" prop="voltage">
              <el-input v-model="form.voltage" disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="电站经度" prop="circuit">
              <el-input v-model="form.circuit" disabled="disabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="设备编号" prop="equipmentCode">
              <el-input v-model="form.equipmentCode" disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="设备名称" prop="equipmentName">
              <el-input disabled="disabled" v-model="form.equipmentName" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="设备类型" prop="partsType">
              <el-select v-model="form.partsType" clearable disabled="disabled" filterable>
                <el-option
                  v-for="bujianleixing in bujianleixingList"
                  :key="bujianleixing.id"
                  :label="bujianleixing.mingzi"
                  :value="parseInt(bujianleixing.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="所属省" prop="provincename">
              <el-input v-model="form.provincename" disabled="disabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="所属市" prop="cityname">
              <el-input v-model="form.cityname" disabled="disabled" />
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="所属县" prop="townname">
              <el-input v-model="form.townname" disabled="disabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="恢复状态" prop="recoverWay">
              <el-select v-model="form.recoverWay" disabled="disabled" clearable size="small">
                <el-option v-for="dict in dictData.recoverWay" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="发送状态" prop="sendState">
              <el-select v-model="form.sendState" disabled="disabled" clearable size="small">
                <el-option v-for="dict in dictData.sendState" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="项目大类" prop="project_category">
              <el-select
                id="project_category"
                v-model="form.project_category"
                clearable
                disabled="disabled"
                placeholder="请选择项目大类"
                size="small"
              >
                <el-option v-for="dict in dictData.project_category" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="项目子类" prop="project_subclass">
              <el-select v-model="form.project_subclass" disabled="disabled" placeholder="请选择项目子类" clearable size="small">
                <el-option v-for="dict in dictInfo" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" type="primary">返回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addWarnRecord,
  allListCity,
  allListProvince,
  allListTown,
  cityListByProvinceid,
  delWarnRecord,
  getWarnRecord,
  listWarnRecord,
  townListByCityid,
  updateWarnRecord
} from '@/api/comm/warn/warnRecord/warnRecord.js';
import { allListBujianleixing } from '@/api/comm/warn/warnRecord/warnsource.js';
import * as proxy from '@/utils/dict.ts';

export default {
  name: 'WarnRecord',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      dictData: undefined,
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      bujianleixingList: [],
      dictInfo: [],
      provinceList: [],
      cityList: [],
      townList: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // warnRecord表格数据
      warnRecordList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        equipmentId: null,
        project_category: null,
        project_class: null,
        assetId: null,
        partsType: null,
        realWarnId: null,
        project_subclass: null,
        warnName: null,
        projectName: null,
        warnSetId: null,
        provinceid: null,
        cityid: null,
        townid: null,
        warnLevel: null,
        //发生时间搜索
        occurTimeStart: null,
        occurTimeEnd: null,
        warnType: null,

        stationId: null,

        occurTime: null,

        occurRecover: null,

        occurRecover_cen: null,

        warnStatus: null,

        stationName: null,

        equipmentName: null,

        warnSource: null,

        provincename: null,

        cityname: null,

        equipmentCode: null,

        sendState: null,

        voltage: null,

        circuit: null,

        recoverWay: null,
        projectCategory: null,
        calculationType: 4
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        warnRecordId: [{ max: 38, message: '异常ID长度不能超过38', trigger: 'blur' }],
        equipmentId: [{ max: 38, message: '设备ID长度不能超过38', trigger: 'blur' }],
        assetId: [{ max: 38, message: '资产ID长度不能超过38', trigger: 'blur' }],
        realWarnId: [{ max: 38, message: '实时库ID长度不能超过38', trigger: 'blur' }],
        warnName: [],
        warnSetId: [{ max: 38, message: '异常配置ID长度不能超过38', trigger: 'blur' }],
        warnLevel: [{ max: 38, message: '异常级别长度不能超过38', trigger: 'blur' }],
        warnType: [],
        stationId: [{ max: 38, message: '电站ID长度不能超过38', trigger: 'blur' }],
        occurTime: [],
        occurRecover: [],
        warnStatus: [],
        stationName: [{ max: 256, message: '电站名称长度不能超过256', trigger: 'blur' }],
        equipmentName: [{ max: 256, message: '设备名称长度不能超过256', trigger: 'blur' }],
        warnSource: [{ max: 38, message: '异常来源长度不能超过38', trigger: 'blur' }],
        provincename: [{ max: 256, message: '所属区县长度不能超过256', trigger: 'blur' }],
        cityname: [{ max: 256, message: '所属乡镇长度不能超过256', trigger: 'blur' }],
        equipmentCode: [{ max: 38, message: '设备编号长度不能超过38', trigger: 'blur' }],
        sendState: [{ max: 38, message: '发送状态长度不能超过38', trigger: 'blur' }],
        voltage: [],
        circuit: [],
        recoverWay: [{ max: 38, message: '恢复状态长度不能超过38', trigger: 'blur' }]
      }
    };
  },
  created() {
    this.getList();
    this.getBujianleixingList();
    this.getProvinceList();
    this.loadDicts();
  },
  computed: {
    levelColor(level) {
      return (level) => {
        switch (level) {
          case 1:
            return 'color: #33cc00';
          case 2:
            return 'color: #ffd700';
          case 3:
            return 'color: red';
        }
      };
    }
  },
  methods: {
    loadDicts() {
      this.dictData = proxy?.useDict(
        'project_category',
        'jienengChild',
        'warnStatus',
        'rebengChild',
        'tynChild',
        'warnLevel',
        'warnSource',
        'recoverWay',
        'sendState'
      );
    },
    /** 查询warnRecord列表 */
    getList() {
      this.loading = true;

      // this.queryParams.occurRecover=new Date(this.queryParams.occurRecover.setDate(this.queryParams.occurRecover.getDate()+1));
      if (this.queryParams.occurRecover != null) {
        let searchRecoverdate = null;
        let searchRecovermonth = this.queryParams.occurRecover.split('-')[1];
        if (parseInt(this.queryParams.occurRecover.split('-')[2]) < 10 && parseInt(this.queryParams.occurRecover.split('-')[2]) > 0) {
          searchRecoverdate = '0' + String(parseInt(this.queryParams.occurRecover.split('-')[2]) + 1);
        } else {
          searchRecoverdate = parseInt(this.queryParams.occurRecover.split('-')[2]) + 1;
        }
        //闰年
        if (
          (parseInt(this.queryParams.occurRecover.split('-')[0]) % 4 == 0 && parseInt(this.queryParams.occurRecover.split('-')[0]) % 100 != 0) ||
          parseInt(this.queryParams.occurRecover.split('-')[0]) % 400 == 0
        ) {
          if (parseInt(this.queryParams.occurRecover.split('-')[1]) == 2 && parseInt(this.queryParams.occurRecover.split('-')[2]) >= 29) {
            searchRecovermonth = parseInt(this.queryParams.occurRecover.split('-')[1]) + 1;
            searchRecoverdate = '01';
          } else if (
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 1 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 3 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 5 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 7 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 8 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 10 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 12
          ) {
            if (parseInt(this.queryParams.occurRecover.split('-')[2]) >= 31) {
              searchRecovermonth = parseInt(this.queryParams.occurRecover.split('-')[1]) + 1;
              searchRecoverdate = '01';
            }
          } else {
            if (parseInt(this.queryParams.occurRecover.split('-')[2]) >= 30) {
              searchRecovermonth = parseInt(this.queryParams.occurRecover.split('-')[1]) + 1;
              searchRecoverdate = '01';
            }
          }
        } //平年
        else {
          if (parseInt(this.queryParams.occurRecover.split('-')[1]) == 2 && parseInt(this.queryParams.occurRecover.split('-')[2]) >= 28) {
            searchRecoverdate = '01';
            searchRecovermonth = 3;
          } else if (
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 1 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 3 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 5 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 7 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 8 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 10 ||
            parseInt(this.queryParams.occurRecover.split('-')[1]) == 12
          ) {
            if (parseInt(this.queryParams.occurRecover.split('-')[2]) >= 31) {
              searchRecovermonth = parseInt(this.queryParams.occurRecover.split('-')[1]) + 1;
              searchRecoverdate = '01';
            }
          } else {
            if (parseInt(this.queryParams.occurRecover.split('-')[2]) >= 30) {
              searchRecovermonth = parseInt(this.queryParams.occurRecover.split('-')[1]) + 1;
              searchRecoverdate = '01';
            }
          }
        }
        this.queryParams.occurRecover_cen = this.queryParams.occurRecover;
        this.queryParams.occurRecover = this.queryParams.occurRecover.split('-')[0] + '-' + searchRecovermonth + '-' + searchRecoverdate;
        /* alert(this.queryParams.occurRecover);*/
      }
      // this.queryParams.occurRecover=new Date(this.queryParams.occurRecover.setDate(this.queryParams.occurRecover.getDate()+1));
      listWarnRecord(this.queryParams).then((response) => {
        this.warnRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      this.queryParams.occurRecover = this.queryParams.occurRecover_cen;
    },
    /** 查询province列表 */
    getProvinceList() {
      allListProvince(this.queryParams).then((response) => {
        this.provinceList = response.data;
      });
    },
    /** 查询city列表 */
    getCityList() {
      allListCity(this.queryParams).then((response) => {
        this.cityList = response.data;
      });
    },
    /** 根据provinceid查询city列表 */
    getCityListByProvinceid(provinceid) {
      if (provinceid != null && provinceid != '') {
        cityListByProvinceid(provinceid).then((response) => {
          this.cityList = response.data;
        });
      } else {
        this.cityList = [];
      }
    },
    /** 查询town列表 */
    getTownList() {
      allListTown(this.queryParams).then((response) => {
        this.townList = response.data;
      });
    },
    /** 根据cityid查询town列表 */
    getTownListByCityid(cityid) {
      if (cityid != null && cityid != '') {
        townListByCityid(cityid).then((response) => {
          this.townList = response.data;
        });
      } else {
        this.townList = [];
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 查询bujianleixing列表 */
    getBujianleixingList() {
      allListBujianleixing(this.queryParams).then((response) => {
        this.bujianleixingList = response.data;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        warnRecordId: null,
        partsType: null,
        equipmentId: null,

        assetId: null,

        realWarnId: null,

        warnName: null,

        warnSetId: null,

        warnLevel: null,

        warnType: null,

        stationId: null,

        occurTime: null,

        occurRecover: null,

        warnStatus: 0,

        stationName: null,

        equipmentName: null,

        warnSource: null,

        provincename: null,

        cityname: null,

        equipmentCode: null,

        sendState: null,

        voltage: null,

        circuit: null,

        recoverWay: null,

        calculationType: 4
      };
      this.resetForm('form');
    },

    handleParentProject: function (e) {
      this.queryParams.project_class = null;
      if (e == 1) {
        //热泵
        this.dictInfo = this.dictData.rebengChild;
      } else if (e == 2) {
        this.dictInfo = this.dictData.jienengChild;
      } else if (e == 3) {
        this.dictInfo = this.dictData.tynChild;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.warnRecordId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加warnRecord';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const warnRecordId = row.warnRecordId || this.ids;
      getWarnRecord(warnRecordId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = '异常记录详情';
        if (this.form.project_category == 1) {
          //热泵
          this.dictInfo = this.dictData.rebengChild;
        } else if (this.form.project_category == 2) {
          this.dictInfo = this.dictData.jienengChild;
        } else if (this.form.project_category == 3) {
          this.dictInfo = this.dictData.tynChild;
        }
      });
    },
    resetAlert(row) {
      this.$confirm('是否恢复该告警状态', '提示', {
        iconClass: 'el-icon-question', //自定义图标样式
        confirmButtonText: '确认', //确认按钮文字更换
        cancelButtonText: '取消', //取消按钮文字更换
        showClose: true, //是否显示右上角关闭按钮
        type: 'warning' //提示类型  success/info/warning/error
      })
        .then(function () {
          //确认操作
          row.warnStatus = 5;
          row.warnCount = 0;
          updateWarnRecord(row).then((response) => {
            this.$modal.msgSuccess('恢复成功');
            this.getList();
          });
        })
        .then((data) => {
          //取消操作
        })
        .catch(function (err) {
          //捕获异常
        });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.warnRecordId != null) {
            updateWarnRecord(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addWarnRecord(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const warnRecordIds = row.warnRecordId || this.ids;
      this.$modal
        .confirm('是否确认删除warnRecord编号为"' + warnRecordIds + '"的数据项？')
        .then(function () {
          return delWarnRecord(warnRecordIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'dbedit/warnRecord/export',
        {
          ...this.queryParams
        },
        `warnRecord_${new Date().getTime()}.xlsx`
      );
    }
  }
};
</script>
