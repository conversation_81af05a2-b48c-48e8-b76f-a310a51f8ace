package com.biz.epms.epfy.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.comm.attach.annotation.AttachUpload;
import com.biz.comm.attach.domain.bo.CommAttachBo;
import com.biz.comm.attach.domain.vo.CommAttachVo;
import com.biz.comm.attach.service.ICommAttachService;
import com.biz.epms.epfy.domain.bo.EpfyTransportApplicationBo;
import com.biz.epms.epfy.domain.bo.EpfyTransportRecordBo;
import com.biz.epms.epfy.domain.vo.EpfyTransportApplicationVo;
import com.biz.epms.epfy.domain.vo.EpfyTransportRecordVo;
import com.biz.epms.epfy.service.IEpfyTransportApplicationService;
import com.biz.epms.epfy.service.IEpfyTransportRecordService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.DictService;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 拉运记录
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/epfy/transportRecord")
public class EpfyTransportRecordController extends BaseController {

    private final IEpfyTransportRecordService epfyTransportRecordService;

    private final DictService dictService;

    private final ICommAttachService commAttachService;

    private final IEpfyTransportApplicationService epfyTransportApplicationService;

    /**
     * 传入年份返回该年的每月第一天组成的List
     *
     * @param year 年份
     */
    public static List<String> generateMonthlyStrings(String year) {
        List<String> months = new ArrayList<>(12);
        for (int month = 1; month <= 12; month++) {
            months.add(year + "-" + month);
        }
        return months;
    }

    /**
     * 查询拉运记录列表
     */
    @SaCheckPermission("epfy:transportRecord:list")
    @GetMapping("/list")
    public TableDataInfo<EpfyTransportRecordVo> list(EpfyTransportRecordBo bo, PageQuery pageQuery) {
        return epfyTransportRecordService.queryPageList(bo, pageQuery);
    }

    @SaCheckPermission("epfy:transportRecord:list")
    @GetMapping("/getListByDaliy")
    public TableDataInfo<EpfyTransportRecordVo> getListByDaliy(Long relationId, Integer mediumCategory, String date, String unloadLocation, PageQuery pageQuery) {
        EpfyTransportApplicationBo appBo = new EpfyTransportApplicationBo();
        appBo.setRelatedId(relationId);
        appBo.setFlowType(2);
        appBo.setUnloadLocation(unloadLocation);
        appBo.setMediumCategory(mediumCategory);
        List<EpfyTransportApplicationVo> applications = epfyTransportApplicationService.queryList(appBo);
        EpfyTransportRecordBo bo = new EpfyTransportRecordBo();
        Page<EpfyTransportRecordVo> result = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        List<EpfyTransportRecordVo> records1 = new ArrayList<>();
        if (date!=null&&!"".equals(date)) {
            LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            Date date1 = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            bo.setTransportTime(date1);
        }
        for (EpfyTransportApplicationVo app : applications) {
            bo.setApplicationId(app.getAppId());
            List<EpfyTransportRecordVo> records = epfyTransportRecordService.queryList(bo);
            records1.addAll(records);
        }
        int pageNum = pageQuery.getPageNum();
        int pageSize = pageQuery.getPageSize();

        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, records1.size());
        try {
            result.setRecords(records1.subList(start, end));
        } catch (IndexOutOfBoundsException e) {
            result.setRecords(Collections.emptyList());
        }
        result.setTotal(records1.size());
        return TableDataInfo.build(result);
    }

    /**
     * 导出拉运记录列表
     */
    @SaCheckPermission("epfy:transportRecord:export")
    @Log(title = "拉运记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EpfyTransportRecordBo bo, HttpServletResponse response) {
        PageQuery pageQuery = new PageQuery(Integer.MAX_VALUE, 1);
        List<EpfyTransportRecordVo> list = epfyTransportRecordService.queryPageList(bo, pageQuery).getRows();
        ExcelUtil.exportExcel(list, "拉运记录", EpfyTransportRecordVo.class, response);
    }

    /**
     * 获取拉运记录详细信息
     *
     * @param transportId 主键
     */
    @SaCheckPermission("epfy:transportRecord:query")
    @GetMapping("/{transportId}")
    public R<EpfyTransportRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long transportId) {
        return R.ok(epfyTransportRecordService.queryById(transportId));
    }

    /**
     * 新增拉运记录
     */
    @SaCheckPermission("epfy:transportRecord:add")
    @Log(title = "拉运记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    @AttachUpload(sourceIdExpression = "#bo.transportId", attachIdExpression = {"#bo.measurementVoucher", "#bo.photo", "#bo.emptyVehicleMeasurement", "#bo.leadSealDischargePort", "#bo.emptyTonnageVehicles", "#bo.heavyVehicleTonnage"})
    public R<Void> add(@Validated(AddGroup.class) @RequestBody EpfyTransportRecordBo bo) {
        Boolean flag = epfyTransportRecordService.insertByBo(bo);
        if (bo.getTransportId() != null && flag) {
            EpfyTransportApplicationBo appBo = new EpfyTransportApplicationBo();
            appBo.setAppId(bo.getApplicationId());
            appBo.setTransportStatus(1);
            epfyTransportApplicationService.updateByBo(appBo);
        }
        return toAjax(flag);
    }

    /**
     * 修改拉运记录
     */
    @SaCheckPermission("epfy:transportRecord:edit")
    @Log(title = "拉运记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EpfyTransportRecordBo bo) {
        return toAjax(epfyTransportRecordService.updateByBo(bo));
    }

    /**
     * 删除拉运记录
     *
     * @param transportIds 主键串
     */
    @SaCheckPermission("epfy:transportRecord:remove")
    @Log(title = "拉运记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{transportIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] transportIds) {
        Map<Long, Long> applicationIdMap = new HashMap<>();
        for (Long transportId : transportIds) {
            EpfyTransportRecordVo recordVo = epfyTransportRecordService.queryById(transportId);
            if (recordVo != null) {
                applicationIdMap.put(transportId, recordVo.getApplicationId());
            }
        }
        Boolean flag = epfyTransportRecordService.deleteWithValidByIds(List.of(transportIds), true);

        for (Map.Entry<Long, Long> entry : applicationIdMap.entrySet()) {
            Long applicationId = entry.getValue();

            EpfyTransportRecordBo bo = new EpfyTransportRecordBo();
            bo.setApplicationId(applicationId);
            List<EpfyTransportRecordVo> list = epfyTransportRecordService.queryList(bo);

            if (list.isEmpty()) {
                EpfyTransportApplicationBo appBo = new EpfyTransportApplicationBo();
                appBo.setAppId(applicationId);
                appBo.setTransportStatus(0);
                epfyTransportApplicationService.updateByBo(appBo);
            }
        }
        return toAjax(flag);
    }

    /**
     * 总览获取数据
     */
    @SaCheckPermission("epfy:transportRecord:list")
    @GetMapping("/statTransportRecordZongLan")
    public TableDataInfo<List<Map>> statTransportRecordZongLan(EpfyTransportRecordBo bo) {
        PageQuery pageQuery = new PageQuery(Integer.MAX_VALUE, 1);
        SimpleDateFormat daysdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthsdf = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat yearsdf = new SimpleDateFormat("yyyy");
        EpfyTransportRecordBo dayQuery = new EpfyTransportRecordBo();
        dayQuery.setMonthQuery(daysdf.format(bo.getTransportTime()));
        dayQuery.setFlowType(bo.getFlowType());
        EpfyTransportRecordBo monthQuery = new EpfyTransportRecordBo();
        monthQuery.setMonthQuery(monthsdf.format(bo.getTransportTime()));
        monthQuery.setFlowType(bo.getFlowType());
        EpfyTransportRecordBo yearQuery = new EpfyTransportRecordBo();
        yearQuery.setMonthQuery(yearsdf.format(bo.getTransportTime()));
        yearQuery.setFlowType(bo.getFlowType());

        List<Map> resultList = new ArrayList<>();
        // 获取当日数据
        TableDataInfo<EpfyTransportRecordVo> dayData = epfyTransportRecordService.queryPageList(dayQuery, pageQuery);
        // 获取当月数据
        TableDataInfo<EpfyTransportRecordVo> monthData = epfyTransportRecordService.queryPageList(monthQuery, pageQuery);
        // 获取当年数据
        TableDataInfo<EpfyTransportRecordVo> yearData = epfyTransportRecordService.queryPageList(yearQuery, pageQuery);
        // 申请数量
        Map<String, Object> appNum = new HashMap<>();
        appNum.put("rowName", "申请数量(个)");
        appNum.put("dayData", dayData.getRows().stream()
                .map(EpfyTransportRecordVo::getApplicationId)
                .distinct()
                .count());
        appNum.put("monthData", monthData.getRows().stream()
                .map(EpfyTransportRecordVo::getApplicationId)
                .distinct()
                .count());
        appNum.put("yearData", yearData.getRows().stream()
                .map(EpfyTransportRecordVo::getApplicationId)
                .distinct()
                .count());
        resultList.add(appNum);

        // 拉运量
        Map<String, Object> transportNum = new HashMap<>();
        transportNum.put("rowName", "拉运量(方)");
        transportNum.put("dayData", dayData.getRows().stream()
                .map(EpfyTransportRecordVo::getNumber)
                .filter(num -> num != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
        );
        transportNum.put("monthData", monthData.getRows().stream()
                .map(EpfyTransportRecordVo::getNumber)
                .filter(num -> num != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
        );
        transportNum.put("yearData", yearData.getRows().stream()
                .map(EpfyTransportRecordVo::getNumber)
                .filter(num -> num != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
        );
        resultList.add(transportNum);

        // 车次
        Map<String, Object> trainNum = new HashMap<>();
        trainNum.put("rowName", "车次(次)");
        trainNum.put("dayData", dayData.getRows().size());
        trainNum.put("monthData", monthData.getRows().size());
        trainNum.put("yearData", yearData.getRows().size());
        resultList.add(trainNum);

        // 获取字典数据
        Map<String, String> epfyMediumType = dictService.getAllDictByDictType("epfy_yexiang_medium_type");
        for (Map.Entry<String, String> entry : epfyMediumType.entrySet()) {
            // 今日对应字典拉运量
            BigDecimal dayCollect = dayData.getRows().stream()
                    .filter(obj -> obj.getMediumType().toString().equals(entry.getKey()))
                    .map(EpfyTransportRecordVo::getNumber)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);


            // 当月对应字典拉运量
            BigDecimal monthCollect = monthData.getRows().stream()
                    .filter(obj -> obj.getMediumType().toString().equals(entry.getKey()))
                    .map(EpfyTransportRecordVo::getNumber)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);

            // 今年对应字典拉运量
            BigDecimal yearCollect = yearData.getRows().stream()
                    .filter(Objects::nonNull) // 过滤掉可能存在的null元素
                    .filter(obj -> obj.getMediumType() == Integer.parseInt(entry.getKey()))
                    .map(EpfyTransportRecordVo::getNumber)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);

            List<EpfyTransportRecordVo> yearList = yearData.getRows().stream()
                    .filter(obj -> obj.getMediumType() == Integer.parseInt(entry.getKey()))
                    .toList();
            Map<String, Object> map = new HashMap<>();
            map.put("rowName", entry.getValue() + "(方)");
            map.put("dayData", dayCollect);
            map.put("monthData", monthCollect);
            map.put("yearData", yearCollect);
            resultList.add(map);
        }
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(resultList);
        return tableDataInfo;
    }

    /**
     * 查询拉运记录列表
     */
    @SaCheckPermission("epfy:transportRecord:list")
    @GetMapping("/ecahrtsTransportRecordZongLan")
    public R<Map<String, List<Object>>> ecahrtsTransportRecordZongLan(
            @RequestParam("danwei") Long danwei,
            @RequestParam("flowType") Integer flowType,
            @RequestParam("dateTime") String dateTime) {

        EpfyTransportRecordBo boQuery = new EpfyTransportRecordBo();
        PageQuery pageQuery = new PageQuery(Integer.MAX_VALUE, 1);
        boQuery.setMonthQuery(dateTime);
        boQuery.setFlowType(flowType);
        // 非公司级
        if (!danwei.toString().equals("83")) {
            boQuery.setWorkArea(danwei);
        }
        // 获取字典数据
        Map<String, String> epfyMediumType = dictService.getAllDictByDictType("epfy_yexiang_medium_type");

        Map<String, List<Object>> dataMap = new HashMap<>();
        for (Map.Entry<String, String> entry : epfyMediumType.entrySet()) {
            dataMap.put(entry.getKey(), new ArrayList<>());
        }

        // 获取当日数据
        TableDataInfo<EpfyTransportRecordVo> dayData = epfyTransportRecordService.queryPageList(boQuery, pageQuery);
        List<EpfyTransportRecordVo> dataList = dayData.getRows();
        List<String> MonthList = generateMonthlyStrings(dateTime);
        for (String month : MonthList) {

            for (Map.Entry<String, String> entry : epfyMediumType.entrySet()) {
                // 该月份对应字典的拉运数据量
                BigDecimal collect = dataList.stream()
                        .filter(obj -> YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-M")).equals(YearMonth.from(obj.getTransportTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())))
                        .filter(obj -> obj.getMediumType().toString().equals(entry.getKey()))
                        .map(EpfyTransportRecordVo::getNumber)

                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .setScale(2, RoundingMode.HALF_UP);

                dataMap.get(entry.getKey()).add(collect);
            }

        }
        dataMap.put("XAxis", Collections.singletonList(MonthList));
        return R.ok(dataMap);
    }

    /**
     * 查询拉运记录列表
     */
    @SaCheckPermission("epfy:transportRecord:list")
    @GetMapping("/ecahrtsNiJiangZongLan")
    public R<Map<String, List<Object>>> ecahrtsNiJiangZongLan(
            @RequestParam("danwei") Long danwei,
            @RequestParam("flowType") Integer flowType,
            @RequestParam("dateTime") String dateTime) {

        EpfyTransportRecordBo boQuery = new EpfyTransportRecordBo();
        PageQuery pageQuery = new PageQuery(Integer.MAX_VALUE, 1);
        boQuery.setMonthQuery(dateTime);
        boQuery.setFlowType(flowType);
        // 非公司级
        if (!danwei.toString().equals("83")) {
            boQuery.setWorkArea(danwei);
        }
        // 获取字典数据
        Map<String, String> epfyMediumType = dictService.getAllDictByDictType("epfy_medium_category");
        Map<String, List<Object>> dataMap = new HashMap<>();
        for (Map.Entry<String, String> entry : epfyMediumType.entrySet()) {
            dataMap.put(entry.getKey(), new ArrayList<>());
        }
        dataMap.put("suizuan", new ArrayList<>());
        dataMap.put("jizhong", new ArrayList<>());

        List<String> Months = generateMonthlyStrings(dateTime);
        // 混合相数据
        EpfyTransportRecordBo bo1 = new EpfyTransportRecordBo();
        bo1.setMonthQuery(dateTime);
        bo1.setFlowType(flowType);
        bo1.setMediumCategory(3);
        if (!danwei.toString().equals("83")) {
            bo1.setOperationArea(danwei);
        }
        TableDataInfo<EpfyTransportRecordVo> hunheData = epfyTransportRecordService.queryPageList(bo1, pageQuery);
        List<EpfyTransportRecordVo> list1 = hunheData.getRows();
        for (String month : Months) {
            BigDecimal collect = list1.stream()
                    .filter(obj -> YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-M")).equals(YearMonth.from(obj.getTransportTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())))
                    .map(EpfyTransportRecordVo::getNumber)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            dataMap.get("jizhong").add(collect);
        }

        // 获取当日数据
        TableDataInfo<EpfyTransportRecordVo> dayData = epfyTransportRecordService.queryPageList(boQuery, pageQuery);
        List<EpfyTransportRecordVo> dataList = dayData.getRows();
        List<String> MonthList = generateMonthlyStrings(dateTime);
        for (String month : MonthList) {
            BigDecimal collect1 = BigDecimal.ZERO;// 固
            BigDecimal collect2 = BigDecimal.ZERO;// 液
            BigDecimal collect3 = BigDecimal.ZERO;// 混合
            for (Map.Entry<String, String> entry : epfyMediumType.entrySet()) {
                if (entry.getKey().equals("1")) {
                    collect1 = dataList.stream()
                            .filter(obj -> YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-M")).equals(YearMonth.from(obj.getTransportTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())))
                            .filter(obj -> obj.getMediumCategory().toString().equals(entry.getKey()))
                            // 固相+液相 = 随钻处理，混合相 = 集中处理，
                            .map(EpfyTransportRecordVo::getNumber)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                } else if (entry.getKey().equals("2")) {
                    collect2 = dataList.stream()
                            .filter(obj -> YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-M")).equals(YearMonth.from(obj.getTransportTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())))
                            .filter(obj -> obj.getMediumCategory().toString().equals(entry.getKey()))
                            // 固相+液相 = 随钻处理，混合相 = 集中处理，
                            .map(EpfyTransportRecordVo::getNumber)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(2, RoundingMode.HALF_UP);
                }
            }
            dataMap.get("suizuan").add(collect1.add(collect2).setScale(2, RoundingMode.HALF_UP));
        }
        dataMap.put("XAxis", Collections.singletonList(MonthList));
        return R.ok(dataMap);
    }

    public Map<String, Object> getMapData(String rowName, List<EpfyTransportRecordVo> dataList) {
        Map<String, Object> map = new HashMap<>();
        map.put("rowName", rowName);
        map.put("applicationNum", dataList.stream()
                .map(EpfyTransportRecordVo::getApplicationId)
                .distinct()
                .count());
        map.put("transportNum", dataList.stream()
                .map(EpfyTransportRecordVo::getNumber)
                .filter(num -> num != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
        );
        map.put("trainNum", dataList.size());
        return map;
    }

    /**
     * 总览获取数据
     */
    @SaCheckPermission("epfy:transportRecord:list")
    @GetMapping("/statNiJiangZongLan")
    public TableDataInfo<List<Map>> statNiJiangZongLan(EpfyTransportRecordBo bo) {
        PageQuery pageQuery = new PageQuery(Integer.MAX_VALUE, 1);
        SimpleDateFormat daysdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthsdf = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat yearsdf = new SimpleDateFormat("yyyy");
        EpfyTransportRecordBo dayQuery = new EpfyTransportRecordBo();
        dayQuery.setMonthQuery(daysdf.format(bo.getTransportTime()));
        dayQuery.setFlowType(bo.getFlowType());
        EpfyTransportRecordBo monthQuery = new EpfyTransportRecordBo();
        monthQuery.setMonthQuery(monthsdf.format(bo.getTransportTime()));
        monthQuery.setFlowType(bo.getFlowType());
        EpfyTransportRecordBo yearQuery = new EpfyTransportRecordBo();
        yearQuery.setMonthQuery(yearsdf.format(bo.getTransportTime()));
        yearQuery.setFlowType(bo.getFlowType());

        List<Map> resultList = new ArrayList<>();
        // 获取当日数据
        TableDataInfo<EpfyTransportRecordVo> dayData = epfyTransportRecordService.queryPageList(dayQuery, pageQuery);
        // 获取当月数据
        TableDataInfo<EpfyTransportRecordVo> monthData = epfyTransportRecordService.queryPageList(monthQuery, pageQuery);
        // 获取当年数据
        TableDataInfo<EpfyTransportRecordVo> yearData = epfyTransportRecordService.queryPageList(yearQuery, pageQuery);
        // 申请数量
        Map<String, Object> appNum = new HashMap<>();
        appNum.put("rowName", "申请数量(个)");
        appNum.put("dayData", dayData.getRows().stream()
                .map(EpfyTransportRecordVo::getApplicationId)
                .distinct()
                .count());
        appNum.put("monthData", monthData.getRows().stream()
                .map(EpfyTransportRecordVo::getApplicationId)
                .distinct()
                .count());
        appNum.put("yearData", yearData.getRows().stream()
                .map(EpfyTransportRecordVo::getApplicationId)
                .distinct()
                .count());
        resultList.add(appNum);

        // 拉运量
        Map<String, Object> transportNum = new HashMap<>();
        transportNum.put("rowName", "拉运量(方)");
        transportNum.put("dayData", dayData.getRows().stream()
                .map(EpfyTransportRecordVo::getNumber)
                .filter(num -> num != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
        );
        transportNum.put("monthData", monthData.getRows().stream()
                .map(EpfyTransportRecordVo::getNumber)
                .filter(num -> num != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
        );
        transportNum.put("yearData", yearData.getRows().stream()
                .map(EpfyTransportRecordVo::getNumber)
                .filter(num -> num != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP)
        );
        resultList.add(transportNum);

        // 车次
        Map<String, Object> trainNum = new HashMap<>();
        trainNum.put("rowName", "车次(次)");
        trainNum.put("dayData", dayData.getRows().size());
        trainNum.put("monthData", monthData.getRows().size());
        trainNum.put("yearData", yearData.getRows().size());
        resultList.add(trainNum);

        // 获取字典数据
        Map<String, String> epfyMediumType = dictService.getAllDictByDictType("epfy_medium_category");
        BigDecimal guDay = BigDecimal.ZERO;
        BigDecimal guMonth = BigDecimal.ZERO;
        BigDecimal guYear = BigDecimal.ZERO;
        BigDecimal yeDay = BigDecimal.ZERO;
        BigDecimal yeMonth = BigDecimal.ZERO;
        BigDecimal yeYear = BigDecimal.ZERO;
        BigDecimal heDay = BigDecimal.ZERO;
        BigDecimal heMonth = BigDecimal.ZERO;
        BigDecimal heYear = BigDecimal.ZERO;
        for (Map.Entry<String, String> entry : epfyMediumType.entrySet()) {
            // 今日对应字典拉运量
            BigDecimal dayCollect = dayData.getRows().stream()
                    .filter(obj -> obj.getMediumCategory().toString().equals(entry.getKey()))
                    .map(EpfyTransportRecordVo::getNumber)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);

            // 当月对应字典拉运量
            BigDecimal monthCollect = monthData.getRows().stream()
                    .filter(obj -> obj.getMediumCategory().toString().equals(entry.getKey()))
                    .map(EpfyTransportRecordVo::getNumber)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);


            // 今年对应字典拉运量
            BigDecimal yearCollect = yearData.getRows().stream()
                    .filter(Objects::nonNull) // 过滤掉可能存在的null元素
                    .filter(obj -> obj.getMediumCategory() == Integer.parseInt(entry.getKey()))
                    .map(EpfyTransportRecordVo::getNumber)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);

            List<EpfyTransportRecordVo> yearList = yearData.getRows().stream()
                    .filter(obj -> obj.getMediumCategory() == Integer.parseInt(entry.getKey()))
                    .toList();

            if (entry.getKey().equals("1")) {
                // 固态
                guDay = guDay.add(dayCollect);
                guMonth = guMonth.add(monthCollect);
                guYear = guYear.add(yearCollect);
            } else if (entry.getKey().equals("2")) {
                // 液质
                yeDay = yeDay.add(dayCollect);
                yeMonth = yeMonth.add(monthCollect);
                yeYear = yeYear.add(yearCollect);
            } else if (entry.getKey().equals("3")) {
                // 混合
                heDay = heDay.add(dayCollect);
                heMonth = heMonth.add(monthCollect);
                heYear = heYear.add(yearCollect);
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("rowName", "集中处理(方)");
        map.put("dayData", heDay);
        map.put("monthData", heMonth);
        map.put("yearData", heYear);
        resultList.add(map);

        Map<String, Object> map1 = new HashMap<>();
        map1.put("rowName", "随钻处理(方)");
        map1.put("dayData", yeDay.add(guDay));
        map1.put("monthData", yeMonth.add(guMonth));
        map1.put("yearData", yeYear.add(guYear));
        resultList.add(map1);

        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(resultList);
        return tableDataInfo;
    }

    /**
     * 月度统计报表接口
     */
    @SaCheckPermission("epfy:transportRecord:list")
    @GetMapping("/monthStat")
    public TableDataInfo<List<Map>> monthStat(EpfyTransportRecordBo bo) {
        PageQuery pageQuery = new PageQuery(Integer.MAX_VALUE, 1);
        List<EpfyTransportRecordVo> list = new ArrayList<>();
        // 集中站月度统计处理
        if (Objects.equals(bo.getUnloadLocationType(), 2) && Objects.equals(bo.getFlowType(), 2)) {
            list = epfyTransportRecordService.queryPageList(bo, pageQuery).getRows();
            list = list.stream()
                    .filter(record -> record.getMediumCategory() != 3)
                    .collect(Collectors.toList());
            if (bo.getWellName() != null) {
                bo.setArrivalPoint(bo.getWellName());
            }
            if (bo.getMediumCategory() == null) {
                bo.setMediumCategory(3);
            }

            bo.setWellName(null);
            List<EpfyTransportRecordVo> listArrival = epfyTransportRecordService.queryPageList(bo, pageQuery).getRows();
            for (EpfyTransportRecordVo vo : listArrival) {
                vo.setWellName(vo.getArrivalPoint());
            }
            list.addAll(listArrival);

        } else {
            list = epfyTransportRecordService.queryPageList(bo, pageQuery).getRows();
        }

        Map<List<Object>, EpfyTransportRecordVo> mergedMap = list.stream()

                .collect(Collectors.groupingBy(
                        e -> {

                            Date transportTime = e.getTransportTime();
                            if (transportTime == null) {
                                throw new IllegalArgumentException("transportTime不能为null");
                            }
                            YearMonth yearMonth = YearMonth.from(
                                    transportTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                            );

                            return Arrays.asList(
                                    yearMonth,
                                    e.getWorkArea(),
//                        e.getWellName(),
                                    e.getMediumCategory(),
                                    e.getMediumType(),
                                    e.getDeparturePoint()
                            );
                        },
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                group -> {
                                    EpfyTransportRecordVo merged = new EpfyTransportRecordVo();
                                    EpfyTransportRecordVo first = group.get(0);

                                    YearMonth yearMonth = YearMonth.from(
                                            first.getTransportTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                                    );
                                    Date firstDayOfMonth = Date.from(
                                            yearMonth.atDay(1).atStartOfDay(ZoneId.systemDefault()).toInstant()
                                    );
                                    merged.setTransportTime(firstDayOfMonth);

                                    merged.setWorkArea(first.getWorkArea());
//                        merged.setWellName(first.getWellName());
                                    merged.setMediumCategory(first.getMediumCategory());
                                    merged.setMediumType(first.getMediumType());
                                    merged.setDeparturePoint(first.getDeparturePoint());
                                    BigDecimal sum = group.stream()
                                            .map(EpfyTransportRecordVo::getNumber)
                                            .filter(Objects::nonNull)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                                            .setScale(2, RoundingMode.HALF_UP);

                                    merged.setNumber(sum);

                                    merged.setTransportId(null);
                                    return merged;
                                }
                        )
                ));
        List<EpfyTransportRecordVo> result = mergedMap.values().stream()
                .filter(vo -> vo.getTransportTime() != null
                        && vo.getWorkArea() != null
//                && vo.getWellName() != null
                        && vo.getMediumCategory() != null
                        && vo.getDeparturePoint() != null
                        && vo.getMediumType() != null)
                .sorted(Comparator
                                .comparing(EpfyTransportRecordVo::getTransportTime)
                                .thenComparing(EpfyTransportRecordVo::getWorkArea)
//                .thenComparing(EpfyTransportRecordVo::getWellName)
                                .thenComparing(EpfyTransportRecordVo::getMediumCategory)
                                .thenComparing(EpfyTransportRecordVo::getDeparturePoint)
                                .thenComparing(EpfyTransportRecordVo::getMediumType)
                )
                .collect(Collectors.toList());
        if (bo.getWorkArea() != null) {
            result = result.stream().filter(record -> Objects.equals(record.getWorkArea(), bo.getWorkArea())).toList();
        }
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(result);
        return tableDataInfo;
    }

    @PostMapping("/downloadMeasurementVoucher")
    public void downloadMeasurementVoucher(EpfyTransportRecordBo bo, HttpServletResponse response) {
        if (bo.getTransportId() == null) {
            throw new ServiceException("未选择记录");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
        EpfyTransportRecordVo epfyTransportRecordVo = epfyTransportRecordService.queryById(bo.getTransportId());
        EpfyTransportApplicationVo epfyTransportApplicationVo = epfyTransportApplicationService.queryById(epfyTransportRecordVo.getApplicationId());


        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("二连分公司废液收集计量凭证.xls", StandardCharsets.UTF_8);
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        InputStream templateInputStream = null;
        ExcelWriter excelWriter = null;
        try {
            // 加载模板资源
            ClassPathResource templateResource = new ClassPathResource("template/二连分公司废液收集计量凭证模板.xls");
            templateInputStream = templateResource.getInputStream();

            String guikouUrl = getQianMingImgUrl(epfyTransportApplicationVo.getSuperviseApprover());
            String zuoyequUrl = getQianMingImgUrl(epfyTransportApplicationVo.getOperationAreaReviewer());
//            String sijiUrl = getQianMingImgUrl(epfyTransportRecordVo.getTransporter());

            // 准备填充数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("liquidLoadingLocation", epfyTransportRecordVo.getDeparturePoint());
            dataMap.put("pumpLocation", epfyTransportRecordVo.getPumpLocation());
            dataMap.put("unloadingLocation", epfyTransportRecordVo.getUnloadLocation());

            dataMap.put("transportTime", epfyTransportRecordVo.getTransportTime() == null ? "" : sdf.format(epfyTransportRecordVo.getTransportTime()));
            dataMap.put("pumpTime", epfyTransportRecordVo.getPumpTime() == null ? "" : sdf.format(epfyTransportRecordVo.getPumpTime()));
            dataMap.put("unloadingTime", epfyTransportRecordVo.getUnloadTime() == null ? "" : sdf.format(epfyTransportRecordVo.getPumpTime()));
            dataMap.put("sealNo", epfyTransportRecordVo.getSealNo());

            dataMap.put("heavyVehicleTonnageNum", epfyTransportRecordVo.getHeavyVehicleTonnageNum() == null ? "" : epfyTransportRecordVo.getHeavyVehicleTonnageNum().toString());
            dataMap.put("emptyTonnageVehiclesNum", epfyTransportRecordVo.getEmptyTonnageVehiclesNum() == null ? "" : epfyTransportRecordVo.getEmptyTonnageVehiclesNum().toString());
            if (epfyTransportRecordVo.getMediumType() == null) {
                dataMap.put("mediumType", "");
            } else {
                dataMap.put("mediumType", dictService.getDictLabel("epfy_medium_type", epfyTransportRecordVo.getMediumType().toString()));
            }
            dataMap.put("number", epfyTransportRecordVo.getNumber() == null ? "" : epfyTransportRecordVo.getNumber().toString());
            dataMap.put("unloadNumber", epfyTransportRecordVo.getUnloadNumber() == null ? "" : epfyTransportRecordVo.getUnloadNumber().toString());
            dataMap.put("licensePlate", epfyTransportRecordVo.getLicensePlate());
            dataMap.put("sender", epfyTransportRecordVo.getSender());
            dataMap.put("superintendent", epfyTransportRecordVo.getSuperintendent());

            dataMap.put("guiKouQianMing", guikouUrl != null ? new URI(guikouUrl).toURL() : "");
            dataMap.put("zuoYeQuQianMing", zuoyequUrl != null ? new URI(zuoyequUrl).toURL() : "");
//            dataMap.put("sijiQianMing", sijiUrl != null ? new URI(sijiUrl).toURL() : "");

            // 使用EasyExcel写入响应流
            excelWriter = EasyExcel.write(response.getOutputStream())
                    .excelType(ExcelTypeEnum.XLS)
                    .withTemplate(templateInputStream)
                    .build();
            excelWriter.fill(dataMap, EasyExcel.writerSheet().build());
        } catch (Exception e) {
            throw new ServiceException("生成凭证失败：" + e.getMessage());
        } finally {
            if (excelWriter != null) {
                excelWriter.close();
            }
            IoUtil.close(templateInputStream);
        }
    }

    private String getQianMingImgUrl(String reviewer) {
        if (reviewer == null) {
            log.warn("未找到签名文件");
            return null;
        }

        CommAttachBo commAttachBo = new CommAttachBo();
        commAttachBo.setAttachSourceId(reviewer);
        List<CommAttachVo> commAttachVos = commAttachService.queryList(commAttachBo);
        if (CollectionUtil.isEmpty(commAttachVos)) {
            log.warn("未找到签名文件 {}", reviewer);
            return null;
        }

        return commAttachVos.getFirst().getAttachUrl();
    }

    /**
     * 复制拉运记录
     */
    @SaCheckPermission("epfy:transportRecord:add")
    @Log(title = "拉运记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/copyTransportRecord")
    public R<Void> copy(@Validated(AddGroup.class) @RequestBody EpfyTransportRecordBo bo) {
        bo.setTransportId(null);
        bo.setMeasurementVoucher(null);
        bo.setPhoto(null);
        bo.setMeasuringPhoto(null);
        bo.setTankerSquare(null);
        bo.setAccessories(null);
        bo.setHeavyVehicleTonnage(null);
        bo.setEmptyTonnageVehicles(null);
        bo.setLeadSealDischargePort(null);
        bo.setEmptyVehicleMeasurement(null);
        bo.setUnloadApproval(1);
        bo.setUnloadApprovalTime(null);
        bo.setSuperintendentApproval(1);
        bo.setSenderApproval(1);
        bo.setSuperintendentApprovalTime(null);
        Boolean flag = epfyTransportRecordService.insertByBo(bo);
        //将复制所选申请的状态设置为已拉运
        EpfyTransportApplicationVo vo = epfyTransportApplicationService.queryById(bo.getApplicationId());
        EpfyTransportApplicationBo applicationBo = new EpfyTransportApplicationBo();
        applicationBo.setAppId(vo.getAppId());
        applicationBo.setExamineApproveTime(vo.getExamineApproveTime());
        applicationBo.setOperationAreaReviewedTime(vo.getOperationAreaReviewedTime());
        applicationBo.setEndExamineApproveTime(vo.getEndExamineApproveTime());
        applicationBo.setEndOperationAreaReviewedTime(vo.getEndOperationAreaReviewedTime());
        applicationBo.setTransportStatus(1);
        epfyTransportApplicationService.updateByBo(applicationBo);
        return toAjax(flag);
    }
}


