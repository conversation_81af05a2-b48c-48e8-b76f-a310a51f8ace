import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MeteringDeviceTypeForm, MeteringDeviceTypeQuery, MeteringDeviceTypeVO } from '@/api/epms/epcom/meteringDeviceType/types';

/**
 * 查询计量器具类型列表
 * @param query
 * @returns {*}
 */

export const listMeteringDeviceType = (query?: MeteringDeviceTypeQuery): AxiosPromise<MeteringDeviceTypeVO[]> => {
  return request({
    url: '/epms/meteringDeviceType/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询计量器具类型详细
 * @param deviceTypeId
 */
export const getMeteringDeviceType = (deviceTypeId: string | number): AxiosPromise<MeteringDeviceTypeVO> => {
  return request({
    url: '/epms/meteringDeviceType/' + deviceTypeId,
    method: 'get'
  });
};

/**
 * 新增计量器具类型
 * @param data
 */
export const addMeteringDeviceType = (data: MeteringDeviceTypeForm) => {
  return request({
    url: '/epms/meteringDeviceType',
    method: 'post',
    data: data
  });
};

/**
 * 修改计量器具类型
 * @param data
 */
export const updateMeteringDeviceType = (data: MeteringDeviceTypeForm) => {
  return request({
    url: '/epms/meteringDeviceType',
    method: 'put',
    data: data
  });
};

/**
 * 删除计量器具类型
 * @param deviceTypeId
 */
export const delMeteringDeviceType = (deviceTypeId: string | number | Array<string | number>) => {
  return request({
    url: '/epms/meteringDeviceType/' + deviceTypeId,
    method: 'delete'
  });
};
