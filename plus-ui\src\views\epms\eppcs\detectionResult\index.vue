<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="检测日期" prop="detectionDateRange">
              <el-date-picker
                v-model="queryParams.detectionDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChange"
                clearable
              />
            </el-form-item>
            <el-form-item label="检测项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" placeholder="请输入检测项目名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="样品编号" prop="sampleNumber">
              <el-input v-model="queryParams.sampleNumber" placeholder="请输入样品编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="检测状态" prop="detectionStatus">
              <el-select v-model="queryParams.detectionStatus" placeholder="请选择检测状态" clearable>
                <el-option v-for="dict in eppcs_detection_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['eppcs:detectionResult:add']">填报检测结果</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['eppcs:detectionResult:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete()"
              v-hasPermi="['eppcs:detectionResult:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['eppcs:detectionResult:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table stripe border v-loading="loading" :data="detectionResultList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="序号"
          align="center"
          width="55"
          type="index"
          :index="(index) => (queryParams.pageNum - 1) * queryParams.pageSize + index + 1"
        />
        <el-table-column label="样品编号" align="center" prop="sampleNumber" show-overflow-tooltip />
        <el-table-column label="检测项目名称" align="center" prop="projectName" show-overflow-tooltip />
        <el-table-column label="检测结果" align="center" prop="resultStatus" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.resultStatus === 0" type="success">达标</el-tag>
            <el-tag v-else-if="scope.row.resultStatus === 1" type="danger">不达标</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="报送时间" align="center" prop="submitTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.submitTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审批时间" align="center" prop="approvalTime" width="110">
          <template #default="scope">
            <span>{{ parseTime(scope.row.approvalTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审批意见" align="center" prop="approvalRemark" show-overflow-tooltip />
        <el-table-column label="检测状态" align="center" prop="detectionStatus" width="120">
          <template #default="scope">
            <dict-tag :options="eppcs_detection_status" :value="scope.row.detectionStatus" />
          </template>
        </el-table-column>
        <el-table-column label="预警标记" align="center" prop="warningFlag" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.warningFlag === 1" type="warning" size="small">预警</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width" width="240">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-tooltip content="修改" placement="top" v-if="[0, 1].includes(scope.row.detectionStatus)">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['eppcs:detectionResult:edit']">
                修改
              </el-button>
            </el-tooltip>
            <el-tooltip content="提交" placement="top" v-if="[0, 1].includes(scope.row.detectionStatus)">
              <el-button link type="success" icon="Upload" @click="handleSubmit(scope.row)" v-hasPermi="['eppcs:detectionResult:submit']">
                提交
              </el-button>
            </el-tooltip>

            <el-tooltip content="属地确认" placement="top" v-if="canLocalConfirm(scope.row)">
              <el-button link type="warning" icon="Check" @click="handleLocalConfirm(scope.row)" v-hasPermi="['eppcs:detectionResult:localConfirm']">
                属地确认
              </el-button>
            </el-tooltip>

            <el-tooltip content="管理中心确认" placement="top" v-if="canCenterConfirm(scope.row)">
              <el-button link type="info" icon="CircleCheck" @click="handleCenterConfirm(scope.row)" v-hasPermi="['eppcs:detectionResult:centerConfirm']">
                管理中心确认
              </el-button>
            </el-tooltip>

            <el-tooltip content="删除" placement="top" v-if="[0, 1].includes(scope.row.detectionStatus)">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['eppcs:detectionResult:remove']">
                删除
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改检测结果对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="900px" append-to-body>
      <el-form ref="detectionResultFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="取样申请" prop="applicationId">
              <el-select v-model="form.applicationId" placeholder="请选择取样申请" style="width: 100%" :disabled="isViewMode" @change="handleApplicationChange">
                <el-option v-for="app in applicationOptions" :key="app.applicationId" :label="`${app.projectName} - ${app.samplingPoint}`" :value="app.applicationId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品编号" prop="sampleNumber">
              <el-input v-model="form.sampleNumber" placeholder="请输入样品编号" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="检测日期" prop="detectionDate">
              <el-date-picker
                v-model="form.detectionDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择检测日期"
                style="width: 100%"
                :disabled="isViewMode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测标准" prop="detectionStandard">
              <el-select v-model="form.detectionStandard" placeholder="请选择检测标准" style="width: 100%" :disabled="isViewMode">
                <el-option v-for="dict in eppcs_detection_standard" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 检测结果数据表单 -->
        <el-card shadow="never" style="margin-bottom: 20px" v-if="!isViewMode">
          <template #header>
            <span style="font-weight: bold">检测结果数据</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8" v-for="(item, key) in detectionItems" :key="key">
              <el-form-item :label="item.label" :prop="`detectionData.${key}`">
                <el-input-number
                  v-model="form.detectionData[key]"
                  :placeholder="`请输入${item.label}`"
                  :precision="item.precision || 2"
                  :min="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-form-item label="检测报告附件" prop="reportFile">
          <attachFileUpload
            v-model="form.reportFile"
            :attach-source-id="form.resultId"
            attach-source-type="detectionResult"
            attach-category="report"
            :limit="10"
            :file-size="50"
            :disabled="isViewMode"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" :disabled="isViewMode" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!isViewMode" :loading="buttonLoading" type="primary" @click="submitForm('save')">保存</el-button>
          <el-button
            v-if="!isViewMode && (form.detectionStatus === 0 || form.detectionStatus === 1)"
            :loading="buttonLoading"
            type="success"
            @click="submitForm('submit')"
            >提交</el-button
          >
          <el-button @click="cancel">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 属地确认对话框 -->
    <el-dialog title="属地确认检测结果" v-model="localConfirmDialog.visible" width="700px" append-to-body>
      <el-form ref="localConfirmFormRef" :model="form" :rules="localConfirmRules" label-width="120px">
        <el-card shadow="never" style="margin-bottom: 20px">
          <template #header>
            <span style="font-weight: bold">检测结果信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="样品编号">
                <span>{{ form.sampleNumber }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测项目名称">
                <span>{{ form.projectName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="检测日期">
                <span>{{ parseTime(form.detectionDate, '{y}-{m}-{d}') }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="当前状态">
                <dict-tag :options="eppcs_detection_status" :value="form.detectionStatus" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="检测报告附件" prop="reportFile">
            <attachFileUpload
              v-model="form.reportFile"
              :attach-source-id="form.resultId"
              attach-source-type="detectionResult"
              attach-category="report"
              :disabled="true"
            />
          </el-form-item>
        </el-card>

        <el-form-item label="审批意见" prop="approvalRemark">
          <el-input v-model="form.approvalRemark" type="textarea" :rows="4" placeholder="请输入审批意见（必填）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="localConfirmLoading" type="success" @click="handleLocalConfirmSubmit(1)">达标确认</el-button>
          <el-button :loading="localConfirmLoading" type="danger" @click="handleLocalConfirmSubmit(2)">不达标报告</el-button>
          <el-button @click="localConfirmDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 管理中心确认对话框 -->
    <el-dialog title="管理中心确认检测结果" v-model="centerConfirmDialog.visible" width="700px" append-to-body>
      <el-form ref="centerConfirmFormRef" :model="form" :rules="centerConfirmRules" label-width="120px">
        <el-card shadow="never" style="margin-bottom: 20px">
          <template #header>
            <span style="font-weight: bold">检测结果信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="样品编号">
                <span>{{ form.sampleNumber }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测项目名称">
                <span>{{ form.projectName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="检测结果">
                <el-tag v-if="form.resultStatus === 0" type="success">达标</el-tag>
                <el-tag v-else-if="form.resultStatus === 1" type="danger">不达标</el-tag>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="当前状态">
                <dict-tag :options="eppcs_detection_status" :value="form.detectionStatus" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="属地审批意见" v-if="form.approvalRemark">
            <span>{{ form.approvalRemark }}</span>
          </el-form-item>
        </el-card>

        <el-form-item label="最终审批意见" prop="finalApprovalRemark">
          <el-input v-model="form.finalApprovalRemark" type="textarea" :rows="4" placeholder="请输入最终审批意见（必填）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="centerConfirmLoading" type="success" @click="handleCenterConfirmSubmit(1)">结束</el-button>
          <el-button :loading="centerConfirmLoading" type="danger" @click="handleCenterConfirmSubmit(2)">驳回</el-button>
          <el-button @click="centerConfirmDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DetectionResult" lang="ts">
import {
  listDetectionResult,
  getDetectionResult,
  delDetectionResult,
  addDetectionResult,
  updateDetectionResult,
  submitDetectionResult,
  localConfirmDetectionResult,
  centerConfirmDetectionResult
} from '@/api/epms/eppcs/detectionResult';
import { DetectionResultVO, DetectionResultQuery, DetectionResultForm } from '@/api/epms/eppcs/detectionResult/types';
import { listSamplingApplication } from '@/api/epms/eppcs/samplingApplication';
import dayjs from 'dayjs';
import { useUserStore } from '@/store/modules/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { eppcs_detection_status, eppcs_detection_standard } = toRefs<any>(
  proxy?.useDict('eppcs_detection_status', 'eppcs_detection_standard')
);

const detectionResultList = ref<DetectionResultVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const detectionResultFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 确认相关
const localConfirmDialog = reactive({
  visible: false
});
const centerConfirmDialog = reactive({
  visible: false
});
const localConfirmFormRef = ref<ElFormInstance>();
const centerConfirmFormRef = ref<ElFormInstance>();
const localConfirmLoading = ref(false);
const centerConfirmLoading = ref(false);
const localConfirmRules = reactive({
  approvalRemark: [{ required: true, message: '审批意见不能为空', trigger: 'blur' }]
});
const centerConfirmRules = reactive({
  finalApprovalRemark: [{ required: true, message: '最终审批意见不能为空', trigger: 'blur' }]
});

// 取样申请选项
const applicationOptions = ref<any[]>([]);

// 表单状态
const isNewForm = computed(() => !form.value.resultId);
const isViewMode = ref(false);

// 检测项目定义
const detectionItems = {
  ph: { label: 'pH值(无量纲)', precision: 2 },
  waterTemp: { label: '水温(℃)', precision: 1 },
  permanganateIndex: { label: '高锰酸钾指数(mg/L)', precision: 2 },
  cod: { label: '化学需氧量(mg/L)', precision: 2 },
  bod5: { label: '五日生化需氧量(mg/L)', precision: 2 },
  dissolvedOxygen: { label: '溶解氧(mg/L)', precision: 2 },
  totalPhosphorus: { label: '总磷(mg/L)', precision: 3 },
  totalNitrogen: { label: '总氮(mg/L)', precision: 3 },
  ammoniaNitrogen: { label: '氨氮(mg/L)', precision: 3 },
  fluoride: { label: '氟化物(mg/L)', precision: 3 },
  volatilePhenol: { label: '挥发酚(mg/L)', precision: 4 },
  cyanide: { label: '氰化物(mg/L)', precision: 4 },
  sulfide: { label: '硫化物(mg/L)', precision: 3 },
  petroleum: { label: '石油类(mg/L)', precision: 3 },
  hexavalentChromium: { label: '六价铬(mg/L)', precision: 4 },
  anionicSurfactant: { label: '阴离子表面活性剂(mg/L)', precision: 3 },
  copper: { label: '铜(mg/L)', precision: 4 },
  zinc: { label: '锌(mg/L)', precision: 4 },
  lead: { label: '铅(mg/L)', precision: 4 },
  cadmium: { label: '镉(mg/L)', precision: 5 },
  arsenic: { label: '砷(mg/L)', precision: 5 },
  totalMercury: { label: '总汞(mg/L)', precision: 5 },
  selenium: { label: '硒(mg/L)', precision: 5 },
  fecalColiform: { label: '粪大肠菌群(CFU/L)', precision: 0 }
};

const initFormData: DetectionResultForm = {
  resultId: undefined,
  applicationId: undefined,
  sampleNumber: undefined,
  projectName: undefined,
  detectionStandard: undefined,
  detectionDate: undefined,
  submitTime: undefined,
  detectionStatus: 0, // 默认草稿状态
  reportFile: undefined,
  resultData: undefined,
  resultStatus: undefined,
  approvalTime: undefined,
  approvalRemark: undefined,
  approver: undefined,
  remark: undefined,
  warningFlag: 0,
  detectionData: {},
  finalApprovalRemark: undefined
};

const testItemsOptions = [
  { value: 'pH值_无量纲', label: 'pH值 (无量纲)' },
  { value: '水温_℃', label: '水温 (℃)' },
  { value: '高锰酸钾指数_mg/L', label: '高锰酸钾指数 (mg/L)' },
  { value: '化学需氧量_mg/L', label: '化学需氧量 (mg/L)' },
  { value: '五日生化需氧量_mg/L', label: '五日生化需氧量 mg/L' },
  { value: '溶解氧_mg/L', label: '溶解氧 mg/L' },
  { value: '总磷_mg/L', label: '总磷 mg/L' },
  { value: '总氮_mg/L', label: '总氮 mg/L' },
  { value: '氨氮_mg/L', label: '氨氮 mg/L' },
  { value: '氟化物_mg/L', label: '氟化物 mg/L' },
  { value: '挥发酚_mg/L', label: '挥发酚 mg/L' },
  { value: '氰化物_mg/L', label: '氰化物 mg/L' },
  { value: '硫化物_mg/L', label: '硫化物 mg/L' },
  { value: '石油类_mg/L', label: '石油类 mg/L' },
  { value: '六价铬_mg/L', label: '六价铬 mg/L' },
  { value: '阴离子表面活性剂_mg/L', label: '阴离子表面活性剂 mg/L' },
  { value: '铜_mg/L', label: '铜 mg/L' },
  { value: '锌_mg/L', label: '锌 mg/L' },
  { value: '铅_mg/L', label: '铅 mg/L' },
  { value: '镉_mg/L', label: '镉 mg/L' },
  { value: '砷_mg/L', label: '砷 mg/L' },
  { value: '总汞_mg/L', label: '总汞 mg/L' },
  { value: '硒_mg/L', label: '硒 mg/L' },
  { value: '粪大肠菌群_CFU/L', label: '粪大肠菌群 CFU/L' }
];

const data = reactive<PageData<DetectionResultForm, DetectionResultQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    applicationId: undefined,
    resultData: undefined,
    resultStatus: undefined,
    approvalTime: undefined,
    approvalRemark: undefined,
    params: {}
  },
  rules: {
    reportFile: [{ required: true, message: '检测报告附件不能为空', trigger: 'blur' }],
    resultData: [{ required: true, message: '检测结果数据 JSON格式不能为空', trigger: 'blur' }],
    approvalTime: [{ required: true, message: '审批时间不能为空', trigger: 'blur' }],
    approvalRemark: [{ required: true, message: '审批建议不能为空', trigger: 'blur' }],
    remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
    testItemsUnits: [{ required: true, message: '检测项目及其单位不能为空', trigger: 'blur' }] // 修改：添加该字段的校验规则
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询检测结果列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDetectionResult(queryParams.value);
  detectionResultList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  detectionResultFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DetectionResultVO[]) => {
  ids.value = selection.map((item) => item.resultId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加检测结果';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: DetectionResultVO) => {
  reset();
  const _resultId = row?.resultId || ids.value[0];
  const res = await getDetectionResult(_resultId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改检测结果';
};

/** 提交按钮 */
const submitForm = () => {
  detectionResultFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.resultId) {
        await updateDetectionResult(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addDetectionResult(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DetectionResultVO) => {
  const _resultIds = row?.resultId || ids.value;
  await proxy?.$modal.confirm('是否确认删除检测结果编号为"' + _resultIds + '"的数据项？').finally(() => (loading.value = false));
  await delDetectionResult(_resultIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'epms/eppcs/detectionResult/export',
    {
      ...queryParams.value
    },
    `detectionResult_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  // 初始化testItemsValuesArray数组
  form.value.testItemsValuesArray = testItemsOptions.map((option) => ({
    name: option.value.split('_')[0], // 提取检测项目名称
    value: '' // 初始值为空
  }));
  getList();
});
</script>
